# OTP Authentication API Guide

This document provides instructions for using the OTP-based authentication system.

## Rate Limiting

The OTP system has rate limiting to prevent abuse:

- Maximum 3 OTP emails within a 5-minute window
- After exceeding the limit, you must wait for the window to expire

## API Endpoints

### 1. Register a New User

Register a new user with their email and name.

**Endpoint:** `http://localhost:9000/auth/customer/otp/register`  
**Method:** `POST`  
**Content-Type:** `application/json`

**Request Body:**

```json
{
  "identifier": "<EMAIL>",
  "first_name": "srinu",
  "last_name": "n"
}
```

**cURL Example:**

```bash
curl -X POST http://localhost:9000/auth/customer/otp/register \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "first_name": "srinu",
    "last_name": "n"
  }'
```

**Success Response:**

```json
{
  "type": "unauthorized",
  "message": "otp_generated"
}
```

**Possible Error Responses:**

```json
{
  "type": "unauthorized",
  "message": "User already registered. Please use login instead."
}
```

```json
{
  "type": "unauthorized",
  "message": "Too many OTP requests. Please try again after 5 minutes."
}
```

### 2. Generate OTP for Login

Generate an OTP for an existing user to log in.

**Endpoint:** `http://localhost:9000/auth/customer/otp`  
**Method:** `POST`  
**Content-Type:** `application/json`

**Request Body:**

```json
{
  "identifier": "<EMAIL>"
}
```

**cURL Example:**

```bash
curl -X POST http://localhost:9000/auth/customer/otp \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>"
  }'
```

**Success Response:**

```json
{
  "location": "otp_generated"
}
```

**Possible Error Responses:**

```json
{
  "type": "unauthorized",
  "message": "User not registered. Please register first."
}
```

```json
{
  "type": "unauthorized",
  "message": "Too many OTP requests. Please try again after 5 minutes."
}
```

```json
{
  "type": "unauthorized",
  "message": "Failed to send OTP email"
}
```

### 3. Verify OTP and Get Token

Verify the OTP and get an authentication token.

**Endpoint:** `http://localhost:9000/auth/customer/otp`  
**Method:** `POST`  
**Content-Type:** `application/json`

**Request Body:**

```json
{
  "identifier": "<EMAIL>",
  "otp": "948943"
}
```

**cURL Example:**

```bash
curl -X POST http://localhost:9000/auth/customer/otp \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "otp": "948943"
  }'
```

**Success Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Possible Error Responses:**

```json
{
  "type": "unauthorized",
  "message": "Invalid <NAME_EMAIL>"
}
```

## Notes

- OTPs are valid for 5 minutes after generation
- Each email can receive a maximum of 3 OTPs within a 5-minute window
- After exceeding the rate limit, you must wait for the window to expire before requesting another OTP
