# Dockerfile for Medusa Backend
# Use the official Node.js 20 image as the base image
FROM node:20

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy the yarn.lock file to ensure consistent dependency installation
COPY yarn.lock ./

# Install Yarn
RUN npm install yarn

# Copy the package.json and package-lock.json (if available) to the working directory
COPY package*.json ./

# Copy the .npmrc file to configure npm if private registries are used
COPY .npmrc ./

# Install only production dependencies to reduce image size
RUN yarn install --production

# Copy the rest of the application code to the container
COPY . .

# Expose the Medusa backend port (default is 9000)
EXPOSE 9000

# Command to seed the database and start the Medusa backend in development mode
CMD ["sh", "-c", "yarn add --dev @types/mocha && yarn add --dev @types/jest && yarn dev"]
