


export type OrdersPaymentProvider = {
  orderCount: string,
  percentage: string,
  paymentProviderId: string
}

export type OrdersPaymentProviderPopularityResult = {
  dateRangeFrom?: number
  dateRangeTo?: number,
  dateRangeFromCompareTo?: number,
  dateRangeToCompareTo?: number,
  current: OrdersPaymentProvider[]
  previous: OrdersPaymentProvider[]
}

export type OrdersPaymentProviderResponse = {
  analytics: OrdersPaymentProviderPopularityResult
}