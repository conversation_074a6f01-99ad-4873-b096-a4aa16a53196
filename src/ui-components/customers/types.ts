

export type Distributions = {
  returnCustomerRate: string,
  orderOneTimeFrequency?: string,
  orderRepeatFrequency?: string
}

export type CustomersRepeatCustomerRateResponse = {
  analytics: {
    dateRangeFrom: number
    dateRangeTo: number,
    dateRangeFromCompareTo?: number,
    dateRangeToCompareTo?: number,
    current: Distributions,
    previous: Distributions
  }
}

export type CustomersRetentionCustomerRateResponse = {
  analytics: {
    dateRangeFrom: number
    dateRangeTo: number,
    dateRangeFromCompareTo?: number,
    dateRangeToCompareTo?: number,
    current?: string,
    previous?: string
  }
}