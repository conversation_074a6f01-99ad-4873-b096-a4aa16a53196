

import { Heading } from "@camped-ai/ui";
import { Grid } from "@mui/material";
import { CustomersRepeatCustomerRateResponse } from "../types"
import { OrderFrequencyDistributionPieChart } from "./order-frequency-distribution-chart";

export const OrderFrequencyDistribution = ({repeatCustomerRateResponse, compareEnabled} : {repeatCustomerRateResponse: CustomersRepeatCustomerRateResponse, compareEnabled?: boolean}) => {
  return (
    <Grid container direction={'column'} alignItems={'center'} paddingTop={3}>
      <Grid item>
        <Heading level="h3">
          How orders were distributed?
        </Heading>
      </Grid>
      <Grid item>
        <OrderFrequencyDistributionPieChart repeatCustomerRateResponse={repeatCustomerRateResponse} compareEnabled={compareEnabled}/>
      </Grid>
    </Grid>
  )
}