

import { Heading, Alert } from "@camped-ai/ui";
import { ShoppingBag } from "@camped-ai/icons";
import { CircularProgress, Grid } from "@mui/material";
import type { DateRange } from "../../utils/types";
import { CustomersRepeatCustomerRateResponse } from "../types";
import { RepeatCustomerRateNummber } from "./customers-repeat-customer-rate-number";
import { OrderStatus } from "../../utils/types";
import { OrderFrequencyDistribution } from "./order-frequency-distribution";
import { deduceDateUrlParams } from "../../../ui-components/utils/helpers";
import { useEffect, useState } from "react";

const RepeatCustomerRateDetails = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled?: boolean}) => {

  const [data, setData] = useState<CustomersRepeatCustomerRateResponse | undefined>(undefined)

  const [error, setError] = useState<any>(undefined);

  const [isLoading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true);
  }, [dateRange, dateRangeCompareTo, orderStatuses])

  useEffect(() => {
    if (!isLoading) {
      return;
    }

    fetch(`/admin/customers-analytics/repeat-customer-rate?${deduceDateUrlParams(dateRange, dateRangeCompareTo, orderStatuses).toString()}`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then((result) => {
      setData(result)
      setLoading(false)
    })
    .catch((error) => {
      setError(error);
      console.error(error);
    }) 
  }, [isLoading])


  if (isLoading) {
    return <CircularProgress size={12}/>
  }

  if (error) {
    const trueError = error as any;
    const errorText = `Error when loading data. It shouldn't have happened - please raise an issue. For developer: ${trueError?.response?.data?.message}`
    return <Alert variant="error">{errorText}</Alert>
  }

  if (data && data.analytics == undefined) {
    return <Heading level="h3">Cannot get orders or customers</Heading>
  }

  if (data && data.analytics.dateRangeFrom) {
    return (
      <Grid container>
        <Grid item xs={12} md={12}>
          <RepeatCustomerRateNummber repeatCustomerRateResponse={data} compareEnabled={compareEnabled}/>
        </Grid>
        <Grid item xs={12} md={12}>
          <OrderFrequencyDistribution repeatCustomerRateResponse={data} compareEnabled={compareEnabled}/>
        </Grid>
      </Grid>
    )
  } else {
    return <Heading level="h3">No orders or customers</Heading>
  }
}

export const CustomersRepeatCustomerRate = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
  return (
    <Grid container paddingBottom={2} spacing={3}>
      <Grid item xs={12} md={12}>
          <Grid container spacing={2}>
            <Grid item>
              <ShoppingBag/>
            </Grid>
            <Grid item>
              <Heading level="h2">
                Repeat customer rate
              </Heading>
            </Grid>
          </Grid>
      </Grid>
      <Grid item xs={12} md={12}>
        <RepeatCustomerRateDetails orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
      </Grid>
    </Grid>
  )
}