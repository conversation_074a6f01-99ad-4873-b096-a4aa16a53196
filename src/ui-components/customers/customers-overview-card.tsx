

import { Heading } from "@camped-ai/ui";
import { Users } from "@camped-ai/icons";
import { Grid } from "@mui/material";
import type { DateRange } from "../utils/types";
import { CustomersNumber } from "./customers-number-overview";
import { CustomersByNewChart } from "./customers-by-new-chart";

export const CustomersOverviewCard = ({dateRange, dateRangeCompareTo, compareEnabled} : 
  {dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
  return (
    <Grid container paddingBottom={2} spacing={3}>
      <Grid item xs={12} md={12}>
          <Grid container spacing={2}>
            <Grid item>
              <Users/>
            </Grid>
            <Grid item>
              <Heading level="h2">
                New customers
              </Heading>
            </Grid>
          </Grid>
      </Grid>
      {/* <Grid item xs={12} md={12}>
        <CustomersNumber dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
      </Grid> */}
      <Grid item xs={12} md={12}>
        <CustomersByNewChart dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
      </Grid>
    </Grid>
  )
}