import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from 'lucide-react';
import type { MetricCard as MetricCardType} from 'src/admin/components/types';
import { Container } from '@camped-ai/ui';

export const MetricCard: React.FC<MetricCardType> = ({ title, value, icon: Icon, change }) => {
    const isPositive = change >= 0;

    return (
      <Container>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Icon className="w-6 h-6 text-blue-600 mr-4" />
            <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
          </div>
          <div className={`flex items-center ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            {isPositive ? <ArrowUpIcon size={16} /> : <ArrowDownIcon size={16} />}
            <span className="text-sm ml-1">{Math.abs(change)}%</span>
          </div>
        </div>
        <p className="text-2xl font-bold text-gray-800 mt-2">
          {value?.toLocaleString()}
        </p>
      </Container>
  );
};