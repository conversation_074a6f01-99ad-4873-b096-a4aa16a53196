import React from "react";
import {
  AreaChart,
  Area,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { format } from "date-fns";
import type { AnalyticsData } from "src/admin/components/types";

interface Props {
  data: AnalyticsData[];
  metric: keyof AnalyticsData;
  color: string;
}

export const AnalyticsChart: React.FC<Props> = ({ data, metric, color }) => {
  const formatMetricName = (name: string) => {
    return name
      .replace(/([A-Z])/g, " $1")
      .replace(/^./, (str) => str.toUpperCase());
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-blue-50">
      <div className="p-6">
        <h3 className="text-xl font-semibold text-blue-900 mb-2">
          {formatMetricName(metric)}
        </h3>
        <p className="text-blue-600 text-sm mb-4">Tracking data over time</p>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient
                  id={`color-${metric}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={color} stopOpacity={0.3} />
                  <stop offset="95%" stopColor={color} stopOpacity={0.05} />
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="3 3"
                vertical={false}
                stroke="#E2E8F0"
              />
              <XAxis
                dataKey="date"
                tickFormatter={(date) => format(new Date(date), "MMM d")}
                stroke="#64748B"
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={{ stroke: "#E2E8F0" }}
              />
              <YAxis
                stroke="#64748B"
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "white",
                  border: "1px solid #E5E7EB",
                  borderRadius: "6px",
                }}
              />
              <Area
                type="monotone"
                dataKey={metric}
                stroke="#2563EB"
                strokeWidth={2}
                fill="#3b82f6"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};
