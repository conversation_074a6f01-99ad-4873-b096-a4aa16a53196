import { Select, Text } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";
import { useState, useEffect } from "react";
import { RegionDTO } from "@camped-ai/framework/types";
import { useStore } from "@nanostores/react";
import { selectedcountry } from "../utils/helpers";

export const RegionSelect = () => {
  const value = useStore(selectedcountry);
  const setValue = (currency: string) => selectedcountry.set(currency);

  const [regions, setRegions] = useState<RegionDTO[] | undefined>(undefined);
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState<any>(undefined);

  useEffect(() => {
    fetch(`/admin/regions/`, { credentials: "include" })
      .then((res) => res.json())
      .then((result) => {
        setRegions(result.regions);
        setLoading(false);
      })
      .catch((error) => {
        setError(error);
        console.error(error);
      });
  }, []);

  return (
    <div className="w-[256px]">
      <Select size="small" onValueChange={setValue} value={value}>
        <Select.Trigger>
          <Select.Value placeholder="Select a currency" />
        </Select.Trigger>
        <Select.Content>
          {isLoading && <CircularProgress />}
          {error && <Text>Error loading regions</Text>}
          {regions && !regions.length && <Text>No regions</Text>}
          {regions &&
            regions.length > 0 &&
            [...new Set(regions.map((region) => region.currency_code))].map((currencyCode) => (
              <Select.Item key={currencyCode} value={currencyCode}>
                {currencyCode.toUpperCase()}
              </Select.Item>
            ))}
        </Select.Content>
      </Select>
    </div>
  );
};
