import { Heading, Tooltip } from "@camped-ai/ui";
import { calculatePercentage } from "../utils/helpers";

export const PercentageComparison = ({
  current,
  label,
  previous,
  headingLevel = "h2",
}: {
  current: string;
  label: string;
  previous: string;
  headingLevel?: string;
}) => {
  const percentage: number | undefined = calculatePercentage(
    parseInt(current),
    parseInt(previous)
  );
  return (
    <Tooltip content={`Previously: ${previous} ${label}`}>
      <span>
        <Heading
          level={headingLevel}
          style={{
            textDecorationStyle: "dotted",
            textDecorationLine: "underline",
            textUnderlineOffset: "3px",
          }}
        >
          {percentage !== undefined ? `${percentage}%` : `N/A`}
        </Heading>
      </span>
    </Tooltip>
  );
};
