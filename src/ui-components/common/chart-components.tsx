

import { Heading, Container, Text } from "@camped-ai/ui";
import {
  calculateResolution,
  getChartDateName,
  getChartTooltipDate,
  getLegendName,
  ChartResolutionType,
  compareDatesBasedOnResolutionType,
} from "./utils/chartUtils";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts";
import { useEffect, useState } from "react";
import { Box, Grid } from "@mui/material";

type ChartDataPoint = {
  current: {
    date: Date;
    value: any;
  };
  previous: {
    date: Date;
    value: any;
  };
};

export type ChartDataType = {
  current: {
    date: Date;
    value: any;
  }[];
  previous: {
    date: Date;
    value: any;
  }[];
};

const incrementDate = (date: Date, resolutionType: ChartResolutionType) => {
  switch (resolutionType) {
    case ChartResolutionType.DayMonth:
      date.setDate(date.getDate() + 1);
      break;
    case ChartResolutionType.Month:
      date.setMonth(date.getMonth() + 1);
      break;
    default:
      date.setDate(date.getDate() + 1);
  }
};

const generateChartData = (
  data: ChartDataType,
  fromDate: Date,
  toDate: Date,
  chartResolutionType: ChartResolutionType,
  toCompareDate?: Date,
  connectEmptyPointsUsingPreviousValue?: boolean
): ChartDataPoint[] => {
  const currentData = data.current;
  const previousData = data.previous;

  const startFromDate = new Date(fromDate);
  const offsetTime =
    toDate.getTime() -
    (toCompareDate ? toCompareDate.getTime() : fromDate.getTime());

  const dataPoints: ChartDataPoint[] = [];
  let currentDataValue: any;
  let previousDataValue: any;

  while (
    startFromDate.getTime() < toDate.getTime() ||
    compareDatesBasedOnResolutionType(
      startFromDate,
      toDate,
      chartResolutionType
    )
  ) {
    const currentOrder = currentData.find((order) =>
      compareDatesBasedOnResolutionType(
        new Date(order.date),
        startFromDate,
        chartResolutionType
      )
    );
    const offsetDate = new Date(startFromDate);
    offsetDate.setTime(offsetDate.getTime() - offsetTime);
    const previousOrder = previousData.find((previous) =>
      compareDatesBasedOnResolutionType(
        new Date(previous.date),
        offsetDate,
        chartResolutionType
      )
    );

    if (connectEmptyPointsUsingPreviousValue) {
      if (currentOrder) {
        currentDataValue = parseInt(currentOrder.value);
      }
      if (previousOrder) {
        previousDataValue = parseInt(previousOrder.value);
      }

      dataPoints.push({
        current: {
          date: new Date(startFromDate),
          value: currentOrder
            ? parseInt(currentOrder.value)
            : currentDataValue
            ? currentDataValue
            : undefined,
        },
        previous: {
          date: new Date(offsetDate),
          value: previousOrder
            ? parseInt(previousOrder.value)
            : previousDataValue
            ? previousDataValue
            : undefined,
        },
      });
    } else {
      dataPoints.push({
        current: {
          date: new Date(startFromDate),
          value: currentOrder ? parseInt(currentOrder.value) : 0,
        },
        previous: {
          date: new Date(offsetDate),
          value: previousOrder ? parseInt(previousOrder.value) : 0,
        },
      });
    }

    incrementDate(startFromDate, chartResolutionType);
  }

  if (connectEmptyPointsUsingPreviousValue) {
    for (let i = dataPoints.length - 1; i >= 0; i--) {
      if (dataPoints[i].current.value === undefined) {
        if (dataPoints[dataPoints.length - 1].previous.value) {
          dataPoints[i].current.value =
            dataPoints[dataPoints.length - 1].previous.value;
        } else {
          dataPoints[i].current.value = 0;
        }
      }
      if (dataPoints[i].previous.value) {
        previousDataValue = dataPoints[i].previous.value;
      } else {
        dataPoints[i].previous.value = previousDataValue;
      }
    }
  }

  return dataPoints;
};

export const ChartCustomTooltip = ({
  active,
  payload,
  label,
  resolutionType,
}) => {
  if (active && payload && payload.length) {
    switch (resolutionType) {
      case ChartResolutionType.DayMonth:
        return (
          <Container>
            <Heading level="h3" style={{ color: payload[0].color }}>
              {`${getChartTooltipDate(
                payload[0].payload.current.date,
                resolutionType
              )}`}{" "}
              : {payload[0].payload.current.value}
            </Heading>
            {payload[1] !== undefined && (
              <Heading level="h3" style={{ color: payload[1].color }}>
                {`${getChartTooltipDate(
                  payload[1].payload.previous.date,
                  resolutionType
                )}`}{" "}
                : {payload[1].payload.previous.value}
              </Heading>
            )}
          </Container>
        );
      case ChartResolutionType.Month:
        return (
          <Container>
            <Heading level="h3" style={{ color: payload[0].color }}>
              {`${getChartTooltipDate(
                payload[0].payload.current.date,
                resolutionType
              )}`}{" "}
              : {payload[0].payload.current.value}
            </Heading>
            {payload[1] !== undefined && (
              <Heading level="h3" style={{ color: payload[1].color }}>
                {`${getChartTooltipDate(
                  payload[1].payload.previous.date,
                  resolutionType
                )}`}{" "}
                : {payload[1].payload.previous.value}
              </Heading>
            )}
          </Container>
        );
    }
  }
  return null;
};

/* 

toDate is inclusive. It means that:
  fromDate: "2024-04-24"
  toDate: "2024-04-30"

  Analytics shall include `toDate` so it takes 7 days (including 2024-04-30)

  fromCompareDate: "2024-04-17"
  toCompareDate: "2024-04-24"

  Analytics shall compare to 7 days excluding 2024-04-24 (e.g. 2024-04-30 is compared to 2024-04-23, not 2024-04-24).

  toDate is inclusive to cover "today" date - so we need to cover situation when someone wants to see everything until now.
  We cannot use 2024-05-01 because then it is taken as day to show, while we want to show maximum 2024-04-30.

  toCompareDate is exclusive because backend is using fetches like created_at < toCompareDate, so it does not cover data at toCompareDate

  Comparison then we will have following algorithm:
  1) Take "toDate", remove "time" part and add whole day.
  2) Take times in milis from every date and compare.
*/

const areRangesTheSame = (
  fromDate: Date,
  toDate: Date,
  fromCompareDate?: Date,
  toCompareDate?: Date
): boolean => {
  function isToday(date: Date): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const givenDate = new Date(date);
    givenDate.setHours(0, 0, 0, 0);
    return today.getTime() === givenDate.getTime();
  }

  if (fromCompareDate) {
    const oneDay = 24 * 60 * 60 * 1000;
    if (toCompareDate) {
      // Cover situation when toDate is today so gives jsut couple of hours while we need the whole day.
      if (isToday(toDate)) {
        const diffBase = Math.ceil(
          Math.abs((toDate.getTime() - fromDate.getTime()) / oneDay)
        );
        const diffCompare = Math.round(
          Math.abs(
            (toCompareDate.getTime() - fromCompareDate.getTime()) / oneDay
          )
        );
        return diffBase == diffCompare;
      }
      const diffBase = Math.round(
        Math.abs((toDate.getTime() - fromDate.getTime()) / oneDay)
      );
      const diffCompare = Math.round(
        Math.abs((toCompareDate.getTime() - fromCompareDate.getTime()) / oneDay)
      );
      return diffBase == diffCompare;
    }

    const diffBase = Math.ceil(
      Math.abs((toDate.getTime() - fromDate.getTime()) / oneDay)
    );
    const diffCompare = Math.ceil(
      Math.abs((Date.now() - fromCompareDate.getTime()) / oneDay)
    );

    return diffBase == diffCompare;
  }
  return true;
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="bg-white p-4 rounded-lg shadow-lg border border-gray-200"
        style={{ backgroundColor: "rgba(255, 255, 255, 0.95)" }}
      >
        <p className="text-sm text-gray-600 mb-2 font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="font-medium text-sm">
              {entry.name}: {entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const ChartCurrentPrevious = ({
  rawChartData,
  fromDate,
  toDate,
  fromCompareDate,
  toCompareDate,
  compareEnabled,
  connectEmptyPointsUsingPreviousValue,
}: {
  rawChartData: ChartDataType;
  fromDate: Date;
  toDate: Date;
  fromCompareDate?: Date;
  toCompareDate?: Date;
  compareEnabled?: boolean;
  connectEmptyPointsUsingPreviousValue?: boolean;
}) => {
  const [chartDataPoints, setChartData] = useState<ChartDataPoint[]>([]);

  const resolutionType = calculateResolution(fromDate, toDate);

  const getLegendName = (isCurrent: boolean) =>
    isCurrent ? "Current" : "Previous";

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <Container className="p-4 rounded-lg shadow-lg border border-gray-200">
          <Text className="text-sm text-gray-600 mb-2">{data.name}</Text>
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: data.payload.fill }}
            />
            <Text className="text-lg font-semibold">{data.value.toLocaleString()}</Text>
          </div>
        </Container>
      );
    }
    return null;
  };

  useEffect(() => {
    const chartDataPoints: ChartDataPoint[] = generateChartData(
      rawChartData,
      fromDate,
      toDate,
      resolutionType,
      toCompareDate,
      connectEmptyPointsUsingPreviousValue
    );
    setChartData(chartDataPoints);
  }, [rawChartData, fromDate, toDate]);

  if (!areRangesTheSame(fromDate, toDate, fromCompareDate, toCompareDate)) {
    const currentPeriodInDays = Math.ceil(
      (toDate.getTime() - fromDate.getTime()) / (24 * 60 * 60 * 1000)
    );
    let precedingPeriodInDays = 0;
    if (fromCompareDate) {
      if (toCompareDate) {
        precedingPeriodInDays = Math.ceil(
          (toCompareDate.getTime() - fromCompareDate.getTime()) /
            (24 * 60 * 60 * 1000)
        );
      } else {
        precedingPeriodInDays = Math.ceil(
          (new Date(Date.now()).getTime() - fromCompareDate.getTime()) /
            (24 * 60 * 60 * 1000)
        );
      }
    }
    return (
      <Box
        width={500}
        height={400}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Grid
          container
          direction={"column"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          <Grid item>
            <Text>Chart can be shown only for the same length of ranges.</Text>
          </Grid>
          <Grid item>
            <Text>{`You are comparing ${currentPeriodInDays} days to ${precedingPeriodInDays} days`}</Text>
          </Grid>
        </Grid>
      </Box>
    );
  }

  const transformData = (apiResponse: any) => {
    return apiResponse.map((item: any) => ({
      date: new Date(item.current.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      value: item.current.value, 
    }));
  };
  const chartData = transformData(chartDataPoints)

  return (
<div className="w-full h-[300px]">
      <ResponsiveContainer width="100%" height="100%">

    <AreaChart
      data={chartData}
      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
    >
      <defs>
        <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor="#2563EB" stopOpacity={0.3} />
          <stop offset="95%" stopColor="#2563EB" stopOpacity={0.1} />
        </linearGradient>
      </defs>
      <XAxis
        dataKey="date"
        axisLine={false}
        tickLine={false}
        tick={{ fontSize: 12 }}
        dy={10}
      />
      <YAxis
        axisLine={false}
        tickLine={false}
        tick={{ fontSize: 12 }}
        width={60}
      />
      <Tooltip content={<CustomTooltip />}/>
      <Area
        type="monotone"
        dataKey="value"
        stroke="#2563EB"
        strokeWidth={2}
        fill="#3b82f6"
      />
    </AreaChart>
    </ResponsiveContainer>
    </div>
  );
};

export const BarChartCurrentPrevious = ({
  rawChartData,
  fromDate,
  toDate,
  fromCompareDate,
  toCompareDate,
  compareEnabled,
  connectEmptyPointsUsingPreviousValue,
}: {
  rawChartData: ChartDataType;
  fromDate: Date;
  toDate: Date;
  fromCompareDate?: Date;
  toCompareDate?: Date;
  compareEnabled?: boolean;
  connectEmptyPointsUsingPreviousValue?: boolean;
}) => {
  const [chartDataPoints, setChartData] = useState<ChartDataPoint[]>([]);

  const resolutionType = calculateResolution(fromDate, toDate);

  const getLegendName = (isCurrent: boolean) =>
    isCurrent ? "Current" : "Previous";
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Container className="bg-opacity-98 rounded-xl shadow-lg p-3 text-sm border-none">
          {/* Label (e.g., Date or Category Name) */}
          <Text className="font-medium mb-2">{label}</Text>
  
          {/* Tooltip Items */}
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2 py-1">
              {/* Color Indicator */}
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
  
              {/* Value and Name */}
              <p className="text-gray-600">
                {entry.name}: <span className="font-semibold">{entry.value}</span>
              </p>
            </div>
          ))}
        </Container>
      );
    }
    return null;
  };
  

  useEffect(() => {
    const chartDataPoints: ChartDataPoint[] = generateChartData(
      rawChartData,
      fromDate,
      toDate,
      resolutionType,
      toCompareDate,
      connectEmptyPointsUsingPreviousValue
    );
    setChartData(chartDataPoints);
  }, [rawChartData, fromDate, toDate]);

  if (!areRangesTheSame(fromDate, toDate, fromCompareDate, toCompareDate)) {
    const currentPeriodInDays = Math.ceil(
      (toDate.getTime() - fromDate.getTime()) / (24 * 60 * 60 * 1000)
    );
    let precedingPeriodInDays = 0;
    if (fromCompareDate) {
      if (toCompareDate) {
        precedingPeriodInDays = Math.ceil(
          (toCompareDate.getTime() - fromCompareDate.getTime()) /
            (24 * 60 * 60 * 1000)
        );
      } else {
        precedingPeriodInDays = Math.ceil(
          (new Date(Date.now()).getTime() - fromCompareDate.getTime()) /
            (24 * 60 * 60 * 1000)
        );
      }
    }

    return (
      <Box
        width={500}
        height={400}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Grid
          container
          direction={"column"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          <Grid item>
            <Text>Chart can be shown only for the same length of ranges.</Text>
          </Grid>
          <Grid item>
            <Text>{`You are comparing ${currentPeriodInDays} days to ${precedingPeriodInDays} days`}</Text>
          </Grid>
        </Grid>
      </Box>
    );
  }

  const transformData = (apiResponse: any) => {
    return apiResponse.map((item: any) => ({
      name: new Date(item.current.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      uv: item.current.value, // Current period's value
      pv: item.previous.value, // Previous period's value
    }));
  };
  const data = transformData(chartDataPoints);
  return (
    <div className="w-full h-[300px]">
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        <XAxis
          dataKey="name"
          tick={{ fill: "#64748b", fontSize: 12 }}
          axisLine={{ stroke: "#cbd5e1" }}
          tickLine={{ stroke: "#cbd5e1" }}
        />
        <YAxis
          tick={{ fill: "#64748b", fontSize: 12 }}
          axisLine={{ stroke: "#cbd5e1" }}
          tickLine={{ stroke: "#cbd5e1" }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar
          dataKey="pv"
          fill="#3b82f6"
          name="Current"
          radius={[4, 4, 0, 0]}
          opacity={0.9}
        />
        <Bar
          dataKey="uv"
          fill="#93c5fd"
          name="Previous"
          radius={[4, 4, 0, 0]}
          opacity={0.9}
        />
      </BarChart>
    </ResponsiveContainer>
  </div>
  );
};
