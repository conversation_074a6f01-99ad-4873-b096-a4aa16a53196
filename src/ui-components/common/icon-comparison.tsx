

import { ArrowUp<PERSON>ini, ArrowDownMini, MinusMini } from "@camped-ai/icons"

export const IconComparison = ({current, previous, switchArrow} : {current: number, previous?: number, switchArrow?: boolean}) => {
  if (current == previous) {
    return <MinusMini color="black"/>
  }
  if (previous && (current > previous)) {
    return <ArrowUpMini color={switchArrow ? "red" : "green"}/>
  }
  if (previous && (current < previous)) {
    return <ArrowDownMini color={switchArrow ? "green" : "red"}/>
  }
}