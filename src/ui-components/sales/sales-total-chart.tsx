

import { Heading } from "@camped-ai/ui";
import { ChartCurrentPrevious } from "../common/chart-components";
import { SalesHistoryResponse } from "./types";
import { amountToDisplay } from "../utils/helpers";

export const SalesByNewChart = ({dateRangeFrom, dateRangeTo, salesHistoryResponse, compareEnabled} : 
  {dateRangeFrom: Date, dateRangeTo: Date, salesHistoryResponse: SalesHistoryResponse, compareEnabled?: boolean}) => {
  const rawChartData = {
    current: salesHistoryResponse.analytics.current.map(currentData => {
      return {
        date: new Date(currentData.date),
        value: amountToDisplay(parseInt(currentData.total), salesHistoryResponse.analytics.currencyDecimalDigits)
      };
    }),
    previous: salesHistoryResponse.analytics.previous.map(previousData => {
      return {
        date: new Date(previousData.date),
        value: amountToDisplay(parseInt(previousData.total), salesHistoryResponse.analytics.currencyDecimalDigits)
      };
    }),
  };
  return (
    <>
      
      <ChartCurrentPrevious          
        rawChartData={rawChartData} 
        fromDate={dateRangeFrom} 
        toDate={dateRangeTo}
        fromCompareDate={salesHistoryResponse.analytics.dateRangeFromCompareTo ? new Date(salesHistoryResponse.analytics.dateRangeFromCompareTo) : undefined}
        toCompareDate={salesHistoryResponse.analytics.dateRangeToCompareTo ? new Date(salesHistoryResponse.analytics.dateRangeToCompareTo) : undefined}
        compareEnabled={compareEnabled}
        />
    </>
  )
}