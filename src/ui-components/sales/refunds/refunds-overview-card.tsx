

import { Heading, Select, Text, Alert } from "@camped-ai/ui";
import { CurrencyDollar } from "@camped-ai/icons";
import { CircularProgress, Grid } from "@mui/material";
import type { DateRange } from "../../utils/types";
import { useEffect, useState } from 'react';
import { RefundsResponse } from "../types";
import { RefundsNumber } from "./refunds-numbers";
import { RegionDTO } from "@camped-ai/framework/utils";
import { deduceDateUrlParams, selectedcountry } from "../../../ui-components/utils/helpers";
import { useStore } from "@nanostores/react";

const RefundsDetails = ({currencyCode, dateRange, dateRangeCompareTo, compareEnabled} : 
  {currencyCode: string, dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled?: boolean}) => {
    
  const [data, setData] = useState<RefundsResponse | undefined>(undefined)

  const [error, setError] = useState<any>(undefined);

  const [isLoading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true);
  }, [dateRange, dateRangeCompareTo, currencyCode])

  useEffect(() => {
    if (!isLoading) {
      return;
    }

    const searchParams = deduceDateUrlParams(dateRange, dateRangeCompareTo);
    searchParams.append('currencyCode', currencyCode)

    fetch(`/admin/sales-analytics/refunds?${searchParams.toString()}`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then((result) => {
      setData(result)
      setLoading(false)
    })
    .catch((error) => {
      setError(error);
      console.error(error);
    }) 
  }, [isLoading])

  if (isLoading) {
    return <CircularProgress size={12}/>
  }

  if (error) {
    const trueError = error as any;
    const errorText = `Error when loading data. It shouldn't have happened - please raise an issue. For developer: ${trueError?.response?.data?.message}`
    return <Alert variant="error">{errorText}</Alert>
  }

  if (data && data.analytics == undefined) {
    return (
      <Grid item xs={12} md={12}> 
        <Heading level="h3">Cannot get refunds</Heading>
      </Grid>
    )
  }

  if (data && data.analytics.dateRangeFrom) {
    return (
      <>
        <Grid item xs={12} md={12}>
          <RefundsNumber refundsResponse={data} compareEnabled={compareEnabled}/>
        </Grid>
      </>
    )
  } else {
    return (
      <Grid item xs={12} md={12}> 
        <Heading level="h3">No orders</Heading>
      </Grid>
    )
  }
}

export const RefundsOverviewCard = ({dateRange, dateRangeCompareTo, compareEnabled} : 
  {dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
    const currency = useStore(selectedcountry)
    const [ value , setValue ] = useState<string | undefined>(currency);

    const [regions, setRegions] = useState<RegionDTO[] | undefined>(undefined)
  
    const [error, setError] = useState<any>(undefined);
  
    const [isLoading, setLoading] = useState(true)
  
    useEffect(() => {
      if (!isLoading) {
        return;
      }
  
      fetch(`/admin/regions/`, {
        credentials: "include",
      })
      .then((res) => res.json())
      .then((result) => {
        setRegions(result.regions)
        setLoading(false)
      })
      .catch((error) => {
        setError(error);
        console.error(error);
      }) 
    }, [isLoading])
  
  return (
    <Grid container paddingBottom={2} spacing={3}>
      <Grid item xs={12} md={12}>
          <Grid container spacing={2} alignItems='center'>
            <Grid item>
              <CurrencyDollar/>
            </Grid>
            <Grid item>
              <Heading level="h2">
                Total refunds
              </Heading>
            </Grid>
          </Grid>
      </Grid>
      {value ? <RefundsDetails currencyCode={value} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/> : 
        <Grid item>
          <Heading level="h2">Please select a currency</Heading>
        </Grid>
      }
    </Grid>
  )
}