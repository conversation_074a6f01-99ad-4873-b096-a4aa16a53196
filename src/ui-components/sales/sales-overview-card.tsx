

import { Heading, Select, Text, Alert, Container } from "@camped-ai/ui";
import { CurrencyDollar } from "@camped-ai/icons";
import { CircularProgress, Grid } from "@mui/material";
import type { DateRange, OrderStatus } from "../utils/types";
import { SalesNumber } from "./sales-number-overview";
import { useEffect, useState } from 'react';
import { SalesByNewChart } from "./sales-total-chart";
import { SalesHistoryResponse } from "./types";
import { deduceDateUrlParams, selectedcountry } from "../utils/helpers";
import { RegionDTO } from "@camped-ai/framework/types";
import { useStore } from "@nanostores/react"

const SalesDetails = ({orderStatuses, currencyCode, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], currencyCode: string, dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled?: boolean}) => {

  const [data, setData] = useState<SalesHistoryResponse | undefined>(undefined)

  const [error, setError] = useState<any>(undefined);

  const [isLoading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true);
  }, [dateRange, dateRangeCompareTo, orderStatuses, currencyCode])

  useEffect(() => {
    if (!isLoading) {
      return;
    }

    const searchParams = deduceDateUrlParams(dateRange, dateRangeCompareTo, orderStatuses);
    searchParams.append('currencyCode', currencyCode)

    fetch(`/admin/sales-analytics/history?${searchParams.toString()}`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then((result) => {
      setData(result)

      setLoading(false)
    })
    .catch((error) => {
      setError(error);
      console.error(error);
    }) 
  }, [isLoading])

  if (isLoading) {
    return <CircularProgress size={12}/>
  }

  if (error) {
    const trueError = error as any;
    const errorText = `Error when loading data. It shouldn't have happened - please raise an issue. For developer: ${trueError?.response?.data?.message}`
    return <Alert variant="error">{errorText}</Alert>
  }

  if (data && data.analytics == undefined) {
    return (
      <Grid item xs={12} md={12}> 
        <Heading level="h3">Cannot get orders</Heading>
      </Grid>
    )
  }

  if (data && data.analytics.dateRangeFrom && data.analytics.dateRangeTo) {
    return (
      <>
        {/* <Grid item xs={12} md={12}>
          <SalesNumber salesHistoryResponse={data} compareEnabled={compareEnabled}/>
        </Grid> */}
        <Grid item xs={12} md={12}>
          <SalesByNewChart dateRangeFrom={new Date(data.analytics.dateRangeFrom)} dateRangeTo={new Date(data.analytics.dateRangeTo)} salesHistoryResponse={data} compareEnabled={compareEnabled}/> 
        </Grid>
      </>
    )
  } else {
    return (
      <Grid item xs={12} md={12}> 
        <Heading level="h3">No orders</Heading>
      </Grid>
    )
  }
}

export const SalesOverviewCard = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {

    const value = useStore(selectedcountry); // Get the selected currency globally
    const setValue = (currency: string) => selectedcountry.set(currency); 

  const [regions, setRegions] = useState<RegionDTO[] | undefined>(undefined)

  const [error, setError] = useState<any>(undefined);

  const [isLoading, setLoading] = useState(true)

  useEffect(() => {
    if (!isLoading) {
      return;
    }

    fetch(`/admin/regions/`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then((result) => {
      setRegions(result.regions)
      setLoading(false)
    })
    .catch((error) => {
      setError(error);
      console.error(error);
    }) 
  }, [isLoading])
  
  return (
    <Container className="p-4">
      <Grid item xs={12} md={12}>
          <Grid container spacing={2}>
            <Grid item>
              <CurrencyDollar/>
            </Grid>
            <Grid item>
              <Heading level="h2">
                Total Sales
              </Heading>
            </Grid>
          </Grid>
      </Grid>

      {value ? (
        <SalesDetails 
          orderStatuses={orderStatuses} 
          currencyCode={value} 
          dateRange={dateRange} 
          dateRangeCompareTo={dateRangeCompareTo} 
          compareEnabled={compareEnabled} 
        />
      ) : (
        <Text className="text-lg font-semibold mt-2">Please select a currency</Text>
      )}
    </Container>
  )
}