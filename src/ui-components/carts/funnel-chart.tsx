import { <PERSON><PERSON>, Container, Heading, Text } from "@camped-ai/ui";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LabelList, Tooltip } from "recharts";
import { deduceDateUrlParams } from "../utils/helpers";
import { DateRange } from "../utils/types";
import { ShoppingCart, AlertTriangle } from "lucide-react";

type CartResponse = {
  value: number;
  name: string;
  fill: string;
};

const CartAbandonmentDetails = ({ dateRange }: { dateRange?: DateRange }) => {
  const [data, setData] = useState<number | undefined>(undefined);
  const [error, setError] = useState<any>(undefined);
  const [isLoading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
  }, [dateRange]);

  useEffect(() => {
    if (!isLoading) return;

    fetch(`/admin/cart/cart-aband?${deduceDateUrlParams(dateRange).toString()}`, {
      credentials: "include",
    })
      .then((res) => res.json())
      .then((result) => {
        const avgValue = result?.Value?.[0]?.avg ? parseFloat(result.Value[0].avg) : undefined;
        setData(avgValue);
        setLoading(false);
      })
      .catch((error) => {
        setError(error);
        console.error(error);
      });
  }, [isLoading]);

  if (isLoading) {
    return <Text className="text-gray-500">Loading...</Text>;
  }

  if (error) {
    const trueError = error as any;
    return (
      <Alert variant="error">
        Error when loading data. Please raise an issue. Developer info: {trueError?.response?.data?.message}
      </Alert>
    );
  }

  if (data === undefined) {
    return <Text className="text-gray-500">No cart abandonment data available</Text>;
  }

  return <Text className="text-3xl font-bold">{`₹${data.toFixed(2)}`}</Text>;
};

const CartValueDetails = ({ dateRange }: { dateRange?: DateRange }) => {
  const [data, setData] = useState<number | undefined>(undefined);
  const [error, setError] = useState<any>(undefined);
  const [isLoading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
  }, [dateRange]);

  useEffect(() => {
    if (!isLoading) return;

    fetch(`/admin/cart/cart-value?${deduceDateUrlParams(dateRange).toString()}`, {
      credentials: "include",
    })
      .then((res) => res.json())
      .then((result) => {
        const avgValue = result?.analytics?.[0]?.avg ? parseFloat(result.analytics[0].avg) : undefined;
        setData(avgValue);
        setLoading(false);
      })
      .catch((error) => {
        setError(error);
        console.error(error);
      });
  }, [isLoading]);

  if (isLoading) {
    return <Text className="text-gray-500">Loading...</Text>;
  }

  if (error) {
    const trueError = error as any;
    return (
      <Alert variant="error">
        Error when loading data. Please raise an issue. Developer info: {trueError?.response?.data?.message}
      </Alert>
    );
  }

  if (data === undefined) {
    return <Text className="text-gray-500">No cart value data available</Text>;
  }

  return <Text className="text-3xl font-bold text-gray-900">{`₹${data.toFixed(2)}`}</Text>;
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <Container className="p-4 rounded-lg shadow-lg border border-gray-200">
        <Text className="text-sm text-gray-600 mb-2">{data.name}</Text>
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: data.payload.fill }}
          />
          <Text className="text-lg font-semibold">{data.value.toLocaleString()}</Text>
        </div>
      </Container>
    );
  }
  return null;
};

const CustomLabel = (props: any) => {
  const { x, y, width, height, value, name } = props;
  return (
    <g>
      <foreignObject x={x + width / 2 - 20} y={y + height / 2 - 10} width="80" height="20">
        <div className="flex items-center justify-center">
          <Text className="text-sm font-medium">{name}</Text>
        </div>
      </foreignObject>
    </g>
  );
};

export const AbandadCard = ({ dateRange }: { dateRange?: DateRange }) => {
  const [data, setData] = useState<CartResponse[] | undefined>(undefined);
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState<any>(undefined);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          `/admin/analytics?${deduceDateUrlParams(dateRange).toString()}`,
          { credentials: "include" }
        );
        const result = await response.json();

        if (result.analytics && Array.isArray(result.analytics)) {
          const formattedData = result.analytics.map((item) => ({
            ...item,
            value: Number(item.value),
          }));
          setData(formattedData);
        } else {
          console.error("Invalid API response", result);
          setData([]);
        }
      } catch (err) {
        setError("Failed to fetch data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isLoading, dateRange]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
      {/* Funnel Chart */}
      <Container className="md:col-span-8  p-6 rounded-xl">
        <div className="flex items-center justify-between mb-6">
          <Heading level="h2" className="text-xl font-semibold ">
            Conversion Funnel
          </Heading>
        </div>
        <div className="h-[500px] relative flex items-center justify-center">
          {isLoading ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <Text className="text-gray-500">Loading...</Text>
            </div>
          ) : error ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <Alert variant="error">{error}</Alert>
            </div>
          ) : (
            <FunnelChart width={800} height={400}>
              <Tooltip content={<CustomTooltip />} />
              <Funnel
                dataKey="value"
                data={data}
                isAnimationActive
                labelLine={false}
                width={400}
              >
                <LabelList
                  position="right"
                  content={<CustomLabel />}
                />
              </Funnel>
            </FunnelChart>
          )}
        </div>
      </Container>

      {/* Stats Cards */}
      <div className="md:col-span-4 space-y-6">
        {/* Average Cart Value */}
        <Container className="p-6 rounded-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="space-y-1">
              <Text className="text-sm font-medium">
                Average Cart Value
              </Text>
              <CartAbandonmentDetails dateRange={dateRange} />
            </div>
            <ShoppingCart className="w-8 h-8 text-blue-600" />
          </div>
        </Container>

        {/* Abandoned Carts */}
        <Container className="p-6 rounded-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="space-y-1">
              <Text className="text-sm font-medium">
                Abandoned Carts
              </Text>
              <CartValueDetails dateRange={dateRange} />
            </div>
            <AlertTriangle className="w-8 h-8 text-orange-600" />
          </div>
        </Container>
      </div>
    </div>
  );
};