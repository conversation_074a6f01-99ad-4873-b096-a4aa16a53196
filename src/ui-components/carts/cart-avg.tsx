import { Grid } from "@mui/material"
import { DateRange } from "../utils/types"
import { ShoppingBag } from "@camped-ai/icons"
import { Heading } from "@camped-ai/ui"

export const SalesChannelPopularityCard = ({ dateRange} :
    {dateRange?: DateRange}) => {
    return (
      <Grid container paddingBottom={2} spacing={3}>
        <Grid item xs={12} md={12}>
            <Grid container spacing={2}>
              <Grid item>
                <ShoppingBag/>
              </Grid>
              <Grid item>
                <Heading level="h2">
                  Sales channel popularity
                </Heading>
              </Grid>
            </Grid>
        </Grid>
       
      </Grid>
    )
  }