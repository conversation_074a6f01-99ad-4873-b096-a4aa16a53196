

import { Container } from "@camped-ai/ui"
import { 
  OrdersOverviewCard,
  OrderStatus,
} from '..';
import type { DateRange } from '..';
import { Grid } from "@mui/material";
import { OrdersPaymentProviderCard } from "../orders/orders-payment-provider-card";

const OrdersTab = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={6} >
        <Container className="h-full flex flex-col">
          <OrdersOverviewCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
        </Container>
      </Grid>
      <Grid item xs={6} >
        <Container className="h-full flex flex-col">
          <OrdersPaymentProviderCard dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
        </Container>
      </Grid>
    </Grid> 
  )
}

export default OrdersTab