

import { Container } from "@camped-ai/ui";
import {
  CustomersOverviewCard,
  CustomersRepeatCustomerRate,
  CumulativeCustomersCard,
  CustomersRetentionCustomerRate,
  OrderStatus,
} from "..";
import type { DateRange } from "..";
import { Grid } from "@mui/material";

const CustomersTab = ({
  orderStatuses,
  dateRange,
  dateRangeCompareTo,
  compareEnabled,
}: {
  orderStatuses: OrderStatus[];
  dateRange?: DateRange;
  dateRangeCompareTo?: DateRange;
  compareEnabled: boolean;
}) => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={6} md={6} xl={6}>
        <Container className="h-full flex flex-col">
          <CustomersOverviewCard
            dateRange={dateRange}
            dateRangeCompareTo={dateRangeCompareTo}
            compareEnabled={compareEnabled}
          />
        </Container>
      </Grid>

      <Grid item xs={6} md={6} xl={6}>
        <Container className="h-full flex flex-col">
          <CumulativeCustomersCard
            dateRange={dateRange}
            dateRangeCompareTo={dateRangeCompareTo}
            compareEnabled={compareEnabled}
          />
        </Container>
      </Grid>
      <Grid item xs={6} md={6} xl={6}>
        <Container>
          <CustomersRepeatCustomerRate
            orderStatuses={orderStatuses}
            dateRange={dateRange}
            dateRangeCompareTo={dateRangeCompareTo}
            compareEnabled={compareEnabled}
          />
        </Container>
      </Grid>
      {/* <Grid item xs={6} md={6} xl={6}>
        <Container>
          <CustomersRetentionCustomerRate
            orderStatuses={orderStatuses}
            dateRange={dateRange}
            dateRangeCompareTo={dateRangeCompareTo}
            compareEnabled={compareEnabled}
          />
        </Container>
      </Grid> */}
    </Grid>
  );
};

export default CustomersTab;
