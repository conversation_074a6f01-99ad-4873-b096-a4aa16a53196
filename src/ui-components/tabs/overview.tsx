import { <PERSON><PERSON>, Container, Heading } from "@camped-ai/ui";
import { Box, Grid, Typography, darken } from "@mui/material";
import {
  OrdersOverviewCard,
  SalesOverviewCard,
  CustomersOverviewCard,
  SalesChannelPopularityCard,
  RegionsPopularityCard,
  VariantsTopByCountCard,
  OrderStatus,
  ProductsSoldCountCard,
  CumulativeCustomersCard,
} from "..";
import type { DateRange } from "..";
import CustomerStatsCard from "../../admin/components/customer-card";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { useEffect, useState } from "react";
import { deduceDateUrlParams } from "../utils/helpers";

const InfoBox = () => {
  return (
    <Alert variant="info">Click on other tabs to see more statistics.</Alert>
  );
};

const OverviewTab = ({
  orderStatuses,
  dateRange,
  dateRangeCompareTo,
  compareEnabled,
}: {
  orderStatuses: OrderStatus[];
  dateRange?: DateRange;
  dateRangeCompareTo?: DateRange;
  compareEnabled: boolean;
}) => {
  
  // const Data = [
  //   { name: "Completed", value: 45 },
  //   { name: "Abandoned", value: 25 },
  //   { name: "In Progress", value: 30 },
  // ];
  type CartData = {
    name: string;
    value: number;
  };
 
  

  const [cartData, setCartData] = useState<CartData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const response = await fetch(`/admin/analytics?${deduceDateUrlParams(dateRange).toString()}`,{
          credentials: "include"
        });
        if (!response.ok) {
          throw new Error("Failed to fetch analytics data");
        }
        const data = await response.json();

        const getValue = (name: string): number => {
          return Number(data.analytics.find((item: any) => item.name === name)?.value || 0);
        };

        const cartCreated = getValue("Cart Created");
        const shippingCreated = getValue("Shipping Created");
        const paymentCreated = getValue("Payment Created");
        const placedOrder = getValue("Placed Order");

        const transformedData: CartData[] = [
          { name: "Completed", value: placedOrder },
          { name: "Abandoned", value: cartCreated - placedOrder },
          { name: "In Progress", value: shippingCreated + paymentCreated - placedOrder },
        ];

        setCartData(transformedData);
      } catch (error) {
        setError((error as Error).message);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [dateRange]);

  if (loading) return <p>Loading...</p>;
  if (error) return <p className="text-red-500">Error: {error}</p>;
  const COLORS = ["#2563eb", "#3b82f6", "#60a5fa", "#93c5fd", "#bfdbfe"];

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white px-3 py-2 rounded-lg shadow-lg border border-gray-100">
          <p className="text-sm font-medium text-gray-900">
            {payload[0].name}: {payload[0].value}%
          </p>
        </div>
      );
    }
    return null;
  };
  return (
    <Grid container spacing={2} alignItems="stretch">
  {/* Left Side (Customer Stats) */}
  <Grid item xs={4} sx={{ display: "flex", flexDirection: "column" }}>
    <div className="flex flex-col h-full">
    <CustomerStatsCard
      dateRange={dateRange}
      dateRangeCompareTo={dateRangeCompareTo}
      compareEnabled={compareEnabled}
    />
    </div>
  </Grid>

  {/* Right Side (Sales Overview) */}
  <Grid item xs={8} sx={{ display: "flex", flexDirection: "column" }}>
    <div className="flex flex-col h-full">
    <SalesOverviewCard
      orderStatuses={orderStatuses}
      dateRange={dateRange}
      dateRangeCompareTo={dateRangeCompareTo}
      compareEnabled={compareEnabled}
    />
    </div>
  </Grid>

  {/* Shopping Details Pie Chart */}
  <Grid item xs={4} sx={{ display: "flex", flexDirection: "column" }}>
    <Container className="flex flex-col h-full">
      <Heading level="h2">Shopping Details</Heading>
      <PieChart width={300} height={250}>
        <Pie
          data={cartData}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          paddingAngle={5}
          dataKey="value"
        >
          {cartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
      <Box sx={{ mt: 2, display: "flex", flexWrap: "wrap", gap: 2 }}>
        {cartData.map((entry, index) => (
          <Box key={entry.name} sx={{ display: "flex", alignItems: "center" }}>
            <Box
              sx={{
                width: 12,
                height: 12,
                bgcolor: COLORS[index % COLORS.length],
                borderRadius: "50%",
                mr: 1,
              }}
            />
            <Typography variant="body2">
              {entry.name}: {entry.value}%
            </Typography>
          </Box>
        ))}
      </Box>
    </Container>
  </Grid>

  {/* Orders Overview (Equal Height with Pie Chart) */}
  <Grid item xs={8} sx={{ display: "flex", flexDirection: "column" }}>
    <Container className="flex flex-col h-full">
      <OrdersOverviewCard
        orderStatuses={orderStatuses}
        dateRange={dateRange}
        dateRangeCompareTo={dateRangeCompareTo}
        compareEnabled={compareEnabled}
      />
    </Container>
  </Grid>

  {/* Variants & Region Popularity (Fixed Heights) */}
  <Grid container item xs={12} spacing={2}>
    <Grid item xs={5} sx={{ display: "flex", flexDirection: "column" }}>
      <Container className="flex flex-col h-full" >
        <VariantsTopByCountCard
          orderStatuses={orderStatuses}
          dateRange={dateRange}
          dateRangeCompareTo={dateRangeCompareTo}
          compareEnabled={compareEnabled}
        />
      </Container>
    </Grid>

    <Grid item xs={3} sx={{ display: "flex", flexDirection: "column" }}>
      <Container className="flex flex-col h-full">
        <RegionsPopularityCard
          orderStatuses={orderStatuses}
          dateRange={dateRange}
          dateRangeCompareTo={dateRangeCompareTo}
          compareEnabled={compareEnabled}
        />
      </Container>
    </Grid>
  </Grid>

  {/* Info Box at the Bottom */}
  <Grid item xs={12} md={12} xl={12} marginTop={3} marginBottom={10}>
    <Grid container justifyContent={"center"}>
      <Grid item>
        <InfoBox />
      </Grid>
    </Grid>
  </Grid>
</Grid>

  );
};

export default OverviewTab;
