

import { Container } from "@camped-ai/ui"
import { 
  ProductsSoldCountCard,
  VariantsTopByCountCard,
  ReturnedVariantsByCountCard,
  OutOfTheStockVariantsCard,
  OrderStatus,
} from '..';
import type { DateRange } from '..';
import { Grid } from "@mui/material";

const ProductsTab = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={6} md={6} xl={6}>
          <Container className="flex flex-col h-full">
          <VariantsTopByCountCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
          </Container>
        </Grid>
        <Grid item xs={6} md={6} xl={6}>
          <Container className="flex flex-col h-full">
          <OutOfTheStockVariantsCard/>
          </Container>
        </Grid>
        <Grid item xs={6} md={6} xl={6}>
          <Container className="flex flex-col h-full">
            <ReturnedVariantsByCountCard dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo}/>
          </Container>
        </Grid>
        <Grid item xs={6} md={6} xl={6}>
          <Container className="flex flex-col h-full">
          <ProductsSoldCountCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
          </Container>
        </Grid>
      </Grid> 
    )
}

export default ProductsTab