

import { Container } from "@camped-ai/ui"
import { 
  DiscountsTopCard,
  SalesChannelPopularityCard,
  OrderStatus,
  SalesOverviewCard,
  RefundsOverviewCard
} from '..';
import type { DateRange } from '..';
import { Grid } from "@mui/material";

const SalesTab = ({orderStatuses, dateRange, dateRangeCompareTo, compareEnabled} : 
  {orderStatuses: OrderStatus[], dateRange?: DateRange, dateRangeCompareTo?: DateRange, compareEnabled: boolean}) => {
    return (
      <Grid container spacing={2}>
        <Grid item xs={8} >
          
            <SalesOverviewCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
         
        </Grid>
        <Grid item xs={4} >
          <Container  className="flex flex-col h-full">
            <SalesChannelPopularityCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
          </Container>
        </Grid>
        <Grid item xs={6} >
          <Container  className="flex flex-col h-full">
            <RefundsOverviewCard dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo} compareEnabled={compareEnabled}/>
          </Container>
        </Grid>
        <Grid item xs={6} >
          <Container  className="flex flex-col h-full">
            <DiscountsTopCard orderStatuses={orderStatuses} dateRange={dateRange} dateRangeCompareTo={dateRangeCompareTo}/>
          </Container>
        </Grid>
      </Grid> 
    )
}

export default SalesTab