import { ExecArgs } from "@camped-ai/framework/types";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
// Email templates
import { productCreated } from "../modules/my-notification/email/templates/product-created";
import { inviteCreated } from "../modules/my-notification/email/templates/invite-created";
import { orderPlaced } from "../modules/my-notification/email/templates/order-placed";
import { orderShipped } from "../modules/my-notification/email/templates/shipment-created";
import { orderDelivered } from "../modules/my-notification/email/templates/delivery-created";
import { passwordReset } from "../modules/my-notification/email/templates/auth-password-reset";
import { refundProcessed } from "../modules/my-notification/email/templates/payment-refunded";
import { orderTransferAccept } from "../modules/my-notification/email/templates/order-transfer-requested";

/**
 * Seed default notification templates
 *
 * This script creates default notification templates for all supported events
 * Templates are only created if they don't already exist
 */
export default async function ({ container }: ExecArgs): Promise<void> {
  // Get the logger from the container
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER) || {
    info: (message: string) => console.log(`[INFO] ${message}`),
    error: (message: string) => console.error(`[ERROR] ${message}`),
    warn: (message: string) => console.warn(`[WARN] ${message}`),
  };

  logger.info("Starting to seed default notification templates...");

  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_SERVICE
  );

  // Define default templates
  const defaultTemplates = [
    // Email templates
    // Product created notification (email)
    {
      event_name: "product.created",
      channel: "email",
      subject: productCreated.subject,
      content: productCreated.body,
      is_default: true,
      is_active: true,
    },
    // Invite created notification (email)
    {
      event_name: "invite.created",
      channel: "email",
      subject: inviteCreated.subject,
      content: inviteCreated.body,
      is_default: true,
      is_active: true,
    },
    // Order placed notification (email)
    {
      event_name: "order.placed",
      channel: "email",
      subject: orderPlaced.subject,
      content: orderPlaced.body,
      is_default: true,
      is_active: true,
    },
    // Shipment created notification (email)
    {
      event_name: "shipment.created",
      channel: "email",
      subject: orderShipped.subject,
      content: orderShipped.body,
      is_default: true,
      is_active: true,
    },
    // Delivery created notification (email)
    {
      event_name: "delivery.created",
      channel: "email",
      subject: orderDelivered.subject,
      content: orderDelivered.body,
      is_default: true,
      is_active: true,
    },
    // Password reset notification (email)
    {
      event_name: "auth.password_reset",
      channel: "email",
      subject: passwordReset.subject,
      content: passwordReset.body,
      is_default: true,
      is_active: true,
    },
    // Payment refunded notification (email)
    {
      event_name: "payment.refunded",
      channel: "email",
      subject: refundProcessed.subject,
      content: refundProcessed.body,
      is_default: true,
      is_active: true,
    },
    // Order transfer requested notification (email)
    {
      event_name: "order.transfer_requested",
      channel: "email",
      subject: orderTransferAccept.subject,
      content: orderTransferAccept.body,
      is_default: true,
      is_active: true,
    },
    // Feed templates for all events
    // Product created notification (feed)
    {
      event_name: "product.created",
      channel: "feed",
      subject: "",
      content: "A new product has been created",
      is_default: false,
      is_active: true,
    },
    // Invite created notification (feed)
    {
      event_name: "invite.created",
      channel: "feed",
      subject: "",
      content: "A new user has been invited",
      is_default: false,
      is_active: true,
    },
    // Order placed notification (feed)
    {
      event_name: "order.placed",
      channel: "feed",
      subject: "",
      content: "A new order has been placed",
      is_default: false,
      is_active: true,
    },
    // Shipment created notification (feed)
    {
      event_name: "shipment.created",
      channel: "feed",
      subject: "",
      content: "An order has been shipped",
      is_default: false,
      is_active: true,
    },
    // Delivery created notification (feed)
    {
      event_name: "delivery.created",
      channel: "feed",
      subject: "",
      content: "An order has been delivered",
      is_default: false,
      is_active: true,
    },
    // Password reset notification (feed)
    {
      event_name: "auth.password_reset",
      channel: "feed",
      subject: "",
      content: "A password reset has been requested",
      is_default: false,
      is_active: true,
    },
    // Payment refunded notification (feed)
    {
      event_name: "payment.refunded",
      channel: "feed",
      subject: "",
      content: "A payment has been refunded",
      is_default: false,
      is_active: true,
    },
    // Order transfer requested notification (feed)
    {
      event_name: "order.transfer_requested",
      channel: "feed",
      subject: "",
      content: "An order transfer has been requested",
      is_default: false,
      is_active: true,
    },
    // Destination events
    // Destination created notification (email)
    {
      event_name: "destination.created",
      channel: "email",
      subject: "New Destination Created",
      content: "<p>A new destination has been created.</p>",
      is_default: true,
      is_active: true,
    },
    // Destination created notification (feed)
    {
      event_name: "destination.created",
      channel: "feed",
      subject: "",
      content: "A new destination has been created",
      is_default: false,
      is_active: true,
    },
    // Destination status changed notification (email)
    {
      event_name: "destination.status_changed",
      channel: "email",
      subject: "Destination Status Changed",
      content: "<p>A destination's status has been updated.</p>",
      is_default: true,
      is_active: true,
    },
    // Destination status changed notification (feed)
    {
      event_name: "destination.status_changed",
      channel: "feed",
      subject: "",
      content: "A destination's status has been updated",
      is_default: false,
      is_active: true,
    },
    // Destination featured changed notification (email)
    {
      event_name: "destination.featured_changed",
      channel: "email",
      subject: "Destination Featured Status Changed",
      content: "<p>A destination's featured status has been updated.</p>",
      is_default: true,
      is_active: true,
    },
    // Destination featured changed notification (feed)
    {
      event_name: "destination.featured_changed",
      channel: "feed",
      subject: "",
      content: "A destination's featured status has been updated",
      is_default: false,
      is_active: true,
    },
    // Destination deleted notification (email)
    {
      event_name: "destination.deleted",
      channel: "email",
      subject: "Destination Deleted",
      content: "<p>A destination has been deleted.</p>",
      is_default: true,
      is_active: true,
    },
    // Destination deleted notification (feed)
    {
      event_name: "destination.deleted",
      channel: "feed",
      subject: "",
      content: "A destination has been deleted",
      is_default: false,
      is_active: true,
    },
    // Payment captured notification (email)
    {
      event_name: "payment.captured",
      channel: "email",
      subject: "Payment Captured",
      content: "<p>Your payment has been captured successfully.</p>",
      is_default: true,
      is_active: true,
    },
    // Payment captured notification (feed)
    {
      event_name: "payment.captured",
      channel: "feed",
      subject: "",
      content: "A payment has been captured",
      is_default: false,
      is_active: true,
    },
    // Inventory updated notification (email)
    {
      event_name: "inventory.updated",
      channel: "email",
      subject: "Inventory Updated",
      content: "<p>Inventory has been updated.</p>",
      is_default: true,
      is_active: true,
    },
    // Inventory updated notification (feed)
    {
      event_name: "inventory.updated",
      channel: "feed",
      subject: "",
      content: "Inventory has been updated",
      is_default: false,
      is_active: true,
    },
    // Hotel events
    // Hotel created notification (email)
    {
      event_name: "hotel.created",
      channel: "email",
      subject: "New Hotel Created",
      content: "<p>A new hotel has been created.</p><p>Hotel Name: {{hotel.name}}</p><p>Destination: {{destination.name}}</p>",
      is_default: true,
      is_active: true,
    },
    // Hotel created notification (feed)
    {
      event_name: "hotel.created",
      channel: "feed",
      subject: "",
      content: "A new hotel has been created",
      is_default: false,
      is_active: true,
    },
    // Hotel status changed notification (email)
    {
      event_name: "hotel.status_changed",
      channel: "email",
      subject: "Hotel Status Changed",
      content: "<p>A hotel's status has been updated.</p><p>Hotel Name: {{hotel.name}}</p><p>Change: {{hotel.change_description}}</p>",
      is_default: true,
      is_active: true,
    },
    // Hotel status changed notification (feed)
    {
      event_name: "hotel.status_changed",
      channel: "feed",
      subject: "",
      content: "A hotel's status has been updated",
      is_default: false,
      is_active: true,
    },
    // Room events (product-variant)
    // Room created notification (email)
    {
      event_name: "room.created",
      channel: "email",
      subject: "New Room Created",
      content: "<p>A new room has been created.</p><p>Room Number: {{room.number}}</p><p>Floor: {{room.floor}}</p><p>Hotel: {{hotel.name}}</p>",
      is_default: true,
      is_active: true,
    },
    // Room created notification (feed)
    {
      event_name: "room.created",
      channel: "feed",
      subject: "",
      content: "A new room has been created",
      is_default: false,
      is_active: true,
    },
    // Room status updated notification (email)
    {
      event_name: "room.status_updated",
      channel: "email",
      subject: "Room Status Updated",
      content: "<p>A room's status has been updated.</p><p>Room Number: {{room.number}}</p><p>Floor: {{room.floor}}</p><p>Hotel: {{hotel.name}}</p><p>Change: {{room.change_description}}</p>",
      is_default: true,
      is_active: true,
    },
    // Room status updated notification (feed)
    {
      event_name: "room.status_updated",
      channel: "feed",
      subject: "",
      content: "A room's status has been updated",
      is_default: false,
      is_active: true,
    },
    // Add new PDF Invoice Template here
    {
      event_name: "booking.invoice.default", // Our chosen event name for PDF invoices
      channel: "pdf", // Specific channel for PDF templates
      subject: "Default Booking Invoice (PDF)", // Subject/Title for admin UI
      content: `<!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <title>Invoice #{{ order.display_id }}</title>
    <style>
        body { font-family: Helvetica, Arial, sans-serif; color: #333; font-size: 12px; line-height: 1.6; margin: 0; padding: 0; }
        .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, .15); font-size: 12px; line-height: 1.6; font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif; color: #555; }
        .invoice-box table { width: 100%; line-height: inherit; text-align: left; border-collapse: collapse; }
        .invoice-box table td { padding: 5px; vertical-align: top; }
        .invoice-box table tr td:nth-child(2) { text-align: right; }
        .invoice-box table tr.top table td { padding-bottom: 20px; }
        .invoice-box table tr.top table td.title img { width: 100%; max-width: 150px; }
        .invoice-box table tr.information table td { padding-bottom: 20px; }
        .invoice-box table tr.heading td { background: #eee; border-bottom: 1px solid #ddd; font-weight: bold; }
        .invoice-box table tr.details td { padding-bottom: 20px; }
        .invoice-box table tr.item td { border-bottom: 1px solid #eee; }
        .invoice-box table tr.item.last td { border-bottom: none; }
        .invoice-box table tr.total td:nth-child(2) { border-top: 2px solid #eee; font-weight: bold; }
        .align-right { text-align: right; }
        .company-details { text-align: right; }
        @media only screen and (max-width: 600px) {
            .invoice-box table tr.top table td { width: 100%; display: block; text-align: center; }
            .invoice-box table tr.information table td { width: 100%; display: block; text-align: center; }
        }
    </style>
</head>
<body>
    <div class=\"invoice-box\">
        <table cellpadding=\"0\" cellspacing=\"0\">
            <tr class=\"top\">
                <td colspan=\"2\">
                    <table>
                        <tr>
                            <td class=\"title\">
                                <!-- Replace with your company logo placeholder or direct URL if static -->
                                <!-- <img src=\"{{ company.logo_url }}\" alt=\"Company logo\"> -->
                                <h2>{{ company.name | default: 'Your Company Name' }}</h2>
                            </td>
                            <td>
                                Invoice #: {{ order.display_id }}<br>
                                Created: {{ order.created_at_formatted }}<br>
                                <!-- Due: {{ order.due_date_formatted }} -->
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr class=\"information\">
                <td colspan=\"2\">
                    <table>
                        <tr>
                            <td>
                                {{ company.address | default: '123 Your Street<br>Your City, ST 12345' }}<br>
                                {{ company.phone | default: '************' }}<br>
                                {{ company.email | default: '<EMAIL>' }}
                            </td>
                            <td class=\"company-details\">
                                {{ customer.first_name }} {{ customer.last_name }}<br>
                                {{ customer.email }}<br>
                                {{{ billing_address.full_address_html }}}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr class=\"heading\">
                <td>Payment Method</td>
                <td></td> <!-- Empty for alignment -->
            </tr>
            <tr class=\"details\">
                <td>{{ order.payment_method_name | default: 'N/A' }}</td>
                <td></td>
            </tr>
            <tr class=\"heading\">
                <td>Item</td>
                <td class=\"align-right\">Price</td>
            </tr>
            {{{ items_html_block }}}
            <!-- Example of a single item row if items_html_block is not used or for static items -->
            <!-- <tr class=\"item\"><td>Sample Item</td><td class=\"align-right\">$10.00</td></tr> -->
            <tr class=\"total\">
                <td></td>
                <td class=\"align-right\">Subtotal: {{ order.subtotal_formatted }}</td>
            </tr>
            <tr class=\"total\">
                <td></td>
                <td class=\"align-right\">Shipping: {{ order.shipping_total_formatted }}</td>
            </tr>
             <tr class=\"total\">
                <td></td>
                <td class=\"align-right\">Tax: {{ order.tax_total_formatted }}</td>
            </tr>
            <tr class=\"total\">
                <td></td>
                <td class=\"align-right\">Total: {{ order.total_formatted }}</td>
            </tr>
        </table>
        <div style=\"margin-top: 30px; text-align: center; font-size: 10px; color: #777;\">
            Thank you for your business! If you have any questions, please contact us.
            <br>{{ company.footer_notes | default: '' }}
        </div>
    </div>
</body>
</html>`,
      is_default: true, // Indicates this is the system-provided default for this event/channel
      is_active: true,  // Make it active by default
    }
  ];

  // Create each template if it doesn't exist
  for (const template of defaultTemplates) {
    try {
      // Check if template already exists
      const existingTemplates = await notificationTemplateService.listNotificationTemplates({
        event_name: template.event_name,
        channel: template.channel,
      });

      if (existingTemplates.length === 0) {
        // Create new template
        await notificationTemplateService.createNotificationTemplates(template);
        logger.info(`Created default template for ${template.event_name} on ${template.channel} channel`);
      } else {
        logger.info(`Template for ${template.event_name} on ${template.channel} channel already exists, skipping`);
      }
    } catch (error) {
      logger.error(`Error creating template for ${template.event_name}: ${error.message}`);
    }
  }

  logger.info("Finished seeding default notification templates");
}
