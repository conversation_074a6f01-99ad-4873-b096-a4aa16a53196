import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IUserModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";

export default async function inviteCreateHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const inviteId = data.id;

  // Resolve required services
  const userModuleService: IUserModuleService = container.resolve(Modules.USER);
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_SERVICE
  );

  // Retrieve the invite
  const user = await userModuleService.retrieveInvite(inviteId);

  // Check if notifications should be sent for this event and channel
  const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
    "invite.created",
    "email"
  );

  if (!shouldSendEmail) {
    console.log(`Notifications for invite.created are disabled. Skipping email for invite ${inviteId}`);
    return;
  }

  // Get the template from the database
  const emailTemplate = await notificationTemplateService.getTemplate(
    "invite.created",
    "email"
  );

  if (!emailTemplate) {
    console.log(`No active template found for invite.created. Skipping email for invite ${inviteId}`);
    return;
  }

  // Prepare data for template rendering
  const templateData = {
    user: {
      token: user.token,
      email: user.email,
    },
    frontendURL: process.env.MEDUSA_STOREFRONT_URL || ""
  };

  // Compile and render the template with Handlebars
  const compiledTemplate = Handlebars.compile(emailTemplate.content);
  const emailBody = compiledTemplate(templateData);

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: user.email,
      channel: "email",
      template: "invite.created",
      data: {
        subject: emailTemplate.subject || "You've been invited",
        html: emailBody,
      },
    });
    console.log(`Email sent successfully to ${user.email}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "invite.created",
};
