import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { formatBookingConfirmationMessage } from "../modules/whatsapp-notification/direct-message";
import { SendWhatsAppMessageWorkflow } from "../workflows/send-whatsapp";

/**
 * Subscriber for order.placed events
 * Sends WhatsApp notifications when an order is placed
 */
export default async function bookingWhatsAppNotificationHandler({
  event: { data, name },
  container,
}: SubscriberArgs<any>) {
  // Add immediate console log to verify the subscriber is being called
  console.log("🔔 WhatsApp notification subscriber triggered for event:", name);
  console.log("🔔 Event data:", JSON.stringify(data, null, 2));
  console.log("🔔 Subscriber ID: booking-whatsapp-notification");

  // Make sure we have an order ID
  console.log("🔔 Full event data structure:", JSON.stringify(data, null, 2));

  // Handle different event data structures
  let orderId: string;

  // Check for different data structures based on the event type
  if (typeof data === 'object') {
    if ('id' in data) {
      orderId = String(data.id);
    } else if (data && typeof data === 'object' && 'order' in data && typeof data.order === 'object' && data.order && 'id' in data.order) {
      orderId = String(data.order.id);
    } else {
      console.error("🔔 No order ID found in event data");
      return;
    }
  } else {
    console.error("🔔 Event data is not an object");
    return;
  }

  console.log(`🔔 Extracted order ID: ${orderId}`);

  try {
    console.log(`🔔 Processing WhatsApp notification for order ${orderId}`);

    // Resolve required services
    const orderService = container.resolve(Modules.ORDER);

    console.log(`🔔 Order service resolved successfully`);

    // First, retrieve the order
    const order = await orderService.retrieveOrder(orderId);
    console.log(`🔔 Order retrieved:`, order ? "Success" : "Not found");

    if (!order) {
      console.warn(
        `🔔 Order ${orderId} not found, skipping WhatsApp notification`
      );
      return;
    }

    // Check if order has metadata with guest_phone
    if (!order.metadata || !order.metadata.guest_phone) {
      console.warn(
        `🔔 Order ${orderId} has no guest_phone in metadata, skipping WhatsApp notification`
      );
      return;
    }

    const guestPhone = order.metadata.guest_phone;
    console.log(`🔔 Found guest phone: ${guestPhone}`);

    // Get number of rooms from metadata
    let numberOfRooms = order.metadata.number_of_rooms;

    // If not in top-level metadata, check line items
    if (!numberOfRooms && order.metadata.line_items && Array.isArray(order.metadata.line_items)) {
      // Look for room booking line items
      const roomItems = order.metadata.line_items.filter((item: any) =>
        item.metadata && (item.metadata.item_type === 'room' || item.metadata.number_of_rooms)
      );

      if (roomItems.length > 0 && roomItems[0].metadata?.number_of_rooms) {
        numberOfRooms = roomItems[0].metadata.number_of_rooms;
        console.log(`🔔 Found number of rooms in line items: ${numberOfRooms}`);
      }
    }

    // Prepare booking data for the message
    const bookingData = {
      id: order.id,
      guest_name: order.metadata.guest_name || "Guest",
      check_in_date:
        order.metadata.check_in_date || new Date().toLocaleDateString(),
      check_out_date:
        order.metadata.check_out_date || new Date().toLocaleDateString(),
      check_in_time: order.metadata.check_in_time || "14:00",
      check_out_time: order.metadata.check_out_time || "12:00",
      adults: order.metadata.adults || 1,
      children: order.metadata.children || 0,
      room_type: order.metadata.room_type || "Standard Room",
      number_of_rooms: numberOfRooms || 1,
      metadata: order.metadata || {},
    };

    // Prepare hotel data for the message
    let hotelName = order.metadata.hotel_name;

    // If hotel name is not in metadata but hotel_id is, try to fetch the hotel name
    if (!hotelName && order.metadata.hotel_id) {
      try {
        const query = container.resolve("query");
        const { data: hotelData } = await query.graph({
          entity: "hotel",
          filters: {
            id: order.metadata.hotel_id,
          },
          fields: ["id", "title", "name"],
        });

        if (hotelData && hotelData.length > 0) {
          // Use type assertion to access properties that might exist
          const hotel = hotelData[0] as any;
          hotelName = hotel.title || hotel.name || String(hotel.id);
          console.log(`🔔 Found hotel name from database: ${hotelName}`);
        }
      } catch (error) {
        console.error("🔔 Error fetching hotel name:", error);
      }
    }

    const hotelData = {
      name: hotelName || "Hotel",
    };

    // Format the booking confirmation message
    const message = formatBookingConfirmationMessage(bookingData, hotelData);

    console.log(`🔔 Message prepared:`, message);

    // Send WhatsApp notification
    console.log(`🔔 Sending WhatsApp notification to ${guestPhone}`);

    const notificationData = {
      to: String(guestPhone), // Ensure it's a string
      channel: "whatsapp",
      template: "booking.created",
      data: {
        directMessage: true,
        message: message,
        order_id: orderId,
        customer_id: order.customer_id,
      },
      provider_id: "direct-whatsapp", // Explicitly specify the provider ID
    };

    console.log(`🔔 Notification data:`, notificationData);


    try {


      // Extract the required fields for the WhatsApp message
      const workflowResult = await SendWhatsAppMessageWorkflow(container).run({ input: {
        to: String(guestPhone),
        message: message,
        order_id: orderId,
        customer_id: order.customer_id
      }});

      // Access the result data from the workflow result
      const result = workflowResult.result;

      if (result && result.success) {
        console.log(`🔔 WhatsApp message sent successfully with ID: ${result.message_id}`);
      } else {
        console.error(`🔔 Failed to send WhatsApp message: ${result?.error || 'Unknown error'}`);
      }
    } catch (innerError) {
      console.error(`🔔 Error in sendWhatsAppMessage:`, innerError.message);
      throw innerError; // Re-throw to be caught by the outer try/catch
    }

    console.log(
      `🔔 WhatsApp notification process completed for order ${orderId}`
    );
  } catch (error) {
    console.error(
      `🔔 Error sending WhatsApp notification for order ${orderId}:`,
      error.message
    );
    console.error(`🔔 Error stack:`, error.stack);
  }
}

export const config: SubscriberConfig = {
  event: "order.placed",
  context: {
    subscriberId: "booking-whatsapp-notification", // Add a unique ID for this subscriber
  },
};
