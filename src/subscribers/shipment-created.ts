import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import {
    IFulfillmentModuleService,
    IOrderModuleService,
} from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { orderShipped } from "src/modules/my-notification/email/templates/shipment-created";
import Handlebars from "handlebars";
import { retrieveOrderDetailsWorkflow } from "src/workflows/retrieve-order-details";

export default async function orderPlaceHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const fulfillmentId = data.id;

const fulfillmentModuleService: IFulfillmentModuleService = container.resolve(
    Modules.FULFILLMENT
  );
  const orderModuleService: IOrderModuleService = container.resolve(
      Modules.ORDER
    );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  const fulfillment = await fulfillmentModuleService.retrieveFulfillment(fulfillmentId,{relations:[ "delivery_address", "labels"]})
  const orderFulfillment = await retrieveOrderDetailsWorkflow(container)
      .run({
        input: {
          id: fulfillmentId,
        },
      })
    const order = await orderModuleService.retrieveOrder(orderFulfillment.result[0].order_id, {relations: [
      "items",]});
  fulfillment.delivery_address.country_code =
    fulfillment.delivery_address.country_code === "in"
      ? "India"
      : fulfillment.delivery_address.country_code;



// Replace placeholders in the email template
  const emailBody = Handlebars.compile(orderShipped.body)({
    fulfillment,
    order,
    frontendURL: `${process.env.MEDUSA_STOREFRONT_URL}`,
  });

  console.log({shippedEmail: order.email})

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: order.email,
      channel: "email",
      template: "shipment.created",
      data: {
        subject: orderShipped.subject,
        html: emailBody,
      },
    });
    console.log(`Email sent successfully for order ${fulfillmentId}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "shipment.created",
};
