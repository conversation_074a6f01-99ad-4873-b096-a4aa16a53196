import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function roomCreatedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; variant?: any }>) {
  const variantId = data.id;
  const variant = data.variant;

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Product variant (room) created event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "room.created",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for room.created are not active, skipping`);
      return;
    }

    // If variant is not provided in the event data, retrieve it
    const variantData = variant || await productModuleService.retrieveProductVariant(variantId);

    if (!variantData) {
      console.log(`Product variant ${variantId} not found, skipping email notification`);
      return;
    }

    // Get the product to which this variant belongs
    const product = await productModuleService.retrieveProduct(variantData.product_id);

    if (!product) {
      console.log(`Product for variant ${variantId} not found, skipping email notification`);
      return;
    }

    // Try to get room details from metadata or options
    let roomNumber = "Unknown";
    let floor = "Unknown";
    let hotelName = "Unknown Hotel";
    let hotelId = null;

    // Extract room details from variant options or metadata
    if (variantData.options) {
      for (const option of variantData.options) {
        if (option.option?.title?.toLowerCase() === "room number") {
          roomNumber = option.value;
        } else if (option.option?.title?.toLowerCase() === "floor") {
          floor = option.value;
        }
      }
    }

    // If not found in options, try metadata
    if (variantData.metadata) {
      if (variantData.metadata.room_number) {
        roomNumber = variantData.metadata.room_number;
      }
      if (variantData.metadata.floor) {
        floor = variantData.metadata.floor;
      }
      if (variantData.metadata.hotel_id) {
        hotelId = variantData.metadata.hotel_id;
        try {
          const hotel = await hotelModuleService.retrieveHotel(hotelId);
          if (hotel) {
            hotelName = hotel.name;
          }
        } catch (error) {
          console.error(`Error retrieving hotel for room ${variantId}:`, error);
        }
      }
    }

    // Get the email template
    // First, list templates to find the one we need
    const templates = await notificationTemplateService.listNotificationTemplates({
      event_name: "room.created",
      channel: "email",
    });

    // Get the first matching template
    const emailTemplate = templates.length > 0 ? templates[0] : null;

    if (!emailTemplate) {
      console.log(`Email template for room.created not found, skipping`);
      return;
    }

    // Prepare data for template rendering
    const templateData = {
      room: {
        id: variantId,
        number: roomNumber,
        floor: floor,
        title: variantData.title || `Room ${roomNumber}`,
        created_at: variantData.created_at
      },
      hotel: {
        id: hotelId,
        name: hotelName
      },
      frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
    };

    // Compile and render the template with Handlebars
    const compiledTemplate = Handlebars.compile(emailTemplate.content);
    const emailBody = compiledTemplate(templateData);

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Send email to each admin
    for (const email of adminEmails) {
      try {
        await notificationModuleService.createNotifications({
          to: email,
          channel: "email",
          template: "room.created",
          data: {
            subject: emailTemplate.subject || `New Room Created: ${roomNumber}`,
            html: emailBody,
          },
        });
        console.log(`Email sent successfully to ${email} for new room ${roomNumber}`);
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "room.created",
};
