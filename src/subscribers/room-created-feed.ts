import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";

export default async function roomCreatedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; variant?: any }>) {
  const variantId = data.id;
  const variant = data.variant;
  console.log("i am in feed",{data})

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Product variant (room) created event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendFeed = await notificationTemplateService.shouldSendNotification(
      "room.created",
      "feed"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendFeed) {
      console.log(`Feed notifications for room.created are not active, skipping`);
      return;
    }

    // If variant is not provided in the event data, retrieve it
    const variantData = variant || await productModuleService.retrieveProductVariant(variantId);

    if (!variantData) {
      console.log(`Product variant ${variantId} not found, skipping feed notification`);
      return;
    }

    // Get the product to which this variant belongs
    const product = await productModuleService.retrieveProduct(variantData.product_id);

    if (!product) {
      console.log(`Product for variant ${variantId} not found, skipping feed notification`);
      return;
    }

    // Try to get room details from metadata or options
    let roomNumber = "Unknown";
    let floor = "Unknown";
    let hotelName = "Unknown Hotel";

    // Extract room details from variant options or metadata
    if (variantData.options) {
      for (const option of variantData.options) {
        if (option.option?.title?.toLowerCase() === "room number") {
          roomNumber = option.value;
        } else if (option.option?.title?.toLowerCase() === "floor") {
          floor = option.value;
        }
      }
    }

    // If not found in options, try metadata
    if (variantData.metadata) {
      if (variantData.metadata.room_number) {
        roomNumber = variantData.metadata.room_number;
      }
      if (variantData.metadata.floor) {
        floor = variantData.metadata.floor;
      }
      if (variantData.metadata.hotel_id) {
        try {
          const hotel = await hotelModuleService.retrieveHotel(variantData.metadata.hotel_id);
          if (hotel) {
            hotelName = hotel.name;
          }
        } catch (error) {
          console.error(`Error retrieving hotel for room ${variantId}:`, error);
        }
      }
    }

    // Prepare notification message
    const title = `New Room Created: ${roomNumber}`;
    const description = `A new room "${roomNumber}" has been created on floor ${floor} in ${hotelName}.`;

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title,
        description,
      },
    });
    console.log(`Feed notification sent successfully for new room ${roomNumber}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "room.created",
};
