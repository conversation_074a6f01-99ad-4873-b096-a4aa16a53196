import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";

/**
 * Handle incoming WhatsApp message events
 * This subscriber creates notifications for new WhatsApp messages
 */
export default async function whatsappMessageReceivedHandler({
  event: { data },
  container,
}: SubscriberArgs<any>) {
  try {
    const logger = container.resolve("logger");
    logger.info(`Processing WhatsApp message notification: ${data.whatsapp_message_id}`);

    // Create a notification in the system
    const notificationModuleService = container.resolve(Modules.NOTIFICATION);
    if (!notificationModuleService) {
      logger.warn("Notification service not found, skipping WhatsApp notification");
      return;
    }

    // Get customer information if available
    let customerInfo = "Unknown";
    if (data.customer_id) {
      try {
        const customerService = container.resolve(Modules.CUSTOMER);
        if (customerService) {
          const customer = await customerService.retrieveCustomer(data.customer_id);
          if (customer) {
            customerInfo = customer.first_name && customer.last_name
              ? `${customer.first_name} ${customer.last_name}`
              : customer.email || customer.phone || "Unknown";
          }
        }
      } catch (err) {
        logger.warn(`Could not retrieve customer info: ${err.message}`);
      }
    }

    // Get order information if available
    let orderInfo = "";
    if (data.order_id) {
      try {
        const orderService = container.resolve(Modules.ORDER);
        if (orderService) {
          const order = await orderService.retrieveOrder(data.order_id);
          if (order) {
            orderInfo = ` for order #${order.display_id || order.id}`;
          }
        }
      } catch (err) {
        logger.warn(`Could not retrieve order info: ${err.message}`);
      }
    }

    // Format phone number for display
    const formattedPhone = data.from_phone.startsWith("+")
      ? data.from_phone
      : `+${data.from_phone}`;

    // Create a notification for admin users
    await notificationModuleService.createNotifications({
      to: "", // Empty string means send to all admin users
      channel: "feed",
      template: "admin-ui",
      data: {
        title: "New WhatsApp Message",
        description: `From ${customerInfo} (${formattedPhone})${orderInfo}: ${data.content.substring(0, 100)}${data.content.length > 100 ? "..." : ""}`,
        message: data.content,
        phone: formattedPhone,
        customer_id: data.customer_id,
        order_id: data.order_id,
        message_id: data.id,
        whatsapp_message_id: data.whatsapp_message_id,
        message_type: data.message_type,
        timestamp: new Date().toISOString()
      },
    });

    logger.info(`Created notification for WhatsApp message: ${data.whatsapp_message_id}`);
  } catch (err) {
    const logger = container.resolve("logger");
    logger.error(`Error creating WhatsApp notification: ${err.message}`);
  }
}

export const config: SubscriberConfig = {
  event: "whatsapp.message.received",
};
