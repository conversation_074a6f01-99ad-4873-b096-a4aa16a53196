import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IOrderModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export default async function orderPlacedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const orderId = data.id;

  // Resolve required services
  const orderModuleService: IOrderModuleService = container.resolve(
    Modules.ORDER
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  try {
    // Retrieve the order
    const order = await orderModuleService.retrieveOrder(orderId, {
      relations: ["items"]
    }).catch(err => {
      console.error(`Error retrieving order ${orderId}:`, err);
      return null;
    });

    if (!order) {
      console.log(`Order ${orderId} not found, skipping feed notification`);
      return;
    }

    // Format currency for display
    const formattedTotal = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: order.currency_code || 'USD',
    }).format(order.total / 100);

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title: `New Order #${order.display_id}`,
        description: `A new order has been placed for ${formattedTotal}`,
      },
    });
    console.log(`Feed notification sent successfully for order ${order.id}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "order.placed",
};
