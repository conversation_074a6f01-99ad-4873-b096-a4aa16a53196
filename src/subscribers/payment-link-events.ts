import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import <PERSON><PERSON> from "stripe";
import { Modules } from "@camped-ai/framework/utils";

/**
 * Subscriber for Stripe payment link events
 * This subscriber handles events from Stripe related to payment links
 */
export default async function paymentLinkEventHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; type: string; data: any }>) {
  try {
    // Get the event type and data
    const eventType = data.type;
    const eventData = data.data?.object;

    if (!eventData) {
      console.warn("⚠️ No event data found in Stripe webhook");
      return;
    }

    console.log(`✅ Processing Stripe webhook event: ${eventType}`);

    // Get the booking service
    const orderService = container.resolve(Modules.ORDER);

    // Handle different event types
    switch (eventType) {
      case "checkout.session.completed": {
        // A checkout session has completed successfully
        const session = eventData as Stripe.Checkout.Session;

        // Get the order ID from the metadata
        const orderId = session.metadata?.order_id;
        // Get the booking
        const booking = await orderService.retrieveOrder(orderId);

        if (!orderId) {
          console.warn("⚠️ No order ID found in session metadata");
          return;
        }

        console.log(`✅ Payment completed for order ${orderId}`);

        // Update the booking status to paid

        await orderService.updateOrders(orderId, {
          status: "completed",
          metadata: {
            ...booking.metadata,
            payment_status: "paid",
            payment_completed_at: new Date().toISOString(),
            payment_session_id: session.id,
          },
        });

        break;
      }

      default:
        // Unhandled event type
        console.log(`Unhandled event type: ${eventType}`);
    }
  } catch (error) {
    console.error("Error processing Stripe webhook event:", error);
  }
}

// Configure the subscriber to listen for Stripe webhook events
export const config: SubscriberConfig = {
  event: "stripe.webhook_received",
};
