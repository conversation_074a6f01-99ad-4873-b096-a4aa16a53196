import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export default async function productCreateFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const productId = data.id;

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  try {
    // Retrieve the product
    const product = await productModuleService.retrieveProduct(productId, {
      relations: ["variants", "options", "categories", "images", "tags"]
    });

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title: `New Product: ${product.title}`,
        description: product.description || "A new product has been created",
      },
    });
    console.log(`Feed notification sent successfully for product ${product.id}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "product.created",
};
