import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function destinationDeletedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{ ids: string[], count?: number, names?: string[] }>) {
  const ids = data.ids || [];
  const count = data.count || ids.length;
  const names = data.names || [];

  // Resolve required services
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Destination deleted event data:", JSON.stringify(data, null, 2));

    if (!ids.length) {
      console.log(`No destination IDs provided, skipping email notification`);
      return;
    }

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "destination.deleted",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for destination.deleted are not active, skipping`);
      return;
    }

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
      "destination.deleted",
      "email",
    );

    if (!emailTemplate) {
      console.log(`Email template for destination.deleted not found, skipping`);
      return;
    }

    // Prepare data for template rendering
    const templateData = {
      destination: {
        ids: ids,
        count: count,
        names: names
      },
      frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
    };

    // Compile and render the template with Handlebars
    const compiledTemplate = Handlebars.compile(emailTemplate.content);
    const emailBody = compiledTemplate(templateData);

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Send email to each admin
    for (const email of adminEmails) {
      try {
        await notificationModuleService.createNotifications({
          to: email,
          channel: "email",
          template: "destination.deleted",
          data: {
            subject: emailTemplate.subject || "Destinations Deleted",
            html: emailBody,
          },
        });
        console.log(`Email sent successfully to ${email} for deleted destinations: ${ids.join(', ')}`);
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.deleted",
};
