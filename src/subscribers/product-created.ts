import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";

export default async function productCreateHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const productId = data.id;

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_SERVICE
  );

  // Retrieve the product
  const product = await productModuleService.retrieveProduct(productId, {
    relations: ["variants", "options", "categories", "images", "tags"]
  });

  // Check if notifications should be sent for this event and channel
  const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
    "product.created",
    "email"
  );

  if (!shouldSendEmail) {
    console.log(`Notifications for product.created are disabled. Skipping email for product ${productId}`);
    return;
  }

  // Get the template from the database
  const emailTemplate = await notificationTemplateService.getTemplate(
    "product.created",
    "email"
  );

  if (!emailTemplate) {
    console.log(`No active template found for product.created. Skipping email for product ${productId}`);
    return;
  }

  // Prepare data for template rendering
  const templateData = {
    product: {
      id: product.id,
      title: product.title,
      description: product.description,
      handle: product.handle,
      thumbnail: product.thumbnail,
      created_at: product.created_at
    },
    frontendURL: process.env.MEDUSA_STOREFRONT_URL || ""
  };

  // Compile and render the template with Handlebars
  const compiledTemplate = Handlebars.compile(emailTemplate.content);
  const emailBody = compiledTemplate(templateData);

  // Get admin emails from environment variable or use a default
  const adminEmails = process.env.ADMIN_EMAILS
    ? process.env.ADMIN_EMAILS.split(',')
    : ["<EMAIL>"];

  // Send email to each admin
  for (const email of adminEmails) {
    try {
      await notificationModuleService.createNotifications({
        to: email.trim(),
        channel: "email",
        template: "product.created",
        data: {
          subject: emailTemplate.subject || "New Product Created",
          html: emailBody,
        },
      });
      console.log(`Email sent successfully to ${email} for product ${product.id}`);
    } catch (error) {
      console.error(`Error sending email to ${email}:`, error);
    }
  }
}

export const config: SubscriberConfig = {
  event: "product.created",
};