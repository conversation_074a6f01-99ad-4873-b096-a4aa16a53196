import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import {IOrderModuleService, IPaymentModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export default async function orderPlaceHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
    const paymentId = data.id

  const paymentModuleService: IPaymentModuleService = container.resolve(
    Modules.PAYMENT
  );
  const orderModuleService: IOrderModuleService = container.resolve(
    Modules.ORDER
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const paymentCapture = await paymentModuleService.listCaptures({payment_id: paymentId});
  const orderTransaction = await orderModuleService.listOrderTransactions({reference_id: paymentCapture[0].id}, {relations: ["order"]});
  const order = await orderModuleService.updateOrders([{id: orderTransaction[0].order.id, is_draft_order: false}])
}

export const config: SubscriberConfig = {
  event: "payment.captured",
};
