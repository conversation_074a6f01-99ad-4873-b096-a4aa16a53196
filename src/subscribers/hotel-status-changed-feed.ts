import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";

export default async function hotelStatusChangedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  hotel?: any;
  hotel_name?: string;
  changes?: Array<{
    type: string;
    new_status?: boolean;
    new_value?: string;
  }>;
}>) {
  const hotelId = data.id;
  const hotel = data?.hotel;
  const hotelName = data?.hotel_name;
  const changes = data?.changes || [];

  // Resolve required services
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Hotel status changed event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendFeed = await notificationTemplateService.shouldSendNotification(
      "hotel.status_changed",
      "feed"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendFeed) {
      console.log(`Feed notifications for hotel.status_changed are not active, skipping`);
      return;
    }

    // If hotel is not provided in the event data, retrieve it
    const hotelData = hotel || await hotelModuleService.retrieveHotel(hotelId);

    if (!hotelData) {
      console.log(`Hotel ${hotelId} not found, skipping feed notification`);
      return;
    }

    // Use hotel name from event data if available, otherwise from retrieved data
    const name = hotelName || hotelData.name;

    // Process each change and create a separate notification for each
    for (const change of changes) {
      let title = `Updated: ${name}`;
      let description = `This hotel has been modified.`;

      // Set title and description based on change type
      if (change.type === 'active_status' && change.new_status !== undefined) {
        const statusText = change.new_status ? "Active" : "Inactive";
        title = `Status Changed: ${name}`;
        description = `This hotel is now ${statusText}.`;
      }

      // Send individual feed notification for this change
      await notificationModuleService.createNotifications({
        to: "",
        channel: "feed",
        template: "admin-ui",
        data: {
          title,
          description,
        },
      });

      console.log(`Feed notification sent for ${change.type} change to hotel ${hotelId}`);
    }

    if (changes.length === 0) {
      console.log(`No changes detected for hotel ${hotelId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "hotel.status_changed",
};
