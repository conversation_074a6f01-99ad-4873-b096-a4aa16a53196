import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import <PERSON><PERSON> from "stripe";
import { Modules } from "@camped-ai/framework/utils";
import { completeCartWorkflow } from "@camped-ai/medusa/core-flows";

/**
 * Subscriber for Stripe checkout session events
 * This subscriber handles events from Stripe related to checkout sessions
 */
export default async function stripeCheckoutEventHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; type: string; data: any }>) {
  try {
    // Get the event type and data
    const eventType = data.type;
    const eventData = data.data?.object;

    if (!eventData) {
      console.warn("⚠️ No event data found in Stripe webhook");
      return;
    }

    console.log(`✅ Processing Stripe checkout webhook event: ${eventType}`);

    // Handle different event types
    switch (eventType) {
      case "checkout.session.completed": {
        // A checkout session has completed successfully
        const session = eventData as Stripe.Checkout.Session;

        // Get the cart ID from the metadata
        const cartId = session.metadata?.cart_id;

        if (!cartId) {
          console.warn("⚠️ No cart ID found in session metadata");
          return;
        }

        console.log(`✅ Payment completed for cart ${cartId}`);

        try {
          // Complete the cart using the workflow
          const { result } = await completeCartWorkflow(container).run({
            input: {
              id: cartId,
            },
          });

          console.log(`✅ Cart ${cartId} completed successfully, order created:`, result.id);

          // Update the order with Stripe session information
          const orderService = container.resolve(Modules.ORDER);
          await orderService.updateOrders(result.id, {
            metadata: {
              ...result.metadata,
              stripe_checkout_session_id: session.id,
              stripe_payment_intent_id: session.payment_intent,
              payment_status: "paid",
              payment_completed_at: new Date().toISOString(),
            },
          });

          console.log(`✅ Order ${result.id} updated with Stripe payment information`);

        } catch (error) {
          console.error(`❌ Failed to complete cart ${cartId}:`, error);
          
          // If the cart completion fails, we should still log the successful payment
          // but not throw an error that would cause the webhook to retry
          console.log(`⚠️ Payment was successful but cart completion failed for cart ${cartId}`);
        }

        break;
      }

      case "checkout.session.expired": {
        // A checkout session has expired
        const session = eventData as Stripe.Checkout.Session;
        const cartId = session.metadata?.cart_id;

        if (cartId) {
          console.log(`⚠️ Checkout session expired for cart ${cartId}`);
          // Optionally, you could clean up the cart or send a notification
        }

        break;
      }

      default:
        console.log(`ℹ️ Unhandled Stripe checkout event type: ${eventType}`);
        break;
    }

  } catch (error) {
    console.error("❌ Error processing Stripe checkout webhook:", error);
    // Don't throw the error to avoid webhook retries for unrecoverable errors
  }
}

export const config: SubscriberConfig = {
  event: "stripe.checkout_session_completed",
};
