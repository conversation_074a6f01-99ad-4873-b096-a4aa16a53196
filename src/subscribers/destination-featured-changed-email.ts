import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function destinationFeaturedChangedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; destination?: any; new_status: boolean }>) {
  const destinationId = data.id;
  const destination = data?.destination;
  const newStatus = data.new_status;

  // Resolve required services
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Destination featured changed event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "destination.featured_changed",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for destination.featured_changed are not active, skipping`);
      return;
    }

    // If destination is not provided in the event data, retrieve it
    const destinationData = destination || await destinationModuleService.retrieveDestination(destinationId);

    if (!destinationData) {
      console.log(`Destination ${destinationId} not found, skipping email notification`);
      return;
    }

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
      "destination.featured_changed",
      "email"
    );

    if (!emailTemplate) {
      console.log(`Email template for destination.featured_changed not found, skipping`);
      return;
    }

    // Prepare data for template rendering
    const featuredText = newStatus ? "Featured" : "Not Featured";
    const templateData = {
      destination: {
        id: destinationData.id,
        name: destinationData.name,
        description: destinationData.description,
        active: destinationData.is_active,
        featured: newStatus,
        currency: destinationData.currency_code,
        featured_text: featuredText
      },
      frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
    };

    // Compile and render the template with Handlebars
    const compiledTemplate = Handlebars.compile(emailTemplate.content);
    const emailBody = compiledTemplate(templateData);

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Send email to each admin
    for (const email of adminEmails) {
      try {
        await notificationModuleService.createNotifications({
          to: email,
          channel: "email",
          template: "destination.featured_changed",
          data: {
            subject: emailTemplate.subject || `Destination Featured Status Changed: ${destinationData.name}`,
            html: emailBody,
          },
        });
        console.log(`Email sent successfully to ${email} for destination ${destinationData.id} featured status change`);
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.featured_changed",
};
