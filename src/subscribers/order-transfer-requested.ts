import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IOrderModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { orderTransferAccept } from "src/modules/my-notification/email/templates/order-transfer-requested";

export default async function orderTransferRequestHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; order_change_id: string }>) {
  const orderId = data.id;
  const orderChangeId = data.order_change_id;

  const orderModuleService: IOrderModuleService = container.resolve(
    Modules.ORDER
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  const order = await orderModuleService.retrieveOrder(orderId);
  const orderChangeAction = await orderModuleService.listOrderChangeActions({
    order_change_id: orderChangeId,
  });

  const orderTransferAcceptLink = `${process.env.MEDUSA_STOREFRONT_URL}/order/${orderId}/transfer/${orderChangeAction[0].details.token}/accept`;

  // Replace placeholders in the email template
  const emailBody = orderTransferAccept.body
    .replace("{{ recipient.email }}", order.email)
    .replace("{{ order.id }}", orderId)
    .replace("{{ order_transfer_accept_link }}", orderTransferAcceptLink)
    .replace("{{ frontendURL }}", `${process.env.MEDUSA_STOREFRONT_URL}`);

  //   Send email
  try {
    await notificationModuleService.createNotifications({
      to: order.email,
      channel: "email",
      template: "invite.created",
      data: {
        subject: orderTransferAccept.subject,
        html: emailBody,
      },
    });

    console.log(`Email sent successfully to ${order.email}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "order.transfer_requested",
};
