import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";

export default async function destinationCreatedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; destination: any }>) {
  const destinationId = data.id;
  const destination = data.destination;

  // Resolve required services
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  try {
    // If destination is not provided in the event data, retrieve it
    const destinationData = destination || await destinationModuleService.retrieveDestination(destinationId);

    if (!destinationData) {
      console.log(`Destination ${destinationId} not found, skipping feed notification`);
      return;
    }

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title: `New Destination: ${destinationData.name}`,
        description: destinationData.description || `A new destination "${destinationData.name}" has been created in ${destinationData.country}.`,
      },
    });
    console.log(`Feed notification sent successfully for destination ${destinationData.id}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.created",
};
