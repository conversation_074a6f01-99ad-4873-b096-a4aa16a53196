import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IUserModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { inviteCreated } from "src/modules/my-notification/email/templates/invite-created";

export default async function inviteResentHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const inviteId = data.id;

  const userModuleService: IUserModuleService = container.resolve(Modules.USER);
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  const invites = await userModuleService.refreshInviteTokens([inviteId]);
  const user = await userModuleService.retrieveInvite(inviteId);


  // Replace placeholders in the email template
  const emailBody = inviteCreated.body
    .replace("{{ user.token }}", user.token)
    .replace("{{ user.email }}", user.email)
    .replace("{{ frontendURL }}", `${process.env.MEDUSA_STOREFRONT_URL}`);

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: user.email,
      channel: "email",
      template: "invite.resent",
      data: {
        subject: inviteCreated.subject,
        html: emailBody,
      },
    });
    console.log(`Email sent successfully to ${user.email}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "invite.resent",
};
