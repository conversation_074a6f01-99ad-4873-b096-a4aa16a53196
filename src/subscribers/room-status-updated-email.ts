import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function roomStatusUpdatedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  variant?: any;
  room_number?: string;
  changes?: Array<{
    type: string;
    old_status?: string;
    new_status?: string;
    old_value?: boolean;
    new_value?: boolean;
  }>;
}>) {
  const variantId = data.id;
  const variant = data.variant;
  const roomNumber = data.room_number;
  const changes = data.changes || [];

  console.log("Room status updated email handler triggered", {data});

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Room status updated event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "room.status_updated",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for room.status_updated are not active, skipping`);
      return;
    }

    // If variant is not provided in the event data, retrieve it
    const variantData = variant || await productModuleService.retrieveProductVariant(variantId);

    if (!variantData) {
      console.log(`Product variant ${variantId} not found, skipping email notification`);
      return;
    }

    // Get the product to which this variant belongs
    const product = await productModuleService.retrieveProduct(variantData.product_id);

    if (!product) {
      console.log(`Product for variant ${variantId} not found, skipping email notification`);
      return;
    }

    // Try to get room details from metadata or options
    let roomNum = roomNumber || "Unknown";
    let floor = "Unknown";
    let hotelName = "Unknown Hotel";
    let hotelId = null;

    // Extract room details from variant options or metadata
    if (variantData.options) {
      for (const option of variantData.options) {
        if (option.option?.title?.toLowerCase() === "room number") {
          roomNum = option.value;
        } else if (option.option?.title?.toLowerCase() === "floor") {
          floor = option.value;
        }
      }
    }

    // If not found in options, try metadata
    if (variantData.metadata) {
      if (variantData.metadata.room_number) {
        roomNum = variantData.metadata.room_number;
      }
      if (variantData.metadata.floor) {
        floor = variantData.metadata.floor;
      }
      if (variantData.metadata.hotel_id) {
        hotelId = variantData.metadata.hotel_id;
        try {
          const hotel = await hotelModuleService.retrieveHotel(hotelId);
          if (hotel) {
            hotelName = hotel.name;
          }
        } catch (error) {
          console.error(`Error retrieving hotel for room ${variantId}:`, error);
        }
      }
    }

    // Get the email template
    // First, list templates to find the one we need
    const templates = await notificationTemplateService.listNotificationTemplates({
      event_name: "room.status_updated",
      channel: "email",
    });

    // Get the first matching template
    const emailTemplate = templates.length > 0 ? templates[0] : null;

    if (!emailTemplate) {
      console.log(`Email template for room.status_updated not found, skipping`);
      return;
    }

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Process each change and create a separate notification for each
    for (const change of changes) {
      let subject = `Room Updated: ${roomNum}`;
      let changeDescription = `Room ${roomNum} has been modified.`;

      // Set subject and description based on change type
      if (change.type === 'status' && change.new_status) {
        subject = `Room Status Changed: ${roomNum}`;
        changeDescription = `Room ${roomNum} on floor ${floor} in ${hotelName} is now ${change.new_status}.`;
      }
      else if (change.type === 'active' && change.new_value !== undefined) {
        const activeText = change.new_value ? "Active" : "Inactive";
        subject = `Room Availability Changed: ${roomNum}`;
        changeDescription = `Room ${roomNum} on floor ${floor} in ${hotelName} is now ${activeText}.`;
      }

      // Prepare data for template rendering
      const templateData = {
        room: {
          id: variantId,
          number: roomNum,
          floor: floor,
          title: variantData.title || `Room ${roomNum}`,
          change_type: change.type,
          change_description: changeDescription,
          old_status: change.old_status,
          new_status: change.new_status,
          old_value: change.old_value,
          new_value: change.new_value
        },
        hotel: {
          id: hotelId,
          name: hotelName
        },
        frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
      };

      // Compile and render the template with Handlebars
      const compiledTemplate = Handlebars.compile(emailTemplate.content);
      const emailBody = compiledTemplate(templateData);

      // Send email to each admin
      for (const email of adminEmails) {
        try {
          await notificationModuleService.createNotifications({
            to: email,
            channel: "email",
            template: "room.status_updated",
            data: {
              subject: subject || emailTemplate.subject,
              html: emailBody,
            },
          });
          console.log(`Email sent successfully to ${email} for room ${roomNum} ${change.type} change`);
        } catch (error) {
          console.error(`Error sending email to ${email}:`, error);
        }
      }
    }

    if (changes.length === 0) {
      console.log(`No changes detected for room ${variantId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "room.status_updated",
};
