import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import {IOrderModuleService, IPaymentModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { refundProcessed } from "src/modules/my-notification/email/templates/payment-refunded";
import Handlebars from "handlebars";

export default async function orderPlaceHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
    const paymentId = data.id

  const paymentModuleService: IPaymentModuleService = container.resolve(
    Modules.PAYMENT
  );
  const orderModuleService: IOrderModuleService = container.resolve(
    Modules.ORDER
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const payment = await paymentModuleService.listPayments({id: paymentId}, {relations: ["payment_collection"]});
  const orderTransaction = await orderModuleService.listOrderTransactions({reference_id: paymentId}, {relations: ["order", "order.shipping_address.*"]});

const currencySymbol =payment[0].currency_code === "inr" ? "₹": payment[0].currency_code

  const emailBody = Handlebars.compile(refundProcessed.body)({
    order: orderTransaction[0].order,
    currencySymbol,
    refundAmount: payment[0].payment_collection.refunded_amount,
    frontendURL: `${process.env.MEDUSA_STOREFRONT_URL}`,
  });


  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: orderTransaction[0].order.email,
      channel: "email",
      template: "delivery.created",
      data: {
        subject: refundProcessed.subject,
        html: emailBody,
      },
    });
    console.log(`Email sent successfully`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "payment.refunded",
};
