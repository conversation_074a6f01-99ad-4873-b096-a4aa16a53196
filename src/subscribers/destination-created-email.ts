import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function destinationCreatedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; destination: any }>) {
  console.log({data})
  const destinationId = data.id;
  const destination = data.destination;

  // Resolve required services
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Destination created event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "destination.created",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for destination.created are not active, skipping`);
      return;
    }

    // If destination is not provided in the event data, retrieve it
    const destinationData = destination || await destinationModuleService.retrieveDestination(destinationId);
    console.log({destinationData});

    if (!destinationData) {
      console.log(`Destination ${destinationId} not found, skipping email notification`);
      return;
    }

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
      "destination.created",
      "email",
  );

    if (!emailTemplate) {
      console.log(`Email template for destination.created not found, skipping`);
      return;
    }

    // Prepare data for template rendering
    const templateData = {
      destination: {
        id: destinationData.id,
        name: destinationData.name,
        description: destinationData.description,
        active: destinationData.is_active,
        featured: destinationData.is_featured,
        currency: destinationData.currency_code,
        created_at: destinationData.created_at
      },
      frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
    };

    // Compile and render the template with Handlebars
    const compiledTemplate = Handlebars.compile(emailTemplate.content);
    const emailBody = compiledTemplate(templateData);

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Send email to each admin
    for (const email of adminEmails) {
      try {
        await notificationModuleService.createNotifications({
          to: email,
          channel: "email",
          template: "destination.created",
          data: {
            subject: emailTemplate.subject || "New Destination Created",
            html: emailBody,
          },
        });
        console.log(`Email sent successfully to ${email} for destination ${destinationData.id}`);
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.created",
};
