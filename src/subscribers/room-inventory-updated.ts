import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";

export default async function roomInventoryUpdatedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  const roomId = data.inventory_item_id || data.id;

  // Log the inventory update
  console.log(`Room inventory updated for room ${roomId}`);

  try {
    // Clear any caches related to this room
    // This is a simplified version that just logs the event
    // In a real implementation, you might want to invalidate specific caches

    // Example: If you have a cache service
    // const cacheService = container.resolve("cacheService");
    // await cacheService.invalidate(`room_availability_${roomId}`);

    console.log(`Cleared cache for room ${roomId}`);
  } catch (error) {
    console.error(`Error handling room inventory update: ${error.message}`);
  }
}

export const config: SubscriberConfig = {
  event: "room_inventory.updated",
};
