import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Mo<PERSON>les, ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import {
  IOrderModuleService,
  ICartModuleService,
} from "@camped-ai/framework/types";

/**
 * Cart Completion Subscriber
 * This subscriber transfers cart metadata to order metadata when an order is placed
 */
export default async function cartCompletionHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string }>) {
  try {
    console.log("Cart completion subscriber triggered", JSON.stringify(data));

    // Get the order ID from the event data
    const orderId = data.id;

    if (!orderId) {
      console.error("No order ID found in event data");
      return;
    }

    // Resolve required services
    const orderModuleService: IOrderModuleService = container.resolve(
      Modules.ORDER
    );
    const cartModuleService: ICartModuleService = container.resolve(
      Modules.CART
    );
    const queryService = container.resolve(ContainerRegistrationKeys.QUERY);

    // Retrieve the order
    const order = await orderModuleService.retrieveOrder(orderId);

    if (!order) {
      console.error(`Order with ID ${orderId} not found`);
      return;
    }

    console.log(`Processing order: ${orderId}`);

    // Find the cart that was used to create this order by querying the order_cart table
    let cartId: string | undefined;

    try {
      // Query the order_cart table to get the cart ID for this order
      const { data: orderCartRelation } = await queryService.graph({
        entity: "order_cart",
        filters: {
          order_id: orderId,
        },
        fields: ["cart_id"],
      });

      if (orderCartRelation && orderCartRelation.length > 0) {
        cartId = orderCartRelation[0].cart_id;
      }
    } catch (queryError) {
      console.error(
        `Error querying order_cart relation: ${queryError.message}`
      );
    }

    if (!cartId) {
      console.log(
        `No cart found for order ${orderId}, skipping metadata transfer`
      );
      return;
    }

    console.log(`Found cart ${cartId} for order ${orderId}`);

    // Retrieve the cart with its metadata
    const cart = await cartModuleService.retrieveCart(cartId);

    if (!cart || !cart.metadata) {
      console.log(`Cart ${cartId} has no metadata, skipping metadata transfer`);
      return;
    }

    // Merge cart metadata with existing order metadata
    const updatedMetadata = {
      ...(order.metadata || {}),
      ...cart.metadata,
      // Ensure we don't overwrite any existing order-specific metadata
      cart_id: cartId,
    };

    // Update the order with the merged metadata
    await orderModuleService.updateOrders(orderId, {
      metadata: updatedMetadata,
    });

    console.log(
      `Successfully transferred metadata from cart ${cartId} to order ${orderId}`
    );
  } catch (error) {
    console.error("Error in cart completion subscriber:", error);
  }
}

export const config: SubscriberConfig = {
  event: "order.placed",
};
