import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function destinationStatusChangedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  destination?: any;
  destination_name?: string;
  changes?: Array<{
    type: string;
    new_status?: boolean;
    new_value?: string;
  }>;
}>) {
  const destinationId = data.id;
  const destination = data?.destination;
  const destinationName = data?.destination_name;
  const changes = data?.changes || [];

  // Resolve required services
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Destination status changed event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "destination.status_changed",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for destination.status_changed are not active, skipping`);
      return;
    }

    // If destination is not provided in the event data, retrieve it
    const destinationData = destination || await destinationModuleService.retrieveDestination(destinationId);

    if (!destinationData) {
      console.log(`Destination ${destinationId} not found, skipping email notification`);
      return;
    }

    // Use destination name from event data if available, otherwise from retrieved data
    const name = destinationName || destinationData.name;

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
     "destination.status_changed",
       "email",
    );

    if (!emailTemplate) {
      console.log(`Email template for destination.status_changed not found, skipping`);
      return;
    }

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Process each change and create a separate notification for each
    for (const change of changes) {
      let subject = `Updated: ${name}`;
      let changeDescription = "This destination has been modified.";

      // Set title and description based on change type
      if (change.type === 'active_status' && change.new_status !== undefined) {
        const statusText = change.new_status ? "Active" : "Inactive";
        subject = `Status Changed: ${name}`;
        changeDescription = `This destination is now ${statusText}.`;
      }
      else if (change.type === 'featured_status' && change.new_status !== undefined) {
        const featuredText = change.new_status ? "Featured" : "Not Featured";
        subject = `Featured Status: ${name}`;
        changeDescription = `This destination is now ${featuredText.toLowerCase()}.`;
      }
      else if (change.type === 'currency' && change.new_value) {
        subject = `Currency Updated: ${name}`;
        changeDescription = `The default currency for this destination has been set to ${change.new_value}.`;
      }

      // Prepare data for template rendering
      const templateData = {
        destination: {
          id: destinationData.id,
          name: name,
          description: destinationData.description,
          active: destinationData.is_active,
          featured: destinationData.is_featured,
          currency: destinationData.currency_code,
          change_type: change.type,
          change_description: changeDescription,
          new_value: change.new_value,
          new_status: change.new_status
        },
        frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
      };

      // Compile and render the template with Handlebars
      const compiledTemplate = Handlebars.compile(emailTemplate.content);
      const emailBody = compiledTemplate(templateData);

      // Send email to each admin
      for (const email of adminEmails) {
        try {
          await notificationModuleService.createNotifications({
            to: email,
            channel: "email",
            template: "destination.status_changed",
            data: {
              subject: subject || emailTemplate.subject,
              html: emailBody,
            },
          });
          console.log(`Email sent successfully to ${email} for destination ${destinationData.id} ${change.type} change`);
        } catch (error) {
          console.error(`Error sending email to ${email}:`, error);
        }
      }
    }

    if (changes.length === 0) {
      console.log(`No changes detected for destination ${destinationId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.status_changed",
};
