import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function hotelCreatedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; hotel?: any }>) {
  const hotelId = data.id;
  const hotel = data.hotel;

  // Resolve required services
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Hotel created event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "hotel.created",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for hotel.created are not active, skipping`);
      return;
    }

    // If hotel is not provided in the event data, retrieve it
    const hotelData = hotel || await hotelModuleService.retrieveHotel(hotelId, {
      relations: ["destination"]
    });

    if (!hotelData) {
      console.log(`Hotel ${hotelId} not found, skipping email notification`);
      return;
    }

    // Get destination information
    let destinationData = null;
    if (hotelData.destination_id) {
      try {
        destinationData = await destinationModuleService.retrieveDestination(hotelData.destination_id);
      } catch (error) {
        console.error(`Error retrieving destination for hotel ${hotelId}:`, error);
      }
    }

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
      "hotel.created",
     "email",
    );

    if (!emailTemplate) {
      console.log(`Email template for hotel.created not found, skipping`);
      return;
    }

    // Prepare data for template rendering
    const templateData = {
      hotel: {
        id: hotelData.id,
        name: hotelData.name,
        description: hotelData.description,
        active: hotelData.is_active,
        address: hotelData.address,
        created_at: hotelData.created_at
      },
      destination: destinationData ? {
        id: destinationData.id,
        name: destinationData.name
      } : {
        id: "unknown",
        name: "Unknown Destination"
      },
      frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
    };

    // Compile and render the template with Handlebars
    const compiledTemplate = Handlebars.compile(emailTemplate.content);
    const emailBody = compiledTemplate(templateData);

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Send email to each admin
    for (const email of adminEmails) {
      try {
        await notificationModuleService.createNotifications({
          to: email,
          channel: "email",
          template: "hotel.created",
          data: {
            subject: emailTemplate.subject || `New Hotel: ${hotelData.name}`,
            html: emailBody,
          },
        });
        console.log(`Email sent successfully to ${email} for hotel ${hotelData.id}`);
      } catch (error) {
        console.error(`Error sending email to ${email}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "hotel.created",
};
