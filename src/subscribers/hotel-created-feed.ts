import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";

export default async function hotelCreatedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; hotel?: any }>) {
  const hotelId = data.id;
  const hotel = data.hotel;

  // Resolve required services
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Hotel created event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendFeed = await notificationTemplateService.shouldSendNotification(
      "hotel.created",
      "feed"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendFeed) {
      console.log(`Feed notifications for hotel.created are not active, skipping`);
      return;
    }

    // If hotel is not provided in the event data, retrieve it
    const hotelData = hotel || await hotelModuleService.retrieveHotel(hotelId, {
      relations: ["destination"]
    });

    if (!hotelData) {
      console.log(`Hotel ${hotelId} not found, skipping feed notification`);
      return;
    }

    // Get destination information
    let destinationName = "Unknown Destination";
    if (hotelData.destination_id) {
      try {
        const destination = await destinationModuleService.retrieveDestination(hotelData.destination_id);
        if (destination) {
          destinationName = destination.name;
        }
      } catch (error) {
        console.error(`Error retrieving destination for hotel ${hotelId}:`, error);
      }
    }

    // Prepare notification message
    const title = `New Hotel: ${hotelData.name}`;
    const description = `A new hotel "${hotelData.name}" has been added to destination "${destinationName}".`;

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title,
        description,
      },
    });
    console.log(`Feed notification sent successfully for hotel ${hotelData.id}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "hotel.created",
};
