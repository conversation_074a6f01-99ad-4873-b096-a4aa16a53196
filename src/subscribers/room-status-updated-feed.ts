import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";

export default async function roomStatusUpdatedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  variant?: any;
  room_number?: string;
  changes?: Array<{
    type: string;
    old_status?: string;
    new_status?: string;
    old_value?: boolean;
    new_value?: boolean;
  }>;
}>) {
  const variantId = data.id;
  const variant = data.variant;
  const roomNumber = data.room_number;
  const changes = data.changes || [];

  console.log("Room status updated feed handler triggered", {data});

  // Resolve required services
  const productModuleService: IProductModuleService = container.resolve(
    Modules.PRODUCT
  );
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Room status updated event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendFeed = await notificationTemplateService.shouldSendNotification(
      "room.status_updated",
      "feed"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendFeed) {
      console.log(`Feed notifications for room.status_updated are not active, skipping`);
      return;
    }

    // If variant is not provided in the event data, retrieve it
    const variantData = variant || await productModuleService.retrieveProductVariant(variantId);

    if (!variantData) {
      console.log(`Product variant ${variantId} not found, skipping feed notification`);
      return;
    }

    // Get the product to which this variant belongs
    const product = await productModuleService.retrieveProduct(variantData.product_id);

    if (!product) {
      console.log(`Product for variant ${variantId} not found, skipping feed notification`);
      return;
    }

    // Try to get room details from metadata or options
    let roomNum = roomNumber || "Unknown";
    let floor = "Unknown";
    let hotelName = "Unknown Hotel";

    // Extract room details from variant options or metadata
    if (variantData.options) {
      for (const option of variantData.options) {
        if (option.option?.title?.toLowerCase() === "room number") {
          roomNum = option.value;
        } else if (option.option?.title?.toLowerCase() === "floor") {
          floor = option.value;
        }
      }
    }

    // If not found in options, try metadata
    if (variantData.metadata) {
      if (variantData.metadata.room_number) {
        roomNum = variantData.metadata.room_number;
      }
      if (variantData.metadata.floor) {
        floor = variantData.metadata.floor;
      }
      if (variantData.metadata.hotel_id) {
        try {
          const hotel = await hotelModuleService.retrieveHotel(variantData.metadata.hotel_id);
          if (hotel) {
            hotelName = hotel.name;
          }
        } catch (error) {
          console.error(`Error retrieving hotel for room ${variantId}:`, error);
        }
      }
    }

    // Process each change and create a separate notification for each
    for (const change of changes) {
      let title = `Room Updated: ${roomNum}`;
      let description = `Room ${roomNum} has been modified.`;

      // Set title and description based on change type
      if (change.type === 'status' && change.new_status) {
        title = `Room Status Changed: ${roomNum}`;
        description = `Room ${roomNum} on floor ${floor} in ${hotelName} is now ${change.new_status}.`;
      }
      else if (change.type === 'active' && change.new_value !== undefined) {
        const activeText = change.new_value ? "Active" : "Inactive";
        title = `Room Availability Changed: ${roomNum}`;
        description = `Room ${roomNum} on floor ${floor} in ${hotelName} is now ${activeText}.`;
      }

      // Send individual feed notification for this change
      await notificationModuleService.createNotifications({
        to: "",
        channel: "feed",
        template: "admin-ui",
        data: {
          title,
          description,
        },
      });

      console.log(`Feed notification sent for ${change.type} change to room ${roomNum}`);
    }

    if (changes.length === 0) {
      console.log(`No changes detected for room ${variantId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "room.status_updated",
};
