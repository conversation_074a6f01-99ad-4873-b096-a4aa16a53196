import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { IFulfillmentModuleService, IOrderModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export default async function shipmentCreatedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ fulfillment_id: string }>) {
  const fulfillmentId = data.fulfillment_id;

  // Resolve required services
  const fulfillmentModuleService: IFulfillmentModuleService = container.resolve(
    Modules.FULFILLMENT
  );
  const orderModuleService: IOrderModuleService = container.resolve(
    Modules.ORDER
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);

  try {
    // Retrieve the fulfillment
    const fulfillment = await fulfillmentModuleService.retrieveFulfillment(fulfillmentId)
      .catch(err => {
        console.error(`Error retrieving fulfillment ${fulfillmentId}:`, err);
        return null;
      });

    if (!fulfillment) {
      console.log(`Fulfillment ${fulfillmentId} not found, skipping feed notification`);
      return;
    }

    // Retrieve the order
    const order = await orderModuleService.retrieveOrder(fulfillment.order_id, {
      relations: ["items", "customer"]
    }).catch(err => {
      console.error(`Error retrieving order for fulfillment ${fulfillmentId}:`, err);
      return null;
    });

    if (!order) {
      console.log(`Order for fulfillment ${fulfillmentId} not found, skipping feed notification`);
      return;
    }

    // Send feed notification to all users (empty "to" field)
    await notificationModuleService.createNotifications({
      to: "",
      channel: "feed",
      template: "admin-ui",
      data: {
        title: `Order #${order.display_id} Shipped`,
        description: `Order #${order.display_id} has been shipped${fulfillment.tracking_number ? ` with tracking number ${fulfillment.tracking_number}` : ''}.`,
      },
    });
    console.log(`Feed notification sent successfully for shipment ${fulfillment.id}`);
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "shipment.created",
};
