import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";

export default async function orderPlaceHandler({
  event: { data },
  container,
}: SubscriberArgs<any>) {
  console.log(
    "i am in the o  rder placed email subscriber",
    JSON.stringify(data)
  );

  // Resolve required services
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService =
    container.resolve(NOTIFICATION_TEMPLATE_SERVICE);
  const orderId = data.id;
  const orderModuleService = container.resolve(Modules.ORDER);
  const order = await orderModuleService.retrieveOrder(orderId, {
    relations: ["items"],
  });

  // Set currency symbol based on currency code
  const currencySymbol =
    order.currency_code === "inr" ? "₹" : order.currency_code;

  // Format numeric values for display
  if (order.items && Array.isArray(order.items)) {
    order.items.forEach((item: any) => {
      if (item.unit_price) {
        item.unit_price = parseFloat(Number(item.unit_price).toFixed(2));
      }
      if (item.raw_subtotal && item.raw_subtotal.value) {
        item.raw_subtotal.value = parseFloat(
          Number(item.raw_subtotal.value).toFixed(2)
        );
      }
    });
  }

  // Format order totals if they exist
  if (order.raw_item_subtotal && order.raw_item_subtotal.value) {
    order.raw_item_subtotal.value = parseFloat(
      Number(order.raw_item_subtotal.value).toFixed(2)
    );
  }

  if (order.raw_shipping_total && order.raw_shipping_total.value) {
    order.raw_shipping_total.value = parseFloat(
      Number(order.raw_shipping_total.value).toFixed(2)
    );
  }

  if (order.raw_item_tax_total && order.raw_item_tax_total.value) {
    order.raw_item_tax_total.value = parseFloat(
      Number(order.raw_item_tax_total.value).toFixed(2)
    );
  }

  if (order.raw_total && order.raw_total.value) {
    order.raw_total.value = parseFloat(
      Number(order.raw_total.value).toFixed(2)
    );
  }

  // Check if notifications should be sent for this event and channel
  const shouldSendEmail =
    await notificationTemplateService.shouldSendNotification(
      "order.placed",
      "email"
    );

  if (!shouldSendEmail) {
    console.log(
      `Notifications for order.placed are disabled. Skipping email for order ${orderId}`
    );
    return;
  }

  console.log("=====>1");
  // Get the template from the database
  const emailTemplate = await notificationTemplateService.getTemplate(
    "order.placed",
    "email"
  );

  console.log("=====>2");
  if (!emailTemplate) {
    console.log(
      `No active template found for order.placed. Skipping email for order ${orderId}`
    );
    return;
  }

  console.log("=====>3");
  // Create a booking object with formatted data from order metadata
  const booking: any = {};

  // Extract booking data from order metadata
  if (order.metadata) {
    // Copy all metadata fields to booking object
    Object.assign(booking, order.metadata);

    // Extract room information from order items if available
    if (order.items && order.items.length > 0) {
      const roomItem = order.items[0];

      // Use title from the line item if available
      if (roomItem.title) {
        const titleMatch = roomItem.title.match(/Room booking: (.+)/);
        if (titleMatch && titleMatch[1]) {
          booking.room_type = titleMatch[1];
        }
      }

      // Get additional data from item metadata
      if (roomItem.metadata) {
        if (!booking.check_in_date && roomItem.metadata.check_in_date) {
          booking.check_in_date = roomItem.metadata.check_in_date;
        }
        if (!booking.check_out_date && roomItem.metadata.check_out_date) {
          booking.check_out_date = roomItem.metadata.check_out_date;
        }
        if (!booking.room_config_id && roomItem.metadata.room_config_id) {
          booking.room_config_id = roomItem.metadata.room_config_id;
        }
        if (!booking.room_type && roomItem.metadata.room_config_name) {
          booking.room_type = roomItem.metadata.room_config_name;
        }
        if (!booking.number_of_rooms && roomItem.metadata.number_of_rooms) {
          booking.number_of_rooms = roomItem.metadata.number_of_rooms;
        }
      }

      // Use quantity as number of rooms if not specified
      if (!booking.number_of_rooms && roomItem.quantity) {
        booking.number_of_rooms = roomItem.quantity;
      }
    }
  }

  console.log("Booking data before formatting:", booking);

  // Format dates for better display
  try {
    // Format check-in date if it exists
    if (booking.check_in_date) {
      const checkInDate = new Date(booking.check_in_date);
      booking.formatted_check_in_date = checkInDate.toLocaleDateString(
        "en-US",
        {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );
    }

    // Format check-out date if it exists
    if (booking.check_out_date) {
      const checkOutDate = new Date(booking.check_out_date);
      booking.formatted_check_out_date = checkOutDate.toLocaleDateString(
        "en-US",
        {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );

      // Calculate number of nights if both dates exist
      if (booking.check_in_date) {
        const checkInDate = new Date(booking.check_in_date);
        const nights = Math.round(
          (checkOutDate.getTime() - checkInDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );
        booking.nights = nights;
      }
    }

    // Set default values for missing fields
    if (!booking.check_in_time) booking.check_in_time = "12:00";
    if (!booking.check_out_time) booking.check_out_time = "12:00";
    if (!booking.room_type) booking.room_type = "Standard Room";
    if (!booking.number_of_guests) booking.number_of_guests = 1;
    if (!booking.nights) booking.nights = 1;

    // Use the actual number of rooms from metadata
    if (
      !booking.number_of_rooms &&
      order.metadata &&
      order.metadata.number_of_rooms
    ) {
      booking.number_of_rooms = order.metadata.number_of_rooms;
    }
    // If not in top-level metadata, check line items
    else if (
      !booking.number_of_rooms &&
      order.metadata &&
      order.metadata.line_items &&
      Array.isArray(order.metadata.line_items)
    ) {
      // Look for room booking line items
      const roomItems = order.metadata.line_items.filter(
        (item: any) =>
          item.metadata &&
          (item.metadata.item_type === "room" || item.metadata.number_of_rooms)
      );

      if (roomItems.length > 0 && roomItems[0].metadata?.number_of_rooms) {
        booking.number_of_rooms = roomItems[0].metadata.number_of_rooms;
        console.log(
          `Found number of rooms in line items: ${booking.number_of_rooms}`
        );
      }
    }

    // Try to get hotel name from metadata
    if (!booking.hotel_name && order.metadata && order.metadata.hotel_name) {
      booking.hotel_name = order.metadata.hotel_name;
      console.log(
        `Using hotel name from order metadata: ${booking.hotel_name}`
      );
    }
    // Check line items for hotel_name
    else if (
      !booking.hotel_name &&
      order.items &&
      order.items.length > 0 &&
      order.items[0].metadata?.hotel_name
    ) {
      booking.hotel_name = order.items[0].metadata.hotel_name;
      console.log(
        `Using hotel name from line item metadata: ${booking.hotel_name}`
      );
    }
    // If hotel_name is still not set but we have hotel_id, try to fetch the hotel name
    else if (!booking.hotel_name && order.metadata && order.metadata.hotel_id) {
      try {
        const query = container.resolve("query");
        const { data: hotelData } = await query.graph({
          entity: "hotel",
          filters: {
            id: order.metadata.hotel_id,
          },
          fields: ["id", "name"],
        });

        if (hotelData && hotelData.length > 0) {
          booking.hotel_name =
            hotelData[0].name || String(hotelData[0].id) || "Hotel";
          console.log(`Found hotel name from database: ${booking.hotel_name}`);
        }
      } catch (error) {
        console.error("Error fetching hotel name:", error);
      }
    }
  } catch (error) {
    console.error("Error formatting dates:", error);
  }

  console.log("Booking data after formatting:", booking);

  // Get payment status from order if available
  const paymentStatus = (order as any).payment_status || "awaiting";

  // Prepare data for template rendering
  const templateData = {
    order,
    currencySymbol,
    frontendURL: process.env.MEDUSA_STOREFRONT_URL || "",
    booking,
    payment_status: paymentStatus,
  };

  console.log("=====>4");
  // Compile and render the template with Handlebars
  const compiledTemplate = Handlebars.compile(emailTemplate.content);
  const emailBody = compiledTemplate(templateData);

  console.log({ orderEmail: order.email });

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: order.email,
      channel: "email",
      template: "order.placed",
      data: {
        subject: emailTemplate.subject || "Order Confirmation",
        html: emailBody,
      },
    });
    console.log(`Email sent successfully for order ${orderId}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "order.placed",
};
