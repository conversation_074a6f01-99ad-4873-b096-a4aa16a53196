import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";

export default async function destinationStatusChangedFeedHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  destination?: any;
  destination_name?: string;
  changes?: Array<{
    type: string;
    new_status?: boolean;
    new_value?: string;
  }>;
}>) {
  console.log({data})
  const destinationId = data.id;
  const destination = data?.destination;
  const destinationName = data?.destination_name;
  const changes = data?.changes || [];

  // Resolve required services
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Destination status changed event data:", JSON.stringify(data, null, 2));

    // If destination is not provided in the event data, retrieve it
    const destinationData = destination || await destinationModuleService.retrieveDestination(destinationId);

    if (!destinationData) {
      console.log(`Destination ${destinationId} not found, skipping feed notification`);
      return;
    }

    // Use destination name from event data if available, otherwise from retrieved data
    const name = destinationName || destinationData.name;

    // First, check if feed notifications are active for destination events
    try {
      // Check if notifications should be sent for this event and channel
      const shouldSendFeed = await notificationTemplateService.shouldSendNotification(
        "destination.status_changed",
        "feed"
      );

      // If notifications are disabled, skip sending
      if (!shouldSendFeed) {
        console.log(`Feed notifications for destination.status_changed are not active, skipping`);
        return;
      }

      // Process each change and create a separate notification for each
      for (const change of changes) {
        let title = `Updated: ${name}`;
        let description = `This destination has been modified.`;

        // Set title and description based on change type
        if (change.type === 'active_status' && change.new_status !== undefined) {
          const statusText = change.new_status ? "Active" : "Inactive";
          title = `Status Changed: ${name}`;
          description = `This destination is now ${statusText}.`;
        }
        else if (change.type === 'featured_status' && change.new_status !== undefined) {
          const featuredText = change.new_status ? "Featured" : "Not Featured";
          title = `Featured Status: ${name}`;
          description = `This destination is now ${featuredText.toLowerCase()}.`;
        }
        else if (change.type === 'currency' && change.new_value) {
          title = `Currency Updated: ${name}`;
          description = `The default currency for this destination has been set to ${change.new_value}.`;
        }

        // Send individual feed notification for this change
        await notificationModuleService.createNotifications({
          to: "",
          channel: "feed",
          template: "admin-ui",
          data: {
            title,
            description,
          },
        });

        console.log(`Feed notification sent for ${change.type} change to destination ${destinationId}`);
      }
    } catch (templateError) {
      console.error(`Error checking notification template:`, templateError);
      // Continue with default behavior if template check fails
    }

    if (changes.length === 0) {
      console.log(`No changes detected for destination ${destinationId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending feed notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "destination.status_changed",
};
