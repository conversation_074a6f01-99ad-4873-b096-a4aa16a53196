import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../modules/hotel-management/hotel";
import HotelModuleService from "../modules/hotel-management/hotel/service";
import { DESTINATION_MODULE } from "../modules/hotel-management/destination";
import DestinationModuleService from "../modules/hotel-management/destination/service";
import { NOTIFICATION_TEMPLATE_MODULE } from "../modules/notification-template";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";
import { getAdminEmails } from "../utils/get-admin-emails";

export default async function hotelStatusChangedEmailHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  id: string;
  hotel?: any;
  hotel_name?: string;
  changes?: Array<{
    type: string;
    new_status?: boolean;
    new_value?: string;
  }>;
}>) {
  const hotelId = data.id;
  const hotel = data?.hotel;
  const hotelName = data?.hotel_name;
  const changes = data?.changes || [];

  // Resolve required services
  const hotelModuleService: HotelModuleService = container.resolve(
    HOTEL_MODULE
  );
  const destinationModuleService: DestinationModuleService = container.resolve(
    DESTINATION_MODULE
  );
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService = container.resolve(
    NOTIFICATION_TEMPLATE_MODULE
  );

  try {
    // Log the event data for debugging
    console.log("Hotel status changed event data:", JSON.stringify(data, null, 2));

    // Check if notifications should be sent for this event and channel
    const shouldSendEmail = await notificationTemplateService.shouldSendNotification(
      "hotel.status_changed",
      "email"
    );

    // If notifications are disabled, skip sending
    if (!shouldSendEmail) {
      console.log(`Email notifications for hotel.status_changed are not active, skipping`);
      return;
    }

    // If hotel is not provided in the event data, retrieve it
    const hotelData = hotel || await hotelModuleService.retrieveHotel(hotelId, {
      relations: ["destination"]
    });

    if (!hotelData) {
      console.log(`Hotel ${hotelId} not found, skipping email notification`);
      return;
    }

    // Use hotel name from event data if available, otherwise from retrieved data
    const name = hotelName || hotelData.name;

    // Get destination information
    let destinationData = null;
    if (hotelData.destination_id) {
      try {
        destinationData = await destinationModuleService.retrieveDestination(hotelData.destination_id);
      } catch (error) {
        console.error(`Error retrieving destination for hotel ${hotelId}:`, error);
      }
    }

    // Get the email template
    const emailTemplate = await notificationTemplateService.getTemplate(
     "hotel.status_changed",
      "email",
    );

    if (!emailTemplate) {
      console.log(`Email template for hotel.status_changed not found, skipping`);
      return;
    }

    // Get admin emails
    const adminEmails = await getAdminEmails(container);

    // Process each change and create a separate notification for each
    for (const change of changes) {
      let subject = `Updated: ${name}`;
      let changeDescription = "This hotel has been modified.";

      // Set subject and description based on change type
      if (change.type === 'active_status' && change.new_status !== undefined) {
        const statusText = change.new_status ? "Active" : "Inactive";
        subject = `Status Changed: ${name}`;
        changeDescription = `This hotel is now ${statusText}.`;
      }

      // Prepare data for template rendering
      const templateData = {
        hotel: {
          id: hotelData.id,
          name: name,
          description: hotelData.description,
          active: hotelData.is_active,
          address: hotelData.address,
          change_type: change.type,
          change_description: changeDescription,
          new_status: change.new_status
        },
        destination: destinationData ? {
          id: destinationData.id,
          name: destinationData.name
        } : {
          id: "unknown",
          name: "Unknown Destination"
        },
        frontendURL: process.env.MEDUSA_ADMIN_URL || "http://localhost:7001"
      };

      // Compile and render the template with Handlebars
      const compiledTemplate = Handlebars.compile(emailTemplate.content);
      const emailBody = compiledTemplate(templateData);

      // Send email to each admin
      for (const email of adminEmails) {
        try {
          await notificationModuleService.createNotifications({
            to: email,
            channel: "email",
            template: "hotel.status_changed",
            data: {
              subject: subject || emailTemplate.subject,
              html: emailBody,
            },
          });
          console.log(`Email sent successfully to ${email} for hotel ${hotelData.id} ${change.type} change`);
        } catch (error) {
          console.error(`Error sending email to ${email}:`, error);
        }
      }
    }

    if (changes.length === 0) {
      console.log(`No changes detected for hotel ${hotelId}, skipping notifications`);
    }
  } catch (error) {
    console.error(`Error sending email notification:`, error);
  }
}

export const config: SubscriberConfig = {
  event: "hotel.status_changed",
};
