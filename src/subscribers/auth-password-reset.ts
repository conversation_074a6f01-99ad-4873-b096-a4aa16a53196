import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { ICustomerModuleService, IUserModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { passwordReset } from "src/modules/my-notification/email/templates/auth-password-reset";

export default async function authPasswordResetHandler({
  event: { data },
  container,
}: SubscriberArgs<{ token: string; entity_id: string; actorType: string }>) {
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const userModuleService: IUserModuleService = container.resolve(Modules.USER);
  const customerModuleService: ICustomerModuleService = container.resolve(Modules.CUSTOMER);

  const user = data.entity_id === "user" ? await userModuleService.listUsers({email: data.entity_id}):  await customerModuleService.listCustomers({email: data.entity_id})

  const baseUrl =
    data.actorType === "user"
      ? process.env.MEDUSA_BACKEND_URL
      : process.env.MEDUSA_STOREFRONT_URL;
  const resetLink = `${baseUrl}/reset-password?email=${data.entity_id}&token=${data.token}`;

  // Replace placeholders in the email template
  const emailBody = passwordReset.body
    .replace("{{ user.resetLink }}", resetLink)
    .replace("{{ user.name }}", `${user[0].first_name} ${user[0].last_name}`)
    .replace("{{ frontendURL }}", `${process.env.MEDUSA_STOREFRONT_URL}`);

  try {
    await notificationModuleService.createNotifications({
      to: data.entity_id,
      channel: "email",
      template: "auth.password_reset",
      data: {
        subject: passwordReset.subject,
        html: emailBody,
      },
    });
    console.log(`Email sent successfully to ${data.entity_id}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "auth.password_reset",
};
