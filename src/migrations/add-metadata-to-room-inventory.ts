import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMetadataToRoomInventory1700000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the column already exists to avoid errors
    const hasMetadataColumn = await queryRunner.hasColumn("room_inventory", "metadata");
    
    if (!hasMetadataColumn) {
      await queryRunner.query(`
        ALTER TABLE "room_inventory" 
        ADD COLUMN "metadata" JSONB;
      `);
      
      console.log("Added metadata column to room_inventory table");
    } else {
      console.log("metadata column already exists in room_inventory table");
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const hasMetadataColumn = await queryRunner.hasColumn("room_inventory", "metadata");
    
    if (hasMetadataColumn) {
      await queryRunner.query(`
        ALTER TABLE "room_inventory" 
        DROP COLUMN "metadata";
      `);
      
      console.log("Removed metadata column from room_inventory table");
    }
  }
}
