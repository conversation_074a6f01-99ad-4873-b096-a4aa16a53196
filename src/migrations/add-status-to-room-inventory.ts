import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusToRoomInventory1715000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add status column to room_inventory table
    await queryRunner.query(
      `ALTER TABLE "room_inventory" ADD COLUMN IF NOT EXISTS "status" character varying DEFAULT 'available'`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove status column from room_inventory table
    await queryRunner.query(
      `ALTER TABLE "room_inventory" DROP COLUMN IF EXISTS "status"`
    );
  }
}
