import { Migration } from "@mikro-orm/migrations";

export class Migration1720000000000CreateWhatsappMessage extends Migration {
  async up(): Promise<void> {
    this.addSql(`
      create table if not exists "whatsapp_message" (
        "id" text not null,
        "whatsapp_message_id" text null,
        "direction" text not null default 'outbound',
        "status" text not null default 'pending',
        "message_type" text not null default 'text',
        "content" text not null,
        "order_id" text null,
        "customer_id" text null,
        "from_phone" text not null,
        "to_phone" text not null,
        "sent_at" timestamptz not null,
        "delivered_at" timestamptz null,
        "read_at" timestamptz null,
        "metadata" jsonb null,
        "template_name" text null,
        "template_language" text null,
        "template_params" jsonb null,
        "media_url" text null,
        "media_id" text null,
        "media_mime_type" text null,
        "retry_count" numeric not null default 0,
        "last_retry_at" timestamptz null,
        "error_message" text null,
        "created_at" timestamptz not null default now(),
        "updated_at" timestamptz not null default now(),
        "deleted_at" timestamptz null,
        constraint "whatsapp_message_pkey" primary key ("id")
      );
    `);

    // Create indexes
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_whatsapp_message_order" ON "whatsapp_message" ("order_id") WHERE deleted_at IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_whatsapp_message_customer" ON "whatsapp_message" ("customer_id") WHERE deleted_at IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_whatsapp_message_whatsapp_id" ON "whatsapp_message" ("whatsapp_message_id") WHERE deleted_at IS NULL;
    `);
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "whatsapp_message" cascade;');
  }
}
