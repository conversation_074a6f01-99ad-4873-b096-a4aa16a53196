import { z } from "zod";

export const PostAdminCreateNotificationTemplate = z.object({
  event_name: z.string(),
  channel: z.string(),
  subject: z.string().optional(),
  content: z.string(),
  is_default: z.boolean().optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export const PostAdminUpdateNotificationTemplate = z.object({
  id: z.string(),
  subject: z.string().optional(),
  content: z.string().optional(),
  is_default: z.boolean().optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export const PostAdminDeleteNotificationTemplate = z.object({
  id: z.string(),
});

export const PostAdminResetNotificationTemplate = z.object({
  id: z.string(),
});
