import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import { UpdateNotificationTemplateWorkflow } from "src/workflows/notification-template/update-notification-template";
import { DeleteNotificationTemplateWorkflow } from "src/workflows/notification-template/delete-notification-template";
import { z } from "zod";
import {
  PostAdminUpdateNotificationTemplate,
  PostAdminDeleteNotificationTemplate,
} from "../validators";

type PostAdminUpdateNotificationTemplateType = z.infer<
  typeof PostAdminUpdateNotificationTemplate
>;

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const notificationTemplateService: NotificationTemplateService = req.scope.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    const notification_template = await notificationTemplateService.retrieveNotificationTemplate(req.params.id);

    res.json({ notification_template });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve notification template",
    });
  }
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateNotificationTemplateType>,
  res: MedusaResponse
) => {
  try {
    // Ensure the ID in the path matches the ID in the body
    const id = req.params.id;
    const input = { ...req.body, id };

    const { result } = await UpdateNotificationTemplateWorkflow(req.scope).run({
      input,
    });

    res.json({ notification_template: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update notification template",
    });
  }
};

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    await DeleteNotificationTemplateWorkflow(req.scope).run({
      input: { id: req.params.id },
    });

    res.json({ success: true });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete notification template",
    });
  }
};
