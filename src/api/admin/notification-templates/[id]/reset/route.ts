import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ResetNotificationTemplateWorkflow } from "src/workflows/notification-template/reset-notification-template";
import { z } from "zod";
import { PostAdminResetNotificationTemplate } from "../../validators";

type PostAdminResetNotificationTemplateType = z.infer<
  typeof PostAdminResetNotificationTemplate
>;

export const POST = async (
  req: MedusaRequest<PostAdminResetNotificationTemplateType>,
  res: MedusaResponse
) => {
  try {
    // Ensure the ID in the path matches the ID in the body
    const id = req.params.id;

    const { result } = await ResetNotificationTemplateWorkflow(req.scope).run({
      input: { id },
    });

    res.json({ notification_template: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to reset notification template",
    });
  }
};
