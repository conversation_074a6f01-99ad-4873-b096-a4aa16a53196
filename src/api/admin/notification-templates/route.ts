import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import { CreateNotificationTemplateWorkflow } from "src/workflows/notification-template/create-notification-template";
import { z } from "zod";
import {
  PostAdminCreateNotificationTemplate,
  PostAdminDeleteNotificationTemplate,
} from "./validators";
import { DeleteNotificationTemplateWorkflow } from "src/workflows/notification-template/delete-notification-template";

type PostAdminCreateNotificationTemplateType = z.infer<
  typeof PostAdminCreateNotificationTemplate
>;
type PostAdminDeleteNotificationTemplateType = z.infer<
  typeof PostAdminDeleteNotificationTemplate
>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateNotificationTemplateType>,
  res: MedusaResponse
) => {
  try {
    const { result } = await CreateNotificationTemplateWorkflow(req.scope).run({
      input: req.body,
    });

    res.json({ notification_template: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create notification template",
    });
  }
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const notificationTemplateService: NotificationTemplateService = req.scope.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    const filters: any = {};

    // Apply filters if provided
    if (req.query.event_name) {
      filters.event_name = req.query.event_name;
    }

    if (req.query.channel) {
      filters.channel = req.query.channel;
    }

    if (req.query.is_default !== undefined) {
      filters.is_default = req.query.is_default === "true";
    }

    if (req.query.is_active !== undefined) {
      filters.is_active = req.query.is_active === "true";
    }

    const notification_templates = await notificationTemplateService.listNotificationTemplates(filters);

    res.json({ notification_templates });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to list notification templates",
    });
  }
};

export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteNotificationTemplateType>,
  res: MedusaResponse
) => {
  try {
    await DeleteNotificationTemplateWorkflow(req.scope).run({
      input: req.body,
    });

    res.json({ success: true });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete notification template",
    });
  }
};
