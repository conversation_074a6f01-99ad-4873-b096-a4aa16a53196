import { z } from "zod";

export const PostAdminCreateHotel = z.object({
  name: z.string(),
  handle: z.string(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  website: z.string().optional(),
  email: z.string().optional(),
  destination_id: z.string(),
  rating: z.number().optional(),
  total_reviews: z.number().optional(),
  notes: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  timezone: z.string().optional(),
  available_languages: z.array(z.string()).optional(),
  tax_type: z.string().optional(),
  tax_number: z.string().optional(),
  tags: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  rules: z.array(z.string()).optional(),
  safety_measures: z.array(z.string()).optional(),
  currency: z.string().optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),
  parent_category_id: z.string().optional(),
  is_featured: z.boolean().default(false),
  is_pets_allowed: z.boolean().default(false),
});

export const PostAdminUpdateHotel = z.object({
  id: z.string(),
  name: z.string().optional(),
  handle: z.string().optional(),
  description: z.string().optional(),
  is_active: z.boolean().optional(),
  website: z.string().optional(),
  email: z.string().optional(),
  destination_id: z.string().optional(),
  rating: z.number().optional(),
  total_reviews: z.number().optional(),
  notes: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  timezone: z.string().optional(),
  available_languages: z.array(z.string()).optional(),
  tax_type: z.string().optional(),
  tax_number: z.string().optional(),
  tags: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  rules: z.array(z.string()).optional(),
  safety_measures: z.array(z.string()).optional(),
  currency: z.string().optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),
  parent_category_id: z.string().optional(),
  is_featured: z.boolean().default(false),
  is_pets_allowed: z.boolean().optional(),
});

export const PostAdminDeleteHotel = z.object({
  ids: z.string(),
});
