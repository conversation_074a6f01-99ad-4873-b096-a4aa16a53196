import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { checkHotelAvailabilityWorkflow } from "src/workflows/hotel-management";

/**
 * Endpoint to check availability and calculate prices for all room configurations in a hotel
 * This endpoint uses the Medusa workflow system for better organization and reusability
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A list of available room configurations with their prices
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Extract query parameters
    const {
      check_in,
      check_out,
      adults = 1,
      children = 0,
      infants = 0,
      currency_code = "USD",
      country_code = "CH",
      province_code,
      include_price_tiers = false,
      include_unavailable = false,
      child_ages,
      sales_channel_id,
    } = req.query;
    console.log({hotelId})
    console.log(`Query parameters:`, {
      check_in,
      check_out,
      adults,
      children,
      infants,
      currency_code,
      country_code,
      province_code,
      include_price_tiers,
      include_unavailable,
      child_ages,
      sales_channel_id
    });

    // Parse child_ages from query parameter if it exists
    let parsedChildAges: Array<{ age: number | string }> = [];
    if (child_ages) {
      try {
        // If child_ages is a string, try to parse it as JSON
        if (typeof child_ages === 'string') {
          const parsed = JSON.parse(child_ages);
          if (Array.isArray(parsed)) {
            parsedChildAges = parsed.map(item => {
              if (typeof item === 'object' && item !== null && 'age' in item) {
                return { age: item.age };
              }
              return { age: String(item) };
            });
          }
        }
      } catch (error) {
        console.error("Error parsing child_ages:", error);
        // If parsing fails, keep as empty array
      }
    }

    // Run the workflow
    const { result } = await checkHotelAvailabilityWorkflow(req.scope)
      .run({
        input: {
          hotel_id: hotelId,
          check_in: check_in as string,
          check_out: check_out as string,
          adults: Number(adults),
          children: Number(children),
          infants: Number(infants),
          currency_code: currency_code as string,
          country_code: country_code as string,
          province_code: province_code as string,
          include_price_tiers: Boolean(include_price_tiers),
          include_unavailable: Boolean(include_unavailable),
          child_ages: parsedChildAges,
          sales_channel_id: sales_channel_id as string,
        },
      });

    // Return the workflow result
    return res.json(result);
  } catch (error) {
    console.error("Error checking hotel availability:", error);
    return res.status(500).json({
      message: "Error checking hotel availability",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
