import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

// Redirect to the seasonal endpoint
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  console.log('GET /admin/hotel-management/hotels/[id]/pricing/season - START');
  const hotelId = req.params.id;

  // Redirect to the seasonal endpoint
  const seasonalUrl = `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal`;
  console.log(`Redirecting from /season to /seasonal: ${seasonalUrl}`);

  try {
    // Forward the request to the seasonal endpoint
    console.log(`Fetching from ${req.protocol}://${req.get('host')}${seasonalUrl}`);
    const response = await fetch(`${req.protocol}://${req.get('host')}${seasonalUrl}`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    if (!response.ok) {
      console.error(`Error response from seasonal endpoint: ${response.status} ${response.statusText}`);
      return res.status(response.status).json({ message: `Error from seasonal endpoint: ${response.statusText}` });
    }

    const data = await response.json();
    console.log(`Received data from seasonal endpoint:`, data);

    // Return the response from the seasonal endpoint
    console.log('GET /admin/hotel-management/hotels/[id]/pricing/season - END');
    return res.json(data);
  } catch (error) {
    console.error('Error forwarding request to seasonal endpoint:', error);
    return res.status(500).json({ message: `Error forwarding request: ${error.message}` });
  }
};

// Forward POST requests as well
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const hotelId = req.params.id;

  // Redirect to the seasonal endpoint
  const seasonalUrl = `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal`;
  console.log(`Redirecting POST from /season to /seasonal: ${seasonalUrl}`);

  // Forward the request to the seasonal endpoint
  const response = await fetch(`${req.protocol}://${req.get('host')}${seasonalUrl}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      cookie: req.headers.cookie || '',
    },
    body: JSON.stringify(req.body),
  });

  const data = await response.json();

  // Return the response from the seasonal endpoint
  res.json(data);
};
