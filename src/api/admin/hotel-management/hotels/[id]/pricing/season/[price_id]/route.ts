import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

// Redirect to the seasonal endpoint
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const hotelId = req.params.id;
  const priceId = req.params.price_id;

  // Redirect to the seasonal endpoint
  const seasonalUrl = `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal/${priceId}`;
  console.log(`Redirecting from /season to /seasonal: ${seasonalUrl}`);

  // Forward the request to the seasonal endpoint
  const response = await fetch(`${req.protocol}://${req.get('host')}${seasonalUrl}`, {
    headers: {
      cookie: req.headers.cookie || '',
    },
  });

  const data = await response.json();

  // Return the response from the seasonal endpoint
  res.json(data);
};

// Forward PUT requests as well
export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  const hotelId = req.params.id;
  const priceId = req.params.price_id;

  // Redirect to the seasonal endpoint
  const seasonalUrl = `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal/${priceId}`;
  console.log(`Redirecting PUT from /season to /seasonal: ${seasonalUrl}`);

  // Forward the request to the seasonal endpoint
  const response = await fetch(`${req.protocol}://${req.get('host')}${seasonalUrl}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      cookie: req.headers.cookie || '',
    },
    body: JSON.stringify(req.body),
  });

  const data = await response.json();

  // Return the response from the seasonal endpoint
  res.json(data);
};

// Forward DELETE requests as well
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  const hotelId = req.params.id;
  const priceId = req.params.price_id;

  // Redirect to the seasonal endpoint
  const seasonalUrl = `/admin/hotel-management/hotels/${hotelId}/pricing/seasonal/${priceId}`;
  console.log(`Redirecting DELETE from /season to /seasonal: ${seasonalUrl}`);

  // Forward the request to the seasonal endpoint
  const response = await fetch(`${req.protocol}://${req.get('host')}${seasonalUrl}`, {
    method: 'DELETE',
    headers: {
      cookie: req.headers.cookie || '',
    },
  });

  const data = await response.json();

  // Return the response from the seasonal endpoint
  res.json(data);
};
