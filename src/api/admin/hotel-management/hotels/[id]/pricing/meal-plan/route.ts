import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import { MealPlanTypeEnum } from "src/modules/hotel-management/hotel-pricing/models/meal-plan";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Validation schema for meal plan
export const PostAdminHotelMealPlan = z.object({
  name: z.string(),
  type: z.string(), // Accept any string for type to allow custom meal plan types
  is_default: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminHotelMealPlanType = z.infer<typeof PostAdminHotelMealPlan>;

// GET endpoint to retrieve meal plans for a hotel
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get all meal plans for this hotel
    let mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    // If no meal plans exist, create default ones
    if (mealPlans.length === 0) {
      // Create default None meal plan
      const noneMealPlan = await hotelPricingService.createMealPlans({
        hotel_id: hotelId,
        name: "No Meals",
        type: MealPlanTypeEnum.NONE,
        is_default: true,
      });

      // Create default Bed & Breakfast meal plan
      const bbMealPlan = await hotelPricingService.createMealPlans({
        hotel_id: hotelId,
        name: "Bed & Breakfast",
        type: MealPlanTypeEnum.BED_BREAKFAST,
        is_default: false,
      });

      // Create default Half Board meal plan
      const hbMealPlan = await hotelPricingService.createMealPlans({
        hotel_id: hotelId,
        name: "Half Board",
        type: MealPlanTypeEnum.HALF_BOARD,
        is_default: false,
      });

      // Create default Full Board meal plan
      const fbMealPlan = await hotelPricingService.createMealPlans({
        hotel_id: hotelId,
        name: "Full Board",
        type: MealPlanTypeEnum.FULL_BOARD,
        is_default: false,
      });

      mealPlans = [noneMealPlan, bbMealPlan, hbMealPlan, fbMealPlan];
    }

    res.json({ meal_plans: mealPlans });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve meal plans",
    });
  }
};

// POST endpoint to create a new meal plan
export const POST = async (req: MedusaRequest<PostAdminHotelMealPlanType>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const {
      name,
      type,
      is_default,
      metadata,
    } = req.body;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Create a new meal plan
    const mealPlan = await hotelPricingService.createMealPlans({
      hotel_id: hotelId,
      name,
      type,
      is_default,
      metadata,
    });

    res.status(201).json({ meal_plan: mealPlan });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create meal plan",
    });
  }
};
