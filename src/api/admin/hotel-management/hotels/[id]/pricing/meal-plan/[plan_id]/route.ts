import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import { MealPlanTypeEnum } from "src/modules/hotel-management/hotel-pricing/models/meal-plan";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Validation schema for updating meal plan
export const PutAdminHotelMealPlan = z.object({
  name: z.string().optional(),
  type: z.nativeEnum(MealPlanTypeEnum).optional(),
  is_default: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PutAdminHotelMealPlanType = z.infer<typeof PutAdminHotelMealPlan>;

// GET endpoint to retrieve a specific meal plan
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const planId = req.params.plan_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get the meal plan
    const mealPlan = await hotelPricingService.retrieveMealPlan(planId);

    res.json({ meal_plan: mealPlan });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve meal plan",
    });
  }
};

// PUT endpoint to update a meal plan
export const PUT = async (req: MedusaRequest<PutAdminHotelMealPlanType>, res: MedusaResponse) => {
  try {
    const planId = req.params.plan_id;
    const {
      name,
      type,
      is_default,
      metadata,
    } = req.body;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Update the meal plan
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (type !== undefined) updateData.type = type;
    if (is_default !== undefined) updateData.is_default = is_default;
    if (metadata !== undefined) updateData.metadata = metadata;

    const mealPlan = await hotelPricingService.updateMealPlans({
      id: planId,
      ...updateData
    });

    res.json({ meal_plan: mealPlan });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update meal plan",
    });
  }
};

// DELETE endpoint to delete a meal plan
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const planId = req.params.plan_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Delete the meal plan
    await hotelPricingService.deleteMealPlans(planId);

    res.status(200).json({success: true });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete meal plan",
    });
  }
};
