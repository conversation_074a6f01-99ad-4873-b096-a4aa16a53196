import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import { OccupancyConfigTypeEnum } from "src/modules/hotel-management/hotel-pricing/models/occupancy-config";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Validation schema for updating occupancy config
export const PutAdminHotelOccupancyConfig = z.object({
  name: z.string().optional(),
  type: z.nativeEnum(OccupancyConfigTypeEnum).optional(),
  min_age: z.number().optional(),
  max_age: z.number().optional(),
  min_occupancy: z.number().optional(),
  max_occupancy: z.number().optional(),
  is_default: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PutAdminHotelOccupancyConfigType = z.infer<typeof PutAdminHotelOccupancyConfig>;

// GET endpoint to retrieve a specific occupancy config
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const configId = req.params.config_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get the occupancy config
    const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(configId);

    res.json({ occupancy_config: occupancyConfig });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve occupancy config",
    });
  }
};

// PUT endpoint to update an occupancy config
export const PUT = async (req: MedusaRequest<PutAdminHotelOccupancyConfigType>, res: MedusaResponse) => {
  try {
    const configId = req.params.config_id;
    const {
      name,
      type,
      min_age,
      max_age,
      min_occupancy,
      max_occupancy,
      is_default,
      metadata,
    } = req.body;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }


    const occupancyConfig = await hotelPricingService.updateOccupancyConfigs({
      id: configId,
      name,
      type,
      min_age,
      max_age,
      min_occupancy,
      max_occupancy,
      is_default,
      metadata,
    });

    res.json({ occupancy_config: occupancyConfig });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update occupancy config",
    });
  }
};

// DELETE endpoint to delete an occupancy config
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const configId = req.params.config_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Delete the occupancy config
    await hotelPricingService.deleteOccupancyConfigs(configId);

    res.status(200).json({success: true });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete occupancy config",
    });
  }
};
