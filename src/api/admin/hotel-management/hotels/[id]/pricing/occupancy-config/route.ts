import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import { OccupancyConfigTypeEnum } from "src/modules/hotel-management/hotel-pricing/models/occupancy-config";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Validation schema for occupancy config
export const PostAdminHotelOccupancyConfig = z.object({
  name: z.string(),
  type: z.nativeEnum(OccupancyConfigTypeEnum),
  min_age: z.number(),
  max_age: z.number(),
  min_occupancy: z.number().optional(),
  max_occupancy: z.number().optional(),
  is_default: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminHotelOccupancyConfigType = z.infer<typeof PostAdminHotelOccupancyConfig>;

// GET endpoint to retrieve occupancy configs for a hotel
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService  = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get all occupancy configs for this hotel
    let occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      
        hotel_id: hotelId,
     
    });

    
    // If no configs exist, create default ones
    if (occupancyConfigs.length === 0) {
      // Create default adult config
      const adultConfig = await hotelPricingService.createOccupancyConfigs({
        hotel_id: hotelId,
        name: "Adult",
        type: OccupancyConfigTypeEnum.EXTRA_ADULT,
        min_age: 18,
        max_age: 120,
        is_default: true,
      }, { entity: "OccupancyConfig" });

      // Create default child config
      const childConfig = await hotelPricingService.createOccupancyConfigs({
        hotel_id: hotelId,
        name: "Child",
        type: OccupancyConfigTypeEnum.CHILD,
        min_age: 2,
        max_age: 17,
        is_default: false,
      }, { entity: "OccupancyConfig" });

      // Create default infant config
      const infantConfig = await hotelPricingService.createOccupancyConfigs({
        hotel_id: hotelId,
        name: "Infant",
        type: OccupancyConfigTypeEnum.INFANT,
        min_age: 0,
        max_age: 1,
        is_default: false,
      }, { entity: "OccupancyConfig" });

      occupancyConfigs = [adultConfig, childConfig, infantConfig];
    }

    res.json({ occupancy_configs: occupancyConfigs });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve occupancy configs",
    });
  }
};

// POST endpoint to create a new occupancy config
export const POST = async (req: MedusaRequest<PostAdminHotelOccupancyConfigType>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const {
      name,
      type,
      min_age,
      max_age,
      min_occupancy,
      max_occupancy,
      is_default,
      metadata,
    } = req.body;

    // Get the hotel pricing service
    let hotelPricingService: any;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Create a new occupancy config
    const occupancyConfig = await hotelPricingService.createOccupancyConfigs({
      hotel_id: hotelId,
      name,
      type,
      min_age,
      max_age,
      min_occupancy,
      max_occupancy,
      is_default,
      metadata,
    }, { entity: "OccupancyConfig" });

    res.status(201).json({ occupancy_config: occupancyConfig });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create occupancy config",
    });
  }
};
