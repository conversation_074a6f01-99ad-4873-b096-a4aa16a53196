import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// GET endpoint to retrieve all pricing data for a hotel
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");

    // Get hotel details
    const { data: hotel } = await query.graph({
      entity: "product_category",
      filters: {
        id: hotelId,
      },
      fields: ["id", "name", "handle"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({ message: "Hotel not found" });
    }

    // Get all room configurations for this hotel
    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters: {
        categories: [hotelId],
        // Filter for products that have price_set_id in metadata (room configs)
        metadata: {
          price_set_id: { exists: true }
        }
      },
      fields: ["id", "title", "handle", "description", "metadata"],
    });

    // Get base prices
    const basePricesResponse = await fetch(`${req.protocol}://${req.get('host')}/admin/hotel-management/hotels/${hotelId}/pricing/base`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    const basePricesData = await basePricesResponse.json();

    // Get seasonal prices
    const seasonalPricesResponse = await fetch(`${req.protocol}://${req.get('host')}/admin/hotel-management/hotels/${hotelId}/pricing/seasonal`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    const seasonalPricesData = await seasonalPricesResponse.json();

    // Get pricing rules
    const pricingRulesResponse = await fetch(`${req.protocol}://${req.get('host')}/admin/hotel-management/hotels/${hotelId}/pricing/rules`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    const pricingRulesData = await pricingRulesResponse.json();

    // Get special offers
    const specialOffersResponse = await fetch(`${req.protocol}://${req.get('host')}/admin/hotel-management/hotels/${hotelId}/pricing/offers`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    const specialOffersData = await specialOffersResponse.json();

    res.json({
      hotel: hotel[0],
      room_configs: roomConfigs,
      base_prices: basePricesData.base_prices || [],
      seasonal_prices: seasonalPricesData.seasonal_prices || [],
      pricing_rules: pricingRulesData.pricing_rules || [],
      special_offers: specialOffersData.special_offers || [],
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve hotel pricing data",
    });
  }
};
