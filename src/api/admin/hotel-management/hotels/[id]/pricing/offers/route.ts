import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { MedusaError, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { createPromotionsWorkflow } from "@camped-ai/medusa/core-flows";

// Validation schema for special offers
export const PostAdminHotelSpecialOffer = z.object({
  code: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: z.enum(["percentage", "fixed", "free_night"]),
  value: z.number(),
  start_date: z.string(),
  end_date: z.string(),
  room_config_ids: z.array(z.string()),
  min_nights: z.number().optional(),
  max_nights: z.number().optional(),
  status: z.enum(["active", "draft"]).optional(),
});

export type PostAdminHotelSpecialOfferType = z.infer<typeof PostAdminHotelSpecialOffer>;

// GET endpoint to retrieve special offers for a hotel
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('GET /admin/hotel-management/hotels/[id]/pricing/offers - START');
    const hotelId = req.params.id;
    console.log(`Hotel ID: ${hotelId}`);

    // Get the promotion module service
    const { Modules } = await import("@camped-ai/framework/utils");
    const promotionService = req.scope.resolve(Modules.PROMOTION);

    console.log('Fetching promotions from database...');

    console.log('Fetching all promotions...');

    // Get all promotions
    const promotions = await promotionService.listPromotions(
      {}, // Get all promotions
      {
        relations: [
          "application_method",
          "rules",
          "rules.values",
        ],
      }
    );

    console.log(`Found ${promotions.length} total promotions`);

    // Log all promotions for debugging
    promotions.forEach((p, index) => {
      console.log(`Promotion ${index + 1}:`, {
        id: p.id,
        code: p.code,
        metadata: p.metadata,
        rules: p.rules?.map(r => ({
          id: r.id,
          attribute: r.attribute,
          operator: r.operator,
          values: r.values
        })),
      });

      // Check if the promotion has rules in the database
      if (p.rules && p.rules.length > 0) {
        console.log(`Promotion ${p.id} has ${p.rules.length} rules`);
      } else {
        console.log(`Promotion ${p.id} has no rules`);

        // Try to fetch rules directly
        console.log(`Attempting to fetch rules directly for promotion ${p.id}`);
        promotionService.listPromotionRules({
          promotion_id: p.id
        }).then(rules => {
          console.log(`Found ${rules.length} rules for promotion ${p.id}:`, rules);
        }).catch(error => {
          console.error(`Error fetching rules for promotion ${p.id}:`, error);
        });
      }
    });

    // Filter promotions to only include those for this hotel
    // First try to filter by metadata
    let hotelPromotions = [];

    console.log(`Filtering promotions for hotel ID: ${hotelId}`);

    // First, get all room configurations for this hotel
    console.log(`Fetching room configurations for hotel ${hotelId}`);

    // Use the already imported Modules
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get all room configurations for this hotel
    // We need to use a filter function since metadata is not directly filterable
    const allRoomConfigs = await productModuleService.listProducts(
      {},
      {
        select: ['id', 'title', 'metadata']
      }
    );

    // Filter room configs to only include those for this hotel
    const roomConfigs = allRoomConfigs.filter(product => {
      return product.metadata && product.metadata.hotel_id === hotelId;
    });

    console.log(`Found ${roomConfigs.length} room configurations for hotel ${hotelId}`);
    console.log('Room configuration IDs:', roomConfigs.map(rc => rc.id));

    // Check each promotion to see if it applies to any of the hotel's room configurations
    for (const promotion of promotions) {
      let belongsToHotel = false;

      // Check metadata first (for backward compatibility)
      if (promotion.metadata && typeof promotion.metadata === 'object') {
        console.log(`Promotion ${promotion.id} metadata:`, promotion.metadata);
        if (promotion.metadata.hotel_id === hotelId) {
          console.log(`Found hotel_id match in metadata for promotion ${promotion.id}`);
          belongsToHotel = true;
        }
      }

      // If not found in metadata, check if any of the promotion's product rules
      // match any of the hotel's room configuration IDs
      if (!belongsToHotel && promotion.rules && promotion.rules.length > 0) {
        console.log(`Checking rules for promotion ${promotion.id}`);

        // Look for product_id rules
        const productRules = promotion.rules.filter(rule => rule.attribute === "product_id");

        if (productRules.length > 0) {
          console.log(`Found ${productRules.length} product_id rules`);

          for (const rule of productRules) {
            console.log(`Rule: ${rule.attribute} ${rule.operator}`, rule.values);

            if (rule.values) {
              // Convert values to array if it's not already
              let ruleValues = [];

              if (Array.isArray(rule.values)) {
                // Extract values from rule.values objects if they have a value property
                ruleValues = rule.values.map(v => v.value || v);
              } else {
                ruleValues = [rule.values.value || rule.values];
              }

              console.log(`Rule values:`, ruleValues);

              // Check if any of the rule values match any of the hotel's room configuration IDs
              const matchingRoomConfigs = roomConfigs.filter(rc =>
                ruleValues.some(v => String(v) === rc.id)
              );

              if (matchingRoomConfigs.length > 0) {
                console.log(`Found ${matchingRoomConfigs.length} matching room configurations for promotion ${promotion.id}`);
                belongsToHotel = true;
                break;
              }
            }
          }
        }
      }

      if (belongsToHotel) {
        hotelPromotions.push(promotion);
      }
    }

    // If no promotions found, just return an empty array
    if (hotelPromotions.length === 0) {
      console.log('No promotions found for this hotel, returning empty array');
    }

    console.log(`Found ${hotelPromotions.length} special offers for hotel ${hotelId}`);

    // Transform promotions to special offers format
    const specialOffers = hotelPromotions.map(promotion => {
      console.log(`Transforming promotion ${promotion.id} to special offer format`);

      // Extract room_config_ids from rules if available
      let roomConfigIds = [];
      if (promotion.metadata && promotion.metadata.room_config_ids) {
        console.log(`Found room_config_ids in metadata:`, promotion.metadata.room_config_ids);
        roomConfigIds = promotion.metadata.room_config_ids;
      } else if (promotion.rules && promotion.rules.length > 0) {
        // Look for product_id rule
        console.log(`Looking for product_id rule in ${promotion.rules.length} rules`);
        const productRule = promotion.rules.find(rule => rule.attribute === "product_id");
        if (productRule && productRule.values) {
          console.log(`Found product_id rule with values:`, productRule.values);
          if (Array.isArray(productRule.values)) {
            roomConfigIds = productRule.values.map(v => v.value || v);
          } else {
            roomConfigIds = [productRule.values.value || productRule.values];
          }
          console.log(`Extracted room_config_ids:`, roomConfigIds);
        }
      }

      // Extract min_nights from rules if available
      let minNights = promotion.metadata?.min_nights;
      if (!minNights && promotion.rules && promotion.rules.length > 0) {
        console.log(`Looking for min_nights rule`);
        const minNightsRule = promotion.rules.find(rule => rule.attribute === "min_nights");
        if (minNightsRule && minNightsRule.values) {
          console.log(`Found min_nights rule with values:`, minNightsRule.values);
          if (Array.isArray(minNightsRule.values) && minNightsRule.values.length > 0) {
            const value = minNightsRule.values[0].value || minNightsRule.values[0];
            minNights = parseInt(String(value), 10);
            console.log(`Extracted min_nights:`, minNights);
          }
        }
      }

      // Extract max_nights from rules if available
      let maxNights = promotion.metadata?.max_nights;
      if (!maxNights && promotion.rules && promotion.rules.length > 0) {
        console.log(`Looking for max_nights rule`);
        const maxNightsRule = promotion.rules.find(rule => rule.attribute === "max_nights");
        if (maxNightsRule && maxNightsRule.values) {
          console.log(`Found max_nights rule with values:`, maxNightsRule.values);
          if (Array.isArray(maxNightsRule.values) && maxNightsRule.values.length > 0) {
            const value = maxNightsRule.values[0].value || maxNightsRule.values[0];
            maxNights = parseInt(String(value), 10);
            console.log(`Extracted max_nights:`, maxNights);
          }
        }
      }

      // Extract offer type from metadata or determine from application method
      let offerType = "percentage";
      if (promotion.metadata && promotion.metadata.offer_type) {
        offerType = promotion.metadata.offer_type;
      } else if (promotion.application_method) {
        offerType = promotion.application_method.type === "fixed" ? "fixed" : "percentage";
      }

      // Get value from application method
      const value = promotion.application_method?.value || 0;

      // Get dates
      const startDate = promotion.starts_at || new Date().toISOString();
      const endDate = promotion.ends_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

      // Create special offer object
      const specialOffer = {
        id: promotion.id,
        code: promotion.code,
        name: promotion.name || promotion.code,
        description: promotion.description || "",
        type: offerType,
        value: value,
        start_date: startDate,
        end_date: endDate,
        room_config_ids: roomConfigIds,
        min_nights: minNights,
        max_nights: maxNights,
        status: promotion.status || "active",
        created_at: promotion.created_at,
        updated_at: promotion.updated_at,
      };

      console.log(`Transformed special offer:`, specialOffer);
      return specialOffer;
    });

    console.log(`Returning ${specialOffers.length} special offers`);

    res.json({
      special_offers: specialOffers,
    });
  } catch (error) {
    console.error("Error fetching special offers:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve special offers",
    });
  }
};

// POST endpoint to add special offer
export const POST = async (req: MedusaRequest<PostAdminHotelSpecialOfferType>, res: MedusaResponse) => {
  try {
    const {
      code,
      name,
      description,
      type,
      value,
      start_date,
      end_date,
      room_config_ids,
      min_nights,
      max_nights,
      status = "active"
    } = req.body;

    // Verify that all room configurations exist
    const query = req.scope.resolve("query");
    const hotelId = req.params.id;

    console.log(`Validating room configurations for hotel ID: ${hotelId}`);
    console.log(`Room config IDs to validate: ${JSON.stringify(room_config_ids)}`);

    // First try to find room configs by ID without additional filters
    const { data: existingRoomConfigs } = await query.graph({
      entity: "product",
      filters: {
        id: room_config_ids
      },
      fields: ["id", "metadata", "title"],
    });

    console.log(`Found ${existingRoomConfigs.length} out of ${room_config_ids.length} room configurations`);

    // Log which room configs were found and which weren't
    const foundIds = existingRoomConfigs.map(config => config.id);
    const missingIds = room_config_ids.filter(id => !foundIds.includes(id));

    if (missingIds.length > 0) {
      console.log(`Missing room config IDs: ${JSON.stringify(missingIds)}`);

      // If we're using mock data, allow the request to proceed
      // Check if the IDs match the pattern of mock room configs
      const isMockData = missingIds.some(id => id.startsWith('prod_01JR') || id.startsWith('prod_mock'));

      if (isMockData) {
        console.log('Using mock room configuration data, proceeding with request');
      } else {
        return res.status(404).json({
          message: `One or more room configurations not found`,
          missing_ids: missingIds
        });
      }
    }

    // Get the promotion module service
    const { Modules } = await import("@camped-ai/framework/utils");
    const promotionService = req.scope.resolve(Modules.PROMOTION);

    // Map special offer type to promotion application method
    let applicationType = "percentage";
    let targetType = "items";
    let allocation = "each";

    if (type === "fixed") {
      applicationType = "fixed";
      targetType = "items";
      allocation = "each";
    } else if (type === "free_night") {
      applicationType = "fixed";
      targetType = "items";
      allocation = "each";
    }

    // Create a new promotion
    const promotionData = {
      code,
      name,
      description,
      type: "standard", // Medusa promotion type
      is_automatic: false,
      starts_at: new Date(start_date),
      ends_at: new Date(end_date),
      status,
      application_method: {
        type: applicationType,
        target_type: targetType,
        value: value,
        allocation: allocation,
        max_quantity: 1, // Maximum number of items the discount applies to
      },
      // Don't include rules in the initial creation
      metadata: {
        hotel_id: hotelId,
        offer_type: type,
        room_config_ids,
        min_nights,
        max_nights,
      },
    };

    // Prepare the promotion data with rules included
    const promotionWithRules = {
      ...promotionData,
      rules: [
        // Add a rule for room configurations
        {
          attribute: "product_id",
          operator: "in",
          values: room_config_ids,
        },
        // Add a rule for minimum nights if specified
        ...(min_nights ? [{
          attribute: "min_nights",
          operator: "gte",
          values: [min_nights.toString()],
        }] : []),
        // Add a rule for maximum nights if specified
        ...(max_nights ? [{
          attribute: "max_nights",
          operator: "lte",
          values: [max_nights.toString()],
        }] : []),
      ],
    };

    console.log('Creating promotion with workflow:', promotionWithRules);

    // Use the workflow to create the promotion with rules
    const { result } = await createPromotionsWorkflow(req.scope)
      .run({
        input: {
          promotionsData: [promotionWithRules],
          additional_data: {
            hotel_id: hotelId,
          },
        },
      });

    if (!result || !result.length) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        "Failed to create promotion"
      );
    }

    // Get the created promotion
    const promotion = result[0];
    const promotionId = promotion.id;

    console.log(`Promotion created with ID: ${promotionId}`);
    console.log('Created promotion:', promotion);

    // Fetch the promotion with rules to verify they were created
    const createdPromotion = await promotionService.retrievePromotion(promotionId, {
      relations: [
        "application_method",
        "rules",
        "rules.values",
      ],
    });

    console.log('Created promotion with rules:', {
      id: createdPromotion.id,
      code: createdPromotion.code,
      rules: createdPromotion.rules?.map(r => ({
        id: r.id,
        attribute: r.attribute,
        operator: r.operator,
        values: r.values
      })),
    });

    console.log('Promotion created successfully:', promotion);

    // Transform promotion to special offer format
    const specialOffer = {
      id: promotion.id,
      code: promotion.code,
      name: promotion.name || promotion.code,
      description: promotion.description || "",
      type,
      value,
      start_date,
      end_date,
      room_config_ids,
      min_nights,
      max_nights,
      status: promotion.status || "active",
      created_at: promotion.created_at,
      updated_at: promotion.updated_at,
    };

    res.json({
      special_offer: specialOffer,
    });
  } catch (error) {
    console.error("Error creating special offer:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to add special offer",
    });
  }
};
