import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    console.log(`Redirecting to /admin/direct-rooms?hotel_id=${hotelId}`);

    // Forward the request to the direct-rooms endpoint
    const response = await fetch(`${req.protocol}://${req.get('host')}/admin/direct-rooms?hotel_id=${hotelId}`, {
      headers: {
        cookie: req.headers.cookie || '',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch from direct-rooms: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Return the response from the direct-rooms endpoint
    res.json(data);
  } catch (error) {
    console.error("Error redirecting to direct-rooms:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to fetch rooms for hotel",
    });
  }
};
