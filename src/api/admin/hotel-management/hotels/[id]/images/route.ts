import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const hotelId = req.params.id

  try {
    const hotelService = req.scope.resolve(HOTEL_MODULE)
    
    // Fetch images for the specific hotel
    const images = await hotelService.getHotelImages(hotelId)
    
    res.status(200).json({ 
      images: images.map(image => ({
        id: image.id,
        url: image.url,
        isThumbnail: image.isThumbnail
      }))
    })
  } catch (error) {
    console.error("Error fetching hotel images:", error)
    res.status(500).json({ 
      message: "Failed to fetch hotel images",
      error: error instanceof Error ? error.message : "Unknown error"
    })
  }
}
