// src/api/admin/hotel-management/hotels/[id]/upload/route.ts
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { MedusaError } from "@camped-ai/framework/utils"
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const hotelId = req.params.id
   //@ts-ignore
  const files = req.files as Express.Multer.File[]

  if (!files?.length) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files were uploaded"
    )
  }

  const hotelService = req.scope.resolve(HOTEL_MODULE)
  const hotelImages = await hotelService.uploadImages(hotelId, files, req.scope)
  
  res.status(200).json({ hotel_images: hotelImages })
}