import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const hotelId = req.params.id
  //@ts-ignore
  const { image_id } = req.body
  
  const hotelService = req.scope.resolve(HOTEL_MODULE)
  const thumbnailImage = await hotelService.setHotelThumbnail(hotelId, image_id)
  
  res.status(200).json({ thumbnail: thumbnailImage })
}