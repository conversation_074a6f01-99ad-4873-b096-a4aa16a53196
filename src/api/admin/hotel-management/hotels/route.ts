import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateHotel,
  PostAdminDeleteHotel,
  PostAdminUpdateHotel,
} from "./validators";
import { CreateHotelWorkflow } from "src/workflows/hotel-management/hotel/create-hotel";
import { UpdateHotelWorkflow } from "src/workflows/hotel-management/hotel/update-hotel";
import { DeleteHotelWorkflow } from "src/workflows/hotel-management/hotel/delete-hotel";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";

type PostAdminCreateHotelType = z.infer<typeof PostAdminCreateHotel>;
type PostAdminDeleteHotelType = z.infer<typeof PostAdminDeleteHotel>;
type PostAdminUpdateHotelType = z.infer<typeof PostAdminUpdateHotel>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateHotelType>,
  res: MedusaResponse
) => {
  const { result } = await CreateHotelWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });
  res.json({ hotel: result });
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateHotelType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateHotelWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });
  res.json({ hotel: result });
};
export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteHotelType>,
  res: MedusaResponse
) => {
  const { result } = await DeleteHotelWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ hotel: result });
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);


  const { limit = 20, offset = 0, is_featured } = req.query || {};
  const filters: Record<string, any> = {};

  if (is_featured !== undefined) {
    filters.is_featured = is_featured;
  }
  const {
    data: hotels,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "hotel",
    fields: [
      "*", "roomConfigs.id", "roomConfigs.name", "images.*",
    ],
    filters,
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });

  res.json({
    hotels,
    count,
    limit: take,
    offset: skip,
  });
};
