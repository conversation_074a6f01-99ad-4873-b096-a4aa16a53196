import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const imageId = req.params.id;

  const hotelService = req.scope.resolve(HOTEL_MODULE);
  const result = await hotelService.deleteHotelImage(imageId);

  res.status(200).json({ result });
}
