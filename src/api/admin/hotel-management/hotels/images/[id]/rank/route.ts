import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const imageId = req.params.id;
  //@ts-ignore
  const { rank } = req.body;

  const hotelService = req.scope.resolve(HOTEL_MODULE);
  const updatedImage = await hotelService.updateHotelImageRank(imageId, rank);

  res.status(200).json({ hotel_image: updatedImage });
}
