import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import linkPriceSetToRoomConfigWorkflow from "../../../../../workflows/hotel-management/room/link-price-set-to-room-config";

// Validation schema for bulk linking price sets
export const PostAdminBulkLinkPriceSets = z.object({
  currency_code: z.string().default("USD"),
  base_price: z.number().optional(),
  hotel_id: z.string().optional(), // Optional: only process room configs for specific hotel
});

export type PostAdminBulkLinkPriceSetsType = z.infer<typeof PostAdminBulkLinkPriceSets>;

/**
 * POST /admin/hotel-management/room-configs/bulk-link-price-sets
 *
 * Create price sets and link them to all room configurations that don't have them
 */
export const POST = async (
  req: MedusaRequest<PostAdminBulkLinkPriceSetsType>,
  res: MedusaResponse
) => {
  try {
    const { currency_code, base_price, hotel_id } = req.body;

    console.log(`Bulk linking price sets to room configurations`);
    console.log(`Currency: ${currency_code}, Base price: ${base_price || 'default'}`);
    if (hotel_id) {
      console.log(`Hotel filter: ${hotel_id}`);
    }

    const query = req.scope.resolve("query");

    // Get all room configurations (products) that don't have price_set_id in metadata
    const filters: any = {
      metadata: {
        hotel_id: { exists: true }, // Ensure it's a room configuration
        price_set_id: { exists: false }, // Only get those without price_set_id
      },
    };

    // Add hotel filter if specified
    if (hotel_id) {
      filters.metadata.hotel_id = hotel_id;
    }

    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters,
      fields: ["id", "title", "metadata"],
    });

    console.log(`Found ${roomConfigs?.length || 0} room configurations without price sets`);

    if (!roomConfigs || roomConfigs.length === 0) {
      return res.json({
        success: true,
        message: "No room configurations found that need price sets",
        processed: 0,
        results: [],
      });
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Process each room configuration
    for (const roomConfig of roomConfigs) {
      try {
        console.log(`Processing room config: ${roomConfig.id} - ${roomConfig.title}`);

        // Run the workflow to create and link the price set
        const { result } = await linkPriceSetToRoomConfigWorkflow(req.scope).run({
          input: {
            room_config_id: roomConfig.id,
            currency_code,
            base_price,
          },
        });

        results.push({
          room_config_id: roomConfig.id,
          room_config_name: roomConfig.title,
          success: true,
          price_set_id: result.price_set_id,
          message: result.message,
        });

        successCount++;
        console.log(`✅ Successfully linked price set to ${roomConfig.title}`);
      } catch (error) {
        console.error(`❌ Error processing room config ${roomConfig.id}:`, error);
        
        results.push({
          room_config_id: roomConfig.id,
          room_config_name: roomConfig.title,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });

        errorCount++;
      }
    }

    console.log(`Bulk operation completed: ${successCount} success, ${errorCount} errors`);

    res.json({
      success: true,
      message: `Processed ${roomConfigs.length} room configurations`,
      processed: roomConfigs.length,
      success_count: successCount,
      error_count: errorCount,
      results,
    });
  } catch (error) {
    console.error("Error in bulk linking price sets:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to bulk link price sets",
    });
  }
};
