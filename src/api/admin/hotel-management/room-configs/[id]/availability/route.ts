import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ROOM_INVENTORY_SERVICE } from "../../../../../../modules/hotel-management/room-inventory";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;
    const { check_in, check_out } = req.query;

    if (!id) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    if (!check_in || !check_out) {
      return res.status(400).json({ message: "Check-in and check-out dates are required" });
    }

    // Get the room inventory service
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);

    // Check availability for the room configuration
    const availability = await roomInventoryService.checkConfigurationAvailability(
      id,
      new Date(check_in as string),
      new Date(check_out as string),
      true // Include details
    );

    res.json(availability);
  } catch (error) {
    console.error("Error checking room configuration availability:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to check room configuration availability",
    });
  }
};
