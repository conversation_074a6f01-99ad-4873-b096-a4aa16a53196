import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";

/**
 * GET /admin/hotel-management/room-configs/:id/seasonal-pricing
 *
 * Get seasonal pricing rules for a room configuration
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`Fetching seasonal pricing for room config: ${roomConfigId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this room configuration
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Get all seasonal overrides for these base price rules
    const basePriceRuleIds = basePriceRules.map(rule => rule.id);
    
    // Group seasonal overrides by name, start_date, and end_date
    const seasonalPrices = [];
    const seasonalGroups = new Map();

    console.log({basePriceRules})

    // For each base price rule, get its seasonal overrides
    for (const basePriceRule of basePriceRules) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      console.log({seasonalOverrides})

      // Group seasonal overrides by name, start_date, and end_date
      for (const override of seasonalOverrides) {
        const key = `${override.description || 'Unnamed'}_${override.start_date}_${override.end_date}`;
        
        if (!seasonalGroups.has(key)) {
          seasonalGroups.set(key, {
            id: override.id, // Use the first override's ID as the group ID
            name: override.description || 'Unnamed',
            start_date: override.start_date,
            end_date: override.end_date,
            weekday_rules: [],
          });
        }
        
        // Get the occupancy type and meal plan from the base price rule
        const occupancyTypeId = basePriceRule.occupancy_type_id;
        const mealPlanId = basePriceRule.meal_plan_id;
        
        // Create weekday prices based on the override amount
        // For simplicity, we'll use the same amount for all days
        const weekdayPrices = {
          mon: override.metadata?.weekday_prices?.mon || override.amount,
          tue: override.metadata?.weekday_prices?.tue ||override.amount,
          wed: override.metadata?.weekday_prices?.wed ||override.amount,
          thu: override.metadata?.weekday_prices?.thu ||override.amount,
          fri: override.metadata?.weekday_prices?.fri ||override.amount,
          sat: override.metadata?.weekday_prices?.sat || override.amount,
          sun: override.metadata?.weekday_prices?.sun || override.amount,
        };

        
        // Add this rule to the group
        seasonalGroups.get(key).weekday_rules.push({
          id: override.id,
          occupancy_type_id: occupancyTypeId,
          meal_plan_id: mealPlanId,
          weekday_prices: weekdayPrices,
          currency_code: override.currency_code || basePriceRule.currency_code,
          priority: override.priority
        });
      }
    }
    
    // Convert the map to an array
    seasonalGroups.forEach(group => {
      seasonalPrices.push(group);
    });

    console.log("before end",seasonalPrices )
    // Return the seasonal prices
    res.json({
      seasonal_prices: seasonalPrices,
    });
  } catch (error) {
    console.error("Error fetching seasonal pricing:", error);
    res.status(500).json({
      message: "An error occurred while fetching seasonal pricing",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
