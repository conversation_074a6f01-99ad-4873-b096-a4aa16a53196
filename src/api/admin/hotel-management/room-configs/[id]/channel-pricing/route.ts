import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";

// Validation schema for channel pricing
export const PostAdminChannelPricing = z.object({
  currency_code: z.string(),
  channel_price_rules: z.array(
    z.object({
      sales_channel_id: z.string(),
      occupancy_type_id: z.string(),
      meal_plan_id: z.string(),
      amount: z.number(),
      metadata: z.object({}).passthrough().optional(),
    })
  ),
});

export type PostAdminChannelPricingType = z.infer<typeof PostAdminChannelPricing>;

/**
 * GET /admin/hotel-management/room-configs/:id/channel-pricing
 *
 * Get channel pricing rules for a room configuration
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`Fetching channel pricing for room config: ${roomConfigId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this room configuration
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Get all channel price overrides for these base price rules
    const basePriceRuleIds = basePriceRules.map(rule => rule.id);
    
    // Group channel overrides by sales channel ID
    const channelPrices = [];
    const channelGroups = new Map();

    // For each base price rule, get its channel overrides
    for (const basePriceRule of basePriceRules) {
      const channelOverrides = await hotelPricingService.listChannelPriceOverrides({
        base_price_rule_id: basePriceRule.id,
      });

      // Group channel overrides by sales channel ID
      for (const override of channelOverrides) {
        const key = override.sales_channel_id;
        
        if (!channelGroups.has(key)) {
          channelGroups.set(key, []);
        }
        
        // Add this override to the group
        channelGroups.get(key).push({
          id: override.id,
          base_price_rule_id: basePriceRule.id,
          occupancy_type_id: basePriceRule.occupancy_type_id,
          meal_plan_id: basePriceRule.meal_plan_id,
          sales_channel_id: override.sales_channel_id,
          amount: override.amount,
          currency_code: override.currency_code || basePriceRule.currency_code,
          metadata: override.metadata,
        });
      }
    }
    
    // Convert the map to an array
    channelGroups.forEach((rules, salesChannelId) => {
      channelPrices.push({
        sales_channel_id: salesChannelId,
        rules,
      });
    });

    // Get sales channels
    const salesChannelService = req.scope.resolve(Modules.SALES_CHANNEL);
    const salesChannels = await salesChannelService.listSalesChannels();

    // Return the channel prices
    res.json({
      channel_prices: channelPrices,
      sales_channels: salesChannels,
    });
  } catch (error) {
    console.error("Error fetching channel pricing:", error);
    res.status(500).json({
      message: "An error occurred while fetching channel pricing",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * POST /admin/hotel-management/room-configs/:id/channel-pricing
 *
 * Create or update channel pricing rules for a room configuration
 */
export const POST = async (req: MedusaRequest<PostAdminChannelPricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Validate the request body
    const { currency_code, channel_price_rules } = req.body;

    console.log(`Creating/updating channel pricing for room config: ${roomConfigId}`);
    console.log(`Rules: ${channel_price_rules.length}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this room configuration
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Create or update channel price overrides
    const createdRules = [];

    for (const rule of channel_price_rules) {
      try {
        // Find the base price rule for this occupancy type and meal plan
        const basePriceRule = basePriceRules.find(
          (bpr) => 
            bpr.occupancy_type_id === rule.occupancy_type_id && 
            bpr.meal_plan_id === rule.meal_plan_id
        );

        if (!basePriceRule) {
          console.warn(`No base price rule found for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id}`);
          continue;
        }

        // Check if there's an existing channel override
        const existingOverrides = await hotelPricingService.listChannelPriceOverrides({
          base_price_rule_id: basePriceRule.id,
          sales_channel_id: rule.sales_channel_id,
        });

        let channelOverride;

        if (existingOverrides.length > 0) {
          // Update the existing override
          const existingOverride = existingOverrides[0];
          
          // Only update if there are changes
          if (existingOverride.amount !== rule.amount) {
            channelOverride = await hotelPricingService.updateChannelPriceOverride(existingOverride.id, {
              amount: rule.amount,
              metadata: rule.metadata,
            });
          } else {
            channelOverride = existingOverride;
          }
        } else {
          // Create a new channel override
          channelOverride = await hotelPricingService.createChannelPriceOverride({
            base_price_rule_id: basePriceRule.id,
            sales_channel_id: rule.sales_channel_id,
            amount: rule.amount,
            currency_code,
            metadata: rule.metadata,
          });
        }

        createdRules.push({
          id: channelOverride.id,
          base_price_rule_id: basePriceRule.id,
          occupancy_type_id: rule.occupancy_type_id,
          meal_plan_id: rule.meal_plan_id,
          sales_channel_id: rule.sales_channel_id,
          amount: rule.amount,
          currency_code,
          metadata: rule.metadata,
        });
      } catch (error) {
        console.error(`Error creating/updating channel override:`, error);
        // Continue with other rules
      }
    }

    // Return the created rules
    res.status(200).json({
      channel_price_rules: createdRules,
    });
  } catch (error) {
    console.error("Error processing request:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create channel pricing rules",
    });
  }
};
