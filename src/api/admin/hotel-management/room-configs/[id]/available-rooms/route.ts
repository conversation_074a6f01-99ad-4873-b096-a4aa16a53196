import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ROOM_INVENTORY_SERVICE } from "../../../../../../modules/hotel-management/room-inventory";
import { Modules } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;
    const { check_in, check_out } = req.query;

    if (!id) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    if (!check_in || !check_out) {
      return res.status(400).json({ message: "Check-in and check-out dates are required" });
    }

    // Get the room inventory service
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);
    const productVariantService = req.scope.resolve(Modules.PRODUCT_VARIANT);

    // Check availability for the room configuration
    const availability = await roomInventoryService.checkConfigurationAvailability(
      id,
      new Date(check_in as string),
      new Date(check_out as string),
      true // Include details
    );

    if (!availability.available) {
      return res.json({ rooms: [] });
    }

    // Get detailed information about available rooms
    const availableRoomIds = availability.availableRooms.map(room => room.id);
    const rooms = await productVariantService.list(
      { id: availableRoomIds },
      { 
        select: ["id", "title", "sku", "metadata", "product_id"],
        relations: ["product"]
      }
    );

    // Map to a more user-friendly format
    const formattedRooms = rooms.map(room => ({
      id: room.id,
      title: room.title,
      room_number: room.metadata?.room_number || "",
      floor: room.metadata?.floor || "",
      status: "available",
      product_id: room.product_id,
      room_config_id: id
    }));

    res.json({ rooms: formattedRooms });
  } catch (error) {
    console.error("Error getting available rooms:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to get available rooms",
    });
  }
};
