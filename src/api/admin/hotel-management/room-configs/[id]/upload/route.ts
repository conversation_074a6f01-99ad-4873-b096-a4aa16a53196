import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError, Modules } from "@camped-ai/framework/utils";
import { uploadFilesWorkflow } from "@camped-ai/medusa/core-flows";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const roomConfigId = req.params.id;
  //@ts-ignore
  const files = req.files as Express.Multer.File[];

  if (!files?.length) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files were uploaded"
    );
  }

  try {
    // First, upload the files using the file module
    console.log("Uploading files...");
    const { result } = await uploadFilesWorkflow(req.scope).run({
      input: {
        files: files.map((f) => ({
          filename: f.originalname,
          mimeType: f.mimetype,
          content: f.buffer.toString("binary"),
          access: "public",
        })),
      },
    });

    console.log("Upload result:", result);

    // Get the product service to update the product images
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Get the product first
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["images"],
    });

    // Create image records for each uploaded file
    const imageIds = [];
    for (const file of result) {
      // Update the product with the new image
      const updatedProduct = await productService.updateProducts(roomConfigId, {
        images: [
          ...product.images,
          {
            url: file.url,
            metadata: {
              fileKey: file.url.split("/").pop(),
              isThumbnail: false,
            },
          },
        ],
      });

      // Get the newly added image
      const newImage = updatedProduct.images[updatedProduct.images.length - 1];
      imageIds.push(newImage.id);
    }

    // Get the updated product with images
    const updatedProduct = await productService.retrieveProduct(roomConfigId, {
      relations: ["images"],
    });

    // Filter the images to only include the ones we just created
    const newImages = updatedProduct.images.filter((img) =>
      imageIds.includes(img.id)
    );

    // Format the response
    const formattedImages = newImages.map((image) => ({
      id: image.id,
      url: image.url,
      created_at: image.created_at,
      updated_at: image.updated_at,
      deleted_at: image.deleted_at,
      metadata: image.metadata,
      isThumbnail: image.metadata?.isThumbnail || false,
    }));

    res.status(200).json({ images: formattedImages });
  } catch (error) {
    console.error("Error uploading room config images:", error);
    res.status(500).json({
      message: "Failed to upload room config images",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
