import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import linkPriceSetToRoomConfigWorkflow from "../../../../../../workflows/hotel-management/room/link-price-set-to-room-config";

// Validation schema for linking price set
export const PostAdminLinkPriceSet = z.object({
  currency_code: z.string().default("USD"),
  base_price: z.number().optional(),
});

export type PostAdminLinkPriceSetType = z.infer<typeof PostAdminLinkPriceSet>;

/**
 * POST /admin/hotel-management/room-configs/:id/link-price-set
 *
 * Create a price set and link it to a room configuration
 */
export const POST = async (
  req: MedusaRequest<PostAdminLinkPriceSetType>,
  res: MedusaResponse
) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    const { currency_code, base_price } = req.body;

    console.log(`Linking price set to room config: ${roomConfigId}`);
    console.log(`Currency: ${currency_code}, Base price: ${base_price || 'default'}`);

    // Run the workflow to create and link the price set
    const { result } = await linkPriceSetToRoomConfigWorkflow(req.scope).run({
      input: {
        room_config_id: roomConfigId,
        currency_code,
        base_price,
      },
    });

    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error linking price set to room configuration:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to link price set",
    });
  }
};
