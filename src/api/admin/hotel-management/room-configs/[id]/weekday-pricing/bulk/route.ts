import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";

// Validation schema for bulk weekday pricing
export const PostAdminBulkWeekdayPricing = z.object({
  currency_code: z.string(),
  weekday_rules: z.array(
    z.object({
      occupancy_type_id: z.string(),
      meal_plan_id: z.string(),
      weekday_prices: z.object({
        mon: z.number(),
        tue: z.number(),
        wed: z.number(),
        thu: z.number(),
        fri: z.number(),
        sat: z.number(),
        sun: z.number(),
      }),
    })
  ),
});

export type PostAdminBulkWeekdayPricingType = z.infer<typeof PostAdminBulkWeekdayPricing>;

/**
 * POST /admin/hotel-management/room-configs/:id/weekday-pricing/bulk
 *
 * Bulk create or update weekday pricing rules for a room configuration
 */
export const POST = async (req: MedusaRequest<PostAdminBulkWeekdayPricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Validate the request body
    const { currency_code, weekday_rules } = req.body;

    console.log(`Bulk upserting ${weekday_rules.length} weekday pricing rules for room config: ${roomConfigId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Extract the hotel ID from the room config
    const productService = req.scope.resolve(Modules.PRODUCT);
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["categories"],
    });

    // Ensure hotelId is a string
    const hotelId = String(product.metadata?.hotel_id || "hotel_default");

    // Create the new base price rules
    const createdRules = [];

    try {
      // Get all existing base price rules for this room configuration
      const existingRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigId,
      });

      // Create a map of existing rules by occupancy_type_id and meal_plan_id
      const existingRulesMap = new Map();
      for (const rule of existingRules) {
        const key = `${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}`;
        existingRulesMap.set(key, rule);
      }

      console.log(`Found ${existingRules.length} existing base price rules for room config: ${roomConfigId}`);

      // Process each rule in the request
      for (const rule of weekday_rules) {
        try {
          // Set amount to Monday's price (required field)
          const amount = rule.weekday_prices.mon;

          // Create a key to look up existing rules
          const key = `${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}`;
          const existingRule = existingRulesMap.get(key);

          if (existingRule) {
            // Update existing rule
            console.log(`Updating existing rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id || 'null'}`);

            // Check if there are actual changes to avoid unnecessary updates
            const hasChanges =
              existingRule.amount !== amount ||
              existingRule.monday_price !== rule.weekday_prices.mon ||
              existingRule.tuesday_price !== rule.weekday_prices.tue ||
              existingRule.wednesday_price !== rule.weekday_prices.wed ||
              existingRule.thursday_price !== rule.weekday_prices.thu ||
              existingRule.friday_price !== rule.weekday_prices.fri ||
              existingRule.saturday_price !== rule.weekday_prices.sat ||
              existingRule.sunday_price !== rule.weekday_prices.sun ||
              existingRule.currency_code !== currency_code;

            if (hasChanges) {
              // Create a history entry for this update
              const historyEntry = {
                timestamp: new Date().toISOString(),
                previous_values: {
                  amount: existingRule.amount,
                  monday_price: existingRule.monday_price,
                  tuesday_price: existingRule.tuesday_price,
                  wednesday_price: existingRule.wednesday_price,
                  thursday_price: existingRule.thursday_price,
                  friday_price: existingRule.friday_price,
                  saturday_price: existingRule.saturday_price,
                  sunday_price: existingRule.sunday_price,
                  currency_code: existingRule.currency_code
                },
                new_values: {
                  amount: amount,
                  monday_price: rule.weekday_prices.mon,
                  tuesday_price: rule.weekday_prices.tue,
                  wednesday_price: rule.weekday_prices.wed,
                  thursday_price: rule.weekday_prices.thu,
                  friday_price: rule.weekday_prices.fri,
                  saturday_price: rule.weekday_prices.sat,
                  sunday_price: rule.weekday_prices.sun,
                  currency_code
                }
              };

              // Get existing history or create a new array
              const existingHistory = existingRule.metadata?.history || [];

              // Update the rule with new values
              const updatedRule = await hotelPricingService.updateBasePriceRules([{
                id: existingRule.id,
                amount: amount,
                monday_price: rule.weekday_prices.mon,
                tuesday_price: rule.weekday_prices.tue,
                wednesday_price: rule.weekday_prices.wed,
                thursday_price: rule.weekday_prices.thu,
                friday_price: rule.weekday_prices.fri,
                saturday_price: rule.weekday_prices.sat,
                sunday_price: rule.weekday_prices.sun,
                currency_code,
                metadata: {
                  ...(existingRule.metadata || {}),
                  last_updated: new Date().toISOString(),
                  history: [...existingHistory, historyEntry]
                }
              }]);

              createdRules.push(updatedRule[0]);
            } else {
              console.log(`No changes detected for rule ${existingRule.id}, skipping update`);
              createdRules.push(existingRule);
            }
          } else {
            // Create new rule
            console.log(`Creating new rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id || 'null'}`);

            // Create a new base price rule
            const newRule = {
              room_config_id: roomConfigId,
              hotel_id: hotelId,
              occupancy_type_id: rule.occupancy_type_id,
              meal_plan_id: rule.meal_plan_id,
              amount: amount, // Required field
              monday_price: rule.weekday_prices.mon,
              tuesday_price: rule.weekday_prices.tue,
              wednesday_price: rule.weekday_prices.wed,
              thursday_price: rule.weekday_prices.thu,
              friday_price: rule.weekday_prices.fri,
              saturday_price: rule.weekday_prices.sat,
              sunday_price: rule.weekday_prices.sun,
              currency_code,
              min_occupancy: 1, // Default value
              max_occupancy: null, // No upper limit
              metadata: {
                created_at: new Date().toISOString(),
                history: [{
                  timestamp: new Date().toISOString(),
                  action: "created",
                  values: {
                    amount: amount,
                    monday_price: rule.weekday_prices.mon,
                    tuesday_price: rule.weekday_prices.tue,
                    wednesday_price: rule.weekday_prices.wed,
                    thursday_price: rule.weekday_prices.thu,
                    friday_price: rule.weekday_prices.fri,
                    saturday_price: rule.weekday_prices.sat,
                    sunday_price: rule.weekday_prices.sun,
                    currency_code
                  }
                }]
              }
            };

            // The method might expect an array or have different signature
            // Try passing the rule directly
            const createdRule = await hotelPricingService.createBasePriceRules(newRule);

            createdRules.push(createdRule);
          }
        } catch (error) {
          console.error(`Error processing base price rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id}:`, error);
          // Continue with other rules
        }
      }
    } catch (error) {
      console.error("Error in bulk create operation:", error);
      return res.status(500).json({
        message: "An error occurred while bulk upserting weekday pricing rules",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Format the response
    const formattedRules = await Promise.all(createdRules.map(async (rule) => {
      // Get the occupancy type
      const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(rule.occupancy_type_id);

      // Get the meal plan
      const mealPlan = rule.meal_plan_id ? await hotelPricingService.retrieveMealPlan(rule.meal_plan_id) : null;

      return {
        id: rule.id,
        occupancy_type_id: rule.occupancy_type_id,
        occupancy_type: occupancyConfig,
        meal_plan_id: rule.meal_plan_id,
        meal_plan: mealPlan,
        room_config_id: rule.room_config_id,
        weekday_prices: {
          mon: rule.monday_price || rule.amount,
          tue: rule.tuesday_price || rule.amount,
          wed: rule.wednesday_price || rule.amount,
          thu: rule.thursday_price || rule.amount,
          fri: rule.friday_price || rule.amount,
          sat: rule.saturday_price || rule.amount,
          sun: rule.sunday_price || rule.amount,
        },
        currency_code: rule.currency_code,
        created_at: rule.created_at,
        updated_at: rule.updated_at,
      };
    }));

    res.status(201).json({
      weekday_rules: formattedRules,
    });
  } catch (error) {
    console.error("Error bulk upserting weekday pricing rules:", error);
    res.status(500).json({
      message: "An error occurred while bulk upserting weekday pricing rules",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
