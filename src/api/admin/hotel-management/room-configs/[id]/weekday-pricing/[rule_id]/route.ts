import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for updating weekday pricing
export const PutAdminWeekdayPricing = z.object({
  occupancy_type_id: z.string().optional(),
  meal_plan_id: z.string().optional(),
  weekday_prices: z.object({
    mon: z.number(),
    tue: z.number(),
    wed: z.number(),
    thu: z.number(),
    fri: z.number(),
    sat: z.number(),
    sun: z.number(),
  }).optional(),
  currency_code: z.string().optional(),
});

export type PutAdminWeekdayPricingType = z.infer<typeof PutAdminWeekdayPricing>;

/**
 * GET /admin/hotel-management/room-configs/:id/weekday-pricing/:rule_id
 *
 * Get a specific weekday pricing rule
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    if (!ruleId) {
      return res.status(400).json({ message: "Rule ID is required" });
    }

    console.log(`Fetching weekday pricing rule: ${ruleId} for room config: ${roomConfigId}`);

    // Get the pricing rule service
    const pricingRuleService = req.scope.resolve("pricingRuleService") as any;

    // Get the base price rule
    const rule = await pricingRuleService.retrieveBasePriceRule(ruleId);

    if (!rule) {
      return res.status(404).json({ message: "Weekday pricing rule not found" });
    }

    if (rule.room_config_id !== roomConfigId) {
      return res.status(404).json({ message: "Weekday pricing rule not found for this room configuration" });
    }

    // Format the response to include weekday_prices
    const formattedRule = {
      id: rule.id,
      occupancy_type_id: rule.occupancy_type_id,
      meal_plan_id: rule.meal_plan_id,
      room_config_id: rule.room_config_id,
      weekday_prices: {
        mon: rule.monday_price || rule.amount,
        tue: rule.tuesday_price || rule.amount,
        wed: rule.wednesday_price || rule.amount,
        thu: rule.thursday_price || rule.amount,
        fri: rule.friday_price || rule.amount,
        sat: rule.saturday_price || rule.amount,
        sun: rule.sunday_price || rule.amount,
      },
      currency_code: rule.currency_code,
      created_at: rule.created_at,
      updated_at: rule.updated_at,
    };

    // Get the hotel pricing service for occupancy and meal plan details
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get the occupancy type
    const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(rule.occupancy_type_id);

    // Get the meal plan if it exists
    let mealPlan = null;
    if (rule.meal_plan_id) {
      mealPlan = await hotelPricingService.retrieveMealPlan(rule.meal_plan_id);
    }

    const ruleWithDetails = {
      ...formattedRule,
      occupancy_type: occupancyConfig,
      meal_plan: mealPlan,
    };

    res.json({
      weekday_rule: ruleWithDetails,
    });
  } catch (error) {
    console.error("Error fetching weekday pricing rule:", error);
    res.status(500).json({
      message: "An error occurred while fetching weekday pricing rule",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * PUT /admin/hotel-management/room-configs/:id/weekday-pricing/:rule_id
 *
 * Update a weekday pricing rule
 */
export const PUT = async (req: MedusaRequest<PutAdminWeekdayPricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    if (!ruleId) {
      return res.status(400).json({ message: "Rule ID is required" });
    }

    // Validate the request body
    const { occupancy_type_id, meal_plan_id, weekday_prices, currency_code } = req.body;

    console.log(`Updating weekday pricing rule: ${ruleId} for room config: ${roomConfigId}`);

    // Get the pricing rule service
    const pricingRuleService = req.scope.resolve("pricingRuleService") as any;

    // Get the existing rule
    const existingRule = await pricingRuleService.retrieveBasePriceRule(ruleId);

    if (!existingRule) {
      return res.status(404).json({ message: "Weekday pricing rule not found" });
    }

    if (existingRule.room_config_id !== roomConfigId) {
      return res.status(404).json({ message: "Weekday pricing rule not found for this room configuration" });
    }

    // Prepare update data
    const updateData: any = {};

    if (occupancy_type_id) {
      updateData.occupancy_type_id = occupancy_type_id;
    }

    if (meal_plan_id) {
      updateData.meal_plan_id = meal_plan_id;
    }

    if (currency_code) {
      updateData.currency_code = currency_code;
    }

    if (weekday_prices) {
      updateData.monday_price = weekday_prices.mon;
      updateData.tuesday_price = weekday_prices.tue;
      updateData.wednesday_price = weekday_prices.wed;
      updateData.thursday_price = weekday_prices.thu;
      updateData.friday_price = weekday_prices.fri;
      updateData.saturday_price = weekday_prices.sat;
      updateData.sunday_price = weekday_prices.sun;

      // Update amount to match Monday's price
      updateData.amount = weekday_prices.mon;
    }

    // Update the base price rule
    const updatedRule = await pricingRuleService.updateBasePriceRule(ruleId, updateData);

    // Format the response to include weekday_prices
    const formattedRule = {
      id: updatedRule.id,
      occupancy_type_id: updatedRule.occupancy_type_id,
      meal_plan_id: updatedRule.meal_plan_id,
      room_config_id: updatedRule.room_config_id,
      weekday_prices: {
        mon: updatedRule.monday_price || updatedRule.amount,
        tue: updatedRule.tuesday_price || updatedRule.amount,
        wed: updatedRule.wednesday_price || updatedRule.amount,
        thu: updatedRule.thursday_price || updatedRule.amount,
        fri: updatedRule.friday_price || updatedRule.amount,
        sat: updatedRule.saturday_price || updatedRule.amount,
        sun: updatedRule.sunday_price || updatedRule.amount,
      },
      currency_code: updatedRule.currency_code,
      created_at: updatedRule.created_at,
      updated_at: updatedRule.updated_at,
    };

    // Get the hotel pricing service for occupancy and meal plan details
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get the occupancy type
    const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(updatedRule.occupancy_type_id);

    // Get the meal plan if it exists
    let mealPlan = null;
    if (updatedRule.meal_plan_id) {
      mealPlan = await hotelPricingService.retrieveMealPlan(updatedRule.meal_plan_id);
    }

    const ruleWithDetails = {
      ...formattedRule,
      occupancy_type: occupancyConfig,
      meal_plan: mealPlan,
    };

    res.json({
      weekday_rule: ruleWithDetails,
    });
  } catch (error) {
    console.error("Error updating weekday pricing rule:", error);
    res.status(500).json({
      message: "An error occurred while updating weekday pricing rule",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * DELETE /admin/hotel-management/room-configs/:id/weekday-pricing/:rule_id
 *
 * Delete a weekday pricing rule
 */
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    if (!ruleId) {
      return res.status(400).json({ message: "Rule ID is required" });
    }

    console.log(`Deleting weekday pricing rule: ${ruleId} for room config: ${roomConfigId}`);

    // Get the pricing rule service
    const pricingRuleService = req.scope.resolve("pricingRuleService") as any;

    // Get the existing rule
    const existingRule = await pricingRuleService.retrieveBasePriceRule(ruleId);

    if (!existingRule) {
      return res.status(404).json({ message: "Weekday pricing rule not found" });
    }

    if (existingRule.room_config_id !== roomConfigId) {
      return res.status(404).json({ message: "Weekday pricing rule not found for this room configuration" });
    }

    // Delete the base price rule
    await pricingRuleService.deleteBasePriceRule(ruleId);

    res.status(204).send();
  } catch (error) {
    console.error("Error deleting weekday pricing rule:", error);
    res.status(500).json({
      message: "An error occurred while deleting weekday pricing rule",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
