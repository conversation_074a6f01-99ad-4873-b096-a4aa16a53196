import { z } from "zod";

export const PostAdminCreateRoomConfig = z.object({
  name: z.string(),
  type: z.string(),
  hotel_id: z.string(),
  description: z.string().optional(),
  room_size: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  bed_type: z.string().optional(),
  max_extra_beds: z.number().default(0),
  max_adults: z.number().default(1),
  max_children: z.number().default(0),
  max_infants: z.number().default(0),
  max_occupancy: z.number().default(1),
});

export const PostAdminUpdateRoomConfig = z.object({
  id: z.string(),
  name: z.string().optional(),
  type: z.string().optional(),
  hotel_id: z.string().optional(),
  description: z.string().optional(),
  room_size: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  bed_type: z.string().optional(),
  max_extra_beds: z.number().optional(),
  max_adults: z.number().optional(),
  max_children: z.number().optional(),
  max_infants: z.number().optional(),
  max_occupancy: z.number().optional(),
});

export const PostAdminDeleteRoomConfig = z.object({
  ids: z.union([z.string(), z.array(z.string())]),
});
