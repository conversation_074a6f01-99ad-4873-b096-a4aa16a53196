import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';
import { HOTEL_MODULE } from "../../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../../modules/hotel-management/hotel/service";
import { Modules } from "@camped-ai/framework/utils";

/**
 * GET endpoint to download a template for bulk room import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get hotel service to fetch hotels for the dropdown
    const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get hotel_id from query params if provided
    const { hotel_id } = req.query;

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for rooms
    const worksheet = workbook.addWorksheet('Rooms');

    // Define columns
    worksheet.columns = [
      { header: 'room_number', key: 'room_number', width: 15 },
      { header: 'room_config_id', key: 'room_config_id', width: 40 },
      { header: 'name', key: 'name', width: 30 },
      { header: 'floor', key: 'floor', width: 10 },
      { header: 'status', key: 'status', width: 15 },
      { header: 'is_active', key: 'is_active', width: 15 },
      { header: 'notes', key: 'notes', width: 40 },
      { header: 'left_room', key: 'left_room', width: 15 },
      { header: 'right_room', key: 'right_room', width: 15 },
      { header: 'opposite_room', key: 'opposite_room', width: 15 },
      { header: 'connected_room', key: 'connected_room', width: 15 },
      { header: 'price', key: 'price', width: 15 },
      { header: 'currency_code', key: 'currency_code', width: 15 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Fetch data based on whether hotel_id is provided
    let hotels = [];
    let roomConfigs = [];

    // Fetch all hotels for reference
    hotels = await hotelService.listHotels({});

    // Get ALL room configurations without filtering by hotel
    try {
      const productsResult = await productModuleService.listProducts({
        is_giftcard: false,
      }, {
        relations: ["metadata"],
      });

      // Handle different return formats
      if (productsResult) {
        if (Array.isArray(productsResult)) {
          // If it's an array, use it directly
          roomConfigs = productsResult;
        } else {
          // Try to access the products property
          const anyResult = productsResult as any;
          if (anyResult.products && Array.isArray(anyResult.products)) {
            roomConfigs = anyResult.products;
          }
        }
        console.log(`Found ${roomConfigs.length} room configurations`);
      }
    } catch (error) {
      console.error('Error fetching room configurations:', error);
    }

    // No hotel reference sheet as requested

    // Add a reference sheet for room configurations
    const roomConfigSheet = workbook.addWorksheet('Room Configs Reference');

    // Define columns for the room config reference sheet
    roomConfigSheet.columns = [
      { header: 'room_config_id', key: 'room_config_id', width: 40 },
      { header: 'name', key: 'name', width: 30 },
      { header: 'hotel_id', key: 'hotel_id', width: 40 },
      { header: 'hotel_name', key: 'hotel_name', width: 30 },
    ];

    // Style the header row
    roomConfigSheet.getRow(1).font = { bold: true };
    roomConfigSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add room config data
    roomConfigs.forEach(config => {
      const hotel = hotels.find(h => h.id === config.metadata?.hotel_id);
      roomConfigSheet.addRow({
        room_config_id: config.id,
        name: config.title,
        hotel_id: config.metadata?.hotel_id || '',
        hotel_name: hotel?.name || ''
      });
    });

    // Add sample rows to the rooms sheet
    if (roomConfigs.length > 0) {
      // Add a few sample rows
      for (let i = 1; i <= 3; i++) {
        const roomConfig = roomConfigs[0]; // Use the first room config as an example
        worksheet.addRow({
          room_number: `10${i}`,
          room_config_id: roomConfig.id,
          name: `Room 10${i}`,
          floor: '1',
          status: 'available',
          is_active: 'true',
          notes: 'Sample room',
          left_room: i > 1 ? `10${i-1}` : '',
          right_room: i < 3 ? `10${i+1}` : '',
          opposite_room: '',
          connected_room: '',
          price: '100',
          currency_code: 'USD',
        });
      }
    }

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add instructions for each field
    const instructions = [
      { field: 'room_number', description: 'The room number (must be unique within the hotel)', required: 'Yes', format: 'Text or Number' },
      { field: 'room_config_id', description: 'The ID of the room configuration this room belongs to. See Room Configs Reference sheet.', required: 'Yes', format: 'Text (UUID)' },
      { field: 'name', description: 'The name of the room (optional, will use room number if not provided)', required: 'No', format: 'Text' },
      { field: 'floor', description: 'The floor number where the room is located', required: 'No', format: 'Text or Number' },
      { field: 'status', description: 'The status of the room (available, maintenance, etc.)', required: 'No', format: 'Text (available, maintenance, reserved, booked, cleaning)' },
      { field: 'is_active', description: 'Whether the room is active and can be booked', required: 'No', format: 'true or false' },
      { field: 'notes', description: 'Additional notes about the room', required: 'No', format: 'Text' },
      { field: 'left_room', description: 'The room number of the room to the left', required: 'No', format: 'Text or Number (Room Number)' },
      { field: 'right_room', description: 'The room number of the room to the right', required: 'No', format: 'Text or Number (Room Number)' },
      { field: 'opposite_room', description: 'The room number of the room opposite', required: 'No', format: 'Text or Number (Room Number)' },
      { field: 'connected_room', description: 'The room number of a connected room', required: 'No', format: 'Text or Number (Room Number)' },
      { field: 'price', description: 'The base price for the room', required: 'No', format: 'Number' },
      { field: 'currency_code', description: 'Currency code for the price (e.g., USD, EUR)', required: 'No', format: 'Text (3-letter code)' },
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=room-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};
