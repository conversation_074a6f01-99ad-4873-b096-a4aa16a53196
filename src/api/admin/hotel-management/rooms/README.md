# Room Management APIs

This directory contains APIs for managing rooms in the hotel booking system.

## Room Creation APIs

### Create Room

**Endpoint:** `POST /api/admin/hotel-management/rooms`

Creates a single room as a product variant without options.

**Request Body:**
```json
{
  "name": "Deluxe Room 101",
  "room_number": "101",
  "status": "available",
  "floor": "1",
  "notes": "Corner room with ocean view",
  "is_active": true,
  "room_config_id": "room_cfg_123456",
  "hotel_id": "hotel_123456",
  "price": 15000,
  "currency_code": "usd"
}
```

**Response:**
```json
{
  "room": {
    "id": "variant_123456",
    "name": "Deluxe Room 101",
    "room_number": "101",
    "status": "available",
    "floor": "1",
    "notes": "Corner room with ocean view",
    "is_active": true,
    "room_config_id": "room_cfg_123456",
    "hotel_id": "hotel_123456",
    "product_id": "prod_123456",
    "variant_id": "variant_123456",
    "inventory_item_id": "inv_item_123456"
  }
}
```

### Create Room with Options

**Endpoint:** `POST /api/admin/hotel-management/rooms/create-with-options`

Creates a single room as a product variant with options.

**Request Body:**
```json
{
  "name": "Deluxe Room 101",
  "room_number": "101",
  "status": "available",
  "floor": "1",
  "notes": "Corner room with ocean view",
  "is_active": true,
  "room_config_id": "room_cfg_123456",
  "hotel_id": "hotel_123456",
  "options": {
    "View": "Ocean",
    "Bed Type": "King",
    "Smoking": "No"
  },
  "price": 15000,
  "currency_code": "usd"
}
```

**Response:**
```json
{
  "room": {
    "id": "variant_123456",
    "name": "Deluxe Room 101",
    "room_number": "101",
    "status": "available",
    "floor": "1",
    "notes": "Corner room with ocean view",
    "is_active": true,
    "room_config_id": "room_cfg_123456",
    "hotel_id": "hotel_123456",
    "product_id": "prod_123456",
    "variant_id": "variant_123456",
    "inventory_item_id": "inv_item_123456",
    "options": {
      "View": "Ocean",
      "Bed Type": "King",
      "Smoking": "No"
    }
  }
}
```

### Create Multiple Rooms with Options

**Endpoint:** `POST /api/admin/hotel-management/rooms/create-variants`

Creates multiple rooms as product variants with options in a single request.

**Request Body:**
```json
{
  "room_config_id": "room_cfg_123456",
  "hotel_id": "hotel_123456",
  "rooms": [
    {
      "name": "Deluxe Room 101",
      "room_number": "101",
      "status": "available",
      "floor": "1",
      "notes": "Corner room with ocean view",
      "is_active": true,
      "options": {
        "View": "Ocean",
        "Bed Type": "King",
        "Smoking": "No"
      },
      "price": 15000,
      "currency_code": "usd"
    },
    {
      "name": "Deluxe Room 102",
      "room_number": "102",
      "status": "available",
      "floor": "1",
      "notes": "Middle room with garden view",
      "is_active": true,
      "options": {
        "View": "Garden",
        "Bed Type": "Queen",
        "Smoking": "No"
      },
      "price": 14000,
      "currency_code": "usd"
    }
  ]
}
```

**Response:**
```json
{
  "rooms": [
    {
      "id": "variant_123456",
      "name": "Deluxe Room 101",
      "room_number": "101",
      "status": "available",
      "floor": "1",
      "notes": "Corner room with ocean view",
      "is_active": true,
      "room_config_id": "room_cfg_123456",
      "hotel_id": "hotel_123456",
      "product_id": "prod_123456",
      "variant_id": "variant_123456",
      "inventory_item_id": "inv_item_123456",
      "options": {
        "View": "Ocean",
        "Bed Type": "King",
        "Smoking": "No"
      }
    },
    {
      "id": "variant_123457",
      "name": "Deluxe Room 102",
      "room_number": "102",
      "status": "available",
      "floor": "1",
      "notes": "Middle room with garden view",
      "is_active": true,
      "room_config_id": "room_cfg_123456",
      "hotel_id": "hotel_123456",
      "product_id": "prod_123456",
      "variant_id": "variant_123457",
      "inventory_item_id": "inv_item_123457",
      "options": {
        "View": "Garden",
        "Bed Type": "Queen",
        "Smoking": "No"
      }
    }
  ]
}
```

## Room Management APIs

### List Rooms

**Endpoint:** `GET /api/admin/hotel-management/rooms`

Lists all rooms, optionally filtered by room configuration ID or hotel ID.

**Query Parameters:**
- `room_config_id` (optional): Filter rooms by room configuration ID
- `hotel_id` (optional): Filter rooms by hotel ID

**Response:**
```json
{
  "rooms": [
    {
      "id": "variant_123456",
      "name": "Deluxe Room 101",
      "room_number": "101",
      "status": "available",
      "floor": "1",
      "notes": "Corner room with ocean view",
      "is_active": true,
      "room_config_id": "room_cfg_123456",
      "hotel_id": "hotel_123456"
    },
    {
      "id": "variant_123457",
      "name": "Deluxe Room 102",
      "room_number": "102",
      "status": "available",
      "floor": "1",
      "notes": "Middle room with garden view",
      "is_active": true,
      "room_config_id": "room_cfg_123456",
      "hotel_id": "hotel_123456"
    }
  ]
}
```

### Get Room

**Endpoint:** `GET /api/admin/hotel-management/rooms/:id`

Gets a specific room by ID.

**Response:**
```json
{
  "room": {
    "id": "variant_123456",
    "name": "Deluxe Room 101",
    "room_number": "101",
    "status": "available",
    "floor": "1",
    "notes": "Corner room with ocean view",
    "is_active": true,
    "room_config_id": "room_cfg_123456",
    "hotel_id": "hotel_123456"
  }
}
```

### Update Room

**Endpoint:** `PUT /api/admin/hotel-management/rooms`

Updates a room.

**Request Body:**
```json
{
  "id": "variant_123456",
  "name": "Deluxe Room 101 Updated",
  "room_number": "101A",
  "status": "maintenance",
  "floor": "1",
  "notes": "Under renovation",
  "is_active": false
}
```

**Response:**
```json
{
  "room": {
    "id": "variant_123456",
    "name": "Deluxe Room 101 Updated",
    "room_number": "101A",
    "status": "maintenance",
    "floor": "1",
    "notes": "Under renovation",
    "is_active": false,
    "room_config_id": "room_cfg_123456",
    "hotel_id": "hotel_123456"
  }
}
```

### Delete Room

**Endpoint:** `DELETE /api/admin/hotel-management/rooms`

Deletes one or more rooms.

**Request Body:**
```json
{
  "ids": "variant_123456,variant_123457"
}
```

**Response:**
```json
{
  "success": true,
  "ids": ["variant_123456", "variant_123457"]
}
```

## Implementation Details

Rooms are implemented as product variants in the Medusa e-commerce system. Each room belongs to a room configuration, which is implemented as a product. Room options are implemented as product options and option values.

The room creation APIs handle:
1. Creating product options if they don't exist
2. Creating option values if they don't exist
3. Creating product variants with the specified options
4. Setting up inventory for the created rooms
5. Setting up pricing for the created rooms

This approach allows the hotel booking system to leverage Medusa's existing inventory and order management capabilities.
