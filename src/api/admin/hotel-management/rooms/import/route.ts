import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../../modules/hotel-management/hotel/service";
import { ROOM_INVENTORY_MODULE } from "../../../../../modules/hotel-management/room-inventory";
import { CreateRoomWorkflow } from "../../../../../workflows/hotel-management/room/create-room";
import * as ExcelJS from 'exceljs';
import multer from 'multer';
import { z } from "zod";
import { lookupRoomIdsByRoomNumbers } from "../utils/room-lookup";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Validation schema for room data
const RoomSchema = z.object({
  // Handle both string and number for room_number, converting numbers to strings
  room_number: z.union([
    z.string().min(1, "Room number is required"),
    z.number().transform(val => String(val))
  ]),

  room_config_id: z.string().min(1, "Room configuration ID is required"),

  // Handle both string and number for name, converting numbers to strings
  name: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => undefined)
  ]).optional(),

  // Handle both string and number for floor, converting numbers to strings
  floor: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => undefined)
  ]).optional(),

  status: z.enum(["available", "occupied", "maintenance", "cleaning"]).default("available"),

  is_active: z.union([
    z.boolean(),
    z.string().transform(val => val?.toLowerCase() === 'true'),
    z.null().transform(() => true)
  ]).default(true),

  // Handle null values for optional string fields
  notes: z.union([z.string(), z.number().transform(val => String(val)), z.null().transform(() => "")]).optional(),

  // Handle relationship fields that can be strings, numbers, or null
  left_room: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => "")
  ]).optional(),

  right_room: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => "")
  ]).optional(),

  opposite_room: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => "")
  ]).optional(),

  connected_room: z.union([
    z.string(),
    z.number().transform(val => String(val)),
    z.null().transform(() => "")
  ]).optional(),

  // Handle price as number, string, or null
  price: z.union([
    z.number(),
    z.string().transform(val => val ? parseFloat(val) : undefined),
    z.null().transform(() => undefined)
  ]).optional(),

  // Handle null values for currency code
  currency_code: z.union([z.string(), z.null().transform(() => undefined)]).optional(),
});

/**
 * POST endpoint to import rooms from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      // Get services
      const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
      const productModuleService = req.scope.resolve(Modules.PRODUCT);

      // Try to resolve the room inventory service, but don't fail if it's not available
      let roomInventoryService = null;
      try {
        roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);
      } catch (error) {
        console.warn("Room inventory service not available. Skipping inventory creation.");
      }

      // Get hotel_id from form data if provided
      const hotelId = req.body.hotel_id;

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split('.').pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === 'csv') {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString('utf8');

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require('fs').writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require('fs').unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error('Error reading CSV:', csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split('\\n').map(row => row.split(','));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet('Sheet1');

              // Add rows to the worksheet
              rows.forEach(row => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error('CSV file is empty or invalid');
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Rooms' or use the first one
          worksheet = workbook.getWorksheet('Rooms') ||
                     workbook.getWorksheet('rooms') ||
                     workbook.getWorksheet('Sheet1') ||
                     workbook.worksheets[0];
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        return res.status(400).json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res.status(400).json({ message: 'Invalid file: No worksheet found' });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};

      console.log('Raw headers:', headers);

      // Create a map of column index to header name
      // Note: ExcelJS uses 1-based indexing for columns
      for (let i = 1; i < headers.length; i++) {
        const header = headers[i];
        if (header) {
          const headerName = header.toString().trim().toLowerCase(); // Convert to lowercase for case-insensitive matching
          headerMap[i] = headerName;
          console.log(`Mapped column ${i} to header '${headerName}'`);
        }
      }

      console.log('Header map:', headerMap);

      // Prepare results
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        created: [],
        errors: []
      };

      // Validate room configuration IDs
      // Fetch room configurations (products)
      let roomConfigs = [];
      try {
        const products = await productModuleService.listProducts({
          is_giftcard: false,
        }, {
          relations: ["metadata"],
        });

        console.log('Products response:', products);

        // Check if products and products.products exist before filtering
        if (products && products.products && Array.isArray(products.products)) {
          roomConfigs = products.products.filter(product =>
            product.metadata && product.metadata.hotel_id
          );
          console.log(`Found ${roomConfigs.length} room configurations`);
        } else {
          console.warn('No products found or products.products is not an array');
        }
      } catch (error) {
        console.error('Error fetching room configurations:', error);
      }

      // Create a set of valid room configuration IDs
      const validRoomConfigIds = new Set(roomConfigs.map(config => config.id));

      // Collect all room numbers from the file for relationship lookups
      const allRoomNumbers: string[] = [];
      const roomRelationships: Record<number, {
        left_room_number?: string,
        right_room_number?: string,
        opposite_room_number?: string,
        connected_room_number?: string
      }> = {};

      // First pass: collect all room numbers and relationship data
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);
        if (!row.hasValues) continue;

        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            rowData[header] = cell.value;
          }
        });

        // Clean up the data
        Object.keys(rowData).forEach(key => {
          if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
        });

        // Add the room number to our collection
        if (rowData.room_number) {
          allRoomNumbers.push(rowData.room_number);
        }

        // Store relationship room numbers for later lookup
        roomRelationships[i] = {
          left_room_number: rowData.left_room,
          right_room_number: rowData.right_room,
          opposite_room_number: rowData.opposite_room,
          connected_room_number: rowData.connected_room
        };

        // Also add relationship room numbers to our collection
        if (rowData.left_room) allRoomNumbers.push(rowData.left_room);
        if (rowData.right_room) allRoomNumbers.push(rowData.right_room);
        if (rowData.opposite_room) allRoomNumbers.push(rowData.opposite_room);
        if (rowData.connected_room) allRoomNumbers.push(rowData.connected_room);
      }

      // Get the hotel ID for the room configurations
      let roomConfigHotelId = "";
      if (roomConfigs.length > 0) {
        // Extract room config IDs from the data
        const roomConfigIds = Array.from(validRoomConfigIds);
        if (roomConfigIds.length > 0) {
          const roomConfig = roomConfigs.find(rc => rc.id === roomConfigIds[0]);
          if (roomConfig && roomConfig.metadata && roomConfig.metadata.hotel_id) {
            roomConfigHotelId = roomConfig.metadata.hotel_id;
          }
        }
      }

      // Look up room IDs by room numbers
      const formHotelId = req.body?.hotel_id as string | undefined;
      const roomNumberToIdMap = await lookupRoomIdsByRoomNumbers(req.scope, formHotelId || roomConfigHotelId, allRoomNumbers);
      console.log(`Room number to ID map:`, Object.fromEntries(roomNumberToIdMap));

      // Process each row (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (!row.hasValues) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell: any, colNumber: number) => {
          const header = headerMap[colNumber];
          if (header) {
            rowData[header] = cell.value;
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach(key => {
          // Convert any objects to strings
          if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
        });

        // Debug log to see what data we're getting
        console.log('Row data:', rowData);

        // Map the lowercase header names back to the expected case-sensitive names
        const normalizedData: Record<string, any> = {};
        Object.keys(rowData).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'room_number') normalizedData.room_number = rowData[key];
          else if (lowerKey === 'room_config_id') normalizedData.room_config_id = rowData[key];
          else if (lowerKey === 'name') normalizedData.name = rowData[key];
          else if (lowerKey === 'floor') normalizedData.floor = rowData[key];
          else if (lowerKey === 'status') normalizedData.status = rowData[key];
          else if (lowerKey === 'is_active') normalizedData.is_active = rowData[key];
          else if (lowerKey === 'notes') normalizedData.notes = rowData[key];
          else if (lowerKey === 'left_room') normalizedData.left_room = rowData[key];
          else if (lowerKey === 'right_room') normalizedData.right_room = rowData[key];
          else if (lowerKey === 'opposite_room') normalizedData.opposite_room = rowData[key];
          else if (lowerKey === 'connected_room') normalizedData.connected_room = rowData[key];
          else if (lowerKey === 'price') normalizedData.price = rowData[key];
          else if (lowerKey === 'currency_code') normalizedData.currency_code = rowData[key];
          else normalizedData[key] = rowData[key]; // Keep any other fields
        });

        console.log('Normalized data:', normalizedData);

        // Ensure required fields are present
        if (!normalizedData.room_number || !normalizedData.room_config_id) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: `Missing required fields: ${!normalizedData.room_number ? 'room_number' : ''} ${!normalizedData.room_config_id ? 'room_config_id' : ''}`
          });
          continue;
        }

        try {
          // Get the relationship room numbers for this row
          const relationships = roomRelationships[i] || {};

          console.log(`Row ${i} - Processing relationship fields:`, {
            left_room_number: relationships.left_room_number,
            right_room_number: relationships.right_room_number,
            opposite_room_number: relationships.opposite_room_number,
            connected_room_number: relationships.connected_room_number,
            left_room_type: relationships.left_room_number ? typeof relationships.left_room_number : 'undefined',
            right_room_type: relationships.right_room_number ? typeof relationships.right_room_number : 'undefined'
          });

          // Convert room numbers to IDs for relationship fields
          if (relationships.left_room_number) {
            const leftRoomNumberStr = String(relationships.left_room_number);
            if (roomNumberToIdMap.has(leftRoomNumberStr)) {
              normalizedData.left_room = roomNumberToIdMap.get(leftRoomNumberStr);
            } else {
              console.log(`Row ${i} - Could not find room ID for left_room number: ${leftRoomNumberStr}`);
            }
          }

          if (relationships.right_room_number) {
            const rightRoomNumberStr = String(relationships.right_room_number);
            if (roomNumberToIdMap.has(rightRoomNumberStr)) {
              normalizedData.right_room = roomNumberToIdMap.get(rightRoomNumberStr);
            } else {
              console.log(`Row ${i} - Could not find room ID for right_room number: ${rightRoomNumberStr}`);
            }
          }

          if (relationships.opposite_room_number) {
            const oppositeRoomNumberStr = String(relationships.opposite_room_number);
            if (roomNumberToIdMap.has(oppositeRoomNumberStr)) {
              normalizedData.opposite_room = roomNumberToIdMap.get(oppositeRoomNumberStr);
            } else {
              console.log(`Row ${i} - Could not find room ID for opposite_room number: ${oppositeRoomNumberStr}`);
            }
          }

          if (relationships.connected_room_number) {
            const connectedRoomNumberStr = String(relationships.connected_room_number);
            if (roomNumberToIdMap.has(connectedRoomNumberStr)) {
              normalizedData.connected_room = roomNumberToIdMap.get(connectedRoomNumberStr);
            } else {
              console.log(`Row ${i} - Could not find room ID for connected_room number: ${connectedRoomNumberStr}`);
            }
          }

          // Log the room number to ID conversions
          if (relationships.left_room_number || relationships.right_room_number ||
              relationships.opposite_room_number || relationships.connected_room_number) {
            console.log(`Row ${i} - Room number to ID conversions:`, {
              left_room: `${relationships.left_room_number} -> ${normalizedData.left_room || 'not found'}`,
              right_room: `${relationships.right_room_number} -> ${normalizedData.right_room || 'not found'}`,
              opposite_room: `${relationships.opposite_room_number} -> ${normalizedData.opposite_room || 'not found'}`,
              connected_room: `${relationships.connected_room_number} -> ${normalizedData.connected_room || 'not found'}`
            });
          }

          // Validate the data
          const validatedData = RoomSchema.parse(normalizedData);

          // Validate room_config_id
          if (validRoomConfigIds.size > 0 && !validRoomConfigIds.has(validatedData.room_config_id)) {
            throw new Error(`Invalid room_config_id: ${validatedData.room_config_id}. Please use a valid room configuration ID from the Room Configs Reference sheet.`);
          }

          // If we couldn't validate room configurations, at least check if the ID exists
          if (validRoomConfigIds.size === 0) {
            try {
              // Try to fetch the product directly to verify it exists
              const product = await productModuleService.retrieveProduct(validatedData.room_config_id, {
                relations: ["metadata"],
              });

              if (!product) {
                throw new Error(`Room configuration with ID ${validatedData.room_config_id} not found.`);
              }

              console.log(`Verified room configuration ${validatedData.room_config_id} exists.`);
            } catch (error) {
              throw new Error(`Invalid room_config_id: ${validatedData.room_config_id}. Room configuration not found.`);
            }
          }

          // Get the hotel_id from the room configuration
          let roomConfigHotelId: string = "";

          // First try to find it in our cached list
          const roomConfig = roomConfigs.find(config => config.id === validatedData.room_config_id);
          if (roomConfig && roomConfig.metadata) {
            roomConfigHotelId = roomConfig.metadata.hotel_id;
          } else {
            // If not found in our list, try to get it directly
            try {
              const product = await productModuleService.retrieveProduct(validatedData.room_config_id, {
                relations: ["metadata"],
              });

              if (product && product.metadata && typeof product.metadata === 'object' && 'hotel_id' in product.metadata) {
                roomConfigHotelId = product.metadata.hotel_id as string;
                console.log(`Got hotel ID ${roomConfigHotelId} for room config ${validatedData.room_config_id}`);
              }
            } catch (error) {
              console.warn(`Could not get hotel ID for room config ${validatedData.room_config_id}:`, error.message);
            }
          }

          // If we still don't have a hotel ID and one was provided in the request, use that
          if (!roomConfigHotelId && hotelId) {
            roomConfigHotelId = hotelId;
            console.log(`Using provided hotel ID ${hotelId} for room config ${validatedData.room_config_id}`);
          }

          // If we still don't have a hotel ID, we can't proceed
          if (!roomConfigHotelId) {
            throw new Error(`Could not determine hotel ID for room configuration ${validatedData.room_config_id}. Please specify a hotel ID.`);
          }

          // If hotelId is provided in the request, make sure it matches the room configuration's hotel_id
          if (hotelId && roomConfigHotelId && String(hotelId) !== String(roomConfigHotelId)) {
            throw new Error(`Room configuration ${validatedData.room_config_id} belongs to hotel ${roomConfigHotelId}, but you're trying to import it to hotel ${hotelId}`);
          }

          // Create the room using the workflow or direct API if workflow fails
          let result: any;
          try {
            const workflowResult = await CreateRoomWorkflow(req.scope).run({
              input: {
                name: validatedData.name || `Room ${validatedData.room_number}`,
                room_number: validatedData.room_number,
                status: validatedData.status,
                floor: validatedData.floor || "1",
                notes: validatedData.notes,
                is_active: validatedData.is_active,
                left_room: validatedData.left_room,
                opposite_room: validatedData.opposite_room,
                connected_room: validatedData.connected_room,
                right_room: validatedData.right_room,
                room_config_id: validatedData.room_config_id,
                hotel_id: roomConfigHotelId,
                price: validatedData.price,
                currency_code: validatedData.currency_code
              }
            });
            result = workflowResult.result;
          } catch (workflowError) {
            console.warn("Workflow failed, trying direct API:", workflowError.message);

            // Fallback to direct API using the product module service
            // Create a variant object that matches the expected format
            // Based on successful examples in the codebase, we'll simplify the variant creation
            const variantData = {
              title: validatedData.name || `Room ${validatedData.room_number}`,
              product_id: validatedData.room_config_id,
              // Don't include options array as it's causing issues
              inventory_quantity: validatedData.status === "available" && validatedData.is_active ? 1 : 0,
              manage_inventory: true,
              allow_backorder: false,
              metadata: {
                room_number: validatedData.room_number,
                floor: validatedData.floor || "1",
                notes: validatedData.notes,
                status: validatedData.status,
                is_active: validatedData.is_active,
                left_room: validatedData.left_room,
                opposite_room: validatedData.opposite_room,
                connected_room: validatedData.connected_room,
                right_room: validatedData.right_room,
                hotel_id: roomConfigHotelId,
                room_config_id: validatedData.room_config_id
              },
            };

            console.log("Creating variant with data:", JSON.stringify(variantData, null, 2));

            // Cast to any to bypass type checking since the API might have changed
            const variant = await (productModuleService.createProductVariants as any)(variantData);

            // Create room inventory entries if room inventory service is available
            let inventoryItemId = variant.id; // Use variant ID as inventory item ID
            if (roomInventoryService) {
              try {
                // Create room inventory entries for the next year
                const today = new Date();
                const nextYear = new Date(today);
                nextYear.setFullYear(today.getFullYear() + 1);

                await roomInventoryService.createRoomInventories([
                  {
                    inventory_item_id: variant.id,
                    from_date: today,
                    to_date: nextYear,
                    status: "available",
                    available_quantity: 1,
                    check_in_time: "14:00",
                    check_out_time: "12:00",
                    is_noon_to_noon: true,
                    metadata: {
                      room_number: validatedData.room_number,
                      floor: validatedData.floor,
                      hotel_id: roomConfigHotelId,
                    }
                  }
                ]);

                console.log(`Created room inventory for ${validatedData.room_number} from ${today.toISOString()} to ${nextYear.toISOString()}`);
              } catch (error) {
                console.warn("Failed to create room inventory:", error);
              }
            }

            // Create a result object similar to what the workflow would return
            result = {
              id: variant.id,
              name: validatedData.name || `Room ${validatedData.room_number}`,
              room_number: validatedData.room_number,
              status: validatedData.status,
              floor: validatedData.floor || "1",
              notes: validatedData.notes,
              is_active: validatedData.is_active,
              left_room: validatedData.left_room,
              opposite_room: validatedData.opposite_room,
              connected_room: validatedData.connected_room,
              right_room: validatedData.right_room,
              room_config_id: validatedData.room_config_id,
              hotel_id: roomConfigHotelId,
              product_id: validatedData.room_config_id,
              variant_id: variant.id,
              inventory_item_id: inventoryItemId
            };
          }

          results.successful++;
          results.created.push({
            id: result.id,
            room_number: validatedData.room_number,
            row: i
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: error.message || 'Unknown error'
          });
        }
      }

      // Return the results
      return res.status(200).json({
        message: 'Import completed',
        results
      });

    } catch (error) {
      console.error('Error importing rooms:', error);
      return res.status(500).json({ message: `Error importing rooms: ${error.message}` });
    }
  });
};
