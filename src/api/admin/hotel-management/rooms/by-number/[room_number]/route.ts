import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { findRoomsByNumber } from "../../utils/find-rooms-by-number";

/**
 * GET endpoint to find all rooms with a specific room number
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { room_number } = req.params;
    
    if (!room_number) {
      return res.status(400).json({ message: "Room number is required" });
    }
    
    console.log(`Finding rooms with room number: ${room_number}`);
    
    // Find all rooms with the specified room number
    const rooms = await findRoomsByNumber(req.scope, room_number);
    
    return res.json({ rooms });
  } catch (error) {
    console.error("Error finding rooms by number:", error);
    return res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to find rooms by number",
    });
  }
};
