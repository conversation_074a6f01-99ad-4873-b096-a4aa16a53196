import { Modules } from "@camped-ai/framework/utils";

/**
 * Lookup room IDs by room numbers within a specific hotel
 * @param scope - The request scope
 * @param hotelId - The hotel ID to search within
 * @param roomNumbers - Array of room numbers to look up
 * @returns A map of room numbers to their corresponding IDs
 */
export async function lookupRoomIdsByRoomNumbers(scope: any, hotelId: string, roomNumbers: string[]) {
  if (!roomNumbers || roomNumbers.length === 0) {
    return new Map<string, string>();
  }

  // Filter out empty room numbers and ensure all are strings
  const validRoomNumbers = roomNumbers
    .map(num => num !== null && num !== undefined ? String(num) : null) // Convert to string or null
    .filter(num => num && num.trim && num.trim() !== ''); // Filter out empty or null values
  if (validRoomNumbers.length === 0) {
    return new Map<string, string>();
  }

  try {
    // Get the product module service
    const productModuleService = scope.resolve(Modules.PRODUCT);

    // Get all product variants for the hotel
    const variantsResponse = await productModuleService.listVariants({}, {
      relations: ["metadata"]
    });

    if (!variantsResponse || !variantsResponse.variants) {
      console.warn('No variants found when looking up room IDs');
      return new Map<string, string>();
    }

    // Filter variants to only include those with room_number and matching hotel_id
    const roomVariants = variantsResponse.variants.filter((variant: any) =>
      variant.metadata &&
      variant.metadata.room_number &&
      variant.metadata.hotel_id === hotelId
    );

    // Create a map of room numbers to IDs
    const roomNumberToIdMap = new Map<string, string>();

    for (const variant of roomVariants) {
      if (variant.metadata && variant.metadata.room_number) {
        const roomNumber = String(variant.metadata.room_number);
        if (roomNumber && validRoomNumbers.includes(roomNumber)) {
          roomNumberToIdMap.set(roomNumber, variant.id);
        }
      }
    }

    console.log(`Found ${roomNumberToIdMap.size} room IDs for ${validRoomNumbers.length} room numbers in hotel ${hotelId}`);
    return roomNumberToIdMap;
  } catch (error) {
    console.error('Error looking up room IDs by room numbers:', error);
    return new Map<string, string>();
  }
}
