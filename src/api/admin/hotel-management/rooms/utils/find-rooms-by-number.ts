import { Modules } from "@camped-ai/framework/utils";

/**
 * Find all room variants that share the same room number
 * @param scope - The request scope
 * @param roomNumber - The room number to search for
 * @returns Array of room variants with the specified room number
 */
export async function findRoomsByNumber(scope: any, roomNumber: string): Promise<any[]> {
  try {
    // Get the product module service
    const productModuleService = scope.resolve(Modules.PRODUCT);
    
    // Find all variants with the given room number
    const { variants } = await productModuleService.listProductVariants({
      metadata: { room_number: roomNumber },
    });
    
    if (!variants || variants.length === 0) {
      return [];
    }
    
    // Get the product IDs for these variants
    const productIds = [...new Set(variants.map(v => v.product_id))];
    
    // Get the products (room configurations)
    const { products } = await productModuleService.listProducts({
      id: productIds,
    });
    
    // Map variants to room objects with product information
    return variants.map(variant => {
      const product = products.find(p => p.id === variant.product_id);
      
      return {
        id: variant.id,
        name: variant.title,
        room_number: variant.metadata?.room_number || "",
        status: variant.metadata?.status || "available",
        floor: variant.metadata?.floor || "",
        notes: variant.metadata?.notes || "",
        is_active: variant.metadata?.is_active !== false,
        room_config_id: product?.id || "",
        room_config_name: product?.title || "",
        hotel_id: product?.metadata?.hotel_id || product?.categories?.[0] || "",
      };
    });
  } catch (error) {
    console.error("Error finding rooms by number:", error);
    return [];
  }
}
