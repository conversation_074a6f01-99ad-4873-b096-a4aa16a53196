import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_SERVICE } from "../../../../../../modules/hotel-management/room-inventory";
import { format, parseISO, eachDayOfInterval } from "date-fns";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomId = req.params.id;
    const { start_date, end_date } = req.query;

    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);

    // Get the product variant (room)
    const variant = await productModuleService.retrieveProductVariant(roomId);

    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Get the room inventory entries for this variant
    try {
      const inventoryEntries = await roomInventoryService.listInventoryEntries({
        inventory_item_id: variant.id
      });

      // Format availability data
      let availability = inventoryEntries.map(entry => ({
        date: format(new Date(entry.from_date), 'yyyy-MM-dd'),
        quantity: entry.available_quantity,
        status: entry.status,
        notes: entry.notes
      })) || [];

      // Filter by date range if provided
      if (start_date && end_date) {
        const startDateObj = new Date(start_date as string);
        const endDateObj = new Date(end_date as string);

        availability = availability.filter(item => {
          const itemDate = new Date(item.date);
          return itemDate >= startDateObj && itemDate <= endDateObj;
        });
      }

      // Sort by date
      const sortedAvailability = [...availability].sort((a, b) => {
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      });

      res.json({
        inventory_entries: inventoryEntries,
        availability: sortedAvailability,
        room: {
          id: variant.id,
          name: variant.title,
          room_number: variant.metadata?.room_number || "",
          product_id: variant.product_id,
        },
      });
    } catch (error) {
      // If no inventory item exists, return empty availability
      res.json({
        inventory_item: null,
        room: {
          id: variant.id,
          name: variant.title,
          room_number: variant.metadata?.room_number || "",
          product_id: variant.product_id,
        },
      });
    }
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve room inventory",
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomId = req.params.id;
    const { start_date, end_date, quantity, dynamic_price } = req.body;

    if (!start_date || !end_date || quantity === undefined) {
      return res.status(400).json({
        message: "start_date, end_date, and quantity are required",
      });
    }

    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);

    // Get the product variant (room)
    const variant = await productModuleService.retrieveProductVariant(roomId);

    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Update inventory status
    const status = quantity > 0 ? 'available' : 'maintenance';
    const notes = req.body.notes || (quantity > 0 ? 'Available' : 'Maintenance');

    // Update room inventory
    const updatedEntries = await roomInventoryService.updateInventoryStatus(
      variant.id,
      start_date,
      end_date,
      status,
      notes,
      null, // bookingInfo
      null, // expiresAt
      null, // No order_id for manual inventory updates
      null  // No cart_id for manual inventory updates
    );

    res.json({
      inventory_entries: updatedEntries,
      room_id: variant.id,
      success: true
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room inventory",
    });
  }
};
