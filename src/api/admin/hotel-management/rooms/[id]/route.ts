import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve("query");
    const roomId = req.params.id;
    
    // Get the product variant (room)
    const { data: variants } = await query.graph({
      entity: "product_variant",
      filters: { id: [roomId] },
      fields: ["id", "title", "product_id", "metadata", "inventory_quantity"],
    });
    
    if (!variants || variants.length === 0) {
      return res.status(404).json({ message: "Room not found" });
    }
    
    const variant = variants[0];
    
    // Get the product (room configuration)
    const { data: products } = await query.graph({
      entity: "product",
      filters: { id: [variant.product_id] },
      fields: ["id", "title", "metadata", "categories"],
    });
    
    if (!products || products.length === 0) {
      return res.status(404).json({ message: "Room configuration not found" });
    }
    
    const product = products[0];
    
    // Map to room object
    const room = {
      id: variant.id,
      name: variant.title,
      room_number: variant.metadata?.room_number || "",
      status: variant.metadata?.status || "available",
      floor: variant.metadata?.floor || "",
      notes: variant.metadata?.notes || "",
      is_active: variant.metadata?.is_active !== false,
      room_config_id: product.metadata?.room_config_id || "",
      hotel_id: product.categories?.[0] || "",
    };
    
    res.json({ room });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve room",
    });
  }
};
