import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ROOM_INVENTORY_SERVICE } from "../../../../../modules/hotel-management/room-inventory";
import { Modules } from "@camped-ai/framework/utils";

/**
 * Update inventory for a room or multiple rooms
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    const {
      room_id,
      room_config_id,
      start_date,
      end_date,
      quantity,
      dynamic_price,
      check_in_time = "12:00",
      check_out_time = "12:00",
      is_noon_to_noon = true
    } = req.body;

    if (!start_date || !end_date) {
      return res.status(400).json({
        message: "Start date and end date are required"
      });
    }

    if (!room_id && !room_config_id) {
      return res.status(400).json({
        message: "Either room_id or room_config_id is required"
      });
    }

    if (quantity === undefined) {
      return res.status(400).json({
        message: "Quantity is required"
      });
    }

    let results = [];

    if (room_config_id) {
      // Bulk update for all rooms in a room configuration
      // Get all variants (rooms) for this product
      const variants = await productModuleService.listVariants({
        product_id: room_config_id
      });

      if (!variants.length) {
        return res.status(404).json({
          message: `No rooms found for room configuration ${room_config_id}`
        });
      }

      // Update inventory for each room
      for (const variant of variants) {
        try {
          // Determine status based on quantity
          const status = quantity > 0 ? 'available' : 'maintenance';
          const notes = req.body.notes || (quantity > 0 ? 'Available' : 'Maintenance');

          // Update room inventory
          const updatedEntries = await roomInventoryService.updateInventoryStatus(
            variant.id,
            start_date,
            end_date,
            status,
            notes,
            null, // bookingInfo
            null, // expiresAt
            null, // No order_id for manual inventory updates
            null  // No cart_id for manual inventory updates
          );

          results.push({
            room_id: variant.id,
            inventory_entries: updatedEntries.map(entry => entry.id),
            success: true
          });
        } catch (error) {
          console.error(`Error updating inventory for room ${variant.id}:`, error);
          results.push({
            room_id: variant.id,
            success: false,
            error: error.message
          });
        }
      }
    } else {
      // Update for a single room
      try {
        // Determine status based on quantity
        const status = quantity > 0 ? 'available' : 'maintenance';
        const notes = req.body.notes || (quantity > 0 ? 'Available' : 'Maintenance');

        // Update room inventory
        const updatedEntries = await roomInventoryService.updateInventoryStatus(
          room_id,
          start_date,
          end_date,
          status,
          notes,
          null, // bookingInfo
          null, // expiresAt
          null, // No order_id for manual inventory updates
          null  // No cart_id for manual inventory updates
        );

        results.push({
          room_id,
          inventory_entries: updatedEntries.map(entry => entry.id),
          success: true
        });
      } catch (error) {
        console.error(`Error updating inventory for room ${room_id}:`, error);
        results.push({
          room_id,
          success: false,
          error: error.message
        });
      }
    }

    return res.json({
      success: results.every(r => r.success),
      results
    });
  } catch (error) {
    console.error("Error updating inventory:", error);
    return res.status(500).json({
      message: "Error updating inventory",
      error: error.message
    });
  }
};
