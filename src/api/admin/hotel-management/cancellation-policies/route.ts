import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { RefundType } from "../../../../modules/hotel-management/cancellation-policy";
import {
  CreateCancellationPolicyWorkflow,
  ListCancellationPoliciesWorkflow
} from "../../../../workflows/hotel-management/cancellation-policy";

// Validation schema for creating a cancellation policy
export const PostAdminCreateCancellationPolicy = z.object({
  id: z.string().optional(),
  name: z.string(),
  description: z.string().nullable().optional().default(null),
  hotel_id: z.string(),
  days_before_checkin: z.number().int().min(0),
  refund_type: z.enum([RefundType.PERCENTAGE, RefundType.FIXED, RefundType.NO_REFUND]).default(RefundType.PERCENTAGE),
  refund_amount: z.number().optional(),
  is_active: z.boolean().default(true),
  metadata: z.record(z.any()).nullable().optional().default(null),
}).passthrough();

export type PostAdminCreateCancellationPolicyType = z.infer<typeof PostAdminCreateCancellationPolicy>;

// GET - List all cancellation policies
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const { hotel_id, is_active } = req.query;

  // Prepare input for the workflow
  const input: any = {};

  if (hotel_id) {
    input.hotel_id = hotel_id;
  }

  if (is_active !== undefined) {
    input.is_active = is_active === 'true';
  }

  // Use the workflow to list policies
  const { result } = await ListCancellationPoliciesWorkflow(req.scope).run({
    input
  });

  return res.json({
    cancellation_policies: result,
  });
};

// POST - Create a new cancellation policy
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateCancellationPolicyType>,
  res: MedusaResponse
) => {
  try {
    const validatedBody = req.validatedBody;

    // Validate refund amount based on refund type
    if (validatedBody.refund_type === RefundType.PERCENTAGE) {
      if (!validatedBody.refund_amount || validatedBody.refund_amount < 0 || validatedBody.refund_amount > 100) {
        return res.status(400).json({
          message: "Refund amount must be between 0 and 100 for percentage refund type",
        });
      }
    } else if (validatedBody.refund_type === RefundType.FIXED) {
      if (!validatedBody.refund_amount || validatedBody.refund_amount < 0) {
        return res.status(400).json({
          message: "Refund amount must be greater than 0 for fixed refund type",
        });
      }
    } else if (validatedBody.refund_type === RefundType.NO_REFUND) {
      // For NO_REFUND type, set refund_amount to 0
      validatedBody.refund_amount = 0;
    }

    // Use the workflow to create the policy
    // Make sure all required fields are present
    const input = {
      name: validatedBody.name,
      hotel_id: validatedBody.hotel_id,
      days_before_checkin: validatedBody.days_before_checkin,
      refund_type: validatedBody.refund_type,
      refund_amount: validatedBody.refund_amount || 0,
      // Optional fields
      id: validatedBody.id,
      description: validatedBody.description,
      is_active: validatedBody.is_active,
      metadata: validatedBody.metadata
    };

    const { result } = await CreateCancellationPolicyWorkflow(req.scope).run({
      input
    });

    return res.status(201).json({
      cancellation_policy: result,
    });
  } catch (error) {
    return res.status(400).json({
      message: "Failed to create cancellation policy",
      error: error.message,
    });
  }
};
