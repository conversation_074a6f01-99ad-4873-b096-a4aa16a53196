import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    
    // Get all destinations
    const { data: destinations } = await query.graph({
      entity: "destination",
      fields: ["id"],
    });
    
    if (!destinations || destinations.length === 0) {
      return res.json({ counts: {} });
    }
    
    // Get all hotels
    const { data: hotels } = await query.graph({
      entity: "hotel",
      fields: ["id", "destination_id"],
    });
    
    // Count hotels per destination
    const counts = {};
    
    if (hotels && hotels.length > 0) {
      destinations.forEach(destination => {
        const count = hotels.filter(hotel => hotel.destination_id === destination.id).length;
        counts[destination.id] = count;
      });
    }
    
    return res.json({ counts });
  } catch (error) {
    console.error("Error fetching hotel counts:", error);
    return res.status(500).json({
      message: "Error fetching hotel counts",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
