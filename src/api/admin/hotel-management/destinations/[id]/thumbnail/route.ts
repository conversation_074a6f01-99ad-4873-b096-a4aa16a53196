import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id
  //@ts-ignore
  const { image_id } = req.body
  
  const destinationService = req.scope.resolve(DESTINATION_MODULE)
  const thumbnailImage = await destinationService.setDestinationThumbnail(destinationId, image_id)
  
  res.status(200).json({ thumbnail: thumbnailImage })
}