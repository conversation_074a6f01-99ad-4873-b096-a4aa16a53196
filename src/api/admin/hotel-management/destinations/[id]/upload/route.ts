import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { MedusaError } from "@camped-ai/framework/utils"
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id
  //@ts-ignore
  const files = req.files as Express.Multer.File[]

  if (!files?.length) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files were uploaded"
    )
  }

  const destinationService = req.scope.resolve(DESTINATION_MODULE)
  const destinationImages = await destinationService.uploadImages(destinationId, files, req.scope)
  
  res.status(200).json({ destination_images: destinationImages })
}