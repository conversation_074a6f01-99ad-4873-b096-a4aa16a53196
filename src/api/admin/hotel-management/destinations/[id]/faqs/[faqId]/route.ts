import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { z } from "zod";

const UpdateFaqSchema = z.object({
  question: z.string().min(1, "Question is required").optional(),
  answer: z.string().min(1, "Answer is required").optional(),
});

export async function PUT(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const faqId = req.params.faqId;

  try {
    const validatedData = UpdateFaqSchema.parse(req.body);
    
    const destinationService = req.scope.resolve(DESTINATION_MODULE);
    const faq = await destinationService.updateDestinationFaq(faqId, validatedData);
    
    res.status(200).json({ 
      faq: {
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        destination_id: faq.destination_id,
        created_at: faq.created_at,
        updated_at: faq.updated_at
      }
    });
  } catch (error) {
    console.error("Error updating destination FAQ:", error);
    if (error instanceof z.ZodError) {
      res.status(400).json({ 
        message: "Validation error",
        errors: error.errors
      });
    } else {
      res.status(500).json({ 
        message: "Failed to update destination FAQ",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  }
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const faqId = req.params.faqId;

  try {
    const destinationService = req.scope.resolve(DESTINATION_MODULE);
    const result = await destinationService.deleteDestinationFaq(faqId);
    
    res.status(200).json(result);
  } catch (error) {
    console.error("Error deleting destination FAQ:", error);
    res.status(500).json({ 
      message: "Failed to delete destination FAQ",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
