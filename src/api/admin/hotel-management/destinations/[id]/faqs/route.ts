import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { z } from "zod";

const CreateFaqSchema = z.object({
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
});

const UpdateFaqSchema = z.object({
  question: z.string().min(1, "Question is required").optional(),
  answer: z.string().min(1, "Answer is required").optional(),
});

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id;

  try {
    const destinationService = req.scope.resolve(DESTINATION_MODULE);
    const faqs = await destinationService.getDestinationFaqs(destinationId);
    
    res.status(200).json({ 
      faqs: faqs.map(faq => ({
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        destination_id: faq.destination_id,
        created_at: faq.created_at,
        updated_at: faq.updated_at
      }))
    });
  } catch (error) {
    console.error("Error fetching destination FAQs:", error);
    res.status(500).json({ 
      message: "Failed to fetch destination FAQs",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id;

  try {
    const validatedData = CreateFaqSchema.parse(req.body);
    
    const destinationService = req.scope.resolve(DESTINATION_MODULE);
    const faq = await destinationService.createDestinationFaq({
      ...validatedData,
      destination_id: destinationId
    });
    
    res.status(201).json({ 
      faq: {
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
        destination_id: faq.destination_id,
        created_at: faq.created_at,
        updated_at: faq.updated_at
      }
    });
  } catch (error) {
    console.error("Error creating destination FAQ:", error);
    if (error instanceof z.ZodError) {
      res.status(400).json({ 
        message: "Validation error",
        errors: error.errors
      });
    } else {
      res.status(500).json({ 
        message: "Failed to create destination FAQ",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  }
}
