import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const imageId = req.params.id;

  const destinationService = req.scope.resolve(DESTINATION_MODULE);
  const result = await destinationService.deleteDestinationImage(imageId);

  res.status(200).json({ result });
}