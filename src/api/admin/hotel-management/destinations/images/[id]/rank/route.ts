import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const imageId = req.params.id;
  //@ts-ignore
  const { rank } = req.body;

  const destinationService = req.scope.resolve(DESTINATION_MODULE);
  const updatedImage = await destinationService.updateDestinationImageRank(imageId, rank);

  res.status(200).json({ destination_image: updatedImage });
}