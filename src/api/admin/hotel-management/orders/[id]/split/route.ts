import type { 
  MedusaRequest, 
  MedusaResponse,
} from "@camped-ai/framework"
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";

/**
 * @swagger
 * /admin/hotel-management/orders/{id}/split:
 *   post:
 *     summary: Split a booking into two parts
 *     description: Split a booking at a specific date, creating two separate bookings
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the order/booking to split
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - split_date
 *             properties:
 *               split_date:
 *                 type: string
 *                 format: date
 *                 description: The date to split the booking (check-out from first part, check-in to second part)
 *               keep_first_part:
 *                 type: boolean
 *                 description: If true, keep first part in current room and move second part to unallocated
 *     responses:
 *       200:
 *         description: Successfully split the booking
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 first_part_id:
 *                   type: string
 *                 second_part_id:
 *                   type: string
 *       400:
 *         description: Bad request
 *       404:
 *         description: Order not found
 */

// Validation schema for request body
export const PostAdminOrderSplitBooking = z.object({
  split_date: z.string().refine((date) => {
    // Validate date format (YYYY-MM-DD)
    return /^\d{4}-\d{2}-\d{2}$/.test(date);
  }, {
    message: "Invalid date format. Use YYYY-MM-DD",
  }),
  keep_first_part: z.boolean().default(true),
});

export type PostAdminOrderSplitBookingType = z.infer<typeof PostAdminOrderSplitBooking>;

export const POST = async (
  req: MedusaRequest<PostAdminOrderSplitBookingType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    
    // Validate request body
    const { split_date, keep_first_part } = req.body;

    // Get services
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");

    // Get the original order
    const order = await orderService.retrieveOrder(id, {
      relations: ["items"],
    });

    if (!order) {
      return res.status(404).json({
        message: "Order not found"
      });
    }

    // Check if order has metadata with reservations
    if (!order.metadata?.reservations) {
      return res.status(400).json({
        message: "Order does not have reservation information"
      });
    }

    // Get reservation details
    const reservations = order.metadata.reservations;
    if (!Array.isArray(reservations) || reservations.length === 0) {
      return res.status(400).json({
        message: "No reservations found in order"
      });
    }

    // Validate split date is within reservation period
    const splitDateObj = new Date(split_date);
    let validSplit = false;
    console.log("Split date object:", splitDateObj);
    
    for (const reservation of reservations) {
      const fromDate = new Date(reservation.from_date);
      const toDate = new Date(reservation.to_date);
      
      // Check if split date is between from_date and to_date
      if (splitDateObj > fromDate && splitDateObj < toDate) {
        validSplit = true;
        break;
      }
    }

    if (!validSplit) {
      return res.status(400).json({
        message: "Split date must be between check-in and check-out dates"
      });
    }

    // Split the booking using the room inventory service
    const result = await roomInventoryService.splitBooking(
      id,
      split_date,
      keep_first_part
    );

    return res.json({
      message: "Booking split successfully",
      order_id: id,
      first_part_id: result.first_part_id,
      second_part_id: result.second_part_id,
      split_date: split_date
    });
  } catch (error) {
    console.error("Error splitting booking:", error);
    
    return res.status(500).json({
      message: error.message || "An unexpected error occurred"
    });
  }
};
