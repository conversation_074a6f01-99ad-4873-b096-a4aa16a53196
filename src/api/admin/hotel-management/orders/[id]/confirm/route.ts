import type {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework"
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";

/**
 * @swagger
 * /admin/hotel-management/orders/{id}/confirm:
 *   post:
 *     summary: Confirm a booking
 *     description: Change a booking status from reserved to booked
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the order/booking to confirm
 *     responses:
 *       200:
 *         description: Successfully confirmed the booking
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 order_id:
 *                   type: string
 *       400:
 *         description: Bad request
 *       404:
 *         description: Order not found
 */

export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    // Get services
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");

    // Get the original order
    const order = await orderService.retrieveOrder(id, {
      relations: ["items"],
    });

    if (!order) {
      return res.status(404).json({
        message: "Order not found"
      });
    }

    // Check if order has metadata with reservations
    if (!order.metadata?.reservations) {
      return res.status(400).json({
        message: "Order does not have reservation information"
      });
    }

    // Get reservation details
    const reservations = order.metadata.reservations;
    if (!Array.isArray(reservations) || reservations.length === 0) {
      return res.status(400).json({
        message: "No reservations found in order"
      });
    }

    // Check if any reservation has a room_id
    const reservationsWithRooms = reservations.filter(reservation => reservation.room_id);
    if (reservationsWithRooms.length === 0) {
      return res.status(400).json({
        message: "No rooms assigned to this booking. Please assign a room first."
      });
    }

    // Update each reservation status to booked
    const updatedReservations = reservations.map(reservation => {
      if (reservation.room_id) {
        return {
          ...reservation,
          status: "booked"
        };
      }
      return reservation;
    });

    // Update the order metadata
    const updatedMetadata = {
      ...order.metadata,
      reservations: updatedReservations,
      confirmed_at: new Date().toISOString(),
      room_id: reservationsWithRooms[0].room_id,
      status: "booked"
    };

    // Update the order
    await orderService.updateOrders(id, {
      metadata: updatedMetadata
    });

    // Update room inventory status for each reservation with a room
    for (const reservation of reservationsWithRooms) {
      await roomInventoryService.updateInventoryStatus(
        reservation.room_id,
        new Date(reservation.from_date),
        new Date(reservation.to_date),
        "booked",
        `Booking confirmed: ${id}`,
        null, // bookingInfo
        null, // expiresAt
        id,   // Set the order_id
        null  // No cart_id for confirmed bookings
      );
    }

    return res.json({
      message: "Booking confirmed successfully",
      order_id: id
    });
  } catch (error) {
    console.error("Error confirming booking:", error);

    return res.status(500).json({
      message: error.message || "An unexpected error occurred"
    });
  }
};
