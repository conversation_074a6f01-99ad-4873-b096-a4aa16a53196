import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get tasks for a specific assignment
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const assignmentId = req.params.id;
    
    // Get tasks for the assignment
    const tasks = await housekeepingService.getTasksForAssignment(
      assignmentId,
      {
        limit: 100,
        order: { created_at: "DESC" }
      }
    );
    
    return res.json({ tasks });
  } catch (error) {
    console.error("Error retrieving tasks for assignment:", error);
    return res.status(500).json({ 
      message: "Error retrieving tasks for assignment", 
      error: error.message 
    });
  }
};
