import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get a specific checklist with its items
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);

    const checklistId = req.params.id;

    const checklist = await housekeepingService.retrieveHousekeepingChecklist(checklistId);

    if (!checklist) {
      return res.status(404).json({ message: `Checklist with ID ${checklistId} not found` });
    }

    // Get checklist items
    const items = await housekeepingService.getChecklistItems(
      checklistId,
      { order: { order: "ASC" } }
    );

    return res.json({
      checklist: {
        ...checklist,
        items,
      }
    });
  } catch (error) {
    console.error("Error retrieving checklist:", error);
    return res.status(500).json({
      message: "Error retrieving checklist",
      error: error.message
    });
  }
};
