import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for checklist item
const ChecklistItemSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  order: z.number().default(0),
  is_required: z.boolean().default(true),
  metadata: z.record(z.any()).optional(),
});

// Validation schema for creating a housekeeping checklist
export const PostAdminCreateHousekeepingChecklist = z.object({
  name: z.string(),
  description: z.string().optional(),
  hotel_id: z.string(),
  room_config_id: z.string().optional(),
  task_type: z.string().default("cleaning"),
  is_active: z.boolean().default(true),
  items: z.array(ChecklistItemSchema).optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminCreateHousekeepingChecklistType = z.infer<typeof PostAdminCreateHousekeepingChecklist>;

// Validation schema for updating a housekeeping checklist
export const PostAdminUpdateHousekeepingChecklist = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  room_config_id: z.string().optional(),
  task_type: z.string().optional(),
  is_active: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminUpdateHousekeepingChecklistType = z.infer<typeof PostAdminUpdateHousekeepingChecklist>;

// GET endpoint to list housekeeping checklists
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);

    // Extract query parameters
    const {
      hotel_id,
      room_config_id,
      task_type,
      is_active,
      limit = 100,
      offset = 0
    } = req.query;

    // Build filters
    const filters: Record<string, any> = {};

    if (hotel_id) filters.hotel_id = hotel_id;
    if (room_config_id) filters.room_config_id = room_config_id;
    if (task_type) filters.task_type = task_type;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    // Get checklists
    const checklists = await housekeepingService.listHousekeepingChecklists(
      filters,
      {
        limit: Number(limit),
        offset: Number(offset),
        order: { created_at: "DESC" }
      }
    );

    // Get count
    const count = await housekeepingService.countHousekeepingChecklists(filters);

    // For each checklist, get its items
    const checklistsWithItems = await Promise.all(
      checklists.map(async (checklist) => {
        const items = await housekeepingService.getChecklistItems(
          checklist.id,
          { order: { order: "ASC" } }
        );
        return { ...checklist, items };
      })
    );

    return res.json({
      checklists: checklistsWithItems,
      count,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    console.error("Error listing housekeeping checklists:", error);
    return res.status(500).json({
      message: "Error listing housekeeping checklists",
      error: error.message
    });
  }
};

// POST endpoint to create a housekeeping checklist
export const POST = async (
  req: MedusaRequest<PostAdminCreateHousekeepingChecklistType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);

    const checklist = await housekeepingService.createHousekeepingChecklist(req.body);

    // Get the checklist with its items
    const items = await housekeepingService.getChecklistItems(
      checklist.id,
      { order: { order: "ASC" } }
    );

    return res.json({ checklist: { ...checklist, items } });
  } catch (error) {
    console.error("Error creating housekeeping checklist:", error);
    return res.status(500).json({
      message: "Error creating housekeeping checklist",
      error: error.message
    });
  }
};

// PUT endpoint to update a housekeeping checklist
export const PUT = async (
  req: MedusaRequest<PostAdminUpdateHousekeepingChecklistType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);

    const { id, ...updateData } = req.body;

    const checklist = await housekeepingService.updateHousekeepingChecklist(id, updateData);

    // Get the checklist with its items
    const items = await housekeepingService.getChecklistItems(
      checklist.id,
      { order: { order: "ASC" } }
    );

    return res.json({ checklist: { ...checklist, items } });
  } catch (error) {
    console.error("Error updating housekeeping checklist:", error);
    return res.status(500).json({
      message: "Error updating housekeeping checklist",
      error: error.message
    });
  }
};

// DELETE endpoint to delete housekeeping checklists
export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);

    const { ids } = req.body as { ids: string[] };

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: "Checklist IDs are required" });
    }

    await Promise.all(ids.map(id => housekeepingService.deleteHousekeepingChecklist(id)));

    return res.json({ success: true, ids });
  } catch (error) {
    console.error("Error deleting housekeeping checklists:", error);
    return res.status(500).json({
      message: "Error deleting housekeeping checklists",
      error: error.message
    });
  }
};
