import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for generating housekeeping tasks
export const PostAdminGenerateHousekeepingTasks = z.object({
  hotel_id: z.string(),
  date: z.string().or(z.date()).optional(),
});

export type PostAdminGenerateHousekeepingTasksType = z.infer<typeof PostAdminGenerateHousekeepingTasks>;

// POST endpoint to generate housekeeping tasks for a hotel
export const POST = async (
  req: MedusaRequest<PostAdminGenerateHousekeepingTasksType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { hotel_id, date } = req.body;
    
    const tasks = await housekeepingService.generateHousekeepingTasks(hotel_id, date ? new Date(date) : new Date());
    
    return res.json({ 
      tasks,
      count: tasks.length,
      message: `Generated ${tasks.length} housekeeping tasks for hotel ${hotel_id}`
    });
  } catch (error) {
    console.error("Error generating housekeeping tasks:", error);
    return res.status(500).json({ 
      message: "Error generating housekeeping tasks", 
      error: error.message 
    });
  }
};
