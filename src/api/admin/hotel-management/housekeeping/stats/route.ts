import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get housekeeping statistics for a hotel
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { hotel_id, date } = req.query;
    
    if (!hotel_id) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }
    
    const stats = await housekeepingService.getHousekeepingStats(
      hotel_id as string,
      date ? new Date(date as string) : new Date()
    );
    
    return res.json({ stats });
  } catch (error) {
    console.error("Error getting housekeeping statistics:", error);
    return res.status(500).json({ 
      message: "Error getting housekeeping statistics", 
      error: error.message 
    });
  }
};
