import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";
import { 
  HousekeepingTaskStatus, 
  HousekeepingTaskPriority, 
  RoomCleaningStatus 
} from "src/modules/hotel-management/housekeeping/models/housekeeping-task";

// Validation schema for creating a housekeeping task
export const PostAdminCreateHousekeepingTask = z.object({
  room_id: z.string(),
  room_config_id: z.string(),
  hotel_id: z.string(),
  task_type: z.string().default("cleaning"),
  status: z.enum([
    HousekeepingTaskStatus.PENDING,
    HousekeepingTaskStatus.IN_PROGRESS,
    HousekeepingTaskStatus.COMPLETED,
    HousekeepingTaskStatus.VERIFIED,
    HousekeepingTaskStatus.CANCELLED
  ]).default(HousekeepingTaskStatus.PENDING),
  priority: z.enum([
    HousekeepingTaskPriority.LOW,
    HousekeepingTaskPriority.MEDIUM,
    HousekeepingTaskPriority.HIGH,
    HousekeepingTaskPriority.URGENT
  ]).default(HousekeepingTaskPriority.MEDIUM),
  room_status: z.enum([
    RoomCleaningStatus.DIRTY,
    RoomCleaningStatus.CLEANING,
    RoomCleaningStatus.CLEAN,
    RoomCleaningStatus.INSPECTED,
    RoomCleaningStatus.OUT_OF_SERVICE
  ]).default(RoomCleaningStatus.DIRTY),
  scheduled_date: z.string().or(z.date()),
  due_time: z.string().optional(),
  assigned_to: z.string().optional(),
  notes: z.string().optional(),
  guest_request: z.boolean().default(false),
  recurring: z.boolean().default(false),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminCreateHousekeepingTaskType = z.infer<typeof PostAdminCreateHousekeepingTask>;

// Validation schema for updating a housekeeping task
export const PostAdminUpdateHousekeepingTask = z.object({
  id: z.string(),
  status: z.enum([
    HousekeepingTaskStatus.PENDING,
    HousekeepingTaskStatus.IN_PROGRESS,
    HousekeepingTaskStatus.COMPLETED,
    HousekeepingTaskStatus.VERIFIED,
    HousekeepingTaskStatus.CANCELLED
  ]).optional(),
  priority: z.enum([
    HousekeepingTaskPriority.LOW,
    HousekeepingTaskPriority.MEDIUM,
    HousekeepingTaskPriority.HIGH,
    HousekeepingTaskPriority.URGENT
  ]).optional(),
  room_status: z.enum([
    RoomCleaningStatus.DIRTY,
    RoomCleaningStatus.CLEANING,
    RoomCleaningStatus.CLEAN,
    RoomCleaningStatus.INSPECTED,
    RoomCleaningStatus.OUT_OF_SERVICE
  ]).optional(),
  scheduled_date: z.string().or(z.date()).optional(),
  due_time: z.string().optional(),
  assigned_to: z.string().optional(),
  notes: z.string().optional(),
  guest_request: z.boolean().optional(),
  recurring: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminUpdateHousekeepingTaskType = z.infer<typeof PostAdminUpdateHousekeepingTask>;

// GET endpoint to list housekeeping tasks
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    // Extract query parameters
    const { 
      hotel_id, 
      room_id, 
      status, 
      priority,
      room_status,
      scheduled_date,
      assigned_to,
      limit = 100,
      offset = 0
    } = req.query;
    
    // Build filters
    const filters: Record<string, any> = {};
    
    if (hotel_id) filters.hotel_id = hotel_id;
    if (room_id) filters.room_id = room_id;
    if (status) filters.status = status;
    if (priority) filters.priority = priority;
    if (room_status) filters.room_status = room_status;
    if (scheduled_date) filters.scheduled_date = scheduled_date;
    if (assigned_to) filters.assigned_to = assigned_to;
    
    // Get tasks
    const tasks = await housekeepingService.listHousekeepingTasks(
      filters,
      {
        limit: Number(limit),
        offset: Number(offset),
        order: { scheduled_date: "DESC", created_at: "DESC" }
      }
    );
    
    // Get count
    const count = await housekeepingService.countHousekeepingTasks(filters);
    
    return res.json({
      tasks,
      count,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    console.error("Error listing housekeeping tasks:", error);
    return res.status(500).json({ 
      message: "Error listing housekeeping tasks", 
      error: error.message 
    });
  }
};

// POST endpoint to create a housekeeping task
export const POST = async (
  req: MedusaRequest<PostAdminCreateHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const task = await housekeepingService.createHousekeepingTask(req.body);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error creating housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error creating housekeeping task", 
      error: error.message 
    });
  }
};

// PUT endpoint to update a housekeeping task
export const PUT = async (
  req: MedusaRequest<PostAdminUpdateHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { id, ...updateData } = req.body;
    
    const task = await housekeepingService.updateHousekeepingTask(id, updateData);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error updating housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error updating housekeeping task", 
      error: error.message 
    });
  }
};

// DELETE endpoint to delete housekeeping tasks
export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { ids } = req.body as { ids: string[] };
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: "Task IDs are required" });
    }
    
    await Promise.all(ids.map(id => housekeepingService.deleteHousekeepingTask(id)));
    
    return res.json({ success: true, ids });
  } catch (error) {
    console.error("Error deleting housekeeping tasks:", error);
    return res.status(500).json({ 
      message: "Error deleting housekeeping tasks", 
      error: error.message 
    });
  }
};
