import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for starting a task
export const PostAdminStartHousekeepingTask = z.object({
  staff_id: z.string(),
});

export type PostAdminStartHousekeepingTaskType = z.infer<typeof PostAdminStartHousekeepingTask>;

// POST endpoint to start a housekeeping task
export const POST = async (
  req: MedusaRequest<PostAdminStartHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    const { staff_id } = req.body;
    
    const task = await housekeepingService.startTask(taskId, staff_id);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error starting housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error starting housekeeping task", 
      error: error.message 
    });
  }
};
