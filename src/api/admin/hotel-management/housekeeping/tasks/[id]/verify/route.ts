import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for verifying a task
export const PostAdminVerifyHousekeepingTask = z.object({
  supervisor_id: z.string(),
  notes: z.string().optional(),
});

export type PostAdminVerifyHousekeepingTaskType = z.infer<typeof PostAdminVerifyHousekeepingTask>;

// POST endpoint to verify a housekeeping task
export const POST = async (
  req: MedusaRequest<PostAdminVerifyHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    const { supervisor_id, notes } = req.body;
    
    const task = await housekeepingService.verifyTask(taskId, supervisor_id, { notes });
    
    return res.json({ task });
  } catch (error) {
    console.error("Error verifying housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error verifying housekeeping task", 
      error: error.message 
    });
  }
};
