import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for completing a task
export const PostAdminCompleteHousekeepingTask = z.object({
  staff_id: z.string(),
  duration_minutes: z.number().optional(),
  checklist_id: z.string().optional(),
  completed_items: z.array(z.string()).optional(),
  skipped_items: z.array(z.string()).optional(),
  issues_found: z.boolean().optional(),
  issue_description: z.string().optional(),
  requires_maintenance: z.boolean().optional(),
  notes: z.string().optional(),
  photos: z.array(z.string()).optional(),
});

export type PostAdminCompleteHousekeepingTaskType = z.infer<typeof PostAdminCompleteHousekeepingTask>;

// POST endpoint to complete a housekeeping task
export const POST = async (
  req: MedusaRequest<PostAdminCompleteHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    const { staff_id, ...completionData } = req.body;
    
    const task = await housekeepingService.completeTask(taskId, staff_id, completionData);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error completing housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error completing housekeeping task", 
      error: error.message 
    });
  }
};
