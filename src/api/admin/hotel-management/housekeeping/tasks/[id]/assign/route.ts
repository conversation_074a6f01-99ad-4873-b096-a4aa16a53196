import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for assigning a task
export const PostAdminAssignHousekeepingTask = z.object({
  staff_id: z.string(),
});

export type PostAdminAssignHousekeepingTaskType = z.infer<typeof PostAdminAssignHousekeepingTask>;

// POST endpoint to assign a housekeeping task to a staff member
export const POST = async (
  req: MedusaRequest<PostAdminAssignHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    const { staff_id } = req.body;
    
    const task = await housekeepingService.assignTask(taskId, staff_id);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error assigning housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error assigning housekeeping task", 
      error: error.message 
    });
  }
};
