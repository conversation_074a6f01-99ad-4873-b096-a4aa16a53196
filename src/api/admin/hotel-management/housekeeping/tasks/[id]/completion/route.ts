import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get task completion details
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    
    // Get task completion
    const completions = await housekeepingService.listHousekeepingTaskCompletions(
      { task_id: taskId },
      { limit: 1 }
    );
    
    const completion = completions.length > 0 ? completions[0] : null;
    
    return res.json({ completion });
  } catch (error) {
    console.error("Error retrieving task completion:", error);
    return res.status(500).json({ 
      message: "Error retrieving task completion", 
      error: error.message 
    });
  }
};
