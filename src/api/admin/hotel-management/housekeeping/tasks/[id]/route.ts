import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get a specific housekeeping task
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    
    const task = await housekeepingService.retrieveHousekeepingTask(taskId);
    
    if (!task) {
      return res.status(404).json({ message: `Task with ID ${taskId} not found` });
    }
    
    return res.json({ task });
  } catch (error) {
    console.error("Error retrieving housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error retrieving housekeeping task", 
      error: error.message 
    });
  }
};
