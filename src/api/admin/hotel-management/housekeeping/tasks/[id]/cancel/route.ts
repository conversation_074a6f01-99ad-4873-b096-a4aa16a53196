import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// Validation schema for cancelling a task
export const PostAdminCancelHousekeepingTask = z.object({
  reason: z.string().optional(),
});

export type PostAdminCancelHousekeepingTaskType = z.infer<typeof PostAdminCancelHousekeepingTask>;

// POST endpoint to cancel a housekeeping task
export const POST = async (
  req: MedusaRequest<PostAdminCancelHousekeepingTaskType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const taskId = req.params.id;
    const { reason } = req.body;
    
    const task = await housekeepingService.cancelTask(taskId, reason);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error cancelling housekeeping task:", error);
    return res.status(500).json({ 
      message: "Error cancelling housekeeping task", 
      error: error.message 
    });
  }
};
