import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";
import { StaffRole } from "src/modules/hotel-management/housekeeping/models/housekeeping-staff";

// Validation schema for creating a housekeeping staff
export const PostAdminCreateHousekeepingStaff = z.object({
  name: z.string(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  role: z.enum([
    StaffRole.HOUSEKEEPER,
    StaffRole.SUPERVISOR,
    StaffRole.MANAGER,
    StaffRole.MAINTENANCE
  ]).default(StaffRole.HOUSEKEEPER),
  hotel_id: z.string(),
  is_active: z.boolean().default(true),
  max_rooms_per_shift: z.number().default(15),
  working_days: z.array(z.string()).default(["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]),
  shift: z.string().default("morning"),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminCreateHousekeepingStaffType = z.infer<typeof PostAdminCreateHousekeepingStaff>;

// Validation schema for updating a housekeeping staff
export const PostAdminUpdateHousekeepingStaff = z.object({
  id: z.string(),
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  role: z.enum([
    StaffRole.HOUSEKEEPER,
    StaffRole.SUPERVISOR,
    StaffRole.MANAGER,
    StaffRole.MAINTENANCE
  ]).optional(),
  is_active: z.boolean().optional(),
  max_rooms_per_shift: z.number().optional(),
  working_days: z.array(z.string()).optional(),
  shift: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PostAdminUpdateHousekeepingStaffType = z.infer<typeof PostAdminUpdateHousekeepingStaff>;

// GET endpoint to list housekeeping staff
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    // Extract query parameters
    const { 
      hotel_id, 
      role, 
      is_active,
      limit = 100,
      offset = 0
    } = req.query;
    
    // Build filters
    const filters: Record<string, any> = {};
    
    if (hotel_id) filters.hotel_id = hotel_id;
    if (role) filters.role = role;
    if (is_active !== undefined) filters.is_active = is_active === 'true';
    
    // Get staff
    const staff = await housekeepingService.listHousekeepingStaffs(
      filters,
      {
        limit: Number(limit),
        offset: Number(offset),
        order: { created_at: "DESC" }
      }
    );
    
    // Get count
    const count = await housekeepingService.countHousekeepingStaffs(filters);
    
    return res.json({
      staff,
      count,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    console.error("Error listing housekeeping staff:", error);
    return res.status(500).json({ 
      message: "Error listing housekeeping staff", 
      error: error.message 
    });
  }
};

// POST endpoint to create a housekeeping staff
export const POST = async (
  req: MedusaRequest<PostAdminCreateHousekeepingStaffType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const staff = await housekeepingService.createHousekeepingStaff(req.body);
    
    return res.json({ staff });
  } catch (error) {
    console.error("Error creating housekeeping staff:", error);
    return res.status(500).json({ 
      message: "Error creating housekeeping staff", 
      error: error.message 
    });
  }
};

// PUT endpoint to update a housekeeping staff
export const PUT = async (
  req: MedusaRequest<PostAdminUpdateHousekeepingStaffType>,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { id, ...updateData } = req.body;
    
    const staff = await housekeepingService.updateHousekeepingStaff(id, updateData);
    
    return res.json({ staff });
  } catch (error) {
    console.error("Error updating housekeeping staff:", error);
    return res.status(500).json({ 
      message: "Error updating housekeeping staff", 
      error: error.message 
    });
  }
};

// DELETE endpoint to delete housekeeping staff
export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const { ids } = req.body as { ids: string[] };
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: "Staff IDs are required" });
    }
    
    await Promise.all(ids.map(id => housekeepingService.deleteHousekeepingStaff(id)));
    
    return res.json({ success: true, ids });
  } catch (error) {
    console.error("Error deleting housekeeping staff:", error);
    return res.status(500).json({ 
      message: "Error deleting housekeeping staff", 
      error: error.message 
    });
  }
};
