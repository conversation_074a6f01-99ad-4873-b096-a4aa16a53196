import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get a specific staff member
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const staffId = req.params.id;
    
    const staff = await housekeepingService.retrieveHousekeepingStaff(staffId);
    
    if (!staff) {
      return res.status(404).json({ message: `Staff with ID ${staffId} not found` });
    }
    
    return res.json({ staff });
  } catch (error) {
    console.error("Error retrieving staff member:", error);
    return res.status(500).json({ 
      message: "Error retrieving staff member", 
      error: error.message 
    });
  }
};
