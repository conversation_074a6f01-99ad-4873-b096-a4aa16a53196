import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { HOUSEKEEPING_MODULE } from "src/modules/hotel-management/housekeeping";

// GET endpoint to get assignments for a specific staff member
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const housekeepingService = req.scope.resolve(HOUSEKEEPING_MODULE);
    
    const staffId = req.params.id;
    
    // Get assignments for the staff member
    const assignments = await housekeepingService.getAssignmentsForStaff(
      staffId,
      {
        limit: 100,
        order: { date: "DESC" }
      }
    );
    
    return res.json({ assignments });
  } catch (error) {
    console.error("Error retrieving assignments for staff:", error);
    return res.status(500).json({ 
      message: "Error retrieving assignments for staff", 
      error: error.message 
    });
  }
};
