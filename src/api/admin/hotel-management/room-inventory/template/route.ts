import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';
import { format, addDays } from "date-fns";
import { Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

/**
 * GET endpoint to download a template for bulk room inventory import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for room inventory
    const worksheet = workbook.addWorksheet('Room Inventory');

    // Define columns
    worksheet.columns = [
      { header: 'inventory_item_id', key: 'inventory_item_id', width: 40 },
      { header: 'from_date', key: 'from_date', width: 15 },
      { header: 'to_date', key: 'to_date', width: 15 },
      { header: 'available_quantity', key: 'available_quantity', width: 15 },
      { header: 'status', key: 'status', width: 15 },
      { header: 'notes', key: 'notes', width: 40 },
      { header: 'check_in_time', key: 'check_in_time', width: 15 },
      { header: 'check_out_time', key: 'check_out_time', width: 15 },
      { header: 'is_noon_to_noon', key: 'is_noon_to_noon', width: 15 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Initialize arrays
    let rooms = [];
    let inventoryItems = [];

    // Get all rooms and their inventory items
    try {
      // Get product module service
      const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

      // First, get all product variants (rooms)
      const variantsResult = await productModuleService.listProductVariants({}, );
      console.log(variantsResult);

      rooms = variantsResult;

      // Then, get all inventory items
      const query = req.scope.resolve("query");
      const { data: inventoryItemsData } = await query.graph({
        entity: "inventory_item",
        filters: {},
        fields: ["id", "variant_link"],
      });

      if (inventoryItemsData && inventoryItemsData.length > 0) {
        inventoryItems = inventoryItemsData;
        console.log(`Found ${inventoryItems.length} inventory items`);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }

    // Add a reference sheet for rooms
    const roomSheet = workbook.addWorksheet('Rooms Reference');

    // Define columns for the room reference sheet
    roomSheet.columns = [
      { header: 'inventory_item_id', key: 'inventory_item_id', width: 40 },
      { header: 'room_number', key: 'room_number', width: 15 },
      { header: 'floor', key: 'floor', width: 10 },
      { header: 'hotel_id', key: 'hotel_id', width: 40 },
    ];

    // Style the header row
    roomSheet.getRow(1).font = { bold: true };
    roomSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add room data to the reference sheet - directly use all rooms without checking for inventory items
    let roomsAdded = 0;

    // Just list all rooms directly
    for (const room of rooms) {
      roomSheet.addRow({
        inventory_item_id: room.id, // Use the room ID as the inventory item ID
        room_number: room.metadata?.room_number || room.title || `Room ${roomsAdded + 1}`,
        floor: room.metadata?.floor || '',
        hotel_id: room.metadata?.hotel_id || ''
      });
      roomsAdded++;
    }

    console.log(`Added ${roomsAdded} rooms to the reference sheet`);

    // If no rooms were found, add a sample one
    if (roomsAdded === 0) {
      const sampleRoomId = 'sample-room-id';
      roomSheet.addRow({
        inventory_item_id: sampleRoomId,
        room_number: '101',
        floor: '1',
        hotel_id: 'sample-hotel-id'
      });
      console.log('Added a sample room for demonstration');
    }

    // Add a sample row to the room inventory sheet
    // Get a sample inventory item ID - use the first room's ID if available
    let sampleInventoryId = 'sample-inventory-item-id';

    // Use the first room's ID if available
    if (rooms.length > 0) {
      sampleInventoryId = rooms[0].id;
    }

    // Add a single sample row
    const today = new Date();
    const tomorrow = addDays(today, 1);

    worksheet.addRow({
      inventory_item_id: sampleInventoryId,
      from_date: format(today, 'yyyy-MM-dd'), // Use ISO format YYYY-MM-DD
      to_date: format(tomorrow, 'yyyy-MM-dd'), // Use ISO format YYYY-MM-DD
      available_quantity: 1,
      status: 'available',
      notes: 'Sample inventory record',
      check_in_time: '14:00',
      check_out_time: '11:00',
      is_noon_to_noon: 'false',
    });

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add instructions for each field
    const instructions = [
      { field: 'inventory_item_id', description: 'The ID of the inventory item (see Rooms Reference sheet)', required: 'Yes', format: 'Text (UUID)' },
      { field: 'from_date', description: 'The start date of the inventory period. Must be in ISO format.', required: 'Yes', format: 'YYYY-MM-DD (e.g., 2023-12-31)' },
      { field: 'to_date', description: 'The end date of the inventory period. Must be in ISO format.', required: 'Yes', format: 'YYYY-MM-DD (e.g., 2024-01-01)' },
      { field: 'available_quantity', description: 'The number of rooms available (usually 0 or 1 for individual rooms)', required: 'Yes', format: 'Number' },
      { field: 'status', description: 'The status of the room during this period', required: 'No', format: 'Text (available, maintenance, reserved, booked, cleaning.)' },
      { field: 'notes', description: 'Additional notes about the inventory period', required: 'No', format: 'Text' },
      { field: 'check_in_time', description: 'The check-in time for this period', required: 'No', format: 'HH:MM (24-hour format)' },
      { field: 'check_out_time', description: 'The check-out time for this period', required: 'No', format: 'HH:MM (24-hour format)' },
      { field: 'is_noon_to_noon', description: 'Whether the check-in/check-out is noon to noon', required: 'No', format: 'true or false' },
    ];

    // Add a note about date formats
    instructionsSheet.addRow({
      field: 'IMPORTANT NOTE',
      description: 'Date fields must be in YYYY-MM-DD format (e.g., 2023-12-31). Excel may automatically format dates differently when you edit them. To ensure proper formatting, you may need to format the date cells as "Text" before entering dates, or use custom date formatting in Excel.',
      required: '',
      format: ''
    });

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=room-inventory-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};
