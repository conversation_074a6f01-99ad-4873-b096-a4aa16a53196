import { z } from "zod";

export const PostAdminCreateOrUpdateRoomInventory = z.object({
  id: z.string().optional(),
  inventory_item_id: z.string(),
  from_date: z.string().or(z.date()),
  to_date: z.string().or(z.date()),
  notes: z.string().optional(),
  is_available: z.boolean().default(true),
  available_quantity: z.number().default(1),
  check_in_time: z.string().default("12:00"),
  check_out_time: z.string().default("12:00"),
  is_noon_to_noon: z.boolean().default(true),
  dynamic_price: z.number().optional(),
});
