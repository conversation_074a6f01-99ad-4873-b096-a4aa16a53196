import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const hotelId = req.params.id;

  if (!hotelId) {
    return res.status(400).json({ message: "Hotel ID is required" });
  }

  try {
    // First, get all rooms for this hotel
    const { data: rooms } = await query.graph({
      entity: "product_variant",
      filters: { "product.metadata.hotel_id": hotelId },
      fields: [
        "id",
        "title",
        "metadata",
        "product.id",
        "product.title",
        "product.metadata"
      ],
    });

    if (!rooms || rooms.length === 0) {
      return res.json({ roomInventory: [] });
    }

    console.log(`Found ${rooms.length} rooms for hotel ${hotelId}`);

    // Get inventory item IDs for all rooms
    const roomIds = rooms.map(room => room.id);

    // Get all inventory items for these rooms
    const { data: inventoryItems } = await query.graph({
      entity: "inventory_item",
      filters: { variant_id: roomIds },
      fields: ["id", "variant_id"],
    });

    if (!inventoryItems || inventoryItems.length === 0) {
      console.log(`No inventory items found for rooms in hotel ${hotelId}`);
      return res.json({ roomInventory: [] });
    }

    console.log(`Found ${inventoryItems.length} inventory items for rooms in hotel ${hotelId}`);

    // Create a map of room ID to inventory item ID
    const roomToInventoryMap = {};
    inventoryItems.forEach(item => {
      roomToInventoryMap[item.variant_id] = item.id;
    });

    // Get inventory for all inventory items
    const inventoryItemIds = inventoryItems.map(item => item.id);

    const { data: roomInventory } = await query.graph({
      entity: "room_inventory",
      filters: { inventory_item_id: inventoryItemIds },
      fields: [
        "id",
        "inventory_item_id",
        "from_date",
        "to_date",
        "available_quantity",
        "status",
        "notes",
        "check_in_time",
        "check_out_time",
        "is_noon_to_noon",
        "order_id",
        "cart_id"
      ],
    });

    // Enhance room inventory with room information
    const enhancedRoomInventory = roomInventory.map(inventory => {
      // Find the room for this inventory
      const roomId = Object.keys(roomToInventoryMap).find(
        key => roomToInventoryMap[key] === inventory.inventory_item_id
      );

      const room = rooms.find(r => r.id === roomId);

      return {
        ...inventory,
        room_id: roomId,
        room_name: room?.title || "",
        room_number: room?.metadata?.room_number || "",
        floor: room?.metadata?.floor || "",
        room_config_id: room?.product?.id || "",
        room_config_name: room?.product?.title || "",
      };
    });

    res.json({
      roomInventory: enhancedRoomInventory,
    });
  } catch (error) {
    console.error("Error fetching room inventory for hotel:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to fetch room inventory",
    });
  }
};
