import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
 import { Modules } from "@camped-ai/framework/utils";
 import { CreateOrUpdateRoomInventoryWorkflow } from "src/workflows/hotel-management/room-inventory/upsert-room-inventory";

 /**
  * Bulk update room availability for a specific room or room configuration
  */
 type BulkUpdateRequest = {
   room_id?: string;
   room_config_id?: string;
   start_date: string | Date;
   end_date: string | Date;
   status: string;
   metadata?: Record<string, any>;
 };

 export const POST = async (req: MedusaRequest<BulkUpdateRequest>, res: MedusaResponse) => {
   try {
     // Log that we're using direct database access for room availability management
     console.log("Using direct database access for room availability management");

     const {
       room_id,
       room_config_id,
       start_date,
       end_date,
       status,
       metadata
     } = req.body;

     if (!start_date || !end_date || !status) {
       return res.status(400).json({
         message: "Start date, end date, and status are required"
       });
     }

     if (!room_id && !room_config_id) {
       return res.status(400).json({
         message: "Either room_id or room_config_id is required"
       });
     }

     // Convert status to quantity (0 = unavailable, 1 = available)
     let quantity = 0;
     if (status === "available") {
       quantity = 1;
     } else if (status === "maintenance") {
       // For maintenance, we set quantity to 0 but keep track of the status
       quantity = 0;
     } else if (status === "booked") {
       // For booked, we set quantity to 0
       quantity = 0;
     } else if (status === "reserved") {
       // For reserved, we set quantity to 0
       quantity = 0;
     } else if (status === "reserved_unassigned") {
       // For reserved_unassigned, we set quantity to 0
       quantity = 0;
     } else if (status === "cart_reserved") {
       // For cart_reserved, we set quantity to 0
       quantity = 0;
     } else if (status === "unavailable") {
       // For unavailable, we set quantity to 0
       quantity = 0;
     }
     let results: any[] = [];

     // Get the product module service
     let productModuleService: any;

     try {
       // Get the product module service from the container
       productModuleService = req.scope.resolve(Modules.PRODUCT);
       console.log("Successfully got product module service");
     } catch (error) {
       console.error("Error getting product module service:", error);
       return res.status(500).json({
         message: "Failed to initialize product module service. Please check server configuration."
       });
     }

     // Get all variants (rooms) to update
     let variants: any[] = [];

     try {
       if (room_config_id) {
         // If room_config_id is provided, get all variants for that product
         console.log(`Finding variants for room_config_id: ${room_config_id}`);

         // Get all variants for the product
         const result = await productModuleService.listProductVariants({
           product_id: room_config_id
         });

         variants = result.variants || [];
         console.log(`Found ${variants.length} variants for room_config_id: ${room_config_id}`);

         if (!variants.length) {
           return res.status(404).json({
             message: `No rooms found for room configuration ${room_config_id}`
           });
         }
       } else if (room_id) {
         // If room_id is provided, get that specific variant
         console.log(`Finding variant for room_id: ${room_id}`);
         try {
           const variant = await productModuleService.retrieveProductVariant(room_id);

           if (!variant) {
             return res.status(404).json({
               message: `Room with ID ${room_id} not found`
             });
           }

           variants = [variant];
           console.log(`Found variant for room_id: ${room_id}`);
         } catch (error) {
           console.error(`Error finding variant for room_id: ${room_id}`, error);
           return res.status(404).json({
             message: `Room with ID ${room_id} not found`
           });
         }
       }
     } catch (error) {
       console.error("Error getting product variants:", error);
       return res.status(500).json({
         message: "Error retrieving room data",
         error: error instanceof Error ? error.message : String(error)
       });
     }

     try {
       // Update availability for each room using the workflow
       for (const variant of variants) {
         try {
           console.log(`Processing variant ${variant.id}`);

           // Use the CreateOrUpdateRoomInventoryWorkflow to update room inventory
           const { result } = await CreateOrUpdateRoomInventoryWorkflow(req.scope).run({
             input: {
               inventory_item_id: variant.id, // Using variant ID as inventory_item_id
               from_date: start_date,
               to_date: end_date,
               available_quantity: quantity,
               is_available: quantity > 0,
               status: status, // Save the actual status value
               check_in_time: metadata?.check_in_time || "12:00",
               check_out_time: metadata?.check_out_time || "12:00",
               is_noon_to_noon: metadata?.is_noon_to_noon !== undefined ? metadata.is_noon_to_noon : true,
               notes: metadata?.notes
             }
           });

           results.push({
             room_id: variant.id,
             status: status,
             success: true,
             inventory_records: result
           });
           console.log(`Successfully processed variant ${variant.id}`);
         } catch (error) {
           console.error(`Error updating room ${variant.id}:`, error);
           results.push({
             room_id: variant.id,
             success: false,
             error: error instanceof Error ? error.message : String(error)
           });
         }
       }
     } catch (error) {
       console.error("Error processing inventory:", error);
       return res.status(500).json({
         message: "Error processing inventory",
         error: error instanceof Error ? error.message : String(error)
       });
     }

     return res.json({
       success: results.every((r: any) => r.success),
       results
     });
   } catch (error: any) {
     console.error("Error bulk updating room availability:", error);
     return res.status(500).json({
       message: "Error bulk updating room availability",
       error: error.message
     });
   }
 };