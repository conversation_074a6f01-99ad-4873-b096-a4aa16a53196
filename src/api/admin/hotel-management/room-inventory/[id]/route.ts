import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";

export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  const inventoryId = req.params.id;
  const query = req.scope.resolve("query");

  const { data: roomInventory } = await query.graph({
    entity: "room_inventory",
    filters: {
      id: [inventoryId]
    },
    fields: ["*"]
  });

  res.json({
    room_inventory: roomInventory
  });
};