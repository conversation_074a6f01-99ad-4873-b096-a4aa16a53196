import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../../modules/hotel-management/hotel/service";
import { ROOM_INVENTORY_MODULE } from "../../../../../modules/hotel-management/room-inventory";
import RoomInventoryModuleService from "../../../../../modules/hotel-management/room-inventory/service";
import { CreateOrUpdateRoomInventoryWorkflow } from "../../../../../workflows/hotel-management/room-inventory/upsert-room-inventory";
import * as ExcelJS from 'exceljs';
import multer from 'multer';
import { z } from "zod";
import { parseISO } from "date-fns";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Validation schema for room inventory data
const RoomInventorySchema = z.object({
  inventory_item_id: z.string().min(1, "Inventory item ID is required"),
  from_date: z.union([
    z.string().min(1, "From date is required"),
    z.date(),
    z.number() // Excel dates are numbers (days since 1900-01-01)
  ]),
  to_date: z.union([
    z.string().min(1, "To date is required"),
    z.date(),
    z.number() // Excel dates are numbers (days since 1900-01-01)
  ]),
  available_quantity: z.union([
    z.number(),
    z.string().transform(val => val ? parseInt(val, 10) : 1),
    z.null().transform(() => 1)
  ]).default(1),
  status: z.union([
    z.string(),
    z.null().transform(() => "available")
  ]).optional().default("available"),
  notes: z.union([
    z.string(),
    z.null().transform(() => ""),
    z.undefined().transform(() => "")
  ]).optional(),
  check_in_time: z.union([
    z.string(),
    z.null().transform(() => "12:00"),
    z.undefined().transform(() => "12:00")
  ]).optional().default("12:00"),
  check_out_time: z.union([
    z.string(),
    z.null().transform(() => "12:00"),
    z.undefined().transform(() => "12:00")
  ]).optional().default("12:00"),
  is_noon_to_noon: z.union([
    z.boolean(),
    z.string().transform(val => val?.toLowerCase() === 'true'),
    z.null().transform(() => true),
    z.undefined().transform(() => true)
  ]).optional().default(true),
});

/**
 * POST endpoint to import room inventory from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      // Get services
      const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
      const roomInventoryService: RoomInventoryModuleService = req.scope.resolve(ROOM_INVENTORY_MODULE);

      // Get parameters from form data
      const hotelId = req.body.hotel_id;
      const overrideExisting = req.body.override_existing === 'true';

      // Log the raw value and the parsed boolean to debug
      console.log(`Import options: override_existing=${overrideExisting} (raw value: '${req.body.override_existing}'), hotel_id=${hotelId || 'not specified'}`);

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split('.').pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === 'csv') {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString('utf8');

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require('fs').writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require('fs').unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error('Error reading CSV:', csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split('\\n').map(row => row.split(','));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet('Sheet1');

              // Add rows to the worksheet
              rows.forEach(row => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error('CSV file is empty or invalid');
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Room Inventory' or use the first one
          worksheet = workbook.getWorksheet('Room Inventory') ||
                     workbook.getWorksheet('room inventory') ||
                     workbook.getWorksheet('Sheet1') ||
                     workbook.worksheets[0];
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        return res.status(400).json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res.status(400).json({ message: 'Invalid file: No worksheet found' });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};

      console.log('Raw headers:', headers);

      // Create a map of column index to header name
      // Note: ExcelJS uses 1-based indexing for columns
      for (let i = 1; i < headers.length; i++) {
        const header = headers[i];
        if (header) {
          const headerName = header.toString().trim().toLowerCase(); // Convert to lowercase for case-insensitive matching
          headerMap[i] = headerName;
          console.log(`Mapped column ${i} to header '${headerName}'`);
        }
      }

      console.log('Header map:', headerMap);

      // Prepare results
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        created: [],
        errors: []
      };

      // Validate inventory item IDs
      // Get all inventory items to validate against
      let inventoryItems = [];
      try {
        const query = req.scope.resolve("query");
        const { data: items } = await query.graph({
          entity: "inventory_item",
          fields: ["id", "variant_id"],
        });

        if (items && Array.isArray(items)) {
          inventoryItems = items;
          console.log(`Found ${inventoryItems.length} inventory items`);
        } else {
          console.warn('No inventory items found or items is not an array');
        }
      } catch (error) {
        console.error('Error fetching inventory items:', error);
      }

      // Create a set of valid inventory item IDs
      const validInventoryItemIds = new Set(inventoryItems.map(item => item.id));

      // Process each row (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (!row.hasValues) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            rowData[header] = cell.value;
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach(key => {
          // Convert any objects to strings
          if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
        });

        // Debug log to see what data we're getting
        console.log('Row data:', rowData);

        // Map the lowercase header names back to the expected case-sensitive names
        const normalizedData: Record<string, any> = {};
        Object.keys(rowData).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'inventory_item_id') normalizedData.inventory_item_id = rowData[key];
          else if (lowerKey === 'from_date') normalizedData.from_date = rowData[key];
          else if (lowerKey === 'to_date') normalizedData.to_date = rowData[key];
          else if (lowerKey === 'available_quantity') normalizedData.available_quantity = rowData[key];
          else if (lowerKey === 'status') normalizedData.status = rowData[key];
          else if (lowerKey === 'notes') normalizedData.notes = rowData[key];
          else if (lowerKey === 'check_in_time') normalizedData.check_in_time = rowData[key];
          else if (lowerKey === 'check_out_time') normalizedData.check_out_time = rowData[key];
          else if (lowerKey === 'is_noon_to_noon') normalizedData.is_noon_to_noon = rowData[key];
          else normalizedData[key] = rowData[key]; // Keep any other fields
        });

        console.log('Normalized data:', normalizedData);

        // Ensure required fields are present
        if (!normalizedData.inventory_item_id || !normalizedData.from_date || !normalizedData.to_date) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: `Missing required fields: ${!normalizedData.inventory_item_id ? 'inventory_item_id' : ''} ${!normalizedData.from_date ? 'from_date' : ''} ${!normalizedData.to_date ? 'to_date' : ''}`
          });
          continue;
        }

        try {
          // Validate the data
          const validatedData = RoomInventorySchema.parse(normalizedData);

          // Validate inventory_item_id
          if (validInventoryItemIds.size > 0 && !validInventoryItemIds.has(validatedData.inventory_item_id)) {
            throw new Error(`Invalid inventory_item_id: ${validatedData.inventory_item_id}. Please use a valid inventory item ID from the Rooms Reference sheet.`);
          }

          // Parse dates
          let fromDate, toDate;
          try {
            console.log('Raw date values:', {
              from_date: validatedData.from_date,
              to_date: validatedData.to_date,
              from_date_type: typeof validatedData.from_date,
              to_date_type: typeof validatedData.to_date
            });

            // DIRECT APPROACH: Try to manually parse the date strings
            // This is a more direct approach that should work regardless of the format
            const parseCustomDate = (dateStr) => {
              console.log(`Attempting to parse date: ${dateStr}`);

              // If it's already a Date object
              if (dateStr instanceof Date) {
                console.log('Date is already a Date object');
                return dateStr;
              }

              // If it's a number (Excel serial date)
              if (typeof dateStr === 'number') {
                console.log('Date is a number (Excel serial date)');
                const excelEpoch = new Date(1899, 11, 30); // Dec 30, 1899
                return new Date(excelEpoch.getTime() + (dateStr * 24 * 60 * 60 * 1000));
              }

              // If it's not a string at this point, we can't handle it
              if (typeof dateStr !== 'string') {
                throw new Error(`Unsupported date type: ${typeof dateStr}`);
              }

              // Clean the string (remove any quotes, trim whitespace)
              dateStr = dateStr.replace(/["']/g, '').trim();
              console.log(`Cleaned date string: ${dateStr}`);

              // Try ISO format (YYYY-MM-DD)
              if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                console.log('Date appears to be in ISO format (YYYY-MM-DD)');
                const [year, month, day] = dateStr.split('-').map(Number);
                return new Date(year, month - 1, day);
              }

              // Try US format (MM/DD/YYYY)
              if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                console.log('Date appears to be in US format (MM/DD/YYYY)');
                const [month, day, year] = dateStr.split('/').map(Number);
                return new Date(year, month - 1, day);
              }

              // Try European format (DD/MM/YYYY)
              if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                console.log('Date appears to be in European format (DD/MM/YYYY)');
                const [day, month, year] = dateStr.split('/').map(Number);
                return new Date(year, month - 1, day);
              }

              // Try Excel's serial date as a string
              if (/^\d+(\.\d+)?$/.test(dateStr)) {
                console.log('Date appears to be an Excel serial date as a string');
                const excelEpoch = new Date(1899, 11, 30); // Dec 30, 1899
                const days = parseFloat(dateStr);
                return new Date(excelEpoch.getTime() + (days * 24 * 60 * 60 * 1000));
              }

              // Try to handle Excel date objects that have been converted to strings
              if (dateStr.includes('GMT') && dateStr.includes('Time')) {
                console.log('Date appears to be an Excel date object converted to string');
                // Extract just the date part (ignore time)
                try {
                  const date = new Date(dateStr);
                  if (!isNaN(date.getTime())) {
                    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
                  }
                } catch (e) {
                  console.log('Failed to parse Excel date object string');
                }
              }

              // Try other common formats
              const formats = [
                'yyyy-MM-dd', // ISO
                'MM/dd/yyyy', // US
                'dd/MM/yyyy', // European
                'MMM d, yyyy', // Jan 1, 2023
                'MMMM d, yyyy', // January 1, 2023
                'd MMM yyyy', // 1 Jan 2023
                'd MMMM yyyy', // 1 January 2023
              ];

              // Try parseISO as a last resort
              try {
                console.log('Trying parseISO as a last resort');
                return parseISO(dateStr);
              } catch (e) {
                console.log('parseISO failed');
              }

              // If we get here, we couldn't parse the date
              throw new Error(`Could not parse date: ${dateStr}`);
            };

            // EMERGENCY FALLBACK: If we're dealing with Excel serial dates like 45764.22928240741
            // Let's try to convert them directly
            const convertExcelSerialDate = (serialDate) => {
              if (typeof serialDate === 'number' || (typeof serialDate === 'string' && /^\d+(\.\d+)?$/.test(serialDate))) {
                const days = typeof serialDate === 'number' ? serialDate : parseFloat(serialDate);
                const excelEpoch = new Date(1899, 11, 30); // Dec 30, 1899
                return new Date(excelEpoch.getTime() + (days * 24 * 60 * 60 * 1000));
              }
              return null;
            };

            // Parse the dates
            try {
              // First try our custom parser
              fromDate = parseCustomDate(validatedData.from_date);
              console.log(`Successfully parsed from_date: ${fromDate.toISOString()}`);
            } catch (e) {
              console.error(`Error parsing from_date: ${e.message}`);

              // Emergency fallback for Excel serial dates
              const fallbackDate = convertExcelSerialDate(validatedData.from_date);
              if (fallbackDate && !isNaN(fallbackDate.getTime())) {
                console.log(`Using fallback method for from_date: ${fallbackDate.toISOString()}`);
                fromDate = fallbackDate;
              } else {
                // If all else fails, try to create a date directly
                try {
                  fromDate = new Date(validatedData.from_date);
                  if (isNaN(fromDate.getTime())) {
                    throw new Error(`Could not parse from_date: ${validatedData.from_date}. Please use YYYY-MM-DD format.`);
                  }
                  console.log(`Created date directly for from_date: ${fromDate.toISOString()}`);
                } catch (directError) {
                  throw new Error(`Could not parse from_date: ${validatedData.from_date}. Please use YYYY-MM-DD format.`);
                }
              }
            }

            try {
              // First try our custom parser
              toDate = parseCustomDate(validatedData.to_date);
              console.log(`Successfully parsed to_date: ${toDate.toISOString()}`);
            } catch (e) {
              console.error(`Error parsing to_date: ${e.message}`);

              // Emergency fallback for Excel serial dates
              const fallbackDate = convertExcelSerialDate(validatedData.to_date);
              if (fallbackDate && !isNaN(fallbackDate.getTime())) {
                console.log(`Using fallback method for to_date: ${fallbackDate.toISOString()}`);
                toDate = fallbackDate;
              } else {
                // If all else fails, try to create a date directly
                try {
                  toDate = new Date(validatedData.to_date);
                  if (isNaN(toDate.getTime())) {
                    throw new Error(`Could not parse to_date: ${validatedData.to_date}. Please use YYYY-MM-DD format.`);
                  }
                  console.log(`Created date directly for to_date: ${toDate.toISOString()}`);
                } catch (directError) {
                  throw new Error(`Could not parse to_date: ${validatedData.to_date}. Please use YYYY-MM-DD format.`);
                }
              }
            }

            // Check if dates are valid
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
              console.error('Invalid date values:', { fromDate, toDate, from_date: validatedData.from_date, to_date: validatedData.to_date });
              throw new Error('Invalid date format');
            }

            console.log('Parsed dates:', {
              fromDate: fromDate.toISOString(),
              toDate: toDate.toISOString(),
              originalFromDate: validatedData.from_date,
              originalToDate: validatedData.to_date
            });
          } catch (error) {
            console.error('Date parsing error:', error, { from_date: validatedData.from_date, to_date: validatedData.to_date });
            throw new Error(`Invalid date format. Please use YYYY-MM-DD format for dates. Error: ${error.message}`);
          }

          // Ensure to_date is after from_date
          if (fromDate >= toDate) {
            throw new Error(`To date must be after from date.`);
          }

          // If not overriding existing records, check if a record already exists for this date range
          if (!overrideExisting) {
            console.log(`Override is disabled. Checking for existing records for item ${validatedData.inventory_item_id} from ${fromDate.toISOString()} to ${toDate.toISOString()}`);
            try {
              // Check if inventory records already exist for this date range and inventory item
              // We need to query the database directly to check for overlapping date ranges
              const query = req.scope.resolve("query");
              const { data: existingInventory } = await query.graph({
                entity: "room_inventory",
                filters: {
                  inventory_item_id: validatedData.inventory_item_id,
                  // Check for any overlapping date ranges
                  // This is a simplified check - in a real system you'd use a more complex query
                  // to check for all possible overlapping scenarios
                  $or: [
                    // Case 1: fromDate is between existing from_date and to_date
                    { from_date: { $lte: fromDate }, to_date: { $gte: fromDate } },
                    // Case 2: toDate is between existing from_date and to_date
                    { from_date: { $lte: toDate }, to_date: { $gte: toDate } },
                    // Case 3: existing range is completely inside new range
                    { from_date: { $gte: fromDate }, to_date: { $lte: toDate } },
                  ]
                },
                fields: ["id", "from_date", "to_date"]
              });

              if (existingInventory && existingInventory.length > 0) {
                console.log(`Found ${existingInventory.length} existing inventory records that overlap with the date range. Skipping since override is disabled.`);
                throw new Error(`Record already exists for this date range and will not be updated (override option is disabled).`);
              } else {
                console.log(`No existing inventory records found for this date range. Proceeding with creation.`);
              }
            } catch (error) {
              if (error.message && error.message.includes('will not be updated')) {
                throw error; // Re-throw our custom error
              }
              // If the error is not our custom error, it's likely a service error, so we'll continue
              console.warn(`Error checking for existing inventory: ${error.message || error}`);
              console.log('Continuing with creation despite error checking for existing records');
            }
          } else {
            console.log(`Override is enabled. Will update any existing records for item ${validatedData.inventory_item_id} from ${fromDate.toISOString()} to ${toDate.toISOString()}`);
          }

          // Create or update the room inventory using the workflow
          const { result } = await CreateOrUpdateRoomInventoryWorkflow(req.scope).run({
            input: {
              inventory_item_id: validatedData.inventory_item_id,
              from_date: fromDate,
              to_date: toDate,
              available_quantity: validatedData.available_quantity,
              status: validatedData.status,
              notes: validatedData.notes,
              check_in_time: validatedData.check_in_time,
              check_out_time: validatedData.check_out_time,
              is_noon_to_noon: validatedData.is_noon_to_noon
            }
          });

          results.successful++;
          results.created.push({
            inventory_item_id: validatedData.inventory_item_id,
            from_date: fromDate,
            to_date: toDate,
            row: i
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: error.message || 'Unknown error'
          });
        }
      }

      // Return the results
      return res.status(200).json({
        message: 'Import completed',
        results
      });

    } catch (error) {
      console.error('Error importing room inventory:', error);
      return res.status(500).json({ message: `Error importing room inventory: ${error.message}` });
    }
  });
};
