import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const {
    data: roomInventory,
  } = await query.graph({
    entity: "room_inventory",
    filters: {inventory_item_id: [req.params.id]},
    fields: ["id", "inventory_item_id", "from_date", "to_date", "available_quantity", "status", "notes", "check_in_time", "check_out_time", "is_noon_to_noon", "order_id", "cart_id"],
  });
  res.json({
    roomInventory,
  });
};
