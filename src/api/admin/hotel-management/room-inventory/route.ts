import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { PostAdminCreateOrUpdateRoomInventory } from "./validators";
import { CreateOrUpdateRoomInventoryWorkflow } from "src/workflows/hotel-management/room-inventory/upsert-room-inventory";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { UpdateRoomInventoryWorkflow } from "src/workflows/hotel-management/room-inventory/update-room-inventory";

type PostAdminCreateOrUpdateRoomInventoryType = z.infer<
  typeof PostAdminCreateOrUpdateRoomInventory
>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateOrUpdateRoomInventoryType>,
  res: MedusaResponse
) => {
  const { result } = req.body.id
    ? await UpdateRoomInventoryWorkflow(req.scope).run({
        //@ts-ignore
        input: req.body,
      })
    : await CreateOrUpdateRoomInventoryWorkflow(req.scope).run({
        //@ts-ignore
        input: req.body,
      });

  res.json({ roomInventory: result });
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const { data: roomInventory } = await query.graph({
    entity: "room_inventory",
    fields: ["id", "inventory_item_id", "date", "available_quantity"],
  });
  res.json({
    roomInventory,
  });
};
