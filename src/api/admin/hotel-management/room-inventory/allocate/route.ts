import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { format, parseISO } from "date-fns";

// Validation schema for allocating a room
export const AllocateRoomSchema = z.object({
  inventory_id: z.string().optional(),
  room_id: z.string(),
  order_id: z.string(),
  from_date: z.string().or(z.date()),
  to_date: z.string().or(z.date()),
  status: z.string().default("reserved_unassigned"),
});

export type AllocateRoomType = z.infer<typeof AllocateRoomSchema>;

/**
 * Endpoint to allocate a room to an order
 * This adds the reservation to the order metadata AND sets the order_id in room_inventory
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Success status and updated order
 */
export const POST = async (
  req: MedusaRequest<AllocateRoomType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = AllocateRoomSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: validationResult.error.format(),
      });
    }

    const { room_id, order_id, from_date, to_date, status, inventory_id } =
      validationResult.data;

    // Get services
    const orderService = req.scope.resolve(Modules.ORDER);
    const roomInventoryService = req.scope.resolve("roomInventoryService");

    // Get the order
    const order = await orderService.retrieveOrder(order_id);
    if (!order) {
      return res.status(404).json({
        message: "Order not found",
      });
    }

    // Check if order has reservations in metadata
    if (
      !order.metadata?.reservations ||
      !Array.isArray(order.metadata.reservations)
    ) {
      // If no reservations array exists, create one
      order.metadata = {
        ...order.metadata,
        reservations: [],
      };
    }

    // Format dates consistently to avoid timezone issues
    const fromDateFormatted = typeof from_date === "string"
      ? format(parseISO(from_date), "yyyy-MM-dd")
      : format(new Date(from_date), "yyyy-MM-dd");

    const toDateFormatted = typeof to_date === "string"
      ? format(parseISO(to_date), "yyyy-MM-dd")
      : format(new Date(to_date), "yyyy-MM-dd");

    // Create a new reservation with formatted dates
    const newReservation = {
      room_id,
      from_date: fromDateFormatted,
      to_date: toDateFormatted,
      status: status || "reserved_unassigned",
      id: inventory_id || `reservation_${Date.now()}`,
    };

    // Check if a reservation with the same room_id and date range already exists
    const existingReservationIndex = order.metadata.reservations.findIndex(
      (r) =>
        r.room_id === room_id &&
        r.from_date === fromDateFormatted &&
        r.to_date === toDateFormatted
    );

    // If a reservation already exists, update it instead of adding a new one
    let updatedReservations;
    if (existingReservationIndex !== -1) {
      console.log(`Updating existing reservation at index ${existingReservationIndex}`);
      updatedReservations = [...order.metadata.reservations];
      updatedReservations[existingReservationIndex] = newReservation;
    } else {
      // Add the reservation to the order metadata
      updatedReservations = [
        ...(Array.isArray(order.metadata.reservations)
          ? order.metadata.reservations
          : []),
        newReservation,
      ];
    }

    console.log({ updatedReservations });

    // Update the order metadata
    const updatedMetadata = {
      ...order.metadata,
      reservations: updatedReservations,
    };

    // Update the order
    await orderService.updateOrders(order_id, {
      metadata: updatedMetadata,
    });

    // Use the updateInventoryStatus function to update the room inventory
    const bookingInfo = {
      order_id,
      reservation_id: newReservation.id,
    };

    try {
      // Use the service function to update inventory status
      // Convert string dates to Date objects for the service call
      const fromDateObj = typeof from_date === "string" ? parseISO(from_date) : new Date(from_date);
      const toDateObj = typeof to_date === "string" ? parseISO(to_date) : new Date(to_date);

      const updatedEntries = await roomInventoryService.updateInventoryStatus(
        room_id,
        fromDateObj,
        toDateObj,
        status || "reserved_unassigned",
        `Allocated to order ${order_id} on ${new Date().toISOString()}`,
        bookingInfo,
        null, // expiresAt
        order_id, // orderId
        null // cartId
      );

      if (updatedEntries && updatedEntries.length > 0) {
        console.log(
          `Successfully updated inventory status for room ${room_id} from ${from_date} to ${to_date}`
        );
      } else {
        console.log(
          `No inventory entries found for room ${room_id} from ${from_date} to ${to_date}, no updates made`
        );
      }
    } catch (inventoryError) {
      console.error("Error updating room inventory:", inventoryError);
      // We don't throw here as the order metadata was already updated
      // Instead, we log the error and continue
    }

    // Get the updated order
    const updatedOrder = await orderService.retrieveOrder(order_id);

    return res.json({
      success: true,
      message: "Room allocated successfully",
      order: updatedOrder,
    });
  } catch (error) {
    console.error("Error allocating room:", error);
    return res.status(500).json({
      message: "Error allocating room",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
