import { MedusaContainer } from "@camped-ai/framework/types";
import { ADD_ON_SERVICE } from "../../../../modules/hotel-management/add-on-service";
import AddOnServiceModuleService from "../../../../modules/hotel-management/add-on-service/service";

/**
 * Register the add-on service module directly
 * This function can be called from any route handler to ensure the module is registered
 */
export function registerAddOnServiceModule(container: MedusaContainer): void {
  try {
    // Check if already registered
    if (!container.hasRegistration(ADD_ON_SERVICE)) {
      // Register the service using the module
      container.register({
        [ADD_ON_SERVICE]: {
          resolve: () => new AddOnServiceModuleService(container),
        },
      });
      console.log(
        "✅ Add-on service module registered successfully from API route"
      );
    }
  } catch (error) {
    console.error("❌ Failed to register add-on service module:", error);
    throw error;
  }
}
