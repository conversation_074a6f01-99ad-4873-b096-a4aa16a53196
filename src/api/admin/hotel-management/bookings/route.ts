import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  getOrdersListWorkflow,
  createOrderWorkflow,
} from "@camped-ai/medusa/core-flows";
import { Modules } from "@camped-ai/framework/utils";
// Import helper functions
import { checkRoomAvailability as checkRoomAvailabilityHelper } from "./helpers";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Fetching bookings list using getOrdersListWorkflow");

    // Get query parameters
    const query = req.query || {};
    console.log("Query parameters:", query);

    // Set up pagination
    const limit = parseInt(query.limit as string) || 100;
    const offset = parseInt(query.offset as string) || 0;
    console.log(`Pagination: limit=${limit}, offset=${offset}`);

    try {
      // Build filters for the workflow
      const filters: Record<string, any> = {};

      // Apply filters for the workflow
      if (query.hotel_id) {
        // Convert to string to ensure consistent type comparison
        const hotelId = String(query.hotel_id);
        console.log(`Adding hotel_id filter to workflow: ${hotelId}`);
        filters["metadata.hotel_id"] = hotelId;
      }

      if (query.status) {
        if (Array.isArray(query.status)) {
          // Handle multiple status values
          const statusArray = query.status as string[];
          filters.status = { $in: statusArray };
        } else if (query.status !== "all") {
          // Handle single status value
          filters.status = query.status;
        }
      }

      if (query.payment_status && query.payment_status !== "all") {
        filters.payment_status = query.payment_status;
      }

      if (query.guest_name) {
        filters["metadata.guest_name"] = { $like: `%${query.guest_name}%` };
      }

      if (query.guest_email) {
        filters.email = { $like: `%${query.guest_email}%` };
      }

      if (query.from_date) {
        filters["metadata.check_in_date"] = { $gte: query.from_date };
      }

      if (query.to_date) {
        filters["metadata.check_out_date"] = { $lte: query.to_date };
      }

      console.log("Workflow filters:", JSON.stringify(filters, null, 2));

      // Run the workflow to get all orders (without filters)
      const { result } = await getOrdersListWorkflow(req.scope).run({
        input: {
          // Request all fields we need for bookings
          fields: [
            "id",
            "status",
            "email",
            "currency_code",
            "payment_status",
            "metadata",
            "created_at",
            "updated_at",
            "total",
          ],
          variables: {
            skip: offset,
            take: 1000, // Use a high limit to get all orders
            order: { created_at: "DESC" },
          },
        },
      });

      console.log("Raw workflow result:", result);

      // Get all orders from the result
      // The structure might be different based on the workflow output
      let allOrders: any[] = [];

      // Type assertion to handle different result structures
      const resultObj = result as any;

      if (Array.isArray(resultObj)) {
        allOrders = resultObj;
      } else if (resultObj && typeof resultObj === "object") {
        if ("rows" in resultObj) {
          allOrders = Array.isArray(resultObj.rows)
            ? resultObj.rows
            : [resultObj.rows];
        } else if ("orders" in resultObj) {
          allOrders = Array.isArray(resultObj.orders)
            ? resultObj.orders
            : [resultObj.orders];
        } else if ("data" in resultObj) {
          allOrders = Array.isArray(resultObj.data)
            ? resultObj.data
            : [resultObj.data];
        } else {
          // If we can't find orders in any of the expected properties, log the structure
          console.log("Unexpected result structure:", resultObj);
          // Try to extract orders from the result object
          allOrders = Object.values(resultObj).filter(Array.isArray)[0] || [];
        }
      }

      console.log(`Retrieved ${allOrders.length} orders from workflow`);

      if (allOrders.length > 0) {
        console.log("Sample order:", JSON.stringify(allOrders[0], null, 2));
      }

      // Apply filters manually on the result
      let filteredOrders = [...allOrders];

      if (query.hotel_id) {
        const hotelId = String(query.hotel_id);
        console.log(`Filtering by hotel_id: ${hotelId}`);

        // Log a sample of orders to debug metadata structure
        if (filteredOrders.length > 0) {
          const sampleOrder = filteredOrders[0];
          console.log(
            "Sample order metadata:",
            JSON.stringify(sampleOrder.metadata, null, 2)
          );
        }

        filteredOrders = filteredOrders.filter((order) => {
          if (!order.metadata) return false;

          // Convert both to strings for comparison to avoid type mismatches
          const orderHotelId = order.metadata.hotel_id
            ? String(order.metadata.hotel_id)
            : null;

          // Log for debugging
          if (orderHotelId) {
            console.log(
              `Comparing order hotel_id: ${orderHotelId} (${typeof orderHotelId}) with filter hotel_id: ${hotelId} (${typeof hotelId})`
            );
          }

          return orderHotelId === hotelId;
        });

        console.log(
          `After hotel_id filtering: ${filteredOrders.length} orders remain`
        );
      }

      if (query.status) {
        if (Array.isArray(query.status)) {
          // Handle multiple status values
          const statusArray = query.status as string[];
          console.log(
            `Filtering by multiple statuses: ${statusArray.join(", ")}`
          );
          filteredOrders = filteredOrders.filter((order) =>
            statusArray.includes(order.status)
          );
        } else if (query.status !== "all") {
          // Handle single status value
          console.log(`Filtering by status: ${query.status}`);
          filteredOrders = filteredOrders.filter(
            (order) => order.status === query.status
          );
        }
      }

      // Handle the has_room_id parameter for differentiating between reserved and confirmed
      if (query.has_room_id) {
        console.log(`Filtering by has_room_id: ${query.has_room_id}`);

        if (query.has_room_id === "true") {
          // Only include orders with room_id in metadata (confirmed/allocated)
          filteredOrders = filteredOrders.filter(
            (order) => order.metadata && order.metadata.room_id
          );
        } else if (query.has_room_id === "false") {
          // Only include orders without room_id in metadata (reserved/unallocated)
          filteredOrders = filteredOrders.filter(
            (order) => !order.metadata || !order.metadata.room_id
          );
        }
        // If "both", no additional filtering needed
      }

      if (query.payment_status && query.payment_status !== "all") {
        console.log(`Filtering by payment_status: ${query.payment_status}`);
        filteredOrders = filteredOrders.filter(
          (order) => order.payment_status === query.payment_status
        );
      }

      if (query.guest_name) {
        console.log(`Filtering by guest_name: ${query.guest_name}`);
        const guestNameLower = (query.guest_name as string).toLowerCase();
        filteredOrders = filteredOrders.filter(
          (order) =>
            order.metadata &&
            order.metadata.guest_name &&
            order.metadata.guest_name.toLowerCase().includes(guestNameLower)
        );
      }

      if (query.guest_email) {
        console.log(`Filtering by guest_email: ${query.guest_email}`);
        const guestEmailLower = (query.guest_email as string).toLowerCase();
        filteredOrders = filteredOrders.filter((order) => {
          // Check both order.email and metadata.guest_email
          const orderEmail = order.email ? order.email.toLowerCase() : "";
          const metadataEmail =
            order.metadata && order.metadata.guest_email
              ? order.metadata.guest_email.toLowerCase()
              : "";

          return (
            orderEmail.includes(guestEmailLower) ||
            metadataEmail.includes(guestEmailLower)
          );
        });
      }

      if (query.from_date) {
        console.log(`Filtering by from_date: ${query.from_date}`);
        const fromDate = new Date(query.from_date as string);
        filteredOrders = filteredOrders.filter(
          (order) =>
            order.metadata &&
            order.metadata.check_in_date &&
            new Date(order.metadata.check_in_date) >= fromDate
        );
      }

      if (query.to_date) {
        console.log(`Filtering by to_date: ${query.to_date}`);
        const toDate = new Date(query.to_date as string);
        filteredOrders = filteredOrders.filter(
          (order) =>
            order.metadata &&
            order.metadata.check_out_date &&
            new Date(order.metadata.check_out_date) <= toDate
        );
      }

      console.log(`After filtering: ${filteredOrders.length} orders remain`);
      if (filteredOrders.length > 0) {
        console.log(
          "Filtered order IDs:",
          filteredOrders.map((order) => order.id)
        );
      }

      // Use the filtered orders for transformation
      const orders = filteredOrders;
      const count = filteredOrders.length;

      // Transform the orders into booking objects
      const bookings = orders.map((order: any) => {
        // Extract metadata
        const metadata = order.metadata || {};

        // Format dates properly if they exist
        let checkInDate = null;
        let checkOutDate = null;

        if (metadata.check_in_date) {
          try {
            checkInDate = new Date(metadata.check_in_date);
            if (isNaN(checkInDate.getTime())) {
              checkInDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            }
          } catch (e) {
            checkInDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
          }
        } else {
          checkInDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
        }

        if (metadata.check_out_date) {
          try {
            checkOutDate = new Date(metadata.check_out_date);
            if (isNaN(checkOutDate.getTime())) {
              checkOutDate = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);
            }
          } catch (e) {
            checkOutDate = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);
          }
        } else {
          checkOutDate = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);
        }

        // Get total amount from metadata or order, including add-ons
        let totalAmount = null;
        if (metadata.total_amount) {
          totalAmount = parseFloat(metadata.total_amount);
          // Add add-ons total if available (stored in cents, so divide by 100)
          if (metadata.add_on_total_amount) {
            totalAmount += parseFloat(metadata.add_on_total_amount) / 100;
          }
        } else if (order.total) {
          totalAmount = parseFloat(order.total);
        }

        // Generate a random guest name if none exists
        const guestNames = [
          "John Doe",
          "Jane Smith",
          "Michael Johnson",
          "Emily Davis",
          "Robert Wilson",
        ];
        const randomGuestName =
          guestNames[Math.floor(Math.random() * guestNames.length)];

        // Get customer information from metadata or order
        const customerEmail =
          order.email || metadata.guest_email || "<EMAIL>";
        const customerName = metadata.guest_name || randomGuestName;
        const customerPhone = metadata.guest_phone || "+1234567890";

        // Create a booking object from the order
        return {
          order_id: order.id,
          guest_email: customerEmail,
          guest_name: customerName,
          guest_phone: customerPhone,
          hotel_id: metadata.hotel_id || "",
          room_id: metadata.room_id || "",
          room_config_id: metadata.room_config_id || "",
          // Store room_type in a temporary field that will be replaced with room_config_name later
          _room_type: metadata.room_type || "Standard",
          check_in_date: checkInDate,
          check_out_date: checkOutDate,
          check_in_time: metadata.check_in_time || "12:00",
          check_out_time: metadata.check_out_time || "12:00",
          number_of_guests: metadata.number_of_guests || 1,
          currency_code: metadata.currency_code || order.currency_code || "USD",
          status: order.status || "pending",
          payment_status: order.payment_status || "awaiting_payment",
          special_requests: metadata.special_requests || "",
          notes: metadata.notes || "",
          total_amount: totalAmount,
          created_at: order.created_at,
          updated_at: order.updated_at,
          metadata: metadata, // Include the full metadata object
        };
      });

      console.log(
        `Successfully transformed ${bookings.length} orders into bookings`
      );

      // Get unique room configuration IDs from all bookings
      const roomConfigIds = [
        ...new Set(
          bookings.map((booking) => booking.room_config_id).filter(Boolean)
        ),
      ];

      // Fetch room configuration details for all room config IDs
      const roomConfigDetails = {};
      if (roomConfigIds.length > 0) {
        try {
          // Get query from scope
          const query = req.scope.resolve("query");

          const { data: products } = await query.graph({
            entity: "product",
            filters: {
              id: roomConfigIds,
            },
            fields: ["id", "title"],
          });

          // Create a map of room_config_id to room config name
          if (products && products.length > 0) {
            products.forEach((product: { id: string; title: string }) => {
              roomConfigDetails[product.id] = product.title;
            });
          }
        } catch (error) {
          console.error("Error fetching room configuration details:", error);
        }
      }

      // Add room configuration names to bookings
      const bookingsWithDetails = bookings.map((booking) => {
        // Get the room configuration name
        const roomConfigName =
          roomConfigDetails[booking.room_config_id] ||
          booking._room_type ||
          "Standard";

        // Create a new object without the temporary _room_type field
        const { _room_type, ...bookingWithoutTempField } = booking;

        return {
          ...bookingWithoutTempField,
          room_config_name: roomConfigName,
          // Add room_type field that contains the room configuration name
          room_type: roomConfigName,
        };
      });

      return res.json({
        bookings: bookingsWithDetails,
        count,
        limit,
        offset,
      });
    } catch (serviceError) {
      console.log("Error using workflow:", serviceError);

      // Return empty array in case of error
      return res.status(400).json({
        message:
          serviceError instanceof Error
            ? serviceError.message
            : "Failed to list bookings using workflow",
        bookings: [],
        count: 0,
        limit,
        offset,
      });
    }
  } catch (error) {
    console.error("Error in GET bookings:", error);
    return res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to list bookings",
      stack:
        process.env.NODE_ENV !== "production" && error instanceof Error
          ? error.stack
          : undefined,
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Creating booking using workflow approach");

    // Check if req.scope exists
    if (!req.scope) {
      console.error(
        "req.scope is undefined in booking creation. This might be due to a middleware issue."
      );
      return res.status(400).json({
        message: "Scope is not available, cannot create booking",
      });
    }

    // Log the request body for debugging
    console.log("Request body:", req.body);

    // Extract booking data from request body
    const {
      hotel_id,
      room_id,
      room_config_id,
      room_type,
      check_in_date,
      check_out_date,
      check_in_time,
      check_out_time,
      guest_name,
      guest_email,
      guest_phone,
      // We'll calculate number_of_guests from adults, children, and infants
      adults = 1, // Default to 1 adult if not provided
      children = 0, // Default to 0 children if not provided
      infants = 0, // Default to 0 infants if not provided
      child_ages = [], // Array of child ages
      number_of_rooms = 1, // Default to 1 room if not provided
      total_amount,
      currency_code,
      region_id = "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
      country_code, // Accept country_code directly from request body
      special_requests,
      notes,
      metadata,
      shipping_address,
      billing_address,
      customer_id: existing_customer_id,
      sales_channel_id,
    } = req.body as Record<string, any>;

    // Log extracted values for debugging
    console.log("Extracted values:", {
      hotel_id,
      room_id,
      check_in_date,
      check_out_date,
      guest_name,
      guest_email,
      adults,
      children,
      infants,
      number_of_rooms,
      total_guests: adults + children + infants,
      total_amount,
      currency_code,
      region_id,
      country_code,
    });

    // Validate required fields
    const missingFields = [];
    if (!hotel_id) missingFields.push("hotel_id");
    if (!room_config_id) missingFields.push("room_config_id");
    if (!check_in_date) missingFields.push("check_in_date");
    if (!check_out_date) missingFields.push("check_out_date");
    if (!guest_name) missingFields.push("guest_name");
    if (!guest_email) missingFields.push("guest_email");
    if (!total_amount) missingFields.push("total_amount");
    if (!currency_code) missingFields.push("currency_code");
    if (!region_id) missingFields.push("region_id");

    if (missingFields.length > 0) {
      console.error(`Missing required fields: ${missingFields.join(", ")}`);
      return res.status(400).json({
        message: `Missing required fields: ${missingFields.join(", ")}`,
      });
    }

    // Parse dates
    const checkInDate = new Date(check_in_date);
    const checkOutDate = new Date(check_out_date);

    // STEP 1: Check room availability
    if (room_id) {
      const availability = await checkRoomAvailabilityHelper(
        room_id,
        checkInDate,
        checkOutDate
      );

      if (!availability.available) {
        return res.status(400).json({
          message: "Room is not available for the selected dates",
          unavailable_dates: availability.unavailableDates,
        });
      }
    }

    // We'll use the automatically generated order ID after order creation

    // STEP 2: Prepare customer data
    // Use existing customer ID if provided, otherwise let createOrderWorkflow handle it
    const customer_id = existing_customer_id;
    console.log(
      customer_id
        ? `Using existing customer ID: ${customer_id}`
        : "Will let order workflow create or find customer if needed"
    );

    // STEP 3: Create order using workflow
    console.log("Creating order using workflow...");

    // Retrieve tax information from the database based on product information
    let taxRate = 0;
    let taxLines: any[] = [];

    try {
      // Get the tax module service
      const taxService = req.scope.resolve(Modules.TAX);

      // Create a taxable item for the room
      const taxableItem = {
        id: room_id || room_config_id,
        product_id: room_config_id,
        product_type_id: "hotel_room",
        unit_price: parseInt(total_amount) * 100, // Convert to cents
        quantity: 1,
      };

      // Get the country code from the request body, shipping address, or use a default
      // Priority: 1. Request body country_code, 2. Shipping address country_code, 3. Default "US"
      let countryCode = "US"; // Default

      if (country_code) {
        // Use country_code from request body if provided
        countryCode = country_code;
        console.log(`Using country_code from request body: ${countryCode}`);
      } else if (shipping_address && shipping_address.country_code) {
        // Fall back to shipping_address country_code if available
        countryCode = shipping_address.country_code;
        console.log(`Using country_code from shipping address: ${countryCode}`);
      } else {
        console.log(`No country_code provided, using default: ${countryCode}`);
      }

      // Create a tax calculation context
      const taxContext = {
        address: {
          country_code: countryCode,
          // Include province_code if available
          ...(shipping_address && shipping_address.province
            ? { province_code: shipping_address.province }
            : {}),
        },
        // Include region_id if it's required, but it might not be necessary
        ...(region_id ? { region_id } : {}),
      };

      console.log(`Getting tax lines for item with context:`, {
        item: taxableItem,
        context: taxContext,
      });

      try {
        // According to the Medusa docs, this will retrieve tax lines from the database
        // based on the tax region and any tax rules that match the product
        const calculatedTaxLines = await taxService.getTaxLines(
          [taxableItem],
          taxContext
        );

        if (calculatedTaxLines && calculatedTaxLines.length > 0) {
          taxLines = calculatedTaxLines;
          // Calculate the combined tax rate from all tax lines
          taxRate = taxLines.reduce((sum, line) => sum + line.rate, 0);
          console.log(
            `Retrieved ${taxLines.length} tax lines with combined rate: ${taxRate}%`
          );
        } else {
          console.log(
            `No tax lines found in the database. Order will be created without tax information.`
          );
          // No tax lines found, so we'll create the order without tax information
          taxLines = [];
        }
      } catch (error) {
        console.error("Error getting tax lines:", error);
        console.log(
          "Error retrieving tax information. Order will be created without tax information."
        );
        taxLines = [];
      }
    } catch (error) {
      console.error("Error retrieving tax information:", error);
      console.log(
        "Error retrieving tax information. Order will be created without tax information."
      );
      taxLines = [];
    }

    // Try to get room configuration name if room_config_id is provided
    let roomConfigName = room_type || "Standard";
    if (room_config_id) {
      try {
        const productService = req.scope.resolve(Modules.PRODUCT);
        const roomConfig = await productService.retrieveProduct(room_config_id);
        if (roomConfig && roomConfig.title) {
          roomConfigName = roomConfig.title;
        }
      } catch (error) {
        console.error("Error fetching room configuration details:", error);
      }
    }

    // Create booking metadata for the order
    const bookingMetadata = {
      hotel_id,
      room_config_id,
      room_id,
      room_type: roomConfigName, // Use room configuration name instead of room type
      room_config_name: roomConfigName, // Add room_config_name field
      check_in_date: checkInDate.toISOString(),
      check_out_date: checkOutDate.toISOString(),
      check_in_time: check_in_time || "12:00",
      check_out_time: check_out_time || "12:00",
      guest_name,
      guest_email,
      guest_phone,
      number_of_guests: adults + children + infants,
      number_of_rooms,
      adults,
      children,
      infants,
      child_ages, // Include child ages in metadata
      total_amount,
      currency_code,
      special_requests,
      notes,
      // Include tax information in metadata if we have tax lines
      ...(taxLines.length > 0
        ? {
            tax_rate: taxRate,
            has_tax_lines: true,
            tax_lines_count: taxLines.length,
          }
        : {}),
      ...metadata,
    };

    try {
      // Split guest name into first and last name for customer data if needed
      const nameParts = guest_name.split(" ");
      const firstName = nameParts[0] || "";
      const lastName = nameParts.slice(1).join(" ") || "";

      // Create a default address if shipping_address is not provided
      const defaultAddress = shipping_address || {
        first_name: firstName,
        last_name: lastName,
        address_1: "Default Address",
        city: "Default City",
        country_code: country_code?.toLowerCase() || "us", // Use country_code from request body if provided
        postal_code: "00000",
        phone: guest_phone || "",
      };

      // Ensure region_id is a string and properly formatted
      const regionIdStr = String(region_id);
      console.log(`Using region_id: ${regionIdStr} for order creation`);

      // Prepare order input with customer data
      const orderInput = {
        customer_id,
        email: guest_email,
        region_id: regionIdStr, // This is required and must be a valid region ID
        currency_code,
        metadata: bookingMetadata,
        sales_channel_id: sales_channel_id,
        shipping_address: shipping_address || defaultAddress,
        billing_address: billing_address || shipping_address || defaultAddress,
      };

      // Log the order input for debugging
      console.log("Order input:", JSON.stringify(orderInput, null, 2));

      // If no customer_id is provided, add customer details for automatic creation
      if (!customer_id) {
        Object.assign(orderInput, {
          customer: {
            email: guest_email,
            first_name: firstName,
            last_name: lastName,
            phone: guest_phone,
            metadata: {
              source: "hotel_booking",
            },
          },
        });
      }

      // Use the roomConfigName we already fetched earlier

      // Add items to the order if needed
      // The createOrderWorkflow might require items to be present
      Object.assign(orderInput, {
        items: [
          {
            title: `Room booking: ${roomConfigName}`,
            quantity: number_of_rooms, // Use the number of rooms as quantity
            unit_price: parseInt(total_amount) / number_of_rooms || 100, // Divide total by number of rooms for unit price
            variant_id: room_id || room_config_id, // Use room_id or room_config_id as variant_id
            is_tax_inclusive: false,
            // Add tax lines to the line item if we have them
            ...(taxLines.length > 0
              ? {
                  tax_lines: taxLines.map((taxLine: Record<string, any>) => ({
                    rate: taxLine.rate,
                    code: taxLine.code || "TAX",
                    name: taxLine.name || "Tax",
                    provider_id: taxLine.provider_id || "system",
                  })),
                }
              : {}),
            metadata: {
              room_id,
              room_config_id,
              room_config_name: roomConfigName,
              check_in_date: checkInDate.toISOString(),
              check_out_date: checkOutDate.toISOString(),
              number_of_rooms,
              ...(taxLines.length > 0
                ? {
                    tax_rate: taxRate,
                    has_tax_lines: true,
                    tax_lines_count: taxLines.length,
                  }
                : {}),
            },
          },
        ],
      });

      console.log(
        "Calling createOrderWorkflow with input:",
        JSON.stringify(orderInput, null, 2)
      );

      if (taxLines.length > 0) {
        console.log("Tax information being used:", {
          taxRate,
          taxLinesCount: taxLines.length,
          firstTaxLine: taxLines[0],
        });
      } else {
        console.log(
          "No tax lines found. Order will be created without tax information."
        );
      }

      try {
        const workflow = createOrderWorkflow(req.scope);
        console.log("Workflow created successfully");

        const result = await workflow.run({
          input: orderInput,
        });
        console.log("Workflow run completed");

        const orderResult = result.result;
        console.log("Order result:", orderResult);

        if (!orderResult) {
          throw new Error("Order result is null or undefined");
        }

        if (!orderResult.id) {
          throw new Error("Order ID is missing from result");
        }

        console.log(`Order created with ID: ${orderResult.id}`);

        // Create a booking object from the order
        let bookingOrder = {
          order_id: orderResult.id,
          guest_email,
          guest_name,
          guest_phone,
          hotel_id,
          room_id,
          room_config_id,
          room_type: roomConfigName, // Use room configuration name instead of room type
          room_config_name: roomConfigName, // Add room_config_name field
          check_in_date: checkInDate,
          check_out_date: checkOutDate,
          check_in_time: check_in_time || "12:00",
          check_out_time: check_out_time || "12:00",
          number_of_guests: adults + children + infants,
          number_of_rooms,
          adults,
          children,
          infants,
          child_ages, // Include child ages in the booking order
          currency_code,
          status: "pending",
          payment_status: "awaiting_payment",
          special_requests,
          notes,
          total_amount,
          // Include tax information if we have tax lines
          ...(taxLines.length > 0
            ? {
                tax_rate: taxRate,
                tax_amount: (
                  (parseFloat(total_amount) * taxRate) /
                  100
                ).toFixed(2),
                total_with_tax: (
                  parseFloat(total_amount) *
                  (1 + taxRate / 100)
                ).toFixed(2),
                tax_lines_count: taxLines.length,
              }
            : {}),
          created_at: new Date(),
          updated_at: new Date(),
          metadata: bookingMetadata,
        };

        // Room configuration name is already set in the booking order

        // STEP 4: Update room inventory
        let reservations: any;
        try {
          if (room_config_id) {
            // Find and reserve an available room for this configuration
            console.log(
              "Finding and reserving available room for configuration..."
            );

            // Resolve the room inventory service
            const roomInventoryService = req.scope.resolve(
              "roomInventoryService"
            );

            // Get all variants for this room configuration
            const productService = req.scope.resolve(Modules.PRODUCT);
            const roomConfig = await productService.retrieveProduct(
              room_config_id,
              {
                relations: ["variants"],
              }
            );

            if (
              roomConfig &&
              roomConfig.variants &&
              roomConfig.variants.length > 0
            ) {
              // Check each variant for availability
              for (const variant of roomConfig.variants) {
                const availability =
                  await roomInventoryService.checkAvailability(
                    variant.id,
                    checkInDate,
                    checkOutDate
                  );
                console.log(
                  "before reserving room",
                  variant.id,
                  checkInDate,
                  checkOutDate
                );

                if (availability.available) {
                  // Reserve this room and set order_id
                  const updatedEntries =
                    await roomInventoryService.updateInventoryStatus(
                      variant.id,
                      checkInDate,
                      checkOutDate,
                      "reserved_unassigned",
                      `Booking created (admin): ${
                        guest_name || "Guest"
                      } | Booking ID: ${
                        orderResult.id
                      } | Room Config: ${room_config_id}`,
                      null, // bookingInfo
                      null, // expiresAt
                      orderResult.id, // Set the order_id
                      null // No cart_id for direct bookings
                    );
                  console.log({ updatedEntries });

                  // Populate reservations from updatedEntries data
                  reservations = updatedEntries.map((entry) => {
                    return {
                      room_id: entry.inventory_item_id,
                      from_date: new Date(entry.from_date),
                      to_date: new Date(entry.to_date),
                      status: entry.status,
                      id: entry.id,
                    };
                  });

                  break; // Found an available room, no need to check more
                }
              }
            }

            console.log("Room reservation result:", reservations);
          } else {
            console.log(
              "Neither room_id nor room_config_id provided, skipping inventory update"
            );
          }

          // Update booking metadata with reservation info if available
          if (reservations) {
            const updatedMetadata = {
              ...bookingMetadata,
              reservations: reservations,
            };

            // Update the booking metadata
            bookingOrder.metadata = updatedMetadata;

            // Get the order service
            const orderService = req.scope.resolve(Modules.ORDER) as any;

            // Update the order with the reservation metadata
            await orderService.updateOrders(orderResult.id, {
              metadata: updatedMetadata,
            });
          }
        } catch (error) {
          console.error("Error reserving rooms:", error);
          // Continue with booking creation even if room reservation fails
        }

        return res.json({
          booking: bookingOrder,
          order: orderResult,
        });
      } catch (innerError) {
        console.error("Error in order workflow execution:", innerError);
        throw innerError; // Re-throw to be caught by the outer catch block
      }
    } catch (orderError) {
      console.error("Error creating order:", orderError);
      return res.status(400).json({
        message: "Failed to create order",
        error:
          orderError instanceof Error ? orderError.message : "Unknown error",
      });
    }
  } catch (error) {
    console.error("Error creating booking:", error);

    // Provide more detailed error information
    return res.status(500).json({
      message: "Failed to create booking",
      error: error instanceof Error ? error.message : "Unknown error",
      stack:
        process.env.NODE_ENV !== "production" && error instanceof Error
          ? error.stack
          : undefined,
    });
  }
}
