import {  withClient, withTransaction } from "src/utils/db";

// Function to check room availability
export async function checkRoomAvailability(roomId: string, checkInDate: Date, checkOutDate: Date) {
  try {
    return await withClient(async (client) => {
      // Check for existing bookings with overlapping dates
      const query = `
        SELECT o.id, o.metadata->>'check_in_date' as check_in_date, o.metadata->>'check_out_date' as check_out_date
        FROM "order" o
        WHERE (o.metadata->>'room_id' = $1 OR o.metadata->>'room_config_id' = $1)
        AND (
          -- Booking starts before or on our end date AND ends after our start date
          (o.metadata->>'check_in_date' <= $3 AND o.metadata->>'check_out_date' > $2)
        )
        AND o.status NOT IN ('canceled', 'archived')
      `;

      const values = [
        roomId,
        checkInDate.toISOString().split("T")[0], // Convert to YYYY-MM-DD
        checkOutDate.toISOString().split("T")[0], // Convert to YYYY-MM-DD
      ];

      const result = await client.query(query, values);

      if (result.rows.length > 0) {
        console.log(
          `Room ${roomId} is already booked for the requested dates:`,
          result.rows
        );

        // Collect all unavailable dates
        const unavailableDates: string[] = [];

        for (const booking of result.rows) {
          const bookingCheckIn = new Date(booking.check_in_date);
          const bookingCheckOut = new Date(booking.check_out_date);

          // Generate dates between check-in and check-out
          const currentDate = new Date(bookingCheckIn);
          while (currentDate < bookingCheckOut) {
            const dateStr = currentDate.toISOString().split("T")[0];
            if (!unavailableDates.includes(dateStr)) {
              unavailableDates.push(dateStr);
            }
            currentDate.setDate(currentDate.getDate() + 1);
          }
        }

        return {
          available: false,
          unavailableDates
        };
      }

      // If we get here, the room is available
      return {
        available: true,
        unavailableDates: []
      };
    });
  } catch (error) {
    console.error("Error checking room availability:", error);
    // For safety, assume the room is available if there's an error
    return {
      available: true,
      unavailableDates: []
    };
  }
}

