import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingInvoiceService = container.resolve("bookingInvoiceService");
    const roomInventoryService = container.resolve("roomInventoryService");

    const {
      hotel_id,
      room_id,
      check_in_date,
      check_out_date,
      guest_name,
      guest_email,
      guest_address,
      total_amount,
      currency_code,
      additional_items,
      hotel_name,
      hotel_address
    } = req.body;

    // Validate required fields
    if (!hotel_id || !room_id || !check_in_date || !check_out_date || !guest_name || !guest_email || !total_amount || !currency_code) {
      return res.status(400).json({
        message: "Missing required fields",
      });
    }

    // Check room availability
    const checkInDate = new Date(check_in_date);
    const checkOutDate = new Date(check_out_date);

    // Check availability
    const availability = await roomInventoryService.checkAvailability(
      room_id,
      checkInDate,
      checkOutDate
    );

    if (!availability.available) {
      return res.status(400).json({
        message: "Room is not available for the selected dates",
        unavailable_dates: availability.unavailableDates,
      });
    }

    // Create quote data
    const quoteData = {
      hotel_id,
      room_id,
      check_in_date: checkInDate,
      check_out_date: checkOutDate,
      guest_name,
      guest_email,
      guest_address,
      total_amount,
      currency_code,
      additional_items,
      hotel_name,
      hotel_address
    };

    // Create quote
    const quote = await bookingInvoiceService.createQuote(quoteData);

    res.json({
      quote
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create quote",
    });
  }
}
