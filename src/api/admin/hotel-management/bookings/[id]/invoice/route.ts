import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import * as pdf from "html-pdf";

// Helper function to format currency
function currencyFormatter(
  amount: number,
  currencyCode: string = "USD"
): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currencyCode.toUpperCase(),
  }).format(amount / 100); // Assuming amounts are in cents
}

// Helper function to format dates
function dateFormatter(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

// Enhanced function to replace placeholders in template content
function replacePlaceholders(
  content: string,
  dataContext: Record<string, any>
): string {
  let processedContent = content;

  // First, normalize the template by removing line breaks within placeholders
  const normalizePlaceholder = (placeholder: string): string => {
    return placeholder
      .replace(/\s*\n\s*/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  };

  // Find all double-brace placeholders and normalize them
  let normalizedTemplate = processedContent.replace(
    /{{\s*[^{}]*?}}/g,
    (match) => normalizePlaceholder(match)
  );

  // Find all triple-brace placeholders and normalize them
  normalizedTemplate = normalizedTemplate.replace(
    /{{{\s*[^{}]*?}}}/g,
    (match) => normalizePlaceholder(match)
  );

  processedContent = normalizedTemplate;

  // Handle default values with the pipe syntax
  const defaultValueRegex =
    /{{\s*([a-zA-Z0-9_.]+)\s*\|\s*default:\s*(?:['"]([^'"]*)['"]|''([^']*)'')\s*}}/g;
  processedContent = processedContent.replace(
    defaultValueRegex,
    (_, path, defaultValue1, defaultValue2) => {
      const defaultValue = defaultValue1 || defaultValue2 || "";
      const parts = path.trim().split(".");

      let value = dataContext;
      for (const part of parts) {
        value = value?.[part];
        if (value === undefined || value === null) {
          return defaultValue;
        }
      }

      return value.toString();
    }
  );

  // Replace placeholders recursively
  function replaceInObject(obj: any, prefix: string = ""): void {
    for (const [key, value] of Object.entries(obj)) {
      const placeholder = prefix ? `{{${prefix}.${key}}}` : `{{${key}}}`;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        // Recursively handle nested objects
        replaceInObject(value, prefix ? `${prefix}.${key}` : key);
      } else {
        // Replace the placeholder with the actual value
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          "g"
        );
        processedContent = processedContent.replace(regex, String(value || ""));
      }
    }
  }

  replaceInObject(dataContext);

  // Handle items_html_block specifically
  if (dataContext.items_html_block) {
    processedContent = processedContent.replace(
      /{{{\s*items_html_block\s*}}}/g,
      dataContext.items_html_block
    );
  }

  return processedContent;
}

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id: bookingId } = req.params;
  const INVOICE_TEMPLATE_EVENT_NAME = "booking.invoice.default"; // Or your chosen event name

  if (!bookingId) {
    return res.status(400).json({ message: "Booking ID is required." });
  }

  const orderService: any = req.scope.resolve(Modules.ORDER);
  const notificationTemplateService: NotificationTemplateService =
    req.scope.resolve(NOTIFICATION_TEMPLATE_SERVICE);

  try {
    // 1. Fetch the Order
    const order = await orderService.retrieveOrder(bookingId, {
      relations: ["billing_address", "items"],
    });

    if (!order) {
      return res.status(404).json({ message: "Booking not found." });
    }

    // 2. Fetch the Invoice Template (Corrected Logic)
    if (
      !notificationTemplateService ||
      typeof notificationTemplateService.listNotificationTemplates !==
        "function"
    ) {
      console.error(
        "NotificationTemplateService or listNotificationTemplates method not found."
      );
      const basicHtml = `<html><body><h1>Invoice Error</h1><p>Template service unavailable.</p><p>Order ID: ${
        order.display_id || order.id
      }</p></body></html>`;
      pdf.create(basicHtml).toBuffer((err, buffer) => {
        if (err) {
          console.error("Error generating basic fallback PDF:", err);
          return res
            .status(500)
            .json({ message: "Fallback PDF generation failed." });
        }
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="invoice-error.pdf"`
        );
        res.send(buffer);
      });
      return;
    }

    let invoiceTemplate = null; // Initialize as null
    try {
      const filters = {
        event_name: INVOICE_TEMPLATE_EVENT_NAME,
        is_active: true,
        channel: "pdf",
      };
      const config = { take: 1 }; // We only need one template

      // Use listNotificationTemplates as seen in the other route file
      const templates =
        await notificationTemplateService.listNotificationTemplates(
          filters,
          config
        );

      if (templates && templates.length > 0) {
        invoiceTemplate = templates[0];
        console.info(`Found invoice template: ${invoiceTemplate.id}`); // Added log
      } else {
        console.warn(
          `No active PDF template found for event: ${INVOICE_TEMPLATE_EVENT_NAME}`
        ); // Added log
      }
    } catch (templateError: any) {
      console.error(
        `Error fetching invoice template '${INVOICE_TEMPLATE_EVENT_NAME}':`,
        templateError
      );
      // Consider a fallback or re-throw if template is critical
    }

    // Use fallback template if no template found
    if (!invoiceTemplate || !invoiceTemplate.content) {
      console.warn(
        `No active PDF template found for event: ${INVOICE_TEMPLATE_EVENT_NAME}. Using fallback template.`
      );
      invoiceTemplate = {
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 30px; }
              .customer-details { margin-bottom: 30px; }
              .booking-details { margin-bottom: 30px; }
              .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
              .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .items-table th { background-color: #f2f2f2; }
              .total-section { text-align: right; }
              .total-line { margin: 5px 0; }
              .total-amount { font-weight: bold; font-size: 1.2em; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>INVOICE</h1>
            </div>

            <div class="invoice-details">
              <p><strong>Invoice #:</strong> {{order.display_id}}</p>
              <p><strong>Date:</strong> {{order.created_at_formatted}}</p>
              <p><strong>Status:</strong> {{order.status}}</p>
            </div>

            <div class="customer-details">
              <h3>Bill To:</h3>
              <p>{{customer.first_name}} {{customer.last_name}}</p>
              <p>{{customer.email}}</p>
              <p>{{customer.guest_phone}}</p>
            </div>

            <div class="booking-details">
              <h3>Booking Details:</h3>
              <p><strong>Hotel:</strong> {{booking.hotel_name}}</p>
              <p><strong>Room Type:</strong> {{booking.room_config_name}}</p>
              <p><strong>Check-in:</strong> {{booking.check_in_date}} at {{booking.check_in_time}}</p>
              <p><strong>Check-out:</strong> {{booking.check_out_date}} at {{booking.check_out_time}}</p>
              <p><strong>Guests:</strong> {{booking.number_of_guests}}</p>
              <p><strong>Special Requests:</strong> {{booking.special_requests}}</p>
            </div>

            <table class="items-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                {{{items_html_block}}}
              </tbody>
            </table>

            <div class="total-section">
              <div class="total-line">Subtotal: {{order.subtotal_formatted}}</div>
              <div class="total-line">Tax: {{order.tax_total_formatted}}</div>
              <div class="total-line total-amount">Total: {{order.total_formatted}}</div>
            </div>
          </body>
          </html>
        `,
      };
    }

    // 3. Prepare enhanced data context with booking metadata
    const metadata = order.metadata || {};

    // Create a comprehensive data context for placeholder replacement
    const dataContext: Record<string, any> = {
      order: {
        display_id: order.display_id || order.id,
        created_at_formatted: dateFormatter(order.created_at),
        subtotal_formatted: currencyFormatter(
          order.subtotal || 0,
          order.currency_code
        ),
        shipping_total_formatted: currencyFormatter(
          order.shipping_total || 0,
          order.currency_code
        ),
        tax_total_formatted: currencyFormatter(
          order.tax_total || 0,
          order.currency_code
        ),
        total_formatted: currencyFormatter(
          order.total || 0,
          order.currency_code
        ),
        currency_code: order.currency_code,
        status: order.status,
        notes: order.notes || "",
        payment_method_name: order.payment_method_name || "N/A",
      },
      customer: {
        email: order.customer?.email || order.email || "N/A",
        guest_phone: metadata.guest_phone || "N/A",
        first_name:
          order.customer?.first_name ||
          metadata.guest_name?.split(" ")[0] ||
          "",
        last_name:
          order.customer?.last_name ||
          metadata.guest_name?.split(" ").slice(1).join(" ") ||
          "",
      },
      billing_address: {
        address_1: order.billing_address?.address_1 || "",
        address_2: order.billing_address?.address_2 || "",
        city: order.billing_address?.city || "",
        province: order.billing_address?.province || "",
        postal_code: order.billing_address?.postal_code || "",
        country_code: order.billing_address?.country_code?.toUpperCase() || "",
        full_address_html: order.billing_address
          ? `${order.billing_address.address_1 || ""}<br>${
              order.billing_address.address_2 || ""
            }<br>${order.billing_address.city || ""}, ${
              order.billing_address.province || ""
            } ${order.billing_address.postal_code || ""}<br>${
              order.billing_address.country_code?.toUpperCase() || ""
            }`
              .replace(/<br><br>/g, "<br>")
              .trim()
          : "N/A",
      },
      booking: {
        hotel_name: metadata.hotel_name || "Hotel",
        room_config_name:
          metadata.room_config_name || metadata.room_type || "Standard Room",
        check_in_date: metadata.check_in_date
          ? dateFormatter(metadata.check_in_date)
          : "N/A",
        check_out_date: metadata.check_out_date
          ? dateFormatter(metadata.check_out_date)
          : "N/A",
        check_in_time: metadata.check_in_time || "12:00",
        check_out_time: metadata.check_out_time || "12:00",
        number_of_guests: metadata.number_of_guests || 1,
        special_requests: metadata.special_requests || "None",
        total_amount: currencyFormatter(
          metadata.total_amount * 100 || 0,
          order.currency_code
        ),
      },
      items_html_block:
        order.items
          ?.map(
            (item: any) =>
              `<tr>
              <td>${item.title || "Hotel Booking"}</td>
              <td style="text-align: center;">${
                item.quantity?.toString() || "1"
              }</td>
              <td style="text-align: right;">${currencyFormatter(
                item.unit_price || 0,
                order.currency_code
              )}</td>
              <td style="text-align: right;">${currencyFormatter(
                item.total || item.unit_price * item.quantity || 0,
                order.currency_code
              )}</td>
            </tr>`
          )
          .join("") || `<tr><td colspan="4">No items found</td></tr>`,
    };

    // 4. Replace placeholders in the template content
    const processedHtmlContent = replacePlaceholders(
      invoiceTemplate.content,
      dataContext
    );

    // 5. Generate PDF with enhanced options
    const options: pdf.CreateOptions = {
      format: "A4",
      orientation: "portrait",
      border: { top: "0.5in", right: "0.5in", bottom: "0.5in", left: "0.5in" },
    };

    pdf.create(processedHtmlContent, options).toBuffer((err, buffer) => {
      if (err) {
        console.error("Error generating PDF with html-pdf:", err);
        return res.status(500).json({
          message: "Failed to generate PDF.",
          errorDetail: err.message,
        });
      }

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="invoice-${order.display_id || order.id}.pdf"`
      );
      res.send(buffer);
    });
  } catch (error: any) {
    console.error("Error in GET /admin/bookings/[id]/invoice route:", error);
    let statusCode = 500;
    let message = "Failed to process invoice request.";

    // Enhanced error handling
    if (error.message?.includes("'strategy'")) {
      message =
        "Failed to process order relations. Please check ORM configuration.";
    } else if (
      error.message?.toLowerCase().includes("not found") ||
      error.name === "NotFoundError" ||
      error.type === "not_found"
    ) {
      statusCode = 404;
      message = error.message || "Booking not found.";
    }

    res.status(statusCode).json({
      message,
      errorDetail: error.message,
    });
  }
}
