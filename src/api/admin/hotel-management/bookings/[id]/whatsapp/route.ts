import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { WHATSAPP_MESSAGE_SERVICE } from "../../../../../../modules/whatsapp-message";
import { Modules } from "@camped-ai/framework/utils";
import { MedusaError } from "@camped-ai/framework/utils";
import { SendWhatsAppMessageWorkflow } from "../../../../../../workflows/send-whatsapp";
import { z } from "zod";

export const SendWhatsAppMessageSchema = z.object({
  message: z.string().min(1, "Message content is required"),
});
export type  SendWhatsAppMessage = z.infer<typeof SendWhatsAppMessageSchema>;

/**
 * Format a message with proper line breaks and structure
 * @param message Original message content
 * @param order Order data for context
 * @returns Formatted message
 */
function formatBookingMessage(message: string, order: any): string {
  // Check if the message already has formatting
  if (message.includes("\n") || message.includes("*")) {
    return message; // Already formatted
  }

  // Check if this looks like a booking confirmation
  if (message.toLowerCase().includes("booking") &&
      (message.toLowerCase().includes("confirm") || message.toLowerCase().includes("details"))) {

    // Extract guest name from order if available
    const guestName = order.metadata?.guest_name || order.shipping_address?.first_name || "Guest";

    // Get hotel name from metadata if available
    const hotelName = order.metadata?.hotel_name || "Our Hotel";

    // Format check-in and check-out dates if available
    const checkInDate = order.metadata?.check_in_date
      ? new Date(order.metadata.check_in_date).toLocaleDateString()
      : "N/A";
    const checkOutDate = order.metadata?.check_out_date
      ? new Date(order.metadata.check_out_date).toLocaleDateString()
      : "N/A";

    // Get check-in and check-out times
    const checkInTime = order.metadata?.check_in_time || "12:00";
    const checkOutTime = order.metadata?.check_out_time || "12:00";

    // Calculate number of nights if dates are available
    let nights = "N/A";
    if (order.metadata?.check_in_date && order.metadata?.check_out_date) {
      const checkIn = new Date(order.metadata.check_in_date);
      const checkOut = new Date(order.metadata.check_out_date);
      const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
      nights = Math.ceil(diffTime / (1000 * 60 * 60 * 24)).toString();
    }

    // Get guest information
    const adults = order.metadata?.adults || "2";
    const roomType = order.metadata?.room_type || "Standard Room";

    // Format as a booking confirmation with detailed information and proper line breaks
    return `*Booking Confirmation*

Hello ${guestName},

Your booking at ${hotelName} is confirmed!

*Booking Details:*
• Booking ID: ${order.id}
• Check-in: ${checkInDate} at ${checkInTime}
• Check-out: ${checkOutDate} at ${checkOutTime}
• Duration: ${nights} nights
• Guests: ${adults} adults
• Room Type: ${roomType}

We look forward to welcoming you!

Thank you for choosing us.`;
  }

  // For general messages, just add some basic formatting
  return message.split(". ").join(".\n\n");
}

/**
 * @swagger
 * /admin/hotel-management/bookings/{id}/whatsapp:
 *   get:
 *     summary: Get WhatsApp messages for a booking
 *     description: Retrieves all WhatsApp messages associated with a booking
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the booking (order)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successfully retrieved messages
 *       400:
 *         description: Bad request
 *       404:
 *         description: Booking not found
 *   post:
 *     summary: Send a WhatsApp message for a booking
 *     description: Sends a WhatsApp message to the guest associated with a booking
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the booking (order)
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - message
 *             properties:
 *               message:
 *                 type: string
 *                 description: The message content
 *     responses:
 *       200:
 *         description: Message sent successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: Booking not found
 */
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> => {
  const { id } = req.params;

  if (!id) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "Booking ID is required"
    );
  }

  try {
    // First, verify that the booking exists
    const orderService = req.scope.resolve(Modules.ORDER);
    const order = await orderService.retrieveOrder(id);

    if (!order) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Booking with ID ${id} not found`
      );
    }

    // Get the WhatsApp message service
    const whatsAppMessageService = req.scope.resolve(WHATSAPP_MESSAGE_SERVICE);

    if (!whatsAppMessageService) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        "WhatsApp message service not found"
      );
    }

    // Get messages for the booking
    const messages = await whatsAppMessageService.getMessagesByOrderId(id);

    // Map status to human-readable format and add status code for UI
    const formattedMessages = messages.map(message => {
      let statusCode = 0; // 0: sent, 1: delivered, 2: read
      let statusText = "sent";

      if (message.status === "read") {
        statusCode = 2;
        statusText = "read";
      } else if (message.status === "delivered") {
        statusCode = 1;
        statusText = "delivered";
      }

      return {
        ...message,
        statusCode,
        statusText,
        isOutbound: message.direction === "outbound" || message.from_phone === process.env.WHATSAPP_PHONE_NUMBER_ID,
        created_at: message.created_at || new Date().toISOString(),
        updated_at: message.updated_at || new Date().toISOString()
      };
    });

    // Sort messages by creation date (oldest first)
    formattedMessages.sort((a, b) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    res.status(200).json({
      messages: formattedMessages
    });
  } catch (error) {
    if (error instanceof MedusaError) {
      throw error;
    }

    throw new MedusaError(
      MedusaError.Types.UNEXPECTED_STATE,
      `Error retrieving WhatsApp messages: ${error.message}`
    );
  }
}

export const POST = async (
  req: MedusaRequest<SendWhatsAppMessage>,
  res: MedusaResponse
): Promise<void> => {
  const { id } = req.params;
  const { message } = req.body;

  if (!id) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "Booking ID is required"
    );
  }

  if (!message) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "Message content is required"
    );
  }

  try {
    // Get the order service
    const orderService = req.scope.resolve(Modules.ORDER);

    // Retrieve the order to get guest phone
    const order = await orderService.retrieveOrder(id);

    if (!order) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Booking with ID ${id} not found`
      );
    }

    // Check if order has guest_phone in metadata
    if (!order.metadata || !order.metadata.guest_phone) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Booking does not have a guest phone number"
      );
    }

    const guestPhone = order.metadata.guest_phone;

    // Format the message with proper line breaks if needed
    const formattedMessage = formatBookingMessage(message, order);

    // Send the WhatsApp message using the workflow
    const {result} = await SendWhatsAppMessageWorkflow(req.scope).run({input:{
      to: String(guestPhone),
      message: formattedMessage, // Use the formatted message
      order_id: id,
      customer_id: order.customer_id
    }
  });
  console.log({result})

    if (result.success) {
      res.status(200).json({
        success: true,
        message_id: result.message_id,
        message: "WhatsApp message sent successfully"
      });
    } else {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to send WhatsApp message: ${result.error || "Unknown error"}`
      );
    }
  } catch (error) {
    if (error instanceof MedusaError) {
      throw error;
    }

    throw new MedusaError(
      MedusaError.Types.UNEXPECTED_STATE,
      `Error sending WhatsApp message: ${error.message}`
    );
  }
}
