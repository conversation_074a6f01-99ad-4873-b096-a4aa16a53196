import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");

    const { id } = req.params;
    const { status, metadata } = req.body;

    if (!status) {
      return res.status(400).json({
        message: "Status is required",
      });
    }

    // Update booking status
    const updatedBooking = await bookingOrderService.updateStatus(id, status, metadata);

    res.json({
      booking: updatedBooking
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update booking status",
    });
  }
}
