import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");

    const { id } = req.params;
    const { payment_status } = req.body;

    if (!payment_status) {
      return res.status(400).json({
        message: "Payment status is required",
      });
    }

    // Update booking payment status
    const updatedBooking = await bookingOrderService.updatePaymentStatus(id, payment_status);

    res.json({
      booking: updatedBooking
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update payment status",
    });
  }
}
