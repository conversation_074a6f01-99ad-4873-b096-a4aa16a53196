import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { STRIPE_PAYMENT_LINK_SERVICE } from "../../../../../../modules/stripe-payment-link";

/**
 * Generate a payment link for a booking
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const { expiry_hours = 24, email } = req.body;

    // Get the booking service
    const orderService = req.scope.resolve(Modules.ORDER);

    // Get the booking
    const booking = await orderService.retrieveOrder(id);

    if (!booking) {
      return res.status(404).json({
        message: `Booking with ID ${id} not found`,
      });
    }

    // Get the Stripe payment link service
    const stripePaymentLinkService = req.scope.resolve(
      STRIPE_PAYMENT_LINK_SERVICE
    );

    // Cast booking to any to avoid TypeScript errors
    const bookingData = booking as any;
    console.log({ bookingData });
    // Calculate total amount including add-ons
    let totalAmount = bookingData.metadata.total_amount || 0;
    if (bookingData.metadata.add_on_total_amount) {
      totalAmount += bookingData.metadata.add_on_total_amount / 100; // Add-ons stored in cents
    }

    // Generate a payment link
    const paymentLink = await (
      stripePaymentLinkService as any
    ).generatePaymentLink({
      amount: totalAmount,
      currency: (bookingData.currency_code || "usd").toLowerCase(),
      orderId: bookingData.id,
      description: `Room booking: ${
        bookingData.metadata?.room_type || "Standard Room"
      }`,
      customerEmail:
        email || bookingData.customer?.email || bookingData.guest_email,
      expiresInHours: expiry_hours,
      metadata: {
        hotel_id: bookingData.hotel_id,
        room_id: bookingData.room_id,
        check_in_date: bookingData.check_in_date,
        check_out_date: bookingData.check_out_date,
      },
    });

    await orderService.updateOrders(id, {
      metadata: {
        ...booking.metadata,
        payment_link: paymentLink.url,
        payment_link_id: paymentLink.id,
        // Calculate expiration time based on creation time and expires_after
        payment_link_expires_at: paymentLink.expires_after
          ? new Date(
              (paymentLink.created + paymentLink.expires_after) * 1000
            ).toISOString()
          : null,
      },
    });

    return res.json({
      payment_link: {
        id: paymentLink.id,
        url: paymentLink.url,
        // Calculate expiration time based on creation time and expires_after
        expires_at: paymentLink.expires_after
          ? new Date(
              (paymentLink.created + paymentLink.expires_after) * 1000
            ).toISOString()
          : null,
      },
    });
  } catch (error) {
    console.error("Error generating payment link:", error);

    // Provide more detailed error information
    const errorDetails =
      error instanceof Error
        ? {
            message: error.message,
            name: error.name,
            stack: error.stack,
            // Include Stripe-specific error details if available
            ...(error["type"] && { type: error["type"] }),
            ...(error["code"] && { code: error["code"] }),
            ...(error["param"] && { param: error["param"] }),
            ...(error["doc_url"] && { doc_url: error["doc_url"] }),
          }
        : "Unknown error";

    return res.status(500).json({
      message: "Failed to generate payment link",
      error: errorDetails,
    });
  }
}

/**
 * Get payment link information for a booking
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;

    // Get the order service
    const orderService = req.scope.resolve(Modules.ORDER);

    // Get the booking
    const booking = await orderService.retrieveOrder(id);

    if (!booking) {
      return res.status(404).json({
        message: `Booking with ID ${id} not found`,
      });
    }

    // Cast booking to any to avoid TypeScript errors
    const bookingData = booking as any;

    // Check if booking has a payment link
    if (!bookingData.metadata?.payment_link_id) {
      return res.status(404).json({
        message: "No payment link found for this booking",
      });
    }

    // Get the Stripe payment link service
    const stripePaymentLinkService = req.scope.resolve(
      STRIPE_PAYMENT_LINK_SERVICE
    );

    // Get payment link details from Stripe
    const paymentLinkId = bookingData.metadata.payment_link_id;
    // Cast to any to avoid TypeScript errors
    const paymentLink = await (
      stripePaymentLinkService as any
    ).retrievePaymentLink(paymentLinkId);

    return res.json({
      payment_link: {
        id: paymentLink.id,
        url: paymentLink.url,
        status: paymentLink.active ? "active" : "inactive",
        // Calculate expiration time based on creation time and expires_after
        expires_at: paymentLink.expires_after
          ? new Date(
              (paymentLink.created + paymentLink.expires_after) * 1000
            ).toISOString()
          : null,
        created_at: new Date(paymentLink.created * 1000).toISOString(),
      },
    });
  } catch (error) {
    console.error("Error retrieving payment link:", error);

    // Provide more detailed error information
    const errorDetails =
      error instanceof Error
        ? {
            message: error.message,
            name: error.name,
            stack: error.stack,
            // Include Stripe-specific error details if available
            ...(error["type"] && { type: error["type"] }),
            ...(error["code"] && { code: error["code"] }),
            ...(error["param"] && { param: error["param"] }),
            ...(error["doc_url"] && { doc_url: error["doc_url"] }),
          }
        : "Unknown error";

    return res.status(500).json({
      message: "Failed to retrieve payment link",
      error: errorDetails,
    });
  }
}
