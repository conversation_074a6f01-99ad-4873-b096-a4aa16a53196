import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ICartModuleService } from "@camped-ai/framework/types";

/**
 * Admin API endpoint for retrieving a specific cart's details
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartModuleService: ICartModuleService = req.scope.resolve(
      Modules.CART
    );

    // Get cart ID from path parameter
    const cartId = req.params.id;

    if (!cartId) {
      return res.status(400).json({ error: "Cart ID is required" });
    }

    // Fetch cart with all related entities
    const cart = await cartModuleService.retrieveCart(cartId, {
      relations: ["items", "shipping_address", "billing_address"],
    });

    if (!cart) {
      return res.status(404).json({ error: "Cart not found" });
    }

    // Check if this cart has been converted to an order
    if (
      cart.metadata &&
      typeof cart.metadata === "object" &&
      cart.metadata.parent_order_id
    ) {
      return res.status(400).json({
        error: "This cart has already been converted to an order",
        order_id: cart.metadata.parent_order_id,
      });
    }

    // Process cart to extract relevant information
    // Extract hotel information from metadata
    const hotelService = req.scope.resolve("hotel");
    const hotels = await hotelService.listHotels();
    const hotelName =
      hotels.find((m) => m.id === cart.metadata?.hotel_id)?.name ||
      "Unknown Hotel";
    const hotelId = cart.metadata?.hotel_id;

    // Extract guest information
    const guestName = cart.metadata?.guest_name || "Guest";
    const guestEmail = cart.metadata?.guest_email || "No email provided";
    const guestPhone = cart.metadata?.guest_phone || "No phone provided";

    // Extract room information from the first line item
    const roomType =
      cart.metadata?.room_config_name ||
      cart.metadata?.room_type ||
      (cart.items && cart.items[0]?.metadata?.room_config_name) ||
      (cart.items && cart.items[0]?.metadata?.room_type) ||
      "Standard Room";

    // Extract check-in and check-out dates
    const checkInDate =
      cart.metadata?.check_in_date ||
      (cart.items && cart.items[0]?.metadata?.check_in_date);
    const checkOutDate =
      cart.metadata?.check_out_date ||
      (cart.items && cart.items[0]?.metadata?.check_out_date);

    // Extract check-in and check-out times
    const checkInTime = cart.metadata?.check_in_time || "12:00";
    const checkOutTime = cart.metadata?.check_out_time || "12:00";

    // Extract number of rooms and guests
    const numberOfRooms =
      cart.metadata?.number_of_rooms ||
      (cart.items && cart.items[0]?.metadata?.number_of_rooms) ||
      1;
    const numberOfGuests =
      cart.metadata?.number_of_guests ||
      (cart.metadata?.adults ? Number(cart.metadata.adults) : 1);

    // Extract special requests
    const specialRequests = cart.metadata?.special_requests || "";

    // Calculate total amount and get currency
    const totalAmount = cart.metadata.total_amount || 0;
    const currencyCode = cart.metadata?.currency_code || "USD";

    // Format line items
    const lineItems =
      cart.items?.map((item) => ({
        id: item.id,
        title: item.title,
        quantity: item.quantity || 1,
        unit_price: item.unit_price || 0,
        total: Number(item.quantity || 1) * Number(item.unit_price || 0),
        metadata: item.metadata || {},
      })) || [];

    // Format payment information
    const paymentSessions = [];

    // Construct the processed cart object
    const processedCart = {
      id: cart.id,
      cart_id: cart.id,
      guest_name: guestName,
      guest_email: guestEmail,
      guest_phone: guestPhone,
      hotel_id: hotelId,
      hotel_name: hotelName,
      room_type: roomType,
      check_in_date: checkInDate,
      check_out_date: checkOutDate,
      check_in_time: checkInTime,
      check_out_time: checkOutTime,
      number_of_rooms: numberOfRooms,
      number_of_guests: numberOfGuests,
      special_requests: specialRequests,
      total_amount: totalAmount,
      currency_code: currencyCode,
      created_at: cart.created_at,
      updated_at: cart.updated_at,
      line_items: lineItems,
      payment_sessions: paymentSessions,
      shipping_address: cart.shipping_address,
      billing_address: cart.billing_address,
      customer: null,
      metadata: cart.metadata || {},
    };

    // Return the processed cart
    return res.status(200).json({
      cart: processedCart,
    });
  } catch (error) {
    console.error("Error retrieving cart details:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

/**
 * Admin API endpoint for deleting a cart
 */
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartModuleService: ICartModuleService = req.scope.resolve(
      Modules.CART
    );

    // Get cart ID from path parameter
    const cartId = req.params.id;

    if (!cartId) {
      return res.status(400).json({ error: "Cart ID is required" });
    }

    // Check if cart exists
    try {
      await cartModuleService.retrieveCart(cartId);
    } catch (error) {
      return res.status(404).json({ error: "Cart not found" });
    }

    // Delete the cart
    await cartModuleService.deleteCarts([cartId]);

    // Return success response
    return res.status(200).json({
      id: cartId,
      object: "cart",
      deleted: true,
    });
  } catch (error) {
    console.error("Error deleting cart:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
