import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

/**
 * Get all room types
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Return mock data
    const mockRoomTypes = [
      {
        id: "prod_01JR545CZ65M4RYNVQ8Z11A7MT",
        name: "Standard Room",
        description: "Comfortable standard room with all basic amenities",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: "prod_01JR09HT50BFWV3FYWDPTEWEGF",
        name: "Deluxe Room",
        description: "Spacious deluxe room with premium amenities",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: "prod_01JR0AKWS6Y0ATMG3H999ZVPV4",
        name: "Suite",
        description: "Luxurious suite with separate living area",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
    
    return res.json({
      roomTypes: mockRoomTypes,
    });
  } catch (error) {
    console.error("Error fetching room types:", error);
    return res.status(500).json({
      message: "Error fetching room types",
      error: error.message,
    });
  }
};
