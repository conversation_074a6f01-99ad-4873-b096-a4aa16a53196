import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../modules/hotel-management/room-inventory";
import { z } from "zod";

// Validation schema for creating a room
export const PostAdminCreateRoom = z.object({
  name: z.string(),
  room_number: z.string(),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]),
  floor: z.string(),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References
  room_config_id: z.string(),
  hotel_id: z.string(),
  price: z.number().optional(),
  currency_code: z.string().optional(),
  // Options for the room (optional)
  options: z.record(z.string(), z.string()).optional(),
});

export type PostAdminCreateRoomType = z.infer<typeof PostAdminCreateRoom>;

// Validation schema for updating a room
export const PostAdminUpdateRoom = z.object({
  id: z.string(),
  name: z.string().optional(),
  room_number: z.string().optional(),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]).optional(),
  floor: z.string().optional(),
  notes: z.string().optional(),
  is_active: z.boolean().optional(),
  // Room relationships
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // Options for the room (optional)
  options: z.record(z.string(), z.string()).optional(),
});

export type PostAdminUpdateRoomType = z.infer<typeof PostAdminUpdateRoom>;

// GET endpoint to list rooms
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const { room_config_id, hotel_id } = req.query;

  // Don't use query filters for now
  // Just fetch all products and filter them in memory

  console.log(`Fetching all products`);

  let products = [];

  // If room_config_id is provided, we only need to fetch that specific product
  if (room_config_id) {
    console.log(`Fetching specific product with ID: ${room_config_id}`);
    try {
      // Use the query to get the product
      const { data: productData } = await query.graph({
        entity: "product",
        filters: { id: room_config_id as string },
        fields: ["id", "title", "metadata"],
      });

      // Get variants for this product
      const { data: variantsData } = await query.graph({
        entity: "product_variant",
        filters: { product_id: room_config_id as string },
        fields: ["id", "title", "product_id", "metadata", "inventory_quantity", "options"],
      });

      if (productData && productData.length > 0) {
        const product = productData[0];
        console.log(`Found product: ${product.title}`);
        products = [product];

        // If we have variants for this product, we can return the rooms directly
        if (variantsData && variantsData.length > 0) {
          console.log(`Found ${variantsData.length} variants for product ${product.title}`);

          // Map variants to room objects
          const rooms = variantsData.map((variant: any) => {
            console.log(`Processing variant: ${variant.id} (${variant.title}) for product: ${product.title}`);

            return {
              id: variant.id,
              name: variant.title,
              room_number: variant.metadata?.room_number || "",
              status: variant.metadata?.status || "available",
              floor: variant.metadata?.floor || "",
              notes: variant.metadata?.notes || "",
              is_active: variant.metadata?.is_active !== false,
              left_room: variant.metadata?.left_room || "",
              opposite_room: variant.metadata?.opposite_room || "",
              connected_room: variant.metadata?.connected_room || "",
              right_room: variant.metadata?.right_room || "",
              room_config_id: product.id,
              hotel_id: product.metadata?.hotel_id || hotel_id as string,
              options: variant.options || {},
            };
          });

          console.log(`Returning ${rooms.length} rooms directly from product service`);
          return res.json({ rooms });
        }
      } else {
        console.log(`Product not found with ID: ${room_config_id}`);
        return res.json({ rooms: [] });
      }
    } catch (error) {
      console.error(`Error fetching product: ${error.message}`);
      return res.json({ rooms: [] });
    }
  } else {
    // Otherwise, get all products and filter by hotel_id
    const { data: allProducts } = await query.graph({
      entity: "product",
      fields: ["id", "title", "metadata"],
    });

    console.log(`Found ${allProducts?.length || 0} total products`);

    if (!allProducts || allProducts.length === 0) {
      console.log(`No products found`);
      return res.json({ rooms: [] });
    }

    // Filter products in memory
    products = allProducts.filter(product => {
      // Check if hotel_id matches
      if (hotel_id) {
        console.log(`Comparing hotel_id: ${hotel_id} with product.metadata?.hotel_id: ${product.metadata?.hotel_id}`);
        // Convert both to strings for comparison to avoid type mismatches
        if (String(product.metadata?.hotel_id) !== String(hotel_id)) {
          return false;
        }
      }

      return true;
    });
  }

  console.log(`After filtering, found ${products.length} products`);

  if (products.length === 0) {
    console.log(`No products found after filtering`);
    return res.json({ rooms: [] });
  }

  // Get all product variants for these products
  const productIds = products.map(p => p.id);
  console.log(`Fetching variants for product IDs: ${productIds.join(', ')}`);

  let variants = [];
  try {
    const { data: variantsData } = await query.graph({
      entity: "product_variant",
      filters: { product_id: productIds },
      fields: ["id", "title", "product_id", "metadata", "inventory_quantity", "options.id", "options.value", "options.option.title"],
    });

    variants = variantsData || [];
    console.log(`Found ${variants.length} variants`);

    if (variants.length === 0) {
      console.log(`No variants found for products`);
      return res.json({ rooms: [] });
    }
  } catch (error) {
    console.error(`Error fetching variants: ${error.message}`);
    return res.json({ rooms: [] });
  }

  // Map variants to room objects
  const rooms = variants.map(variant => {
    const product = products.find(p => p.id === variant.product_id);
    console.log(`Processing variant: ${variant.id} (${variant.title}) for product: ${product?.title || 'unknown'}`);

    // Get options for this variant
    const variantOptions = {};
    if (variant.options && Array.isArray(variant.options)) {
      variant.options.forEach(optionValue => {
        if (optionValue.option) {
          variantOptions[optionValue.option.title] = optionValue.value;
        }
      });
    }

    return {
      id: variant.id,
      name: variant.title,
      room_number: variant.metadata?.room_number || "",
      status: variant.metadata?.status || "available",
      floor: variant.metadata?.floor || "",
      notes: variant.metadata?.notes || "",
      is_active: variant.metadata?.is_active !== false,
      left_room: variant.metadata?.left_room || "",
      opposite_room: variant.metadata?.opposite_room || "",
      connected_room: variant.metadata?.connected_room || "",
      right_room: variant.metadata?.right_room || "",
      room_config_id: product?.id || "",
      hotel_id: product?.metadata?.hotel_id || (hotel_id as string),
      options: variantOptions,
      created_at: variant.created_at,
      updated_at: variant.updated_at,
    };
  });

  console.log(`Returning ${rooms.length} actual rooms`);
  console.log(`Sample room:`, rooms.length > 0 ? JSON.stringify(rooms[0]) : 'No rooms found');

  res.json({ rooms });
};

// POST endpoint to create a room
export const POST = async (req: MedusaRequest<PostAdminCreateRoomType>, res: MedusaResponse) => {
  try {
    const {
      name,
      room_number,
      status,
      floor,
      notes,
      is_active,
      left_room,
      opposite_room,
      connected_room,
      right_room,
      room_config_id,
      hotel_id,
      price,
      currency_code,
      options
    } = req.body;

    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Try to resolve the room inventory service, but don't fail if it's not available
    let roomInventoryService = null;
    try {
      roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);
    } catch (error) {
      console.warn("Room inventory service not available. Skipping inventory creation.");
    }

    // First, check if a product exists for this room configuration
    const { data: products } = await req.scope.resolve("query").graph({
      entity: "product",
      filters: { id: room_config_id },
      fields: ["id", "title", "subtitle", "description", "metadata"],
    });

    if (!products || products.length === 0) {
      return res.status(404).json({
        message: "Room configuration not found. Please create a room configuration first."
      });
    }

    const productId = products[0].id;

    // Process options if provided
    const optionIds = {};
    if (options && Object.keys(options).length > 0) {
      console.log("Processing options:", options);

      // Get existing product options
      const { data: existingOptions } = await req.scope.resolve("query").graph({
        entity: "product_option",
        filters: { product_id: productId },
        fields: ["id", "title"],
      });

      console.log("Existing options:", existingOptions);

      // Create options and option values
      for (const [optionTitle, optionValue] of Object.entries(options)) {
        console.log(`Processing option: ${optionTitle} = ${optionValue}`);

        // Check if option already exists
        let optionId = existingOptions?.find(o => o.title === optionTitle)?.id;
        console.log(`Option ${optionTitle} exists: ${!!optionId}`);

        // If option doesn't exist, create it
        if (!optionId) {
          console.log(`Creating option: ${optionTitle}`);
          const option = await productModuleService.createProductOptions({
            title: optionTitle,
            product_id: productId,
          });
          optionId = option.id;
          console.log(`Created option with ID: ${optionId}`);
        }

        // Get existing option values
        const { data: existingValues } = await req.scope.resolve("query").graph({
          entity: "product_option_value",
          filters: { option_id: optionId },
          fields: ["id", "value"],
        });

        console.log(`Existing values for option ${optionId}:`, existingValues);

        // Check if option value already exists
        let optionValueId = existingValues?.find(v => v.value === optionValue)?.id;
        console.log(`Option value ${optionValue} exists: ${!!optionValueId}`);

        // If option value doesn't exist, create it
        if (!optionValueId) {
          console.log(`Creating option value: ${optionValue}`);
          const newOptionValue = await productModuleService.createProductOptionValues({
            value: optionValue,
            option_id: optionId,
          });
          optionValueId = newOptionValue.id;
          console.log(`Created option value with ID: ${optionValueId}`);
        }

        // Store option ID and value ID
        optionIds[optionId] = optionValueId;
      }

      console.log("Final optionIds:", optionIds);
    }

    // Create a product variant for the specific room
    console.log("Creating product variant with options:", Object.keys(optionIds).length > 0 ? optionIds : undefined);

    // Format options for variant creation - this is the key change
    // Instead of passing option_id: option_value_id, we need to pass an array of objects
    // with option_id and value properties
    const formattedOptions = [];
    if (Object.keys(optionIds).length > 0) {
      for (const [optionId, optionValueId] of Object.entries(optionIds)) {
        // Get the option value from the database
        const { data: optionValues } = await req.scope.resolve("query").graph({
          entity: "product_option_value",
          filters: { id: optionValueId },
          fields: ["id", "value"],
        });

        if (optionValues && optionValues.length > 0) {
          formattedOptions.push({
            option_id: optionId,
            value: optionValues[0].value,
          });
        }
      }
    }

    console.log("Formatted options:", formattedOptions);

    const variantData = {
      title: name || `Room ${room_number}`,
      product_id: productId,
      inventory_quantity: status === "available" && is_active ? 1 : 0,
      manage_inventory: true,
      allow_backorder: false,
      options: formattedOptions.length > 0 ? formattedOptions : undefined, // Set the options if any
      metadata: {
        room_number: room_number,
        floor: floor,
        notes: notes,
        status: status,
        is_active: is_active,
        left_room: left_room,
        opposite_room: opposite_room,
        connected_room: connected_room,
        right_room: right_room,
      },
    };

    console.log("Variant data:", JSON.stringify(variantData, null, 2));

    const variant = await productModuleService.createProductVariants(variantData);
    console.log("Created variant:", variant);

   
    // Emit the room.created event
    try {
      const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);
      await eventModuleService.emit({
        name: "room.created",
        data: {
          id: variant.id,
          variant: {
            ...variant,
            metadata: {
              ...variant.metadata,
              hotel_id: hotel_id,
              room_config_id: room_config_id
            }
          }
        },
      });
      console.log(`Emitted room.created event for room ${room_number}`);
    } catch (eventError) {
      console.error("Error emitting room.created event:", eventError);
      // Continue even if event emission fails
    }

    // Return the created room (variant) with additional information
    res.json({
      room: {
        id: variant.id,
        name: name || `Room ${room_number}`,
        room_number: room_number,
        status: status,
        floor: floor,
        notes: notes,
        is_active: is_active,
        left_room: left_room,
        opposite_room: opposite_room,
        connected_room: connected_room,
        right_room: right_room,
        room_config_id: room_config_id,
        hotel_id: hotel_id,
        product_id: productId,
        variant_id: variant.id,
        inventory_item_id: variant.id,
        options: options || {},
      }
    });
  } catch (error) {
    console.error("Error creating room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create room",
    });
  }
};

// PUT endpoint to update a room
export const PUT = async (req: MedusaRequest<PostAdminUpdateRoomType>, res: MedusaResponse) => {
  try {
    console.log("PUT /admin/direct-rooms - Request body:", req.body);
    const { id, options, ...updateData } = req.body;
    console.log("Extracted updateData:", updateData);

    // Get the product variant
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const variant = await productModuleService.retrieveProductVariant(id, {
      relations: ["product", "options"],
    });

    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }

    // Update the variant metadata
    const updatedMetadata = { ...variant.metadata };

    if (updateData.room_number) updatedMetadata.room_number = updateData.room_number;
    if (updateData.floor) updatedMetadata.floor = updateData.floor;
    if (updateData.notes !== undefined) updatedMetadata.notes = updateData.notes;
    if (updateData.status) updatedMetadata.status = updateData.status;
    if (updateData.is_active !== undefined) updatedMetadata.is_active = updateData.is_active;
    if (updateData.left_room !== undefined) updatedMetadata.left_room = updateData.left_room;
    if (updateData.opposite_room !== undefined) updatedMetadata.opposite_room = updateData.opposite_room;
    if (updateData.connected_room !== undefined) updatedMetadata.connected_room = updateData.connected_room;
    if (updateData.right_room !== undefined) updatedMetadata.right_room = updateData.right_room;

    // Update options if provided
    if (options && Object.keys(options).length > 0) {
      // Get existing product options
      const { data: existingOptions } = await req.scope.resolve("query").graph({
        entity: "product_option",
        filters: { product_id: variant.product_id },
        fields: ["id", "title"],
      });

      const optionIds = {};

      // Process each option
      for (const [optionTitle, optionValue] of Object.entries(options)) {
        // Check if option already exists
        let optionId = existingOptions?.find(o => o.title === optionTitle)?.id;

        // If option doesn't exist, create it
        if (!optionId) {
          const option = await productModuleService.createProductOptions({
            title: optionTitle,
            product_id: variant.product_id,
          });
          optionId = option.id;
        }

        // Get existing option values
        const { data: existingValues } = await req.scope.resolve("query").graph({
          entity: "product_option_value",
          filters: { option_id: optionId },
          fields: ["id", "value"],
        });

        // Check if option value already exists
        let optionValueId = existingValues?.find(v => v.value === optionValue)?.id;

        // If option value doesn't exist, create it
        if (!optionValueId) {
          const optionValueObj = await productModuleService.createProductOptionValues({
            value: optionValue,
            option_id: optionId,
          });
          optionValueId = optionValueObj.id;
        }

        // Store option ID and value ID
        optionIds[optionId] = optionValueId;
      }

      // Update variant options
      if (Object.keys(optionIds).length > 0) {
        await productModuleService.updateProductVariantOptions(id, optionIds);
      }
    }

    // Update the variant
    console.log("Updating variant with data:", {
      id,
      title: updateData.name,
      metadata: updatedMetadata
    });

    // Update the variant using the correct method signature
    console.log("Calling updateProductVariants with id:", id);
    const updatedVariant = await productModuleService.updateProductVariants(id, {
      title: updateData.name,
      metadata: updatedMetadata
    });

    // Get updated options for this variant
    const optionValues = [];
    // Note: We're skipping option values retrieval since it's causing issues
    // This would normally fetch the option values for the variant

    // Map option values to a more readable format
    const variantOptions = {};
    if (optionValues) {
      optionValues.forEach(ov => {
        if (ov.option) {
          variantOptions[ov.option.title] = ov.value;
        }
      });
    }

    // Collect changes for event emission
    const changes = [];

    // Check for status change
    if (updateData.status && updateData.status !== variant.metadata?.status) {
      changes.push({
        type: 'status',
        old_status: variant.metadata?.status,
        new_status: updateData.status
      });
    }

    // Check for active status change
    if (updateData.is_active !== undefined && updateData.is_active !== variant.metadata?.is_active) {
      changes.push({
        type: 'active',
        old_value: variant.metadata?.is_active,
        new_value: updateData.is_active
      });
    }

    // Emit room-status-updated event if there are changes
    if (changes.length > 0) {
      try {
        const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);
        await eventModuleService.emit({
          name: "room.status_updated",
          data: {
            id: variant.id,
            room_number: variant.metadata?.room_number || updateData.room_number,
            changes: changes
          },
        });
        console.log(`Emitted room.status_updated event for room ${variant.metadata?.room_number || updateData.room_number}`);
      } catch (eventError) {
        console.error("Error emitting room.status_updated event:", eventError);
        // Continue even if event emission fails
      }
    }

    // Map the updated variant to a room object
    const room = {
      id: updatedVariant.id,
      name: updatedVariant.title,
      room_number: updatedVariant.metadata?.room_number || "",
      status: updatedVariant.metadata?.status || "available",
      floor: updatedVariant.metadata?.floor || "",
      notes: updatedVariant.metadata?.notes || "",
      is_active: updatedVariant.metadata?.is_active !== false,
      left_room: updatedVariant.metadata?.left_room || "",
      opposite_room: updatedVariant.metadata?.opposite_room || "",
      connected_room: updatedVariant.metadata?.connected_room || "",
      right_room: updatedVariant.metadata?.right_room || "",
      room_config_id: variant.product.metadata?.room_config_id || "",
      hotel_id: variant.product.categories?.[0] || "",
      options: variantOptions,
    };

    res.json({ room });
  } catch (error) {
    console.error("Error updating room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room",
    });
  }
};

// DELETE endpoint to delete a room
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const body = req.body as { ids?: string[] };
    const { ids } = body;

    if (!ids) {
      return res.status(400).json({ message: "Room IDs are required" });
    }

    const roomIds = ids;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Delete each variant (room)
    for (const id of roomIds) {
      await productModuleService.deleteProductVariants(id);
    }

    res.json({ success: true, ids: roomIds });
  } catch (error) {
    console.error("Error deleting room:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete room",
    });
  }
};
