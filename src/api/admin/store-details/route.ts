import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { createStoreDetailWorkflow } from "src/workflows/create-store-detail";
import { z } from "zod";
import { PostAdminCreateStoreDetail } from "./validators";

type PostAdminCreateStoreDetailType = z.infer<typeof PostAdminCreateStoreDetail>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateStoreDetailType>,
  res: MedusaResponse
) => {
  const { result } = await createStoreDetailWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ storeDetail: result });
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: storeDetails } = await query.graph({
    entity: "store_detail",
    filters: {
        id: [
          req.params.id,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ storeDetails });
};
