import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { updateStoreDetailWorkflow } from "src/workflows/update-store-detail";
import { PostAdminUpdateStoreDetail } from "../validators";
import { z } from "zod";

type PostAdminUpdateStoreDetailType = z.infer<typeof PostAdminUpdateStoreDetail>;

export const POST = async (
  req: MedusaRequest<PostAdminUpdateStoreDetailType>,
  res: MedusaResponse
) => {
  const { result } = await updateStoreDetailWorkflow(req.scope).run({
    input: {
      store_id: req.params.store_id,
      storefront_url: req.body.storefront_url
    },
  });

  res.json({ storeDetail: result });
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: storeDetails } = await query.graph({
    entity: "store_detail",
    filters: {
        store_id: [
          req.params.store_id,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ storeDetails });
};


