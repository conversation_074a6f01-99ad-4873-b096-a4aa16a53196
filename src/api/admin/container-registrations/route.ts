import { 
  AuthenticatedMedusaRequest, 
  MedusaResponse 
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";

/**
 * GET endpoint to list all registrations available in the container
 * This is an admin-only endpoint that provides visibility into the DI container
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    // Get all registrations from the container
    const registrations = Object.keys(req.scope.registrations || {});
    
    // Organize registrations by category
    const categorizedRegistrations = {
      // Core container keys
      containerKeys: registrations.filter(key => 
        Object.values(ContainerRegistrationKeys).includes(key as any)
      ),
      
      // Module services
      moduleServices: registrations.filter(key => 
        Object.values(Modules).includes(key as any)
      ),
      
      // Custom services (anything not in the above categories)
      customServices: registrations.filter(key => 
        !Object.values(ContainerRegistrationKeys).includes(key as any) && 
        !Object.values(Modules).includes(key as any) &&
        !key.endsWith('_STORE')
      ),
      
      // Store registrations (internal container mechanism)
      storeRegistrations: registrations.filter(key => 
        key.endsWith('_STORE')
      ),
    };

    // Get available functions for each registration where possible
    const registrationDetails = {};
    
    // Process module services to extract available functions
    for (const key of categorizedRegistrations.moduleServices) {
      try {
        const service = req.scope.resolve(key);
        if (service) {
          // Get all methods that aren't from Object.prototype
          const functions = Object.getOwnPropertyNames(Object.getPrototypeOf(service))
            .filter(method => 
              method !== 'constructor' && 
              typeof service[method] === 'function' &&
              !Object.prototype.hasOwnProperty(method)
            );
          
          registrationDetails[key] = {
            type: 'module',
            functions
          };
        }
      } catch (error) {
        registrationDetails[key] = {
          type: 'module',
          error: `Could not resolve: ${error.message}`
        };
      }
    }
    
    // Process custom services
    for (const key of categorizedRegistrations.customServices) {
      try {
        const service = req.scope.resolve(key);
        if (service) {
          // Get all methods that aren't from Object.prototype
          const functions = Object.getOwnPropertyNames(Object.getPrototypeOf(service))
            .filter(method => 
              method !== 'constructor' && 
              typeof service[method] === 'function' &&
              !Object.prototype.hasOwnProperty(method)
            );
          
          registrationDetails[key] = {
            type: 'custom',
            functions
          };
        }
      } catch (error) {
        registrationDetails[key] = {
          type: 'custom',
          error: `Could not resolve: ${error.message}`
        };
      }
    }
    
    // Process container keys
    for (const key of categorizedRegistrations.containerKeys) {
      try {
        const service = req.scope.resolve(key);
        registrationDetails[key] = {
          type: 'container',
          isFunction: typeof service === 'function',
          isObject: typeof service === 'object' && service !== null,
        };
      } catch (error) {
        registrationDetails[key] = {
          type: 'container',
          error: `Could not resolve: ${error.message}`
        };
      }
    }

    res.json({
      registrations: categorizedRegistrations,
      details: registrationDetails,
      // Include raw list for reference
      allRegistrations: registrations,
      // Include module and container key constants for reference
      constants: {
        modules: Object.entries(Modules).map(([key, value]) => ({ key, value })),
        containerKeys: Object.entries(ContainerRegistrationKeys).map(([key, value]) => ({ key, value }))
      }
    });
  } catch (error) {
    res.status(500).json({
      message: `Failed to list container registrations: ${error.message}`,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};
