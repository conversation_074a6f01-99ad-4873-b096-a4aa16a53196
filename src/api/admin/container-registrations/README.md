# Container Registrations API

This API endpoint provides a way to list all registrations available in the Medusa container along with their available functions.

## Endpoint

```
GET /admin/container-registrations
```

## Authentication

This endpoint requires admin authentication. You must include a valid admin API token in the Authorization header:

```
Authorization: Bearer YOUR_ADMIN_API_TOKEN
```

## Response

The response includes:

- `registrations`: Object containing categorized registrations
  - `containerKeys`: Core container registration keys
  - `moduleServices`: Module services registered in the container
  - `customServices`: Custom services registered in the container
  - `storeRegistrations`: Internal container store registrations
- `details`: Object containing details about each registration, including available functions
- `allRegistrations`: Array of all registration keys
- `constants`: Reference to module and container key constants

## Example Response

```json
{
  "registrations": {
    "containerKeys": [
      "configModule",
      "featureFlagRouter",
      "logger",
      "remoteQuery",
      "__pg_connection__",
      "query",
      "link",
      "remoteLink"
    ],
    "moduleServices": [
      "cache",
      "event_bus",
      "workflows",
      "locking",
      "stock_location",
      "inventory",
      "product",
      "pricing",
      "promotion",
      "customer",
      "sales_channel",
      "cart",
      "region",
      "api_key",
      "store",
      "tax",
      "currency",
      "payment",
      "order",
      "auth",
      "user",
      "file",
      "fulfillment",
      "notification",
      "index"
    ],
    "customServices": [
      "wishlist",
      "subscriptionModuleService",
      "lookup",
      "roomInventoryService",
      "destination",
      "campaign_extension",
      "hotel",
      "housekeeping",
      "cancellationPolicyService",
      "add_on_service",
      "storeAnalyticsModuleService",
      "store_detail",
      "metafield",
      "metafield_definition",
      "field_validation",
      "notification_template",
      "stripePaymentLinkService",
      "whatsAppMessageService"
    ],
    "storeRegistrations": []
  },
  "details": {
    "cache": {
      "type": "module",
      "functions": [
        "get",
        "set",
        "invalidate",
        "clear",
        "__joinerConfig"
      ]
    },
    "event_bus": {
      "type": "module",
      "functions": [
        "emit",
        "groupOrEmitEvent",
        "groupEvent",
        "releaseGroupedEvents",
        "clearGroupedEvents",
        "subscribe",
        "unsubscribe",
        "__joinerConfig"
      ]
    },
    "workflows": {
      "type": "module",
      "functions": [
        "run",
        "getRunningTransaction",
        "setStepSuccess",
        "setStepFailure",
        "subscribe",
        "unsubscribe"
      ]
    },
    "locking": {
      "type": "module",
      "functions": [
        "execute",
        "acquire",
        "release",
        "releaseAll",
        "__joinerConfig"
      ]
    },
    "stock_location": {
      "type": "module",
      "functions": [
        "createStockLocations",
        "createStockLocations_",
        "upsertStockLocations",
        "upsertStockLocations_",
        "updateStockLocations",
        "updateStockLocations_",
        "updateStockLocationAddresses",
        "updateStockLocationAddresses_",
        "upsertStockLocationAddresses",
        "upsertStockLocationAddresses_"
      ]
    },
    "inventory": {
      "type": "module",
      "functions": [
        "ensureInventoryLevels",
        "sanitizeInventoryLevelInput",
        "sanitizeInventoryItemInput",
        "createReservationItems",
        "createReservationItems_",
        "createInventoryItems",
        "createInventoryItems_",
        "createInventoryLevels",
        "createInventoryLevels_",
        "updateInventoryItems",
        "updateInventoryItems_",
        "deleteInventoryItemLevelByLocationId",
        "deleteInventoryLevel",
        "updateInventoryLevels",
        "updateInventoryLevels_",
        "updateReservationItems",
        "updateReservationItems_",
        "softDeleteReservationItems",
        "restoreReservationItems",
        "deleteReservationItemByLocationId",
        "deleteReservationItemsByLineItem",
        "restoreReservationItemsByLineItem",
        "adjustInventory",
        "adjustInventory_",
        "retrieveInventoryLevelByItemAndLocation",
        "retrieveAvailableQuantity",
        "retrieveStockedQuantity",
        "retrieveReservedQuantity",
        "confirmInventory",
        "adjustInventoryLevelsForReservationsDeletion",
        "adjustInventoryLevelsForReservationsRestore",
        "adjustInventoryLevelsForReservations_"
      ]
    },
    "product": {
      "type": "module",
      "functions": [
        "retrieveProduct",
        "listProducts",
        "listAndCountProducts",
        "getProductFindConfig_",
        "createProductVariants",
        "createVariants_",
        "upsertProductVariants",
        "updateProductVariants",
        "updateVariants_",
        "createProductTags",
        "upsertProductTags",
        "updateProductTags",
        "createProductTypes",
        "upsertProductTypes",
        "updateProductTypes",
        "createProductOptions",
        "createOptions_",
        "upsertProductOptions",
        "updateProductOptions",
        "updateOptions_",
        "createProductCollections",
        "createCollections_",
        "upsertProductCollections",
        "updateProductCollections",
        "updateCollections_",
        "createProductCategories",
        "upsertProductCategories",
        "updateProductCategories",
        "createProducts",
        "upsertProducts",
        "updateProducts",
        "createProducts_",
        "updateProducts_",
        "validateProductPayload",
        "validateProductCreatePayload",
        "validateProductUpdatePayload",
        "normalizeCreateProductInput",
        "normalizeUpdateProductInput"
      ]
    },
    "pricing": {
      "type": "module",
      "functions": [
        "setupCalculatedPriceConfig_",
        "retrievePriceSet",
        "listPriceSets",
        "listAndCountPriceSets",
        "calculatePrices",
        "createPriceSets",
        "upsertPriceSets",
        "updatePriceSets",
        "updatePriceSets_",
        "normalizeUpdateData",
        "normalizePrices",
        "addPrices",
        "createPriceLists",
        "updatePriceLists",
        "updatePriceListPrices",
        "removePrices",
        "addPriceListPrices",
        "setPriceListRules",
        "removePriceListRules",
        "createPricePreferences",
        "upsertPricePreferences",
        "updatePricePreferences",
        "createPricePreferences_",
        "updatePricePreferences_",
        "createPriceSets_",
        "addPrices_",
        "createPriceLists_",
        "updatePriceLists_",
        "updatePriceListPrices_",
        "removePrices_",
        "addPriceListPrices_",
        "setPriceListRules_",
        "removePriceListRules_",
        "normalizePriceListDate",
        "normalizePriceSetConfig"
      ]
    },
    "promotion": {
      "type": "module",
      "functions": [
        "listActivePromotions",
        "registerUsage",
        "revertUsage",
        "computeActions",
        "createPromotions",
        "createPromotions_",
        "updatePromotions",
        "updatePromotions_",
        "updatePromotionRules",
        "updatePromotionRules_",
        "addPromotionRules",
        "addPromotionTargetRules",
        "addPromotionBuyRules",
        "createPromotionRulesAndValues_",
        "removePromotionRules",
        "removePromotionRules_",
        "removePromotionTargetRules",
        "removePromotionBuyRules",
        "removeApplicationMethodRules_",
        "createCampaigns",
        "createCampaigns_",
        "validateCampaignBudgetData",
        "updateCampaigns",
        "updateCampaigns_",
        "addPromotionsToCampaign",
        "addPromotionsToCampaign_",
        "removePromotionsFromCampaign",
        "removePromotionsFromCampaign_"
      ]
    },
    "customer": {
      "type": "module",
      "functions": [
        "createCustomers",
        "createCustomers_",
        "updateCustomers",
        "createCustomerGroups",
        "updateCustomerGroups",
        "addCustomerToGroup",
        "createCustomerAddresses",
        "createCustomerAddresses_",
        "updateCustomerAddresses",
        "removeCustomerFromGroup",
        "flush"
      ]
    },
    "sales_channel": {
      "type": "module",
      "functions": [
        "createSalesChannels",
        "createSalesChannels_",
        "updateSalesChannels",
        "updateSalesChannels_",
        "upsertSalesChannels"
      ]
    },
    "cart": {
      "type": "module",
      "functions": [
        "shouldIncludeTotals",
        "addRelationsToCalculateTotals",
        "retrieveCart",
        "listCarts",
        "listAndCountCarts",
        "createCarts",
        "createCarts_",
        "updateCarts",
        "updateCarts_",
        "addLineItems",
        "addLineItems_",
        "addLineItemsBulk_",
        "updateLineItems",
        "updateLineItem_",
        "updateLineItemsWithSelector_",
        "createAddresses",
        "createAddresses_",
        "updateAddresses",
        "updateAddresses_",
        "addShippingMethods",
        "addShippingMethods_",
        "addShippingMethodsBulk_",
        "addLineItemAdjustments",
        "setLineItemAdjustments",
        "setShippingMethodAdjustments",
        "addShippingMethodAdjustments",
        "addLineItemTaxLines",
        "setLineItemTaxLines",
        "addShippingMethodTaxLines",
        "setShippingMethodTaxLines"
      ]
    },
    "region": {
      "type": "module",
      "functions": [
        "createRegions",
        "createRegions_",
        "softDeleteRegions",
        "upsertRegions",
        "updateRegions",
        "updateRegions_",
        "validateCountries"
      ]
    },
    "api_key": {
      "type": "module",
      "functions": [
        "deleteApiKeys",
        "createApiKeys",
        "createApiKeys_",
        "upsertApiKeys",
        "updateApiKeys",
        "updateApiKeys_",
        "retrieveApiKey",
        "listApiKeys",
        "listAndCountApiKeys",
        "revoke",
        "revoke_",
        "authenticate",
        "authenticate_",
        "validateCreateApiKeys_",
        "normalizeUpdateInput_",
        "validateRevokeApiKeys_"
      ]
    },
    "store": {
      "type": "module",
      "functions": [
        "createStores",
        "create_",
        "upsertStores",
        "updateStores",
        "update_"
      ]
    },
    "tax": {
      "type": "module",
      "functions": [
        "createTaxRates",
        "createTaxRates_",
        "updateTaxRates",
        "updateTaxRates_",
        "setTaxRateRulesForTaxRates",
        "getTaxRateIdsFromSelector",
        "upsertTaxRates",
        "createTaxRegions",
        "createTaxRegions_",
        "createTaxRateRules",
        "createTaxRateRules_",
        "getTaxLines",
        "getTaxLinesFromProvider",
        "normalizeTaxCalculationContext",
        "prepareTaxRegionInputForCreate",
        "verifyProvinceToCountryMatch",
        "getTaxRatesForItem",
        "getTaxRateQueryForItem",
        "checkRuleMatches",
        "prioritizeRates",
        "normalizeRegionCodes"
      ]
    },
    "currency": {
      "type": "module",
      "functions": [
        "retrieveCurrency",
        "listCurrencies",
        "listAndCountCurrencies"
      ]
    },
    "payment": {
      "type": "module",
      "functions": [
        "createPaymentCollections",
        "createPaymentCollections_",
        "updatePaymentCollections",
        "updatePaymentCollections_",
        "upsertPaymentCollections",
        "completePaymentCollections",
        "createPaymentSession",
        "createPaymentSession_",
        "updatePaymentSession",
        "deletePaymentSession",
        "authorizePaymentSession",
        "authorizePaymentSession_",
        "retrievePaymentSession",
        "listPaymentSessions",
        "updatePayment",
        "capturePayment",
        "capturePayment_",
        "capturePaymentFromProvider_",
        "refundPayment",
        "refundPayment_",
        "refundPaymentFromProvider_",
        "cancelPayment",
        "getWebhookActionAndData",
        "listPaymentProviders",
        "listAndCountPaymentProviders",
        "listPaymentMethods",
        "listAndCountPaymentMethods",
        "createPaymentMethods",
        "maybeUpdatePaymentCollection_"
      ]
    },
    "order": {
      "type": "module",
      "functions": [
        "shouldIncludeTotals",
        "addRelationsToCalculateTotals",
        "retrieveOrder",
        "listOrders",
        "listAndCountOrders",
        "retrieveReturn",
        "listReturns",
        "listAndCountReturns",
        "retrieveOrderClaim",
        "listOrderClaims",
        "listAndCountOrderClaims",
        "retrieveOrderExchange",
        "listOrderExchanges",
        "listAndCountOrderExchanges",
        "createOrders",
        "createOrders_",
        "createOrderAddresses_",
        "updateOrders",
        "updateOrders_",
        "createOrderLineItems",
        "createOrderLineItems_",
        "createOrderLineItemsBulk_",
        "updateOrderLineItems",
        "updateOrderLineItem_",
        "updateOrderLineItemsWithSelector_",
        "updateOrderItem",
        "updateOrderItem_",
        "updateOrderItemWithSelector_",
        "createOrderShippingMethods",
        "createOrderShippingMethods_",
        "createOrderShippingMethodsBulk_",
        "softDeleteOrderShippingMethods",
        "restoreOrderShippingMethods",
        "createOrderLineItemAdjustments",
        "setOrderLineItemAdjustments",
        "setOrderShippingMethodAdjustments",
        "createOrderShippingMethodAdjustments",
        "createOrderLineItemTaxLines",
        "setOrderLineItemTaxLines",
        "createOrderShippingMethodTaxLines",
        "setOrderShippingMethodTaxLines",
        "createReturns",
        "createOrderClaims",
        "createOrderExchanges",
        "createOrderRelatedEntity_",
        "createOrderChange",
        "createOrderChange_",
        "previewOrderChange",
        "cancelOrderChange",
        "confirmOrderChange",
        "declineOrderChange",
        "registerOrderChange",
        "applyPendingOrderActions",
        "revertLastVersion",
        "undoLastChange",
        "undoLastChange_",
        "revertLastChange_",
        "getActiveOrderChange_",
        "getAndValidateOrderChange_",
        "addOrderAction",
        "applyOrderChanges_",
        "addOrderTransactions",
        "deleteOrderTransactions",
        "softDeleteOrderTransactions",
        "restoreOrderTransactions",
        "updateOrderPaidRefundableAmount_",
        "archive",
        "completeOrder",
        "cancel",
        "createReturn",
        "receiveReturn",
        "receiveReturn_",
        "cancelReturn",
        "cancelReturn_",
        "createClaim",
        "createClaim_",
        "cancelClaim",
        "cancelClaim_",
        "createExchange",
        "updateReturnReasons",
        "createExchange_",
        "cancelExchange",
        "cancelExchange_",
        "registerFulfillment",
        "cancelFulfillment",
        "registerShipment",
        "registerDelivery"
      ]
    },
    "auth": {
      "type": "module",
      "functions": [
        "createAuthIdentities",
        "updateAuthIdentities",
        "register",
        "createProviderIdentities",
        "updateProviderIdentities",
        "updateProvider",
        "authenticate",
        "validateCallback",
        "getAuthIdentityProviderService"
      ]
    },
    "user": {
      "type": "module",
      "functions": [
        "validateInviteToken",
        "refreshInviteTokens",
        "refreshInviteTokens_",
        "createUsers",
        "updateUsers",
        "createInvites",
        "createInvites_",
        "updateInvites",
        "generateToken"
      ]
    },
    "file": {
      "type": "module",
      "functions": [
        "createFiles",
        "deleteFiles",
        "retrieveFile",
        "listFiles",
        "listAndCountFiles"
      ]
    },
    "fulfillment": {
      "type": "module",
      "functions": [
        "listShippingOptions",
        "listShippingOptionsForContext",
        "retrieveFulfillment",
        "listFulfillments",
        "listAndCountFulfillments",
        "createFulfillmentSets",
        "createFulfillmentSets_",
        "createServiceZones",
        "createServiceZones_",
        "createShippingOptions",
        "createShippingOptions_",
        "createShippingProfiles",
        "createShippingProfiles_",
        "createGeoZones",
        "createShippingOptionRules",
        "createShippingOptionRules_",
        "createFulfillment",
        "deleteFulfillment",
        "createReturnFulfillment",
        "updateFulfillmentSets",
        "updateFulfillmentSets_",
        "updateServiceZones",
        "updateServiceZones_",
        "upsertServiceZones",
        "upsertServiceZones_",
        "updateShippingOptions",
        "updateShippingOptions_",
        "handleShippingOptionUpdateEvents",
        "upsertShippingOptions",
        "upsertShippingOptions_",
        "updateShippingProfiles",
        "upsertShippingProfiles",
        "updateGeoZones",
        "updateShippingOptionRules",
        "updateShippingOptionRules_",
        "updateFulfillment",
        "updateFulfillment_",
        "handleFulfillmentUpdateEvents",
        "cancelFulfillment",
        "retrieveFulfillmentOptions",
        "validateFulfillmentData",
        "validateFulfillmentOption",
        "validateShippingOption",
        "validateShippingOptionsForPriceCalculation",
        "calculateShippingOptionsPrices",
        "deleteShippingProfiles",
        "softDeleteShippingProfiles",
        "validateShippingProfileDeletion"
      ]
    },
    "notification": {
      "type": "module",
      "functions": [
        "createNotifications",
        "createNotifications_"
      ]
    },
    "wishlist": {
      "type": "custom",
      "functions": [
        "getWishlistsOfVariants",
        "getProductsWithWishlistCount"
      ]
    },
    "subscriptionModuleService": {
      "type": "custom",
      "functions": [
        "createSubscriptions",
        "recordNewSubscriptionOrder",
        "expireSubscription",
        "cancelSubscriptions",
        "getNextOrderDate",
        "getExpirationDate",
        "getFixedPeriod",
        "getIntervalUnit"
      ]
    },
    "lookup": {
      "type": "custom",
      "functions": []
    },
    "roomInventoryService": {
      "type": "custom",
      "functions": [
        "checkAvailability",
        "updateInventoryStatus",
        "releaseRoom"
      ]
    },
    "destination": {
      "type": "custom",
      "functions": [
        "uploadImages",
        "createDestinationImage",
        "updateDestinationImageRank",
        "setDestinationThumbnail",
        "getDestinationImages",
        "deleteDestinationImage"
      ]
    },
    "campaign_extension": {
      "type": "custom",
      "functions": []
    },
    "hotel": {
      "type": "custom",
      "functions": [
        "uploadImages",
        "createHotelImage",
        "updateHotelImageRank",
        "setHotelThumbnail",
        "getHotelImages",
        "deleteHotelImage"
      ]
    },
    "housekeeping": {
      "type": "custom",
      "functions": [
        "getTasksForAssignment",
        "createHousekeepingTask",
        "updateHousekeepingTask",
        "getHousekeepingTasksByHotel",
        "getHousekeepingTasksByRoom",
        "getHousekeepingTasksByDateRange",
        "assignTask",
        "startTask",
        "completeTask",
        "verifyTask",
        "cancelTask",
        "updateRoomStatus",
        "getAssignmentsForStaff",
        "createHousekeepingStaff",
        "getChecklistItems",
        "createHousekeepingChecklist",
        "generateHousekeepingTasks",
        "getHousekeepingStats"
      ]
    },
    "cancellationPolicyService": {
      "type": "custom",
      "functions": [
        "createCancellationPolicy",
        "updateCancellationPolicy",
        "getCancellationPolicy",
        "findCancellationPolicies",
        "deleteCancellationPolicy"
      ]
    },
    "add_on_service": {
      "type": "custom",
      "functions": [
        "createAddOnService",
        "listAddOnServices",
        "retrieveAddOnService",
        "updateAddOnService",
        "deleteAddOnService"
      ]
    },
    "storeAnalyticsModuleService": {
      "type": "custom",
      "functions": [
        "getCustomersHistory",
        "getCustomersNewCount",
        "getCustomersCumulativeHistory",
        "getCustomersRetentionRate",
        "getCustomersRepeatRate",
        "getOrdersSales",
        "getSalesChannelsPopularity",
        "getSalesRegionsPopularity",
        "getSalesRefunds",
        "getProductsTopVariantsByCount",
        "getProductsSoldCount",
        "getProductsOutOfTheStockVariants",
        "getProductsTopReturnedVariantsByCount",
        "getMarketingTopDiscounts",
        "getOrdersHistory",
        "getOrdersCount",
        "getOrdersPaymentProviderPopularity",
        "getAbandonedCarts",
        "getCartValue",
        "getAbandonedCartValue",
        "getHideProSetting"
      ]
    },
    "store_detail": {
      "type": "custom",
      "functions": []
    },
    "metafield": {
      "type": "custom",
      "functions": []
    },
    "metafield_definition": {
      "type": "custom",
      "functions": []
    },
    "field_validation": {
      "type": "custom",
      "functions": []
    },
    "notification_template": {
      "type": "custom",
      "functions": [
        "getTemplate",
        "getOrCreateTemplate",
        "shouldSendNotification",
        "setAsDefault",
        "getOriginalTemplateContent"
      ]
    },
    "stripePaymentLinkService": {
      "type": "custom",
      "functions": [
        "generatePaymentLink",
        "retrievePaymentLink"
      ]
    },
    "whatsAppMessageService": {
      "type": "custom",
      "functions": [
        "saveSentMessage",
        "saveIncomingMessage",
        "updateMessageStatus",
        "getMessagesByOrderId",
        "getMessagesByCustomerId",
        "getConversationByPhone",
        "getMessageByWhatsAppId",
        "formatPhoneNumber"
      ]
    },
    "configModule": {
      "type": "container",
      "isFunction": false,
      "isObject": true
    },
    "featureFlagRouter": {
      "type": "container",
      "isFunction": false,
      "isObject": true
    },
    "logger": {
      "type": "container",
      "isFunction": false,
      "isObject": true
    },
    "remoteQuery": {
      "type": "container",
      "isFunction": true,
      "isObject": false
    },
    "__pg_connection__": {
      "type": "container",
      "isFunction": true,
      "isObject": false
    },
    "query": {
      "type": "container",
      "isFunction": true,
      "isObject": false
    },
    "link": {
      "type": "container",
      "isFunction": false,
      "isObject": true
    },
    "remoteLink": {
      "type": "container",
      "isFunction": false,
      "isObject": true
    }
  },
  "allRegistrations": [
    "configModule",
    "featureFlagRouter",
    "logger",
    "remoteQuery",
    "__pg_connection__",
    "query",
    "link",
    "remoteLink",
    "wishlist",
    "cache",
    "event_bus",
    "workflows",
    "locking",
    "stock_location",
    "inventory",
    "product",
    "pricing",
    "promotion",
    "customer",
    "sales_channel",
    "cart",
    "region",
    "api_key",
    "store",
    "tax",
    "currency",
    "payment",
    "order",
    "auth",
    "user",
    "file",
    "fulfillment",
    "notification",
    "subscriptionModuleService",
    "lookup",
    "roomInventoryService",
    "destination",
    "campaign_extension",
    "hotel",
    "housekeeping",
    "cancellationPolicyService",
    "add_on_service",
    "storeAnalyticsModuleService",
    "store_detail",
    "metafield",
    "metafield_definition",
    "field_validation",
    "notification_template",
    "stripePaymentLinkService",
    "whatsAppMessageService",
    "index"
  ],
  "constants": {
    "modules": [
      {
        "key": "AUTH",
        "value": "auth"
      },
      {
        "key": "CACHE",
        "value": "cache"
      },
      {
        "key": "CART",
        "value": "cart"
      },
      {
        "key": "CUSTOMER",
        "value": "customer"
      },
      {
        "key": "EVENT_BUS",
        "value": "event_bus"
      },
      {
        "key": "INVENTORY",
        "value": "inventory"
      },
      {
        "key": "LINK",
        "value": "link_modules"
      },
      {
        "key": "PAYMENT",
        "value": "payment"
      },
      {
        "key": "PRICING",
        "value": "pricing"
      },
      {
        "key": "PRODUCT",
        "value": "product"
      },
      {
        "key": "PROMOTION",
        "value": "promotion"
      },
      {
        "key": "SALES_CHANNEL",
        "value": "sales_channel"
      },
      {
        "key": "TAX",
        "value": "tax"
      },
      {
        "key": "FULFILLMENT",
        "value": "fulfillment"
      },
      {
        "key": "STOCK_LOCATION",
        "value": "stock_location"
      },
      {
        "key": "USER",
        "value": "user"
      },
      {
        "key": "WORKFLOW_ENGINE",
        "value": "workflows"
      },
      {
        "key": "REGION",
        "value": "region"
      },
      {
        "key": "ORDER",
        "value": "order"
      },
      {
        "key": "API_KEY",
        "value": "api_key"
      },
      {
        "key": "STORE",
        "value": "store"
      },
      {
        "key": "CURRENCY",
        "value": "currency"
      },
      {
        "key": "FILE",
        "value": "file"
      },
      {
        "key": "NOTIFICATION",
        "value": "notification"
      },
      {
        "key": "INDEX",
        "value": "index"
      },
      {
        "key": "LOCKING",
        "value": "locking"
      }
    ],
    "containerKeys": [
      {
        "key": "PG_CONNECTION",
        "value": "__pg_connection__"
      },
      {
        "key": "MANAGER",
        "value": "manager"
      },
      {
        "key": "CONFIG_MODULE",
        "value": "configModule"
      },
      {
        "key": "LOGGER",
        "value": "logger"
      },
      {
        "key": "REMOTE_QUERY",
        "value": "remoteQuery"
      },
      {
        "key": "QUERY",
        "value": "query"
      },
      {
        "key": "REMOTE_LINK",
        "value": "remoteLink"
      },
      {
        "key": "LINK",
        "value": "link"
      },
      {
        "key": "FEATURE_FLAG_ROUTER",
        "value": "featureFlagRouter"
      }
    ]
  }
}
```

## Use Cases

- Debugging dependency injection issues
- Understanding available services in the container
- Exploring available functions in registered services
- Development and troubleshooting

## Security Considerations

This endpoint should only be accessible in development or by trusted administrators as it exposes internal system details.
