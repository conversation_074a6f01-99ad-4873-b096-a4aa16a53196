import { BetaAnalyticsDataClient } from "@google-analytics/data";
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { parseISO, subDays, startOfWeek, format } from "date-fns";

const propertyId = process.env.GA4_PROPERTY_ID;

export const credentials = {
  type: process.env.GA4_TYPE,
  project_id: process.env.GA4_PROJECT_ID,
  private_key_id: process.env.GA4_PRIVATE_KEY_ID,
  private_key: process.env.GA4_PRIVATE_KEY.replace(/\\n/g, '\n'), // Fix newline issue
  client_email: process.env.GA4_CLIENT_EMAIL,
  client_id: process.env.GA4_CLIENT_ID,
  auth_uri: process.env.GA4_AUTH_URI,
  token_uri: process.env.GA4_TOKEN_URI,
};
 
const getDateRange = (range: string) => {
    const today = new Date();
    switch (range) {
      case "yesterday":
        return { startDate: format(subDays(today, 1), "yyyy-MM-dd"), endDate: format(subDays(today, 1), "yyyy-MM-dd") };
      case "this_week":
        return { startDate: format(startOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_week":
        return { startDate: format(subDays(startOfWeek(today, { weekStartsOn: 1 }), 7), "yyyy-MM-dd"), endDate: format(subDays(startOfWeek(today, { weekStartsOn: 1 }), 1), "yyyy-MM-dd") };
      case "last_7_days":
        return { startDate: format(subDays(today, 7), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_14_days":
        return { startDate: format(subDays(today, 14), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_30_days":
        return { startDate: format(subDays(today, 30), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      default:
        return { startDate: format(subDays(today, 7), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") }; // Default to last 7 days
    }
  };
 
export async function GET(req: MedusaRequest, res: MedusaResponse) {

    const { range } = req.query as { range?: string };
    const dateRange = getDateRange(range || "last_7_days");
 
  try {
    const analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: credentials,
    });
 
 
    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: dateRange.startDate, endDate: dateRange.endDate }],
      "dimensions": [{ name: "date" }],
      "metrics": [{ name: "activeUsers" }],
    });
 
    const formattedData = response.rows.map((row) => {
        const rawDate = row.dimensionValues?.[0]?.value; // YYYYMMDD format
        const value = parseInt(row.metricValues?.[0]?.value || "0", 10);
        const dateObj = new Date(
          `${rawDate.slice(0, 4)}-${rawDate.slice(4, 6)}-${rawDate.slice(6, 8)}`
        );
        const formattedDate = dateObj.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        });
  
        return { date: formattedDate, value, timestamp: dateObj.getTime() };
      });
  
      formattedData.sort((a, b) => a.timestamp - b.timestamp);
  
      const finalData = formattedData.map(({ timestamp, ...rest }) => rest);
  
      return res.json(finalData);
 
 
  } catch (error: any) {
    console.error("GA4 API Error:", error.message);
    return res.status(500).json({
      error: "Failed to fetch analytics data",
      details: error.message,
    });
  }
}