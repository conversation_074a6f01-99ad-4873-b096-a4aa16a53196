import { BetaAnalyticsDataClient } from "@google-analytics/data";
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { parseISO, subDays, startOfWeek, format } from "date-fns";
import { credentials } from "../activeuser/route";

 
const propertyId = process.env.GA4_PROPERTY_ID;

const extractPageViews = (response) => {
    if (!response || !response.rows) {
      console.error("Invalid response format");
      return [];
    }
  
    return response.rows.map(row => ({
      page: row.dimensionValues[0]?.value || "Unknown",
      views: parseInt(row.metricValues[0]?.value || "0", 10),
    }));
  };
  
  const getTotalViews = (data) => {
    return data.reduce((sum, item) => sum + item.views, 0);
  };

  const getDateRange = (range: string) => {
    const today = new Date();
    switch (range) {
      case "yesterday":
        return { startDate: format(subDays(today, 1), "yyyy-MM-dd"), endDate: format(subDays(today, 1), "yyyy-MM-dd") };
      case "this_week":
        return { startDate: format(startOfWeek(today, { weekStartsOn: 1 }), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_week":
        return { startDate: format(subDays(startOfWeek(today, { weekStartsOn: 1 }), 7), "yyyy-MM-dd"), endDate: format(subDays(startOfWeek(today, { weekStartsOn: 1 }), 1), "yyyy-MM-dd") };
      case "last_7_days":
        return { startDate: format(subDays(today, 7), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_14_days":
        return { startDate: format(subDays(today, 14), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      case "last_30_days":
        return { startDate: format(subDays(today, 30), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") };
      default:
        return { startDate: format(subDays(today, 7), "yyyy-MM-dd"), endDate: format(today, "yyyy-MM-dd") }; // Default to last 7 days
    }
  };
 
export async function GET(req: MedusaRequest, res: MedusaResponse) {
 
 
  try {
    const analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: credentials,
    });

    const { range } = req.query as { range?: string };
    const dateRange = getDateRange(range || "last_7_days");
 
    const [response] = await analyticsDataClient.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: dateRange.startDate, endDate: dateRange.endDate }],
      "dimensions": [
       
        { "name": "pageTitle" },
      ],
      "metrics": [{ "name": "screenPageViews" },
      { "name": "totalUsers" },
      { "name": "eventCount" }
      ]
    });
    
      const totalViews = getTotalViews(extractPageViews(response));
      const PageViews = extractPageViews(response);

    return res.json({ PageViews , totalViews, dateRange});
 
 
  } catch (error: any) {
    console.error("GA4 API Error:", error.message);
    return res.status(500).json({
      error: "Failed to fetch analytics data",
      details: error.message,
    });
  }
}