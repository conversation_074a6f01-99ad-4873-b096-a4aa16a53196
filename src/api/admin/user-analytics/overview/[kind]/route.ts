import { BetaAnalyticsDataClient } from "@google-analytics/data";
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { parseISO, subDays, startOfWeek, format } from "date-fns";
import { credentials } from "../../activeuser/route";

const propertyId = process.env.GA4_PROPERTY_ID;


async function getDeviceCategoryData(client: BetaAnalyticsDataClient) {
    const [response] = await client.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: "30daysAgo", endDate: "today" }],
      dimensions: [{ name: "deviceCategory" }],
      metrics: [{ name: "activeUsers" }],
    });
    return response.rows.map((row: any) => ({
      name: row.dimensionValues[0].value,
      value: parseFloat(row.metricValues[0].value),
    }));
  }
  
  async function getBrowserData(client: BetaAnalyticsDataClient) {
    const [response] = await client.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: "30daysAgo", endDate: "today" }],
      dimensions: [{ name: "browser" }],
      metrics: [{ name: "activeUsers" }],
    });
    return response.rows.map((row: any) => ({
      browser: row.dimensionValues[0].value, 
      users: parseInt(row.metricValues[0].value, 10),
    }));
  }
  
  async function getCityData(client: BetaAnalyticsDataClient) {
    const [response] = await client.runReport({
      property: `properties/${propertyId}`,
      dateRanges: [{ startDate: "30daysAgo", endDate: "today" }],
      dimensions: [{ name: "city" }],
      metrics: [{ name: "activeUsers" }],
    });
    return response.rows.map((row: any) => ({
      city: row.dimensionValues[0].value, 
      users: parseInt(row.metricValues[0].value, 10),
    }));
  
  }
  
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const kind = req.params.kind;
  try {
    const analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: credentials,
    });
    let response;

    switch (kind) {
      case "device-category":
        response = await getDeviceCategoryData(analyticsDataClient);
        break;
      case "browser":
        response = await getBrowserData(analyticsDataClient);
        break;
      case "city":
        response = await getCityData(analyticsDataClient);
        break;
      default:
        return res.status(400).json({ error: "Invalid slug" });
    }

    return res.json(response);
 
 
  } catch (error: any) {
    console.error("GA4 API Error:", error.message);
    return res.status(500).json({
      error: "Failed to fetch analytics data",
      details: error.message,
    });
  }
}