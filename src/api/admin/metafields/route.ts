import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateMetafieldWorkflow } from "src/workflows/metafield/create-metafield";
import { UpdateMetafieldWorkflow } from "src/workflows/metafield/update-metafield";
import { z } from "zod";
import { PostAdminUpsertMetafield } from "./validators";

type PostAdminUpsertMetafieldType = z.infer<typeof PostAdminUpsertMetafield>;

export const POST = async (
  req: MedusaRequest<PostAdminUpsertMetafieldType>,
  res: MedusaResponse
) => {
  const { body } = req;

  // Separate into create and update arrays
  const toCreate = body.filter(item => !item.id);
  const toUpdate = body.filter(item => item.id);

  let createdResult = [];
  let updatedResult = [];

  // Run create workflow if there are items to create
  if (toCreate.length > 0) {
    const { result } = await CreateMetafieldWorkflow(req.scope).run({
      input: toCreate,
    });
    createdResult = result;
  }

  // Run update workflow if there are items to update
  if (toUpdate.length > 0) {
    const { result } = await UpdateMetafieldWorkflow(req.scope).run({
      input: toUpdate,
    });
    updatedResult = result;
  }

  // Combine results
  res.json({ metafields: [...createdResult, ...updatedResult] });
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: metafield } = await query.graph({
    entity: "metafield",
    filters: {
      owner_id: [
          req.params.owner_id,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ metafield });
};
