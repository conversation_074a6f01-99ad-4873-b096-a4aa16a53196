import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to export rooms data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('Room export request received');

    // Parse query parameters
    const { 
      fields, 
      status, 
      hotel_id, 
      format = 'xlsx' 
    } = req.query;

    console.log(`Exporting rooms with filters:`, { 
      status, hotel_id, format 
    });

    // Use the query service
    const queryService = req.scope.resolve("query");
    if (!queryService) {
      throw new Error('Query service not found');
    }

    // Build filters for the graph query
    const filters: Record<string, any> = {};
    
    // Add status filter if provided
    if (status && status !== 'all') {
      filters.status = status;
    }
    
    // We'll handle hotel_id filtering after fetching the data
    
    // Execute query using the graph query API
    const { data: roomsData } = await queryService.graph({
      entity: "product_variant",
      filters: filters,
      fields: [
        "id", 
        "title", 
        "sku",
        "barcode",
        "ean",
        "upc",
        "status",
        "metadata",
        "product.id",
        "product.title",
        "product.metadata",
        "created_at", 
        "updated_at"
      ],
    });
    
    console.log(`Found ${roomsData?.length || 0} rooms`);

    // Process and export the data
    if (!roomsData || roomsData.length === 0) {
      console.log('No rooms found');
      // Create an empty export
      await processAndExport([], fields as string, format as string, res);
    } else {
      // Process the data to match our expected format
      let processedItems = roomsData.map(room => {
        const hotelId = room.product?.metadata?.hotel_id || '';
        const roomNumber = room.metadata?.room_number || room.title || '';
        const floor = room.metadata?.floor || '';
        const roomConfigId = room.product?.id || '';
        
        return {
          id: room.id,
          title: room.title || '',
          room_number: roomNumber,
          hotel_id: hotelId,
          status: room.status || '',
          floor: floor,
          room_config_id: roomConfigId,
          created_at: room.created_at,
          updated_at: room.updated_at
        };
      });

      // If hotel_id filter is provided, we need to filter the results
      if (hotel_id && hotel_id !== 'all') {
        console.log(`Filtering by hotel_id: ${hotel_id}`);
        
        // Filter by hotel_id
        processedItems = processedItems.filter(item => item.hotel_id === hotel_id);
        
        console.log(`Filtered to ${processedItems.length} rooms for hotel ${hotel_id}`);
      }

      await processAndExport(processedItems, fields as string, format as string, res);
    }
  } catch (error) {
    console.error('Error exporting rooms:', error);
    console.error('Error stack:', error.stack);
    
    res.status(500).json({ 
      message: 'Error exporting rooms', 
      error: error.message
    });
  }
};

/**
 * Process room data and export to the requested format
 */
async function processAndExport(
  roomsData: any[], 
  fieldsParam: string, 
  format: string, 
  res: MedusaResponse
) {
  // Parse fields to include
  const fieldsToInclude = fieldsParam ? fieldsParam.split(',') : [
    'id', 'title', 'room_number', 'hotel_id', 'status', 'floor', 'room_config_id',
    'created_at', 'updated_at'
  ];

  // Process the data
  const processedData = roomsData.map(item => {
    const data: Record<string, any> = {};
    
    fieldsToInclude.forEach(field => {
      if (field === 'created_at' || field === 'updated_at') {
        // Format date as YYYY-MM-DD
        const date = new Date(item[field]);
        data[field] = date.toISOString().split('T')[0];
      } else {
        data[field] = item[field] !== undefined ? item[field] : '';
      }
    });
    
    return data;
  });

  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Rooms');
  
  // Define columns
  const columns = fieldsToInclude.map(field => ({
    header: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),
    key: field,
    width: 20
  }));
  
  worksheet.columns = columns;
  
  // Style header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };
  
  // Add data rows
  processedData.forEach(data => {
    worksheet.addRow(data);
  });
  
  // Set response headers based on format
  if (format === 'csv') {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=rooms-export.csv');
    
    // For CSV, we would need to implement CSV export
    // But for now, we'll just use Excel for both formats
  } else {
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=rooms-export.xlsx');
  }
  
  // Write workbook to response
  await workbook.xlsx.write(res);
  console.log('Export completed successfully');
}
