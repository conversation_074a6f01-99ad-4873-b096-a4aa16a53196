import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { MedusaError, MedusaErrorTypes } from "@camped-ai/utils";
import { STORE_ANALYTICS_MODULE } from "src/modules/store-analytics";
import StoreAnalyticsModuleService from "src/modules/store-analytics/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const dateRangeFrom = req.query.dateRangeFrom;
  const dateRangeTo = req.query.dateRangeTo;

  const storeAnalyticsModuleService: StoreAnalyticsModuleService = req.scope.resolve(STORE_ANALYTICS_MODULE);

  try {
    const Cartsvalue = await storeAnalyticsModuleService.getCartValue(
      dateRangeFrom ? new Date(Number(dateRangeFrom)) : undefined, 
      dateRangeTo ? new Date(Number(dateRangeTo)) : undefined,
    );

    res.status(200).json({
      analytics: Cartsvalue
    });
  } catch (error) {
    throw new MedusaError(
      MedusaErrorTypes.DB_ERROR,
      error.message
    );
  }
};