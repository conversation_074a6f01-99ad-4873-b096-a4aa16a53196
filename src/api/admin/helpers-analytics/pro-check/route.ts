

import type { 
  MedusaRequest, 
  MedusaResponse,
} from "@camped-ai/framework"
import { MedusaError, MedusaErrorTypes } from "@camped-ai/utils"
import { STORE_ANALYTICS_MODULE } from "src/modules/store-analytics";
import StoreAnalyticsModuleService from "src/modules/store-analytics/service";


export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {

  const storeAnalyticsModuleService: StoreAnalyticsModuleService = req.scope.resolve(STORE_ANALYTICS_MODULE)
  try {
    res.status(200).json({
      hideProTab: storeAnalyticsModuleService.getHideProSetting()
    });
  } catch (error) {
    throw new MedusaError(
      MedusaErrorTypes.DB_ERROR,
      error.message
    )
  }
}