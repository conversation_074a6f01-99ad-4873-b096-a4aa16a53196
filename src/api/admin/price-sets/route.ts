import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { IPricingModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import createPriceSetWorkflow from "../../../workflows/price-set/create-price-set";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { result } = await createPriceSetWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });

    res.status(200).json({ price_set: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create price set",
    });
  }
}

// export async function GET(req: MedusaRequest, res: MedusaResponse) {
//   const pricingService: IPricingModuleService = 
//     req.scope.resolve(Modules.PRICING);
//   const query = req.scope.resolve("DbQuery");

//   const { limit = 20, offset = 0 } = req.query;

//   const [priceSets, count] = await Promise.all([
//     query.graph({
//       entity: "price_set",
//       fields: ["*"],
//       pagination: {
//         skip: Number(offset),
//         take: Number(limit),
//       },
//     }),
//     pricingService.count(),
//   ]);

//   res.status(200).json({
//     price_sets: priceSets,
//     count,
//     limit: Number(limit),
//     offset: Number(offset),
//   });
// }