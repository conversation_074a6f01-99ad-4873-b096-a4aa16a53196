// import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
// import { IPricingModuleService } from "@camped-ai/framework/types";
// import { Modules } from "@camped-ai/framework/utils";

// export async function GET(req: MedusaRequest, res: MedusaResponse) {
//   const pricingService: IPricingModuleService = 
//     req.scope.resolve(Modules.PRICING);
  
//   try {
//     const priceSet = await pricingService.retrieve(req.params.id);
//     res.status(200).json({ price_set: priceSet });
//   } catch (error) {
//     res.status(404).json({ message: "Price set not found" });
//   }
// }

// export async function PUT(req: MedusaRequest, res: MedusaResponse) {
//   const pricingService: IPricingModuleService = 
//     req.scope.resolve(Modules.PRICING);
  
//   try {
//     const updatedPriceSet = await pricingService.update(
//       req.params.id,
//       req.body
//     );
    
//     res.status(200).json({ price_set: updatedPriceSet });
//   } catch (error) {
//     res.status(400).json({
//       message: error instanceof Error ? error.message : "Failed to update price set",
//     });
//   }
// }

// export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
//   const pricingService: IPricingModuleService = 
//     req.scope.resolve(Modules.PRICING);
  
//   try {
//     await pricingService.delete(req.params.id);
//     res.status(200).json({ id: req.params.id, deleted: true });
//   } catch (error) {
//     res.status(400).json({
//       message: error instanceof Error ? error.message : "Failed to delete price set",
//     });
//   }
// }