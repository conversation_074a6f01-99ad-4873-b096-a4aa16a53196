import { z } from "zod";

export const PostAdminCreateMetafieldDefinition = z.object({
  owner_type: z.string(),
  namespace: z.string(),
  key: z.string(),
  type: z.string(),
  label: z.string(),
  namespace_label: z.string(),
  description: z.string(),
  // default_value: z.string();
  required: z.boolean(),
  scope: z.string(),
  categories: z.array(z.string()),
});

export const PostAdminUpdateMetafieldDefinition = z.array(
  z.object({
    id: z.string(),
    label: z.string(),
    namespace_label: z.string().optional(),
    description: z.string(),
    // default_value: z.string();
    required: z.boolean(),
    scope: z.string(),
    categories: z.array(z.string()),
  })
);

export const PostAdminDeleteMetafieldDefinition = z.object({
  ids: z.string(),
  delete_linked_values: z.boolean(),
});
