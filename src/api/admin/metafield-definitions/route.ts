import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateMetafieldDefinitionWorkflow } from "src/workflows/metafield-definition/create-metafield-definition";
import { DeleteMetafieldDefinitionWorkflow } from "src/workflows/metafield-definition/delete-metafield-definition";
import { UpdateMetafieldDefinitionWorkflow } from "src/workflows/metafield-definition/update-metafield-definition";
import { z } from "zod";
import {
  PostAdminCreateMetafieldDefinition,
  PostAdminDeleteMetafieldDefinition,
  PostAdminUpdateMetafieldDefinition,
} from "./validators";
import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";
import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";

type PostAdminCreateMetafieldDefinitionType = z.infer<
  typeof PostAdminCreateMetafieldDefinition
>;
type PostAdminDeleteMetafieldDefinitionType = z.infer<
  typeof PostAdminDeleteMetafieldDefinition
>;
type PostAdminUpdateMetafieldDefinitionType = z.infer<
  typeof PostAdminUpdateMetafieldDefinition
>;

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateMetafieldDefinitionType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateMetafieldDefinitionWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ metafieldDefinition: result });
};

export const POST = async (
  req: MedusaRequest<PostAdminCreateMetafieldDefinitionType>,
  res: MedusaResponse
) => {
  const { result } = await CreateMetafieldDefinitionWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ metafieldDefinition: result });
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const metafieldDefinitionModuleService: MetafieldDefinitionModuleService = 
    req.scope.resolve(METAFIELD_DEFINITION_MODULE)
  const filters: any = {};
  if (req.query.id) {
    filters.id = req.query.id;
  } 
  if (req.query.owner_type) {
    filters.owner_type = req.query.owner_type;
  } 
  if (req.query.categories) {
    const categoriesArray = Array.isArray(req.query.categories)
      ? req.query.categories
      : req.query.categories?.split(','); 
  
    filters.categories = { $overlap: categoriesArray };
  }

  const metafieldDefinition = await metafieldDefinitionModuleService.listMetafieldDefinitions({
    ...filters
  })
  res.json({ metafieldDefinition });
};


export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteMetafieldDefinitionType>,
  res: MedusaResponse
) => {
  const { result } = await DeleteMetafieldDefinitionWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ metafieldDefinition: result });
};
