import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to export room inventory data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('Room inventory export request received');

    // Parse query parameters
    const {
      fields,
      from_date,
      to_date,
      status,
      hotel_id,
      format = 'xlsx'
    } = req.query;

    // Validate required parameters
    if (!from_date || !to_date) {
      return res.status(400).json({ message: 'From date and To date are required' });
    }

    console.log(`Exporting room inventory with filters:`, {
      from_date, to_date, status, hotel_id, format
    });

    // Use direct database query approach
    return await exportWithDirectQuery(req, res);
  } catch (error) {
    console.error('Error exporting room inventory:', error);
    console.error('Error stack:', error.stack);

    res.status(500).json({
      message: 'Error exporting room inventory',
      error: error.message
    });
  }
};

/**
 * Fallback function to export inventory using direct database query
 */
async function exportWithDirectQuery(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('Using direct database query for inventory export');

    // Parse query parameters
    const {
      fields,
      from_date,
      to_date,
      status,
      hotel_id,
      format = 'xlsx'
    } = req.query;

    // Use the query service instead of direct SQL
    const queryService = req.scope.resolve("query");
    if (!queryService) {
      throw new Error('Query service not found');
    }

    // Build filters for the graph query
    const filters: Record<string, any> = {};

    // Add date range filter
    filters.from_date = {
      $lte: to_date
    };

    filters.to_date = {
      $gte: from_date
    };

    // Add status filter if provided
    if (status && status !== 'all') {
      filters.status = status;
    }

    // Note: We can't directly filter by hotel_id in room_inventory
    // We'll need to filter the results after fetching them

    console.log('Using filters:', filters);

    // Execute query using the graph query API
    const { data: inventoryItems } = await queryService.graph({
      entity: "room_inventory",
      filters: filters,
      fields: [
        "id",
        "status",
        "from_date",
        "to_date",
        "available_quantity",
        "notes",
        "check_in_time",
        "check_out_time",
        "is_noon_to_noon",
        "inventory_item_id",
        "created_at",
        "updated_at"
      ],
    });

    console.log(`Found ${inventoryItems?.length || 0} inventory items`);

    // Process and export the data
    if (!inventoryItems || inventoryItems.length === 0) {
      console.log('No inventory items found');
      // Create an empty export
      await processAndExport([], fields as string, format as string, res);
    } else {
      // First, let's get all the inventory item IDs
      const inventoryItemIds = inventoryItems.map(item => item.inventory_item_id).filter(Boolean);

      // Fetch room information for these inventory items
      let roomInfo: Record<string, any> = {};

      try {
        // Since inventory_item_id in room_inventory corresponds to product_variant
        // We need to fetch product variant information
        const { data: roomsData } = await queryService.graph({
          entity: "product_variant",
          filters: { id: { $in: inventoryItemIds } },
          fields: ["id", "title", "product.metadata"]
        });

        if (roomsData && roomsData.length > 0) {
          // Create a mapping from variant ID to room info
          roomsData.forEach((variant: any) => {
            if (variant.id) {
              const hotelId = variant.product?.metadata?.hotel_id || '';
              roomInfo[variant.id] = {
                room_number: variant.title || '',
                hotel_id: hotelId
              };
            }
          });

          console.log(`Found room information for ${Object.keys(roomInfo).length} rooms`);
        }
      } catch (error) {
        console.error('Error fetching room information:', error);
      }

      // Process the data to match our expected format
      let processedItems = inventoryItems.map(item => {
        const roomData = roomInfo[item.inventory_item_id] || {};

        // We'll still include inventory_item_id in the data for internal use
        // but it won't be exposed in the fields selection
        const processedItem = {
          id: item.id,
          room_id: item.inventory_item_id || '',  // Add room_id which is the same as inventory_item_id
          inventory_item_id: item.inventory_item_id || '', // Keep this for internal use
          room_number: roomData.room_number || '',
          hotel_id: roomData.hotel_id || '',
          from_date: item.from_date,
          to_date: item.to_date,
          status: item.status,
          available_quantity: item.available_quantity,
          notes: item.notes || '',
          check_in_time: item.check_in_time || '',
          check_out_time: item.check_out_time || '',
          is_noon_to_noon: item.is_noon_to_noon || false,
          created_at: item.created_at,
          updated_at: item.updated_at
        };

        return processedItem;
      });

      // If hotel_id filter is provided, we need to filter the results
      if (hotel_id && hotel_id !== 'all') {
        console.log(`Filtering by hotel_id: ${hotel_id}`);

        // Now we can filter by hotel_id directly since we have it in our processed items
        processedItems = processedItems.filter(item => item.hotel_id === hotel_id);

        console.log(`Filtered to ${processedItems.length} room inventory items for hotel ${hotel_id}`);
      }

      await processAndExport(processedItems, fields as string, format as string, res);
    }
  } catch (error) {
    console.error('Error with direct database query:', error);
    res.status(500).json({
      message: 'Error exporting room inventory',
      error: error.message
    });
  }
}

/**
 * Process inventory data and export to the requested format
 */
async function processAndExport(
  inventoryItems: any[],
  fieldsParam: string,
  format: string,
  res: MedusaResponse
) {
  // Parse fields to include
  const fieldsToInclude = fieldsParam ? fieldsParam.split(',') : [
    'id', 'room_id', 'room_number', 'hotel_id', 'date', 'status', 'price',
    'created_at', 'updated_at'
  ];

  // Process the data
  const processedData = inventoryItems.map(item => {
    const data: Record<string, any> = {};

    fieldsToInclude.forEach(field => {
      if (field === 'date' && item[field]) {
        // Format date as YYYY-MM-DD
        const date = new Date(item[field]);
        data[field] = date.toISOString().split('T')[0];
      } else if (field === 'price' && item[field]) {
        // Format price
        data[field] = typeof item[field] === 'number'
          ? item[field].toFixed(2)
          : item[field];
      } else {
        data[field] = item[field] !== undefined ? item[field] : '';
      }
    });

    return data;
  });

  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Room Inventory');

  // Define columns
  const columns = fieldsToInclude.map(field => ({
    header: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),
    key: field,
    width: 20
  }));

  worksheet.columns = columns;

  // Style header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };

  // Add data rows
  processedData.forEach(data => {
    worksheet.addRow(data);
  });

  // Set response headers based on format
  if (format === 'csv') {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=room-inventory-export.csv');

    // For CSV, we would need to implement CSV export
    // But for now, we'll just use Excel for both formats
  } else {
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=room-inventory-export.xlsx');
  }

  // Write workbook to response
  await workbook.xlsx.write(res);
  console.log('Export completed successfully');
}
