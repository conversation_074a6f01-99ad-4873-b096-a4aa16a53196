import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// Helper function to generate mock room configurations
function getMockRoomConfigs(hotelId: string) {
  return []
}

// GET endpoint to list room configurations
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { hotel_id, room_config_id } = req.query;

  try {
    console.log("=== GET /admin/direct-room-configs ====");
    console.log("Request query:", req.query);
    console.log("hotel_id:", hotel_id);
    console.log("room_config_id:", room_config_id);

    // Check if req.scope exists
    if (!req.scope) {
      console.error("req.scope is undefined. This might be due to a middleware issue.");

      // Return mock data as fallback
      return res.json({
        roomConfigs: getMockRoomConfigs(hotel_id as string),
        room_configs: getMockRoomConfigs(hotel_id as string),
        count: 3
      });
    }

    // Get the product module service
    let productModuleService: any;
    try {
      productModuleService = req.scope.resolve(Modules.PRODUCT);
    } catch (error) {
      console.error("Error resolving product module service:", error);

      // Return mock data as fallback
      return res.json({
        roomConfigs: getMockRoomConfigs(hotel_id as string),
        room_configs: getMockRoomConfigs(hotel_id as string),
        count: 3
      });
    }

    // If room_config_id is provided, fetch that specific room config
    if (room_config_id) {
      try {
        // Get the product by ID
        const product = await productModuleService.retrieveProduct(room_config_id as string);

        if (!product) {
          console.log(`No product found with ID: ${room_config_id}`);
          return res.status(404).json({ message: "Room configuration not found" });
        }

        console.log("Found product:", product.title);

        // Transform product to room configuration
        const roomConfig = {
          id: product.id,
          name: product.title,
          title: product.title, // Ensure title is always set for UI components
          type: product.metadata?.type || "standard",
          description: product.description,
          room_size: product.metadata?.room_size || "",
          bed_type: product.metadata?.bed_type || "",
          max_extra_beds: product.metadata?.max_extra_beds || 0,
          max_adults: product.metadata?.max_adults || 1,
          max_children: product.metadata?.max_children || 0,
          max_infants: product.metadata?.max_infants || 0,
          max_occupancy: product.metadata?.max_occupancy || 1,
          amenities: product.metadata?.amenities || [],
          hotel_id: product.metadata?.hotel_id || hotel_id,
        };

        return res.json({
          roomConfigs: [roomConfig],
          room_configs: [roomConfig],
          count: 1
        });
      } catch (error) {
        console.error("Error fetching room configuration by ID:", error);
        return res.status(404).json({ message: "Room configuration not found" });
      }
    }

    // Otherwise, fetch all room configs for the hotel
    if (!hotel_id) {
      return res.status(400).json({ message: "hotel_id is required" });
    }

    // Get all products
    let products = [];
    try {
      console.log("Calling productModuleService.listProducts...");
      const result = await productModuleService.listProducts({
        is_giftcard: false,
      }, {
        relations: ["categories"],
      });

      console.log("Result from listProducts:", result ? "success" : "null");

      if (!result) {
        console.log("No products found in result");
        products = [];
      } else {
        // Check if result is an array or an object with products property
        if (Array.isArray(result)) {
          products = result;
        } else {
          // Use type assertion to handle potential object structure
          const resultObj = result as any;
          if (resultObj.products && Array.isArray(resultObj.products)) {
            products = resultObj.products;
          } else {
            products = [];
          }
        }
        console.log(`Found ${products.length} total products`);
      }
    } catch (error) {
      console.error("Error listing products:", error);
      products = [];
    }

    // Filter products by metadata.hotel_id and exclude add-on services
    const filteredProducts = products.filter(product => {
      try {
        // Check if product has metadata
        if (!product.metadata) {
          return false;
        }

        // Exclude add-on services
        if (product.metadata.add_on_service === true) {
          console.log(`Excluding add-on service: ${product.id} (${product.title})`);
          return false;
        }

        // Convert both to strings for comparison to avoid type mismatches
        const productHotelId = String(product.metadata.hotel_id || "");
        const queryHotelId = String(hotel_id || "");

        const matches = productHotelId === queryHotelId;
        console.log(`Product ${product.id} (${product.title}) hotel_id: ${productHotelId}, query hotel_id: ${queryHotelId}, matches: ${matches}`);
        return matches;
      } catch (error) {
        console.error(`Error filtering product ${product.id}:`, error);
        return false;
      }
    });

    console.log(`After filtering, found ${filteredProducts.length} products for hotel_id: ${hotel_id}`);

    // Transform products to room configurations
    const roomConfigs = filteredProducts.map(product => ({
      id: product.id,
      name: product.title,
      title: product.title, // Ensure title is always set for UI components
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size || "",
      bed_type: product.metadata?.bed_type || "",
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    }));

    console.log(`Returning ${roomConfigs.length} room configurations`);

    // If no room configurations found, return mock data
    if (roomConfigs.length === 0) {
      console.log("No room configurations found, returning mock data");
      const mockRoomConfigs = getMockRoomConfigs(hotel_id as string);
      return res.json({
        roomConfigs: mockRoomConfigs,
        room_configs: mockRoomConfigs,
        count: mockRoomConfigs.length
      });
    }

    console.log(`Returning ${roomConfigs.length} room configurations`);
    console.log('First room config:', roomConfigs.length > 0 ? roomConfigs[0] : 'None');

    // Return both roomConfigs (camelCase) and room_configs (snake_case) for backward compatibility
    return res.json({
      roomConfigs,
      room_configs: roomConfigs,
      count: roomConfigs.length
    });
  } catch (error) {
    console.error("Error fetching room configurations:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to fetch room configurations",
    });
  }
};
