import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const { data: campaign_extension } = await query.graph({
    entity: "campaign_extension",
    filters: {
      campaign_id: [req.params.id],
    },
    fields: ["*"],
  });
  res.json({ campaign_extension });
};
