import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateCampaignExtension,
  PostAdminDeleteCampaignExtension,
  PostAdminUpdateCampaignExtension,
} from "./validators";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { CreateCampaignExtensionWorkflow } from "src/workflows/hotel-management/campaign-extension/create-campaign";
import { UpdateCampaignExtensionWorkflow } from "src/workflows/hotel-management/campaign-extension/update-campaign";
import { DeleteCampaignExtensionWorkflow } from "src/workflows/hotel-management/campaign-extension/delete-campaign";

type PostAdminCreateCampaignExtensionType = z.infer<
  typeof PostAdminCreateCampaignExtension
>;
type PostAdminDeleteCampaignExtensionType = z.infer<
  typeof PostAdminDeleteCampaignExtension
>;
type PostAdminUpdateCampaignExtensionType = z.infer<
  typeof PostAdminUpdateCampaignExtension
>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateCampaignExtensionType>,
  res: MedusaResponse
) => {
  const { result } = await CreateCampaignExtensionWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ campaign_extension: result });
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateCampaignExtensionType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateCampaignExtensionWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ campaign_extension: result });
};
export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteCampaignExtensionType>,
  res: MedusaResponse
) => {
  const { result } = await DeleteCampaignExtensionWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ campaign_extension: result });
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { limit = 20, offset = 0 } = req.query || {};

  const {
    data: campaign_extension,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "campaign_extension",
    fields: ["*", ...(req.validatedQuery?.fields.split(",") || [])],
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });

  res.json({
    campaign_extension,
    count,
    limit: take,
    offset: skip,
  });
};
