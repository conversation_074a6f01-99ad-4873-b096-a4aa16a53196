import { z } from "zod";

export const PostAdminCreateCampaignExtension = z.object({
  campaign_id: z.string(),
  booking_to_date: z.date(),
  booking_from_date: z.date(),
});

export const PostAdminUpdateCampaignExtension = z.object({
  id: z.string(),
  campaign_id: z.string(),
  booking_to_date: z.date(),
  booking_from_date: z.date(),
  // board_basis: z.string().optional(),
  // type: z.string().optional(),
});

export const PostAdminDeleteCampaignExtension = z.object({
  ids: z.string(),
});
