import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreatePriceListWorkflow } from "src/workflows/price-list/create-price-list";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { result } = await CreatePriceListWorkflow(req.scope).run({
      input: req.body,
    });

    res.status(200).json({ price_list: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create price list",
    });
  }
}

// export async function GET(req: MedusaRequest, res: MedusaResponse) {
//   const query = req.scope.resolve("DbQuery");
//   const { limit = 20, offset = 0 } = req.query;

//   const { data: priceLists, metadata } = await query.graph({
//     entity: "price_list",
//     fields: ["*"],
//     pagination: {
//       skip: Number(offset),
//       take: Number(limit),
//     },
//   });

//   res.status(200).json({
//     price_lists: priceLists,
//     count: metadata.count,
//     limit: metadata.take,
//     offset: metadata.skip,
//   });
// }