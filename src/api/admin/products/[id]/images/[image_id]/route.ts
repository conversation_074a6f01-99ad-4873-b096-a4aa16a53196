import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { Modules } from "@camped-ai/framework/utils"

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const productId = req.params.id;
  const imageId = req.params.image_id;
  
  console.log(`Deleting image ${imageId} from product ${productId}`);

  if (!productId || !imageId) {
    return res.status(400).json({
      message: "Product ID and Image ID are required"
    });
  }

  try {
    // Get the product service
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Get the product with images
    const product = await productService.retrieveProduct(productId, {
      relations: ["images"]
    });
    
    if (!product) {
      return res.status(404).json({
        message: `Product with id ${productId} not found`
      });
    }

    // Find the image to delete
    const imageToDelete = product.images.find(img => img.id === imageId);
    if (!imageToDelete) {
      return res.status(404).json({
        message: `Image with id ${imageId} not found in product ${productId}`
      });
    }

    // Filter out the image to delete
    const updatedImages = product.images.filter(img => img.id !== imageId);
    
    // Update the product with the filtered images
    await productService.updateProducts(productId, {
      images: updatedImages
    });
    
    return res.status(200).json({ 
      id: imageId,
      deleted: true
    });
  } catch (error) {
    console.error("Error deleting product image:", error);
    res.status(500).json({ 
      message: "Failed to delete product image",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
