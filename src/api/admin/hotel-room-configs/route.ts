import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// GET endpoint to list room configurations
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { hotel_id } = req.query;

  try {
    console.log("=== GET /admin/hotel-room-configs ====");
    console.log("Request query:", req.query);
    console.log("hotel_id:", hotel_id);

    if (!hotel_id) {
      return res.status(400).json({ message: "hotel_id is required" });
    }

    // Get the product module service
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get all products
    let products = [];
    try {
      console.log("Calling productModuleService.listProducts...");
      const result = await productModuleService.listProducts({
        is_giftcard: false,
      }, {
        relations: ["categories"],
      });

      console.log("Result from listProducts:", result);

      if (!result || !result.products) {
        console.log("No products found in result");
        products = [];
      } else {
        products = result.products;
        console.log(`Found ${products.length} total products`);
      }
    } catch (error) {
      console.error("Error listing products:", error);
      products = [];
    }

    // Filter products by metadata.hotel_id and exclude add-on services
    const filteredProducts = products.filter(product => {
      try {
        // Check if product has metadata
        if (!product.metadata) {
          return false;
        }

        // Exclude add-on services
        if (product.metadata.add_on_service === true) {
          console.log(`Excluding add-on service: ${product.id} (${product.title})`);
          return false;
        }

        // Convert both to strings for comparison to avoid type mismatches
        const productHotelId = String(product.metadata.hotel_id || "");
        const queryHotelId = String(hotel_id || "");

        const matches = productHotelId === queryHotelId;
        console.log(`Product ${product.id} (${product.title}) hotel_id: ${productHotelId}, query hotel_id: ${queryHotelId}, matches: ${matches}`);
        return matches;
      } catch (error) {
        console.error(`Error filtering product ${product.id}:`, error);
        return false;
      }
    });

    console.log(`After filtering, found ${filteredProducts.length} products for hotel_id: ${hotel_id}`);

    // Transform products to room configurations
    const roomConfigs = filteredProducts.map(product => ({
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size || "",
      bed_type: product.metadata?.bed_type || "",
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    }));

    console.log(`Returning ${roomConfigs.length} room configurations`);

    res.json({ roomConfigs, count: roomConfigs.length });
  } catch (error) {
    console.error("Error fetching room configurations:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to fetch room configurations",
    });
  }
};
