import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";
import * as ExcelJS from 'exceljs';
import { CreateDestinationWorkflow } from "../../../../workflows/hotel-management/destination/create-destination";
import { IProductModuleService } from "@camped-ai/framework/types";
import multer from 'multer';
import { z } from "zod";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Validation schema for destination data
const DestinationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().min(1, "Handle is required"),
  description: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  is_active: z.union([z.boolean(), z.string().transform(val => val?.toLowerCase() === 'true'), z.null()]).optional().default(true),
  country: z.string().min(1, "Country is required"),
  currency: z.string().min(1, "Currency is required"),
  location: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  is_featured: z.union([z.boolean(), z.string().transform(val => val?.toLowerCase() === 'true'), z.null()]).optional().default(false),
  tags: z.union([z.string(), z.null()]).optional().transform(val => {
    if (val === null || val === undefined) return [];
    return typeof val === 'string' ? val.split(',').map(tag => tag.trim()) : [];
  }),
  website: z.union([
    z.string(),
    z.object({}).transform(() => ""), // Handle empty objects
    z.null(),
    z.undefined()
  ]).optional().transform(val => val || null),
});

// Type for the validated data that matches the workflow input requirements
type ValidatedDestinationData = z.infer<typeof DestinationSchema> & {
  name: string; // Ensure name is required
  handle: string; // Ensure handle is required
  country: string; // Ensure country is required
  currency: string; // Ensure currency is required
};

/**
 * POST endpoint to import destinations from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      // Get services
      const destinationModuleService: DestinationModuleService = req.scope.resolve(DESTINATION_MODULE);
      const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split('.').pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === 'csv') {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString('utf8');

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require('fs').writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require('fs').unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error('Error reading CSV:', csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split('\n').map(row => row.split(','));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet('Sheet1');

              // Add rows to the worksheet
              rows.forEach(row => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error('CSV file is empty or invalid');
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Destinations' or use the first one
          worksheet = workbook.getWorksheet('Destinations') ||
                     workbook.getWorksheet('destinations') ||
                     workbook.getWorksheet('Sheet1') ||
                     workbook.worksheets[0];
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        return res.status(400).json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res.status(400).json({ message: 'Invalid file: No worksheet found' });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};
      headers.forEach((header, index) => {
        if (header) {
          // Remove the asterisk from required fields
          const cleanHeader = header.replace('*', '').trim().toLowerCase();
          headerMap[index] = cleanHeader;
        }
      });

      // Process rows
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        errors: [],
        created: []
      };

      // Start from row 2 (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (row.values.filter(Boolean).length <= 1) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            // Handle special cases for certain fields
            if (header === 'website' && cell.value) {
              // Convert website to string if it's an object (happens with hyperlinks in Excel)
              rowData[header] = cell.value.text || cell.value.hyperlink || String(cell.value);
            } else {
              rowData[header] = cell.value;
            }
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach(key => {
          // Handle null values
          if (rowData[key] === null) {
            // Keep it as null for the validator to handle
          }
          // Convert any objects to strings
          else if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
          // Handle empty strings for fields that should be null
          else if (rowData[key] === '' && ['description', 'location', 'tags', 'website'].includes(key)) {
            rowData[key] = null;
          }
        });

        try {
          // Validate the data
          const validatedData = DestinationSchema.parse(rowData);

          // Check if required fields are present
          if (!validatedData.name || !validatedData.handle || !validatedData.country || !validatedData.currency) {
            throw new Error('Missing required fields: name, handle, country, and currency are required');
          }

          // Create the destination using the workflow
          const { result } = await CreateDestinationWorkflow(req.scope).run({
            input: {
              name: validatedData.name,
              handle: validatedData.handle,
              description: validatedData.description || '',
              is_active: validatedData.is_active,
              country: validatedData.country,
              currency: validatedData.currency,
              location: validatedData.location || null,
              is_featured: validatedData.is_featured,
              tags: validatedData.tags || [],
              website: validatedData.website || null
            }
          });

          results.successful++;
          results.created.push({
            id: result.id,
            name: validatedData.name,
            row: i
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: rowData,
            error: error.message || 'Unknown error'
          });
        }
      }

      res.json({
        message: `Import completed. ${results.successful} destinations created, ${results.failed} failed.`,
        results
      });
    } catch (error) {
      console.error('Error processing Excel file:', error);
      res.status(500).json({ message: 'Failed to process Excel file', error: error.message });
    }
  });
};
