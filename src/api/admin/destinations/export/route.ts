import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";
import * as ExcelJS from 'exceljs';
import { createObjectCsvStringifier } from 'csv-writer';

/**
 * GET endpoint to export destinations data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get destination service
    const destinationService: DestinationModuleService = req.scope.resolve(DESTINATION_MODULE);

    // Parse query parameters
    const { fields, is_active, is_featured, country, format } = req.query;

    // Build filter object
    const filters: Record<string, any> = {};

    if (is_active && is_active !== 'all') {
      filters.is_active = is_active === 'true';
    }

    if (is_featured && is_featured !== 'all') {
      filters.is_featured = is_featured === 'true';
    }

    if (country && country !== 'all') {
      filters.country = country;
    }

    console.log('Export filters:', filters);

    // Fetch destinations with filters
    const destinations = await destinationService.listDestinations(filters);

    // Parse fields to include
    const fieldsToInclude = fields ? (fields as string).split(',') : [
      'id', 'name', 'handle', 'description', 'is_active', 'is_featured',
      'country', 'currency', 'location', 'tags', 'website', 'created_at', 'updated_at'
    ];

    // Process destinations data
    const processedData = destinations.map(destination => {
      const data: Record<string, any> = {};

      fieldsToInclude.forEach(field => {
        if (field === 'tags' && destination[field]) {
          // Handle tags field specially
          if (Array.isArray(destination[field])) {
            data[field] = destination[field].join(', ');
          } else if (typeof destination[field] === 'string') {
            data[field] = destination[field];
          } else {
            try {
              data[field] = JSON.stringify(destination[field]);
            } catch (e) {
              data[field] = '';
            }
          }
        } else {
          data[field] = destination[field];
        }
      });

      return data;
    });

    // Export based on format
    if (format === 'csv') {
      // CSV export
      if (processedData.length === 0) {
        // Create empty CSV with headers
        const headers = fieldsToInclude.map(field => ({
          id: field,
          title: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        }));

        const csvStringifier = createObjectCsvStringifier({
          header: headers
        });

        const csvContent = csvStringifier.getHeaderString();

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=destinations-export.csv');
        return res.send(csvContent);
      }

      // Create CSV with data
      const headers = fieldsToInclude.map(field => ({
        id: field,
        title: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }));

      const csvStringifier = createObjectCsvStringifier({
        header: headers
      });

      const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(processedData);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=destinations-export.csv');
      return res.send(csvContent);
    } else {
      // Excel export (default)
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Destinations');

      // Add headers
      const headers = fieldsToInclude.map(field => ({
        header: field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        key: field,
        width: 20
      }));

      worksheet.columns = headers;

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add data
      processedData.forEach(data => {
        worksheet.addRow(data);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        const lengths = column.values?.filter(v => v !== undefined).map(v => v.toString().length);
        if (lengths && lengths.length > 0) {
          const maxLength = Math.max(column.header.length, ...lengths);
          column.width = maxLength < 10 ? 10 : maxLength < 50 ? maxLength : 50;
        }
      });

      // Set content type and headers for Excel file download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=destinations-export.xlsx');

      // Write the workbook to the response
      await workbook.xlsx.write(res);
    }
  } catch (error) {
    console.error('Error exporting destinations:', error);
    res.status(500).json({ message: 'Error exporting destinations' });
  }
};
