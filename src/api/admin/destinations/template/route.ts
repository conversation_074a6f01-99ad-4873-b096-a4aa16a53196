import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to download a destination import template
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Destinations');

    // Define columns with validation
    worksheet.columns = [
      { header: 'Name*', key: 'name', width: 30 },
      { header: 'Handle*', key: 'handle', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Is Active', key: 'is_active', width: 15 },
      { header: 'Country*', key: 'country', width: 20 },
      { header: 'Currency*', key: 'currency', width: 15 },
      { header: 'Location', key: 'location', width: 30 },
      { header: 'Is Featured', key: 'is_featured', width: 15 },
      { header: 'Tags (comma separated)', key: 'tags', width: 30 },
      { header: 'Website', key: 'website', width: 30 },
    ];

    // Add styles to header row
    worksheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
    worksheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    // Add data validation for boolean fields
    worksheet.getColumn('is_active').eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      if (rowNumber > 1) {
        cell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"TRUE,FALSE"']
        };
      }
    });

    worksheet.getColumn('is_featured').eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      if (rowNumber > 1) {
        cell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"TRUE,FALSE"']
        };
      }
    });

    // Add currency validation (common currencies)
    worksheet.getColumn('currency').eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      if (rowNumber > 1) {
        cell.dataValidation = {
          type: 'list',
          allowBlank: false,
          formulae: ['"USD,EUR,GBP,JPY,AUD,CAD,CHF,CNY,INR"']
        };
      }
    });

    // Add sample data row
    worksheet.addRow({
      name: 'Paris',
      handle: 'paris',
      description: 'The capital of France',
      is_active: 'TRUE',
      country: 'France',
      currency: 'EUR',
      location: 'Europe',
      is_featured: 'TRUE',
      tags: 'europe,city,romantic',
      website: 'https://www.paris.fr'
    });

    // Add instructions worksheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 25 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Description', key: 'description', width: 60 }
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
    instructionsSheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

    // Add instructions for each field
    const instructions = [
      { field: 'Name', required: 'Yes', description: 'The name of the destination' },
      { field: 'Handle', required: 'Yes', description: 'URL-friendly version of the name (lowercase, no spaces)' },
      { field: 'Description', required: 'No', description: 'A detailed description of the destination' },
      { field: 'Is Active', required: 'No', description: 'Whether the destination is active (TRUE or FALSE, defaults to TRUE)' },
      { field: 'Country', required: 'Yes', description: 'The country where the destination is located' },
      { field: 'Currency', required: 'Yes', description: 'The currency code used at the destination (e.g., USD, EUR)' },
      { field: 'Location', required: 'No', description: 'Geographic location or region' },
      { field: 'Is Featured', required: 'No', description: 'Whether the destination is featured (TRUE or FALSE, defaults to FALSE)' },
      { field: 'Tags', required: 'No', description: 'Comma-separated list of tags (e.g., "beach,summer,family")' },
      { field: 'Website', required: 'No', description: 'Official website URL of the destination' }
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Add general instructions at the top
    instructionsSheet.insertRow(1, { field: 'BULK IMPORT INSTRUCTIONS', required: '', description: 'Please follow these guidelines when filling out the template.' });
    instructionsSheet.getRow(1).font = { bold: true, size: 14, color: { argb: 'FF0000FF' } };
    instructionsSheet.getRow(1).height = 30;

    instructionsSheet.insertRow(2, { field: 'Required fields', required: '', description: 'Fields marked with * are required and must be filled in.' });
    instructionsSheet.insertRow(3, { field: 'File format', required: '', description: 'Save the file as .xlsx format before uploading.' });
    instructionsSheet.insertRow(4, { field: 'Sample data', required: '', description: 'A sample row is provided in the Destinations sheet. You can delete or modify it.' });
    instructionsSheet.insertRow(5, { field: '', required: '', description: '' });

    // Set the response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=destination-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Failed to generate template' });
  }
};
