import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../modules/hotel-management/hotel/service";
import * as ExcelJS from 'exceljs';
import multer from 'multer';
import { z } from "zod";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Validation schema for room configuration data
const RoomConfigSchema = z.object({
  name: z.string().min(1, "Name is required"),
  hotel_id: z.string().min(1, "Hotel ID is required"),
  description: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  is_active: z.union([z.boolean(), z.string().transform(val => val?.toLowerCase() === 'true'), z.null()]).optional().default(true),
  type: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val), // Room type (standard, deluxe, suite, etc.)
  area: z.union([z.string(), z.number().transform(val => String(val)), z.null()]).optional().transform(val => val === null ? "" : val),
  bed_type: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val), // Bed type (king, queen, twin, etc.)
  view_type: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  amenities: z.union([z.string(), z.null()]).optional().transform(val => {
    if (val === null || val === undefined) return [];
    return typeof val === 'string' ? val.split(',').map(item => item.trim()) : [];
  }),
  tags: z.union([z.string(), z.null()]).optional().transform(val => {
    if (val === null || val === undefined) return [];
    return typeof val === 'string' ? val.split(',').map(tag => tag.trim()) : [];
  }),
  max_adults: z.union([z.number(), z.string().transform(val => val ? parseInt(val, 10) : undefined), z.null()]).optional().transform(val => val === null ? 1 : val),
  max_children: z.union([z.number(), z.string().transform(val => val ? parseInt(val, 10) : undefined), z.null()]).optional().transform(val => val === null ? 0 : val),
  max_infants: z.union([z.number(), z.string().transform(val => val ? parseInt(val, 10) : undefined), z.null()]).optional().transform(val => val === null ? 0 : val),
  base_price: z.union([z.number(), z.string().transform(val => val ? parseFloat(val) : undefined), z.null()]).optional(),
  currency_code: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
});

/**
 * POST endpoint to import room configurations from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      // Get services
      const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
      const productModuleService = req.scope.resolve(Modules.PRODUCT);

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split('.').pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === 'csv') {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString('utf8');

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require('fs').writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require('fs').unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error('Error reading CSV:', csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split('\\n').map(row => row.split(','));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet('Sheet1');

              // Add rows to the worksheet
              rows.forEach(row => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error('CSV file is empty or invalid');
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'RoomConfigs' or use the first one
          worksheet = workbook.getWorksheet('RoomConfigs') ||
                     workbook.getWorksheet('roomconfigs') ||
                     workbook.getWorksheet('Sheet1') ||
                     workbook.worksheets[0];
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        return res.status(400).json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res.status(400).json({ message: 'Invalid file: No worksheet found' });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};

      console.log('Raw headers:', headers);

      // Create a map of column index to header name
      // Note: ExcelJS uses 1-based indexing for columns
      for (let i = 1; i < headers.length; i++) {
        const header = headers[i];
        if (header) {
          const headerName = header.toString().trim().toLowerCase(); // Convert to lowercase for case-insensitive matching
          headerMap[i] = headerName;
          console.log(`Mapped column ${i} to header '${headerName}'`);
        }
      }

      console.log('Header map:', headerMap);

      // Prepare results
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        created: [],
        errors: []
      };

      // Validate hotel IDs
      const hotels = await hotelService.listHotels({});
      const validHotelIds = new Set(hotels.map(h => h.id));

      // Process each row (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (!row.hasValues) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            // Handle special cases for certain fields
            if (header === 'website' && cell.value) {
              // Convert website to string if it's an object (happens with hyperlinks in Excel)
              rowData[header] = cell.value.text || cell.value.hyperlink || String(cell.value);
            } else {
              rowData[header] = cell.value;
            }
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach(key => {
          // Handle null values
          if (rowData[key] === null) {
            // Keep it as null for the validator to handle
          }
          // Convert any objects to strings
          else if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
          // Handle empty strings for numeric fields
          else if (rowData[key] === '' && ['max_adults', 'max_children', 'max_infants', 'base_price'].includes(key)) {
            rowData[key] = null;
          }
        });

        // Debug log to see what data we're getting
        console.log('Row data:', rowData);

        // Map the lowercase header names back to the expected case-sensitive names
        const normalizedData: Record<string, any> = {};
        Object.keys(rowData).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'name') normalizedData.name = rowData[key];
          else if (lowerKey === 'hotel_id') normalizedData.hotel_id = rowData[key];
          else if (lowerKey === 'capacity') normalizedData.capacity = rowData[key];
          else if (lowerKey === 'description') normalizedData.description = rowData[key];
          else if (lowerKey === 'is_active') normalizedData.is_active = rowData[key];
          else if (lowerKey === 'type') normalizedData.type = rowData[key]; // Room type
          else if (lowerKey === 'area') normalizedData.area = rowData[key];
          else if (lowerKey === 'bed_type') normalizedData.bed_type = rowData[key]; // Bed type
          else if (lowerKey === 'view_type') normalizedData.view_type = rowData[key];
          else if (lowerKey === 'amenities') normalizedData.amenities = rowData[key];
          else if (lowerKey === 'tags') normalizedData.tags = rowData[key];
          else if (lowerKey === 'max_adults') normalizedData.max_adults = rowData[key];
          else if (lowerKey === 'max_children') normalizedData.max_children = rowData[key];
          else if (lowerKey === 'max_infants') normalizedData.max_infants = rowData[key];
          else if (lowerKey === 'base_price') normalizedData.base_price = rowData[key];
          else if (lowerKey === 'currency_code') normalizedData.currency_code = rowData[key];
          else normalizedData[key] = rowData[key]; // Keep any other fields
        });

        console.log('Normalized data:', normalizedData);

        // Ensure required fields are present
        if (!normalizedData.name || !normalizedData.hotel_id) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: `Missing required fields: ${!normalizedData.name ? 'name' : ''} ${!normalizedData.hotel_id ? 'hotel_id' : ''}`
          });
          continue;
        }

        try {
          // Validate the data
          const validatedData = RoomConfigSchema.parse(normalizedData);

          // Check if required fields are present
          if (!validatedData.name || !validatedData.hotel_id) {
            throw new Error('Missing required fields: name and hotel_id are required');
          }

          // Validate hotel_id
          if (!validHotelIds.has(validatedData.hotel_id)) {
            throw new Error(`Invalid hotel_id: ${validatedData.hotel_id}. Please use a valid hotel ID from the Hotels Reference sheet.`);
          }

          // Create the room configuration using the product module service
          // Calculate max_occupancy as the sum of max_adults and max_children
          const maxAdults = validatedData.max_adults || 1;
          const maxChildren = validatedData.max_children || 0;
          const maxOccupancy = maxAdults + maxChildren;

          const metadata = {
            type: validatedData.type || "standard", // Use the type field
            room_size: validatedData.area || "",
            bed_type: validatedData.bed_type || "", // Use the bed_type field
            max_extra_beds: 0,
            max_adults: maxAdults,
            max_children: maxChildren,
            max_infants: validatedData.max_infants || 0,
            max_occupancy: maxOccupancy, // Calculated from max_adults + max_children
            amenities: validatedData.amenities || [],
            hotel_id: validatedData.hotel_id,
            view_type: validatedData.view_type || "",
            tags: validatedData.tags || []
          };

          const productData = {
            title: validatedData.name,
            description: validatedData.description || "",
            is_giftcard: false,
            status: "published",
            metadata
          };

          // Create the product (room configuration)
          const product = await productModuleService.createProducts(productData);

          // Create a default variant if none exists
          let variantId;
          if (!product.variants || product.variants.length === 0) {
            // Create a default variant
            const variant = await productModuleService.createProductVariants({
              title: validatedData.name,
              product_id: product.id,
              inventory_quantity: 0,
              manage_inventory: true,
              allow_backorder: false,
            });
            variantId = variant.id;
          } else {
            variantId = product.variants[0].id;
          }

          // Note: We're skipping price creation as it's not needed
          // The base_price and currency_code fields are still in the template for future use
          if (validatedData.base_price && validatedData.currency_code) {
            console.log(`Price information received but not processed: ${validatedData.base_price} ${validatedData.currency_code}`);
          }

          results.successful++;
          results.created.push({
            id: product.id,
            name: validatedData.name,
            row: i
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: error.message || 'Unknown error'
          });
        }
      }

      // Return the results
      return res.status(200).json({
        message: 'Import completed',
        results
      });

    } catch (error) {
      console.error('Error importing room configurations:', error);
      return res.status(500).json({ message: `Error importing room configurations: ${error.message}` });
    }
  });
};
