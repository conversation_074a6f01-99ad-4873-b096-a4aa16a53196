import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import * as ExcelJS from 'exceljs';
import { createObjectCsvStringifier } from 'csv-writer';

/**
 * GET endpoint to export room configurations data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get product module service to access room configurations (products)
    const productModuleService = req.scope.resolve('ProductService');

    if (!productModuleService) {
      throw new Error('ProductService not found in container');
    }

    // Parse query parameters
    const { fields, is_active, type, hotel_id, format } = req.query;

    // Build filter object
    const filters: Record<string, any> = {
      type: 'room_config', // Ensure we only get room configurations
    };

    if (is_active && is_active !== 'all') {
      filters.is_active = is_active === 'true';
    }

    if (type && type !== 'all') {
      // For room type, we need to filter differently since it's in metadata
      // This will be handled after fetching the data
    }

    if (hotel_id && hotel_id !== 'all') {
      filters.hotel_id = hotel_id;
    }

    console.log('Export filters:', filters);

    // Fetch room configurations with filters
    console.log('Fetching room configurations with filters:', filters);

    // Use a more direct database query approach
    let roomConfigs = [];
    try {
      // First try the listProducts method
      const result = await productModuleService.listProducts(filters);
      roomConfigs = result.products || [];

      // Apply type filter manually since it's in metadata
      if (type && type !== 'all' && roomConfigs.length > 0) {
        roomConfigs = roomConfigs.filter(config =>
          config.metadata?.type?.toLowerCase() === (type as string).toLowerCase()
        );
      }
    } catch (listError) {
      console.error('Error using listProducts:', listError);

      // Fallback to direct database query if available
      try {
        const productRepository = req.scope.resolve('productRepository');
        if (productRepository) {
          const query = { where: { ...filters } };
          roomConfigs = await productRepository.find(query);

          // Apply type filter manually
          if (type && type !== 'all' && roomConfigs.length > 0) {
            roomConfigs = roomConfigs.filter(config =>
              config.metadata?.type?.toLowerCase() === (type as string).toLowerCase()
            );
          }
        }
      } catch (dbError) {
        console.error('Error with direct database query:', dbError);
        throw new Error('Failed to fetch room configurations: ' + (listError.message || 'Unknown error'));
      }
    }

    console.log(`Found ${roomConfigs.length} room configs${type !== 'all' ? ` with type: ${type}` : ''}`);

    if (roomConfigs.length === 0) {
      console.log('No room configurations found with the specified filters');
    }

    // Parse fields to include
    const fieldsToInclude = fields ? (fields as string).split(',') : [
      'id', 'title', 'handle', 'description', 'is_active', 'type', 'bed_type',
      'max_occupancy', 'min_occupancy', 'max_adults', 'max_children',
      'is_bathroom_shared', 'is_breakfast_included', 'is_featured', 'amenities',
      'created_at', 'updated_at'
    ];

    // Process room configurations data
    const processedData = roomConfigs.map(config => {
      const data: Record<string, any> = {};

      fieldsToInclude.forEach(field => {
        // First check if it's a direct field on the product
        if (config[field] !== undefined) {
          data[field] = config[field];
          return;
        }

        // Then check if it's in the metadata
        if (config.metadata && config.metadata[field] !== undefined) {
          if (field === 'amenities') {
            // Handle amenities field specially
            if (Array.isArray(config.metadata.amenities)) {
              data[field] = config.metadata.amenities.join(', ');
            } else if (typeof config.metadata.amenities === 'string') {
              data[field] = config.metadata.amenities;
            } else {
              try {
                data[field] = JSON.stringify(config.metadata.amenities);
              } catch (e) {
                data[field] = '';
              }
            }
          } else {
            // Handle other metadata fields
            data[field] = config.metadata[field];
          }
        } else {
          // Field not found, set to empty string
          data[field] = '';
        }
      });

      // Special handling for title/name field
      if (fieldsToInclude.includes('name') && !data['name'] && config.title) {
        data['name'] = config.title;
      }

      return data;
    });

    // Export based on format
    if (format === 'csv') {
      // CSV export
      if (processedData.length === 0) {
        // Create empty CSV with headers
        const headers = fieldsToInclude.map(field => ({
          id: field,
          title: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())
        }));

        const csvStringifier = createObjectCsvStringifier({
          header: headers
        });

        const csvContent = csvStringifier.getHeaderString();

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=room-configs-export.csv');
        return res.send(csvContent);
      }

      // Create CSV with data
      const headers = fieldsToInclude.map(field => ({
        id: field,
        title: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())
      }));

      const csvStringifier = createObjectCsvStringifier({
        header: headers
      });

      const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(processedData);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=room-configs-export.csv');
      return res.send(csvContent);
    } else {
      // Excel export (default)
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Room Configurations');

      // Add headers
      const headers = fieldsToInclude.map(field => ({
        header: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),
        key: field,
        width: 20
      }));

      worksheet.columns = headers;

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add data
      processedData.forEach(data => {
        worksheet.addRow(data);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        const lengths = column.values?.filter(v => v !== undefined).map(v => v.toString().length);
        if (lengths && lengths.length > 0) {
          const maxLength = Math.max(column.header.length, ...lengths);
          column.width = maxLength < 10 ? 10 : maxLength < 50 ? maxLength : 50;
        }
      });

      // Set content type and headers for Excel file download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=room-configs-export.xlsx');

      // Write the workbook to the response
      await workbook.xlsx.write(res);
    }
  } catch (error) {
    console.error('Error exporting room configurations:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    console.error('Error stack:', error.stack);

    // Send a more detailed error response
    res.status(500).json({
      message: 'Error exporting room configurations',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};
