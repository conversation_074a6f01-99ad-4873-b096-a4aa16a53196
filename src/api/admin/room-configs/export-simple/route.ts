import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to export room configurations data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log('Room config export request received');

    // Parse query parameters
    const { hotel_id, type, is_active, format = 'xlsx' } = req.query;

    console.log(`Exporting room configs with filters:`, { hotel_id, type, is_active, format });

    // Get product service using the correct module resolution
    const productService = req.scope.resolve(Modules.PRODUCT);
    if (!productService) {
      throw new Error('Product module service not found');
    }

    // Fetch all products
    console.log('Fetching all products...');
    const products = await productService.listProducts({});
    console.log(`Found ${products.length} total products`);

    // Filter for room configurations based on the provided filters
    const roomConfigs = products.filter(product => {
      // Check if it has metadata and is a room configuration (has type)
      if (!product.metadata || !product.metadata.type) return false;

      // Apply hotel_id filter if provided
      if (hotel_id && hotel_id !== 'all' && product.metadata.hotel_id !== hotel_id) {
        return false;
      }

      // Apply type filter if provided
      if (type && type !== 'all' && product.metadata.type !== type) {
        return false;
      }

      // Apply is_active filter if provided
      if (is_active && is_active !== 'all') {
        const isActiveValue = is_active === 'true';
        if (product.status === 'draft' && isActiveValue) return false;
        if (product.status === 'published' && !isActiveValue) return false;
      }

      return true;
    });

    console.log(`Filtered to ${roomConfigs.length} room configurations`);

    console.log(`Found ${roomConfigs.length} room configurations`);

    // Process the data
    const processedData = roomConfigs.map(config => {
      const metadata = config.metadata || {};

      // Convert metadata to proper types
      const max_occupancy = metadata.max_occupancy ? Number(metadata.max_occupancy) : 0;
      const max_adults = metadata.max_adults ? Number(metadata.max_adults) : 0;
      const max_children = metadata.max_children ? Number(metadata.max_children) : 0;

      // Handle amenities array
      let amenitiesStr = '';
      if (metadata.amenities) {
        if (Array.isArray(metadata.amenities)) {
          amenitiesStr = metadata.amenities.join(', ');
        } else if (typeof metadata.amenities === 'string') {
          amenitiesStr = metadata.amenities;
        } else {
          try {
            amenitiesStr = JSON.stringify(metadata.amenities);
          } catch (e) {
            amenitiesStr = '';
          }
        }
      }

      return {
        id: config.id,
        title: config.title || '',
        handle: config.handle || '',
        description: config.description || '',
        status: config.status || '',
        hotel_id: metadata.hotel_id || '',
        type: metadata.type || '',
        bed_type: metadata.bed_type || '',
        max_occupancy,
        max_adults,
        max_children,
        amenities: amenitiesStr,
        created_at: config.created_at,
        updated_at: config.updated_at
      };
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Room Configurations');

    // Define columns
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 20 },
      { header: 'Name', key: 'title', width: 30 },
      { header: 'Handle', key: 'handle', width: 20 },
      { header: 'Description', key: 'description', width: 40 },
      { header: 'Status', key: 'status', width: 10 },
      { header: 'Hotel ID', key: 'hotel_id', width: 20 },
      { header: 'Type', key: 'type', width: 15 },
      { header: 'Bed Type', key: 'bed_type', width: 15 },
      { header: 'Max Occupancy', key: 'max_occupancy', width: 15 },
      { header: 'Max Adults', key: 'max_adults', width: 15 },
      { header: 'Max Children', key: 'max_children', width: 15 },
      { header: 'Amenities', key: 'amenities', width: 40 },
      { header: 'Created At', key: 'created_at', width: 20 },
      { header: 'Updated At', key: 'updated_at', width: 20 }
    ];

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add data rows
    processedData.forEach(data => {
      worksheet.addRow(data);
    });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=room-configs-export.xlsx');

    // Write workbook to response
    await workbook.xlsx.write(res);
    console.log('Export completed successfully');

  } catch (error) {
    console.error('Error exporting room configurations:', error);
    console.error('Error stack:', error.stack);

    res.status(500).json({
      message: 'Error exporting room configurations',
      error: error.message
    });
  }
};
