import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// GET endpoint to list rooms by room configuration ID
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params; // Room configuration ID (product ID)

    if (!id) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`Fetching rooms for room configuration: ${id}`);

    // Get the product module service to fetch the product and its variants
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    try {
      // Get the product with its variants
      const product = await productModuleService.retrieveProduct(id, {
        relations: ["variants"],
      });

      if (!product) {
        console.log(`No product found with ID: ${id}`);
        return res.status(404).json({ message: "Room configuration not found" });
      }

      console.log(`Found product: ${product.title} with ${product.variants?.length || 0} variants`);

      if (!product.variants || product.variants.length === 0) {
        console.log(`No variants (rooms) found for product (room configuration): ${id}`);
        return res.json({ rooms: [] });
      }

      // Map variants to room objects
      const rooms = product.variants.map(variant => {
        return {
          id: variant.id,
          name: variant.title,
          room_number: variant.metadata?.room_number || "",
          status: variant.metadata?.status || "available",
          floor: variant.metadata?.floor || "",
          notes: variant.metadata?.notes || "",
          is_active: variant.metadata?.is_active !== false,
          room_config_id: id,
          hotel_id: product.metadata?.hotel_id || "",
          options: {}, // We'll skip options for now since they're causing issues
        };
      });

      console.log(`Returning ${rooms.length} real rooms for room configuration: ${id}`);
      return res.json({ rooms });
    } catch (error) {
      console.error("Error fetching product and variants:", error);

      // Try a different approach using the query tool
      try {
        const query = req.scope.resolve("query");

        // First, get the product (room configuration)
        const { data: products } = await query.graph({
          entity: "product",
          filters: { id },
          fields: ["id", "title", "metadata"],
        });

        if (!products || products.length === 0) {
          console.log(`No product found with ID: ${id} using query tool`);
          return res.status(404).json({ message: "Room configuration not found" });
        }

        const product = products[0];
        console.log(`Found product using query tool: ${product.title}`);

        // Get all product variants for this product
        const { data: variants } = await query.graph({
          entity: "product_variant",
          filters: { product_id: id },
          fields: ["id", "title", "product_id", "metadata", "inventory_quantity"],
        });

        if (!variants || variants.length === 0) {
          console.log(`No variants found using query tool for product: ${id}`);
          return res.json({ rooms: [] });
        }

        console.log(`Found ${variants.length} variants using query tool for product: ${id}`);

        // Map variants to room objects
        const rooms = variants.map(variant => {
          return {
            id: variant.id,
            name: variant.title,
            room_number: variant.metadata?.room_number || "",
            status: variant.metadata?.status || "available",
            floor: variant.metadata?.floor || "",
            notes: variant.metadata?.notes || "",
            is_active: variant.metadata?.is_active !== false,
            room_config_id: id,
            hotel_id: product.metadata?.hotel_id || "",
            options: {}, // We'll skip options for now since they're causing issues
          };
        });

        console.log(`Returning ${rooms.length} real rooms using query tool for room configuration: ${id}`);
        return res.json({ rooms });
      } catch (queryError) {
        console.error("Error fetching rooms using query tool:", queryError);

        // If all else fails, return empty array instead of mock data
        console.log(`Returning empty rooms array for room configuration: ${id}`);
        return res.json({ rooms: [] });
      }
    }
  } catch (error) {
    console.error("Error fetching rooms by room configuration:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to fetch rooms",
    });
  }
};
