import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { Modules } from "@camped-ai/framework/utils"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const roomConfigId = req.params.id

  try {
    // Get the product service
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Get the product with images
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["images"]
    });

    // Format the images
    const images = product.images.map(image => ({
      id: image.id,
      url: image.url,
      isThumbnail: image.metadata?.isThumbnail || false
    }));

    res.status(200).json({ images })
  } catch (error) {
    console.error("Error fetching room config images:", error);
    res.status(500).json({
      message: "Failed to fetch room config images",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const roomConfigId = req.params.id;
  console.log("DELETE request body:", req.body);
  console.log("DELETE request query:", req.query);

  // Try to get image_id from various sources
  let image_id = req.body?.image_id || req.query?.image_id;

  if (!image_id) {
    return res.status(400).json({
      message: "Image ID is required"
    });
  }

  try {
    // Get the product service
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Get the product with images
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["images"]
    });

    // Find the image to delete
    const imageToDelete = product.images.find(img => img.id === image_id);
    if (!imageToDelete) {
      return res.status(404).json({
        message: `Image with id ${image_id} not found`
      });
    }

    // Filter out the image to delete
    const updatedImages = product.images.filter(img => img.id !== image_id);

    // Update the product with the filtered images
    await productService.updateProducts(roomConfigId, {
      images: updatedImages
    });

    res.status(200).json({
      id: image_id,
      deleted: true
    });
  } catch (error) {
    console.error("Error deleting room config image:", error);
    res.status(500).json({
      message: "Failed to delete room config image",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
