import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { Modules } from "@camped-ai/framework/utils"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const roomConfigId = req.params.id
  const { image_id } = req.body

  if (!image_id) {
    return res.status(400).json({
      message: "Image ID is required"
    });
  }

  try {
    // Get the product service
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Get the product with images
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["images"]
    });

    // Find the image to set as thumbnail
    const thumbnailImage = product.images.find(img => img.id === image_id);
    if (!thumbnailImage) {
      return res.status(404).json({
        message: `Image with id ${image_id} not found`
      });
    }

    // Update all images to not be thumbnails and set the selected one as thumbnail
    const updatedImages = product.images.map(img => ({
      ...img,
      metadata: {
        ...img.metadata,
        isThumbnail: img.id === image_id
      }
    }));

    // Update the product with the updated images
    await productService.updateProducts(roomConfigId, {
      images: updatedImages,
      thumbnail: thumbnailImage.url
    });

    res.status(200).json({
      thumbnail: thumbnailImage
    });
  } catch (error) {
    console.error("Error setting room config thumbnail:", error);
    res.status(500).json({
      message: "Failed to set room config thumbnail",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
