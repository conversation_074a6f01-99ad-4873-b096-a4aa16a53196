import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

// GET endpoint to retrieve a specific room configuration
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get the product that represents the room configuration
    const product = await productModuleService.retrieveProduct(roomConfigId, {
      relations: ["categories"],
    });

    if (!product) {
      return res.status(404).json({ message: "Room configuration not found" });
    }

    // Transform the product to a room configuration
    const roomConfig = {
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size,
      bed_type: product.metadata?.bed_type,
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    };

    res.json({ roomConfig });
  } catch (error) {
    console.error("Error fetching room configuration:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to fetch room configuration",
    });
  }
};

// DELETE endpoint to delete a room configuration
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Delete the product that represents the room configuration
    await productModuleService.deleteProduct(roomConfigId);

    res.json({ id: roomConfigId, deleted: true });
  } catch (error) {
    console.error("Error deleting room configuration:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to delete room configuration",
    });
  }
};
