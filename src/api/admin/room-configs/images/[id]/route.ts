import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { Modules } from "@camped-ai/framework/utils"

export async function DELETE(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const imageId = req.params.id;
  console.log("Deleting image with ID:", imageId);

  if (!imageId) {
    return res.status(400).json({
      message: "Image ID is required"
    });
  }

  try {
    // Get the product service
    const productService = req.scope.resolve(Modules.PRODUCT);

    // Find the product that contains this image
    const products = await productService.listProducts();

    const productWithImage = products.find(product =>
      product.images && product.images.some(img => img.id === imageId)
    );

    if (!productWithImage) {
      return res.status(404).json({
        message: `No product found with image id ${imageId}`
      });
    }

    // Filter out the image to delete
    const updatedImages = productWithImage.images.filter(img => img.id !== imageId);

    // Update the product with the filtered images
    await productService.updateProducts(productWithImage.id, {
      images: updatedImages
    });

    return res.status(200).json({
      id: imageId,
      deleted: true
    });
  } catch (error) {
    console.error("Error deleting room config image:", error);
    res.status(500).json({
      message: "Failed to delete room config image",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
