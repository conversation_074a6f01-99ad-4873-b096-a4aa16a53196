import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';
import { HOTEL_MODULE } from "../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../modules/hotel-management/hotel/service";

/**
 * GET endpoint to download a template for bulk room configuration import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get hotel service to fetch hotels for the dropdown
    const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);

    // Fetch all hotels to provide as reference
    const hotels = await hotelService.listHotels({});

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for room configurations
    const worksheet = workbook.addWorksheet('RoomConfigs');

    // Define columns
    worksheet.columns = [
      { header: 'name', key: 'name', width: 30 },
      { header: 'hotel_id', key: 'hotel_id', width: 40 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'is_active', key: 'is_active', width: 15 },
      { header: 'type', key: 'type', width: 20 }, // Room type (standard, deluxe, suite, etc.)
      { header: 'area', key: 'area', width: 15 },
      { header: 'bed_type', key: 'bed_type', width: 20 }, // Bed type (king, queen, twin, etc.)
      { header: 'view_type', key: 'view_type', width: 20 },
      { header: 'amenities', key: 'amenities', width: 40 },
      { header: 'tags', key: 'tags', width: 30 },
      { header: 'max_adults', key: 'max_adults', width: 15 },
      { header: 'max_children', key: 'max_children', width: 15 },
      { header: 'max_infants', key: 'max_infants', width: 15 },
      { header: 'base_price', key: 'base_price', width: 15 },
      { header: 'currency_code', key: 'currency_code', width: 15 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add a second worksheet with hotel reference data
    const hotelSheet = workbook.addWorksheet('Hotels Reference');

    // Define columns for the hotel reference sheet
    hotelSheet.columns = [
      { header: 'id', key: 'id', width: 40 },
      { header: 'name', key: 'name', width: 30 },
    ];

    // Style the header row
    hotelSheet.getRow(1).font = { bold: true };
    hotelSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add hotel data
    hotels.forEach(hotel => {
      hotelSheet.addRow({
        id: hotel.id,
        name: hotel.name
      });
    });

    // Add a sample row to the room configs sheet
    worksheet.addRow({
      name: 'Standard Double Room',
      hotel_id: hotels.length > 0 ? hotels[0].id : 'hotel-id-here',
      description: 'A comfortable standard double room with all basic amenities',
      is_active: 'true',
      type: 'Standard', // Room type
      area: '25',
      bed_type: 'Double', // Bed type
      view_type: 'Garden',
      amenities: 'WiFi,TV,Air Conditioning,Mini Bar',
      tags: 'standard,double',
      max_adults: 2,
      max_children: 1,
      max_infants: 1,
      base_price: 100,
      currency_code: 'USD',
    });

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add instructions for each field
    const instructions = [
      { field: 'name', description: 'The name of the room configuration', required: 'Yes', format: 'Text' },
      { field: 'hotel_id', description: 'The ID of the hotel this room configuration belongs to. See Hotels Reference sheet.', required: 'Yes', format: 'Text (UUID)' },
      { field: 'description', description: 'A detailed description of the room configuration', required: 'Yes', format: 'Text' },
      { field: 'is_active', description: 'Whether the room configuration is active and visible', required: 'No', format: 'true or false' },
      { field: 'type', description: 'The type/category of room (e.g., Standard, Deluxe, Suite)', required: 'No', format: 'Text' },
      { field: 'area', description: 'The area of the room in square meters/feet', required: 'No', format: 'Number' },
      { field: 'bed_type', description: 'The type of bed(s) in the room (e.g., King, Queen, Twin)', required: 'No', format: 'Text' },
      { field: 'view_type', description: 'The type of view from the room', required: 'No', format: 'Text' },
      { field: 'amenities', description: 'List of amenities available in the room', required: 'No', format: 'Comma-separated list' },
      { field: 'tags', description: 'Tags to categorize the room configuration', required: 'No', format: 'Comma-separated list' },
      { field: 'max_adults', description: 'Maximum number of adults allowed (used to calculate max_occupancy)', required: 'No', format: 'Number' },
      { field: 'max_children', description: 'Maximum number of children allowed (used to calculate max_occupancy)', required: 'No', format: 'Number' },
      { field: 'max_infants', description: 'Maximum number of infants allowed (not counted in max_occupancy)', required: 'No', format: 'Number' },
      { field: 'base_price', description: 'Base price for the room', required: 'No', format: 'Number' },
      { field: 'currency_code', description: 'Currency code for the price (e.g., USD, EUR)', required: 'No', format: 'Text (3-letter code)' },
    ];

    // Add a note about max_occupancy calculation
    instructionsSheet.addRow({
      field: 'Note',
      description: 'max_occupancy is automatically calculated as the sum of max_adults and max_children. You do not need to provide it directly.',
      required: '',
      format: ''
    });

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=room-config-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};
