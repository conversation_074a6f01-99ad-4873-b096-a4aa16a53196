import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateLookup,
  PostAdminDeleteLookup,
  PostAdminUpdateLookup,
} from "./validators";
import { CreateLookupWorkflow } from "src/workflows/lookup/create-lookup";
import { UpdateLookupWorkflow } from "src/workflows/lookup/update-lookup";
import { DeleteLookupWorkflow } from "src/workflows/lookup/delete-lookup";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

type PostAdminCreateLookupType = z.infer<typeof PostAdminCreateLookup>;
type PostAdminDeleteLookupType = z.infer<typeof PostAdminDeleteLookup>;
type PostAdminUpdateLookupType = z.infer<typeof PostAdminUpdateLookup>;

export const POST = async (
  req: MedusaRequest<PostAdminCreateLookupType>,
  res: MedusaResponse
) => {
  const { result } = await CreateLookupWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ lookup: result });
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateLookupType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateLookupWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ lookup: result });
};

export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteLookupType>,
  res: MedusaResponse
) => {
  const { result } = await DeleteLookupWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });

  res.json({ lookup: result });
};
