import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { data: lookup } = await query.graph({
    entity: "lookup",
    filters: {
      id: [req.params.id],
    },
    fields: ["*"],
  });

  res.json({ lookup });
};