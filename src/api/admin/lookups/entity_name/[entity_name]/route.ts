import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { limit = 20, offset = 0 } = req.query || {};
  const {
    data: lookups,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "lookup",
    filters: {
      entity_name: req.params.entity_name,
    },
    fields: ["*"],
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });

  res.json({
    lookups,
    count,
    limit: take,
    offset: skip,
  });
};
