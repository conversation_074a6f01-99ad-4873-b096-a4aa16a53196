import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";

/**
 * GET endpoint to download a template for bulk hotel import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get destination service to fetch destinations for the dropdown
    const destinationService: DestinationModuleService = req.scope.resolve(DESTINATION_MODULE);

    // Fetch all destinations to provide as reference
    const destinations = await destinationService.listDestinations({});

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for hotels
    const worksheet = workbook.addWorksheet('Hotels');

    // Define columns
    worksheet.columns = [
      { header: 'name', key: 'name', width: 30 },
      { header: 'handle', key: 'handle', width: 30 },
      { header: 'destination_id', key: 'destination_id', width: 40 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'is_active', key: 'is_active', width: 15 },
      { header: 'address', key: 'address', width: 40 },
      { header: 'city', key: 'city', width: 20 },
      { header: 'postal_code', key: 'postal_code', width: 15 },
      { header: 'phone', key: 'phone', width: 20 },
      { header: 'email', key: 'email', width: 30 },
      { header: 'website', key: 'website', width: 30 },
      { header: 'check_in_time', key: 'check_in_time', width: 15 },
      { header: 'check_out_time', key: 'check_out_time', width: 15 },
      { header: 'star_rating', key: 'star_rating', width: 15 },
      { header: 'amenities', key: 'amenities', width: 40 },
      { header: 'tags', key: 'tags', width: 30 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add a second worksheet with destination reference data
    const destinationSheet = workbook.addWorksheet('Destinations Reference');

    // Define columns for the destination reference sheet
    destinationSheet.columns = [
      { header: 'id', key: 'id', width: 40 },
      { header: 'name', key: 'name', width: 30 },
      { header: 'country', key: 'country', width: 20 },
    ];

    // Style the header row
    destinationSheet.getRow(1).font = { bold: true };
    destinationSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add destination data
    destinations.forEach(destination => {
      destinationSheet.addRow({
        id: destination.id,
        name: destination.name,
        country: destination.country
      });
    });

    // Add a sample row to the hotels sheet
    worksheet.addRow({
      name: 'Sample Hotel',
      handle: 'sample-hotel',
      destination_id: destinations.length > 0 ? destinations[0].id : 'destination-id-here',
      description: 'A beautiful hotel with amazing views',
      is_active: 'true',
      address: '123 Main Street',
      city: 'Sample City',
      postal_code: '12345',
      phone: '+1234567890',
      email: '<EMAIL>',
      website: 'https://www.samplehotel.com',
      check_in_time: '14:00',
      check_out_time: '11:00',
      star_rating: '4',
      amenities: 'WiFi,Pool,Spa,Restaurant',
      tags: 'luxury,family-friendly',
    });

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add instructions for each field
    const instructions = [
      { field: 'name', description: 'The name of the hotel', required: 'Yes', format: 'Text' },
      { field: 'handle', description: 'URL-friendly identifier for the hotel (lowercase, hyphens instead of spaces)', required: 'Yes', format: 'Text (lowercase, hyphens)' },
      { field: 'destination_id', description: 'The ID of the destination where the hotel is located. See Destinations Reference sheet.', required: 'Yes', format: 'Text (UUID)' },
      { field: 'description', description: 'A detailed description of the hotel', required: 'Yes', format: 'Text' },
      { field: 'is_active', description: 'Whether the hotel is active and visible', required: 'No', format: 'true or false' },
      { field: 'address', description: 'The street address of the hotel', required: 'No', format: 'Text' },
      { field: 'city', description: 'The city where the hotel is located', required: 'No', format: 'Text' },
      { field: 'postal_code', description: 'The postal/zip code of the hotel', required: 'No', format: 'Text' },
      { field: 'phone', description: 'Contact phone number for the hotel', required: 'No', format: 'Text' },
      { field: 'email', description: 'Contact email for the hotel', required: 'No', format: 'Email' },
      { field: 'website', description: 'Website URL for the hotel', required: 'No', format: 'URL' },
      { field: 'check_in_time', description: 'Standard check-in time', required: 'No', format: 'HH:MM (24-hour)' },
      { field: 'check_out_time', description: 'Standard check-out time', required: 'No', format: 'HH:MM (24-hour)' },
      { field: 'star_rating', description: 'Hotel star rating (1-5)', required: 'No', format: 'Number (1-5)' },
      { field: 'amenities', description: 'List of amenities offered by the hotel', required: 'No', format: 'Comma-separated list' },
      { field: 'tags', description: 'Tags to categorize the hotel', required: 'No', format: 'Comma-separated list' },
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=hotel-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};
