import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";
import * as ExcelJS from 'exceljs';
import { CreateHotelWorkflow } from "../../../../workflows/hotel-management/hotel/create-hotel";
import { IProductModuleService } from "@camped-ai/framework/types";
import multer from 'multer';
import { z } from "zod";
import { HOTEL_MODULE } from "../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../modules/hotel-management/hotel/service";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

// Validation schema for hotel data
const HotelSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().min(1, "Handle is required"),
  destination_id: z.string().min(1, "Destination ID is required"),
  description: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  is_active: z.union([z.boolean(), z.string().transform(val => val?.toLowerCase() === 'true'), z.null()]).optional().default(true),
  address: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  city: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  postal_code: z.union([z.string(), z.number().transform(val => String(val)), z.null()]).optional().transform(val => val === null ? "" : val),
  phone: z.union([z.string(), z.number().transform(val => String(val)), z.null()]).optional().transform(val => val === null ? "" : val),
  email: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  website: z.union([
    z.string(),
    z.object({}).transform(() => ""), // Handle empty objects
    z.null(),
    z.undefined()
  ]).optional().transform(val => val || null),
  check_in_time: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  check_out_time: z.union([z.string(), z.null()]).optional().transform(val => val === null ? "" : val),
  star_rating: z.union([z.number(), z.string().transform(val => val ? parseInt(val, 10) : undefined), z.null()]).optional(),
  amenities: z.union([z.string(), z.null()]).optional().transform(val => {
    if (val === null || val === undefined) return [];
    return typeof val === 'string' ? val.split(',').map(item => item.trim()) : [];
  }),
  tags: z.union([z.string(), z.null()]).optional().transform(val => {
    if (val === null || val === undefined) return [];
    return typeof val === 'string' ? val.split(',').map(tag => tag.trim()) : [];
  }),
});

// Type for the validated data that matches the workflow input requirements
type ValidatedHotelData = z.infer<typeof HotelSchema> & {
  name: string; // Ensure name is required
  handle: string; // Ensure handle is required
  destination_id: string; // Ensure destination_id is required
};

/**
 * POST endpoint to import hotels from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      // Get services
      const hotelModuleService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
      const destinationModuleService: DestinationModuleService = req.scope.resolve(DESTINATION_MODULE);
      const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split('.').pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === 'csv') {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString('utf8');

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require('fs').writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require('fs').unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error('Error reading CSV:', csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split('\\n').map(row => row.split(','));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet('Sheet1');

              // Add rows to the worksheet
              rows.forEach(row => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error('CSV file is empty or invalid');
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Hotels' or use the first one
          worksheet = workbook.getWorksheet('Hotels') ||
                     workbook.getWorksheet('hotels') ||
                     workbook.getWorksheet('Sheet1') ||
                     workbook.worksheets[0];
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        return res.status(400).json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res.status(400).json({ message: 'Invalid file: No worksheet found' });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};

      console.log('Raw headers:', headers);

      // Create a map of column index to header name
      // Note: ExcelJS uses 1-based indexing for columns
      for (let i = 1; i < headers.length; i++) {
        const header = headers[i];
        if (header) {
          const headerName = header.toString().trim().toLowerCase(); // Convert to lowercase for case-insensitive matching
          headerMap[i] = headerName;
          console.log(`Mapped column ${i} to header '${headerName}'`);
        }
      }

      console.log('Header map:', headerMap);

      // Prepare results
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        created: [],
        errors: []
      };

      // Validate destination IDs
      const destinations = await destinationModuleService.listDestinations({});
      const validDestinationIds = new Set(destinations.map(d => d.id));

      // Process each row (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (!row.hasValues) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            // Handle special cases for certain fields
            if (header === 'website' && cell.value) {
              // Convert website to string if it's an object (happens with hyperlinks in Excel)
              rowData[header] = cell.value.text || cell.value.hyperlink || String(cell.value);
            } else {
              rowData[header] = cell.value;
            }
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach(key => {
          // Handle null values
          if (rowData[key] === null) {
            // Keep it as null for the validator to handle
          }
          // Convert any objects to strings
          else if (rowData[key] && typeof rowData[key] === 'object' && !(rowData[key] instanceof Array)) {
            rowData[key] = String(rowData[key]);
          }
          // Handle empty strings for numeric fields
          else if (rowData[key] === '' && ['star_rating'].includes(key)) {
            rowData[key] = null;
          }
        });

        // Debug log to see what data we're getting
        console.log('Row data:', rowData);

        // Map the lowercase header names back to the expected case-sensitive names
        const normalizedData: Record<string, any> = {};
        Object.keys(rowData).forEach(key => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === 'name') normalizedData.name = rowData[key];
          else if (lowerKey === 'handle') normalizedData.handle = rowData[key];
          else if (lowerKey === 'destination_id') normalizedData.destination_id = rowData[key];
          else if (lowerKey === 'description') normalizedData.description = rowData[key];
          else if (lowerKey === 'is_active') normalizedData.is_active = rowData[key];
          else if (lowerKey === 'address') normalizedData.address = rowData[key];
          else if (lowerKey === 'city') normalizedData.city = rowData[key];
          else if (lowerKey === 'postal_code') normalizedData.postal_code = rowData[key];
          else if (lowerKey === 'phone') normalizedData.phone = rowData[key];
          else if (lowerKey === 'email') normalizedData.email = rowData[key];
          else if (lowerKey === 'website') normalizedData.website = rowData[key];
          else if (lowerKey === 'check_in_time') normalizedData.check_in_time = rowData[key];
          else if (lowerKey === 'check_out_time') normalizedData.check_out_time = rowData[key];
          else if (lowerKey === 'star_rating') normalizedData.star_rating = rowData[key];
          else if (lowerKey === 'amenities') normalizedData.amenities = rowData[key];
          else if (lowerKey === 'tags') normalizedData.tags = rowData[key];
          else normalizedData[key] = rowData[key]; // Keep any other fields
        });

        console.log('Normalized data:', normalizedData);

        // Ensure required fields are present
        if (!normalizedData.name || !normalizedData.handle || !normalizedData.destination_id) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: `Missing required fields: ${!normalizedData.name ? 'name' : ''} ${!normalizedData.handle ? 'handle' : ''} ${!normalizedData.destination_id ? 'destination_id' : ''}`
          });
          continue;
        }

        try {
          // Validate the data
          const validatedData = HotelSchema.parse(normalizedData);

          // Check if required fields are present
          if (!validatedData.name || !validatedData.handle || !validatedData.destination_id) {
            throw new Error('Missing required fields: name, handle, and destination_id are required');
          }

          // Validate destination_id
          if (!validDestinationIds.has(validatedData.destination_id)) {
            throw new Error(`Invalid destination_id: ${validatedData.destination_id}. Please use a valid destination ID from the Destinations Reference sheet.`);
          }

          // Create the hotel using the workflow
          const { result } = await CreateHotelWorkflow(req.scope).run({
            input: {
              name: validatedData.name,
              handle: validatedData.handle,
              destination_id: validatedData.destination_id,
              description: validatedData.description || '',
              is_active: validatedData.is_active,
              address: validatedData.address || null,
              location: validatedData.city || null, // Use city as location
              phone_number: validatedData.phone || null, // Correct field name
              email: validatedData.email || null,
              website: validatedData.website || null,
              check_in_time: validatedData.check_in_time || null,
              check_out_time: validatedData.check_out_time || null,
              rating: validatedData.star_rating || null, // Use rating instead of star_rating
              amenities: validatedData.amenities || [],
              tags: validatedData.tags || []
            }
          });

          results.successful++;
          results.created.push({
            id: result.id,
            name: validatedData.name,
            row: i
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: rowData,
            error: error.message || 'Unknown error'
          });
        }
      }

      // Return the results
      return res.status(200).json({
        message: 'Import completed',
        results
      });

    } catch (error) {
      console.error('Error importing hotels:', error);
      return res.status(500).json({ message: `Error importing hotels: ${error.message}` });
    }
  });
};
