import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../modules/hotel-management/hotel/service";
import * as ExcelJS from 'exceljs';
import { createObjectCsvStringifier } from 'csv-writer';

/**
 * GET endpoint to export hotels data
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get hotel service
    const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);

    // Parse query parameters
    const { fields, is_active, is_featured, destination_id, format } = req.query;

    // Build filter object
    const filters: Record<string, any> = {};

    if (is_active && is_active !== 'all') {
      filters.is_active = is_active === 'true';
    }

    if (is_featured && is_featured !== 'all') {
      filters.is_featured = is_featured === 'true';
    }

    if (destination_id && destination_id !== 'all') {
      filters.destination_id = destination_id;
    }

    console.log('Export filters:', filters);

    // Fetch hotels with filters
    const hotels = await hotelService.listHotels(filters);

    // Parse fields to include
    const fieldsToInclude = fields ? (fields as string).split(',') : [
      'id', 'name', 'handle', 'description', 'is_active', 'website', 'email',
      'destination_id', 'rating', 'total_reviews', 'notes', 'location', 'address',
      'phone_number', 'timezone', 'available_languages', 'tax_type', 'tax_number',
      'tags', 'amenities', 'currency', 'check_in_time', 'check_out_time',
      'created_at', 'updated_at'
    ];

    // Process hotels data
    const processedData = hotels.map(hotel => {
      const data: Record<string, any> = {};

      fieldsToInclude.forEach(field => {
        if (['tags', 'amenities', 'available_languages'].includes(field) && hotel[field]) {
          // Handle array fields specially
          if (Array.isArray(hotel[field])) {
            data[field] = hotel[field].join(', ');
          } else if (typeof hotel[field] === 'string') {
            data[field] = hotel[field];
          } else {
            try {
              data[field] = JSON.stringify(hotel[field]);
            } catch (e) {
              data[field] = '';
            }
          }
        } else {
          data[field] = hotel[field];
        }
      });

      return data;
    });

    // Export based on format
    if (format === 'csv') {
      // CSV export
      if (processedData.length === 0) {
        // Create empty CSV with headers
        const headers = fieldsToInclude.map(field => ({
          id: field,
          title: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())
        }));

        const csvStringifier = createObjectCsvStringifier({
          header: headers
        });

        const csvContent = csvStringifier.getHeaderString();

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=hotels-export.csv');
        return res.send(csvContent);
      }

      // Create CSV with data
      const headers = fieldsToInclude.map(field => ({
        id: field,
        title: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())
      }));

      const csvStringifier = createObjectCsvStringifier({
        header: headers
      });

      const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(processedData);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=hotels-export.csv');
      return res.send(csvContent);
    } else {
      // Excel export (default)
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Hotels');

      // Add headers
      const headers = fieldsToInclude.map(field => ({
        header: field.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),
        key: field,
        width: 20
      }));

      worksheet.columns = headers;

      // Style the header row
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Add data
      processedData.forEach(data => {
        worksheet.addRow(data);
      });

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        const lengths = column.values?.filter(v => v !== undefined).map(v => v.toString().length);
        if (lengths && lengths.length > 0) {
          const maxLength = Math.max(column.header.length, ...lengths);
          column.width = maxLength < 10 ? 10 : maxLength < 50 ? maxLength : 50;
        }
      });

      // Set content type and headers for Excel file download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename=hotels-export.xlsx');

      // Write the workbook to the response
      await workbook.xlsx.write(res);
    }
  } catch (error) {
    console.error('Error exporting hotels:', error);
    res.status(500).json({ message: 'Error exporting hotels' });
  }
};
