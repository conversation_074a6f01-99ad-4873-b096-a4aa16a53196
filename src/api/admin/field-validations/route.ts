import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateFieldValidationWorkflow } from "src/workflows/field-validation/create-field-validation";
import { UpdateFieldValidationWorkflow } from "src/workflows/field-validation/update-field-validation";
import { z } from "zod";
import {
  PostAdminCreateFieldValidation,
  PostAdminUpdateFieldValidation,
} from "./validators";

type PostAdminCreateFieldValidationType = z.infer<
  typeof PostAdminCreateFieldValidation
>;
type PostAdminUpdateFieldValidationType = z.infer<
  typeof PostAdminUpdateFieldValidation
>;

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateFieldValidationType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateFieldValidationWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ fieldValidation: result });
};

export const POST = async (
  req: MedusaRequest<PostAdminCreateFieldValidationType>,
  res: MedusaResponse
) => {
  const { result } = await CreateFieldValidationWorkflow(req.scope).run({
    input: req.body,
  });

  res.json({ fieldValidation: result });
};

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: fieldValidation } = await query.graph({
    entity: "field_validation",
    // filters: {
    //     id: [
    //       req.params.id,
    //     ],
    //   },

    fields: ["*"],
  });

  res.json({ fieldValidation });
};
