import {
  defineMiddlewares,
  validateAndTransformBody,
} from "@camped-ai/framework/http";
import multer from "multer";
import { PostAdminCreateCancellationPolicy } from "./admin/hotel-management/cancellation-policies/route";
import { PostAdminUpdateCancellationPolicy } from "./admin/hotel-management/cancellation-policies/[id]/route";

const upload = multer({ storage: multer.memoryStorage() });

export default defineMiddlewares({
  routes: [
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/hotels/:id/upload",
      middlewares: [upload.array("files")],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/destinations/:id/upload",
      middlewares: [upload.array("files")],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/cancellation-policies",
      middlewares: [
        validateAndTransformBody(PostAdminCreateCancellationPolicy),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/cancellation-policies/:id",
      middlewares: [
        validateAndTransformBody(PostAdminUpdateCancellationPolicy),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/room-configs/:id/upload",
      middlewares: [upload.array("files")],
    },
  ],
});
