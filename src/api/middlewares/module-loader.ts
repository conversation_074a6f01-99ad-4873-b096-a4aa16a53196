import { NextFunction, Request, Response } from "express";
import loaders from "../../loaders";

/**
 * Middleware to ensure all modules are loaded
 * This runs once on server startup
 */
let initialized = false;

export default () => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!initialized) {
      try {
        // Load all modules
        await loaders(req.scope);
        initialized = true;
        console.log("All modules loaded successfully");
      } catch (error) {
        console.error("Failed to load modules:", error);
      }
    }
    next();
  };
};
