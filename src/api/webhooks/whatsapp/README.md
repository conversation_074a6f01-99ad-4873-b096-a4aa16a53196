# WhatsApp Webhook Integration

This webhook integration allows your application to receive and process incoming WhatsApp messages and message status updates from the WhatsApp Business API.

## Features

- Webhook verification for Meta/WhatsApp Business API
- Processing of incoming WhatsApp messages (text, media, location, contacts)
- Handling of message status updates (sent, delivered, read, failed)
- Secure signature verification
- Storing messages and status updates in the database

## Prerequisites

Before you begin, you'll need:

1. A Meta Developer account
2. A WhatsApp Business Account
3. A verified phone number for WhatsApp Business API
4. A publicly accessible HTTPS endpoint for your webhook

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your `.env` file or deployment configuration:

```
# WhatsApp API Configuration
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id

# Webhook Configuration
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret
```

- `WHATSAPP_PHONE_NUMBER_ID`: Your WhatsApp Business Account phone number ID
- `WHATSAPP_ACCESS_TOKEN`: Your WhatsApp Business API access token
- `WHATSAPP_BUSINESS_ACCOUNT_ID`: Your WhatsApp Business Account ID
- `WHATSAPP_WEBHOOK_VERIFY_TOKEN`: A random string you create to verify webhook setup
- `WHATSAPP_WEBHOOK_SECRET`: A random string used to verify webhook signatures

### 2. Deploy Your Application

Ensure your application is deployed and accessible via HTTPS. The webhook endpoint should be:

```
https://your-domain.com/api/webhooks/whatsapp
```

### 3. Configure Webhook in Meta Developer Portal

1. Go to [Meta Developer Portal](https://developers.facebook.com/)
2. Select your app
3. Navigate to "WhatsApp" > "Configuration"
4. In the "Webhooks" section, click "Configure"
5. Enter your webhook URL: `https://your-domain.com/api/webhooks/whatsapp`
6. Enter your `WHATSAPP_WEBHOOK_VERIFY_TOKEN` as the "Verify Token"
7. Select the following webhook fields:
   - `messages`
   - `message_status`
8. Click "Verify and Save"

If the verification is successful, your webhook is now configured!

### 4. Subscribe to Webhook Events

1. In the "Webhooks" section, click "Manage"
2. Select your WhatsApp Business Account
3. Subscribe to the following fields:
   - `messages`
   - `message_status`
4. Click "Save"

### 5. Testing the Webhook

#### Verification Test

Meta will automatically test your webhook during setup by sending a GET request with the verification token.

#### Using the Test Script

1. Update the configuration in `test-webhook.js` with your webhook URL and secret
2. Run the test script:
   ```
   node src/api/webhooks/whatsapp/test-webhook.js
   ```

#### Real-World Testing

1. Send a message to your WhatsApp Business number
2. Check your application logs to see if the webhook received the message
3. Send a message from your application to a WhatsApp user
4. Check your application logs to see if you receive status updates

## Webhook Payload Examples

### Incoming Message

```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "PHONE_NUMBER",
              "phone_number_id": "PHONE_NUMBER_ID"
            },
            "contacts": [
              {
                "profile": {
                  "name": "CONTACT_NAME"
                },
                "wa_id": "CONTACT_PHONE"
              }
            ],
            "messages": [
              {
                "from": "CONTACT_PHONE",
                "id": "MESSAGE_ID",
                "timestamp": "TIMESTAMP",
                "text": {
                  "body": "MESSAGE_CONTENT"
                },
                "type": "text"
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

### Message Status Update

```json
{
  "object": "whatsapp_business_account",
  "entry": [
    {
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [
        {
          "value": {
            "messaging_product": "whatsapp",
            "metadata": {
              "display_phone_number": "PHONE_NUMBER",
              "phone_number_id": "PHONE_NUMBER_ID"
            },
            "statuses": [
              {
                "id": "MESSAGE_ID",
                "recipient_id": "RECIPIENT_PHONE",
                "status": "delivered",
                "timestamp": "TIMESTAMP",
                "conversation": {
                  "id": "CONVERSATION_ID",
                  "origin": {
                    "type": "business_initiated"
                  }
                },
                "pricing": {
                  "billable": true,
                  "pricing_model": "CBP",
                  "category": "business_initiated"
                }
              }
            ]
          },
          "field": "messages"
        }
      ]
    }
  ]
}
```

## Troubleshooting

### Webhook Verification Fails

- Check that your `WHATSAPP_WEBHOOK_VERIFY_TOKEN` matches what you entered in the Meta Developer Portal
- Ensure your webhook endpoint is publicly accessible
- Check server logs for any errors during the verification process

### Missing Messages or Status Updates

- Verify that you've subscribed to the correct webhook fields in the Meta Developer Portal
- Check that your webhook is returning a 200 OK response for all requests
- Look for any error logs in your application

### Signature Verification Fails

- Ensure your `WHATSAPP_WEBHOOK_SECRET` matches what you set in the Meta Developer Portal
- Check that your webhook is receiving the raw request body for signature verification
- Verify that the `x-hub-signature-256` header is being passed correctly

## Security Considerations

- Always verify the webhook signature in production environments
- Store your webhook secret securely
- Process webhook events idempotently to handle potential duplicate events
- Return 200 OK responses promptly to acknowledge receipt of the webhook
