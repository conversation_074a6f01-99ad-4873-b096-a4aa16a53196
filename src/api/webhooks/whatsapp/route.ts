import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import crypto from "crypto";
import { MessageDirection, MessageStatus, MessageType } from "../../../modules/whatsapp-message/models/whatsapp-message";
import { WHATSAPP_MESSAGE_SERVICE } from "../../../modules/whatsapp-message";

/**
 * Verify WhatsApp webhook signature
 * @param req Request object
 * @param logger Logger instance
 * @returns Whether the signature is valid
 */
function verifyWhatsAppSignature(req: MedusaRequest, logger: any): boolean {
  try {
    const signature = req.headers["x-hub-signature-256"];
    const webhookSecret = process.env.WHATSAPP_WEBHOOK_SECRET;

    if (!signature || !webhookSecret) {
      logger.warn("Missing signature or webhook secret");
      return false;
    }

    // Get raw body as string
    const rawBody = typeof req.body === 'string'
      ? req.body
      : JSON.stringify(req.body);

    const hmac = crypto.createHmac("sha256", webhookSecret);
    const expectedSignature = "sha256=" + hmac.update(rawBody).digest("hex");

    try {
      return crypto.timingSafeEqual(
        Buffer.from(signature as string),
        Buffer.from(expectedSignature)
      );
    } catch (err) {
      logger.error(`Signature comparison error: ${err.message}`);
      return false;
    }
  } catch (error) {
    logger.error(`Error verifying WhatsApp signature: ${error.message}`);
    return false;
  }
}

/**
 * Handle GET requests for webhook verification
 * This endpoint is called by Meta/WhatsApp when setting up the webhook
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {

    const logger = req.scope.resolve("logger");

    // Handle webhook verification from Meta
    const mode = req.query["hub.mode"];
    const token = req.query["hub.verify_token"];
    const challenge = req.query["hub.challenge"];

    const webhookVerifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN;

    logger.info(`WhatsApp webhook verification request received: mode=${mode}, token=${token ? "provided" : "missing"}`);

    if (!webhookVerifyToken) {
      logger.error("WHATSAPP_WEBHOOK_VERIFY_TOKEN is not configured in environment variables");
      return res.status(500).send("Webhook verify token not configured");
    }

    // Check if mode and token are in the query string
    if (mode === "subscribe" && token === webhookVerifyToken) {
      // Respond with the challenge token from the request
      logger.info("WhatsApp webhook verified successfully");
      return res.status(200).send(challenge);
    } else {
      // Respond with '403 Forbidden' if verify tokens do not match
      logger.error("WhatsApp webhook verification failed: invalid mode or token");
      return res.status(403).send("Forbidden");
    }
  } catch (error) {
    console.error("Error handling WhatsApp webhook verification:", error);
    return res.status(500).send("Error processing webhook verification");
  }
}

/**
 * Handle POST requests for webhook events
 * This endpoint receives all webhook events from WhatsApp including:
 * - Message status updates (sent, delivered, read, failed)
 * - Incoming messages (text, media, location, etc.)
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('here')
    const { body } = req;

    const logger = req.scope.resolve("logger");

    // Verify webhook signature if in production
    if (process.env.NODE_ENV === "production") {
      const isValid = verifyWhatsAppSignature(req, logger);
      if (!isValid) {
        logger.warn("Invalid WhatsApp webhook signature");
        return res.status(401).send("Invalid signature");
      }
    }

    logger.info(`Received WhatsApp webhook: ${JSON.stringify(body)}`);

    // Process different types of webhook events
    if (body.entry && body.entry.length > 0) {
      for (const entry of body.entry) {
        const changes = entry.changes || [];

        for (const change of changes) {
          if (change.field === "messages") {
            const value = change.value;

            // Get the WhatsApp message service
            const whatsappMessageService = req.scope.resolve(WHATSAPP_MESSAGE_SERVICE);
            if (!whatsappMessageService) {
              logger.error("WhatsApp message service not found");
              continue;
            }

            // Handle message status updates
            await handleStatusUpdates(value, whatsappMessageService, logger);

            // Handle incoming messages
            await handleIncomingMessages(value, whatsappMessageService, logger, req.scope);
          }
        }
      }
    }

    // Always acknowledge receipt of the webhook to prevent retries
    return res.status(200).send("OK");
  } catch (error) {
    console.error(`Error processing WhatsApp webhook: ${error.message}`);
    // Still return 200 to acknowledge receipt and prevent retries
    return res.status(200).send("OK");
  }
}

/**
 * Handle message status updates
 * @param value The webhook value object
 * @param whatsappMessageService The WhatsApp message service
 * @param logger The logger instance
 */
async function handleStatusUpdates(value: any, whatsappMessageService: any, logger: any) {
  if (!value.statuses || !value.statuses.length) {
    return;
  }

  for (const status of value.statuses) {
    try {
      logger.info(`Processing WhatsApp message status update: ${status.id} -> ${status.status}`);

      if (!status.id) {
        logger.warn("Status update missing message ID, skipping");
        continue;
      }

      // Map WhatsApp status to our status enum
      let messageStatus: MessageStatus;
      switch (status.status) {
        case "sent":
          messageStatus = MessageStatus.SENT;
          break;
        case "delivered":
          messageStatus = MessageStatus.DELIVERED;
          break;
        case "read":
          messageStatus = MessageStatus.READ;
          break;
        case "failed":
          messageStatus = MessageStatus.FAILED;
          break;
        default:
          logger.warn(`Unknown status type: ${status.status}, defaulting to PENDING`);
          messageStatus = MessageStatus.PENDING;
      }

      // Get timestamp if available
      const timestamp = status.timestamp ? new Date(parseInt(status.timestamp) * 1000) : new Date();

      // Update message status in the database
      await whatsappMessageService.updateMessageStatus(status.id, messageStatus);

      logger.info(`Successfully updated message status: ${status.id} -> ${messageStatus}`);
    } catch (err) {
      logger.error(`Failed to process status update for message ${status.id}: ${err.message}`);
      // Continue processing other status updates
    }
  }
}

/**
 * Handle incoming messages
 * @param value The webhook value object
 * @param whatsappMessageService The WhatsApp message service
 * @param logger The logger instance
 * @param scope The request scope
 */
async function handleIncomingMessages(value: any, whatsappMessageService: any, logger: any, scope: any) {
  if (!value.messages || !value.messages.length) {
    return;
  }

  const phoneNumberId = value.metadata?.phone_number_id;
  if (!phoneNumberId) {
    logger.warn("Missing phone_number_id in webhook metadata");
    return;
  }

  for (const message of value.messages) {
    try {
      logger.info(`Processing incoming WhatsApp message: ${message.id} from ${message.from}`);

      if (!message.id || !message.from) {
        logger.warn("Incoming message missing required fields, skipping");
        continue;
      }

      // Check if this message already exists in our database
      const existingMessage = await whatsappMessageService.getMessageByWhatsAppId(message.id);
      if (existingMessage) {
        logger.info(`Message ${message.id} already exists in database, skipping`);
        continue;
      }

      // Determine message type and extract content
      const { messageType, content, mediaUrl, mediaId, mediaMimeType } = extractMessageContent(message, logger);

      // Try to find customer_id and order_id from the latest message sent to this phone number
      let customerId = null;
      let orderId = null;
      try {
        // Find the latest message sent to this phone number
        const latestMessages = await whatsappMessageService.getConversationByPhone("+"+ message.from);

        // If we have previous messages, use the customer_id and order_id from the latest one
        if (latestMessages && latestMessages.length > 0) {
          // Find the latest outbound message (from us to the customer)
          const latestOutboundMessage = latestMessages.find((msg: any) =>
            msg.direction === MessageDirection.OUTBOUND &&
            msg.to_phone ==="+"+ message.from
          );

          if (latestOutboundMessage) {
            customerId = latestOutboundMessage.customer_id;
            orderId = latestOutboundMessage.order_id;

            logger.info(`Found customer_id ${customerId} and order_id ${orderId} from previous conversation`);
          }
        }
      } catch (err) {
        logger.debug(`Could not find previous conversation for phone ${message.from}: ${err.message}`);
      }

      // Save the incoming message to the database
      const savedMessage = await whatsappMessageService.saveIncomingMessage({
        whatsapp_message_id: message.id,
        content,
        from_phone: message.from,
        to_phone: phoneNumberId,
        order_id: orderId,
        customer_id: customerId,
        message_type: messageType,
        media_url: mediaUrl,
        media_id: mediaId,
        media_mime_type: mediaMimeType,
        metadata: message,
        timestamp: message.timestamp
      });

      logger.info(`Successfully saved incoming message: ${message.id}`);

      // Emit notification event for the incoming message
      try {
        const eventBusService = scope.resolve("eventBusService");
        if (eventBusService) {
          await eventBusService.emit("whatsapp.message.received", {
            id: savedMessage.id,
            whatsapp_message_id: message.id,
            content,
            from_phone: message.from,
            order_id: orderId,
            customer_id: customerId,
            message_type: messageType
          });
          logger.info(`Emitted whatsapp.message.received event for message: ${message.id}`);
        }
      } catch (err) {
        logger.error(`Failed to emit notification event for message ${message.id}: ${err.message}`);
        // Continue processing even if notification fails
      }
    } catch (err) {
      logger.error(`Failed to process incoming message ${message.id}: ${err.message}`);
      // Continue processing other messages
    }
  }
}

/**
 * Extract message content based on message type
 * @param message The message object from webhook
 * @param logger The logger instance
 * @returns Object containing message type, content, and media information
 */
function extractMessageContent(message: any, logger: any) {
  let messageType = MessageType.TEXT;
  let content = "";
  let mediaUrl = "";
  let mediaId = "";
  let mediaMimeType = "";

  try {
    if (message.text) {
      messageType = MessageType.TEXT;
      content = message.text.body || "";
    } else if (message.image) {
      messageType = MessageType.IMAGE;
      content = message.image.caption || "[Image]";
      mediaUrl = message.image.url || "";
      mediaId = message.image.id || "";
      mediaMimeType = message.image.mime_type || "image/jpeg";
    } else if (message.video) {
      messageType = MessageType.VIDEO;
      content = message.video.caption || "[Video]";
      mediaUrl = message.video.url || "";
      mediaId = message.video.id || "";
      mediaMimeType = message.video.mime_type || "video/mp4";
    } else if (message.document) {
      messageType = MessageType.DOCUMENT;
      content = message.document.caption || "[Document]";
      mediaUrl = message.document.url || "";
      mediaId = message.document.id || "";
      mediaMimeType = message.document.mime_type || "application/pdf";
    } else if (message.audio) {
      messageType = MessageType.AUDIO;
      content = "[Audio]";
      mediaUrl = message.audio.url || "";
      mediaId = message.audio.id || "";
      mediaMimeType = message.audio.mime_type || "audio/ogg";
    } else if (message.location) {
      messageType = MessageType.LOCATION;
      content = `[Location: ${message.location.latitude}, ${message.location.longitude}]`;
    } else if (message.contacts) {
      messageType = MessageType.CONTACT;
      const contactName = message.contacts[0]?.name?.formatted_name || "Unknown";
      content = `[Contact: ${contactName}]`;
    } else {
      logger.warn(`Unsupported message type received: ${JSON.stringify(message)}`);
      content = "[Unsupported message type]";
    }
  } catch (err) {
    logger.error(`Error extracting message content: ${err.message}`);
    content = "[Error processing message]";
  }

  return { messageType, content, mediaUrl, mediaId, mediaMimeType };
}
