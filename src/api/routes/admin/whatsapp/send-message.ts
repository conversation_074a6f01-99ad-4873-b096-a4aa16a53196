import { Request, Response } from "express";
import { sendWhatsAppMessage } from "../../../../modules/whatsapp-notification/workflows";

/**
 * @swagger
 * /admin/whatsapp/send:
 *   post:
 *     summary: Send a WhatsApp message
 *     description: Send a WhatsApp message to a customer
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - to
 *               - message
 *             properties:
 *               to:
 *                 type: string
 *                 description: The recipient's phone number
 *               message:
 *                 type: string
 *                 description: The message content
 *               order_id:
 *                 type: string
 *                 description: Optional order ID to associate with the message
 *               customer_id:
 *                 type: string
 *                 description: Optional customer ID to associate with the message
 *     responses:
 *       200:
 *         description: Message sent successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal server error
 */
export async function sendMessage(req: Request, res: Response): Promise<void> {
  const { to, message, order_id, customer_id } = req.body;

  if (!to || !message) {
    res.status(400).json({
      success: false,
      message: "Both 'to' and 'message' are required"
    });
    return;
  }

  try {
    const result = await sendWhatsAppMessage(req.scope, {
      to,
      message,
      order_id,
      customer_id
    });

    if (result.success) {
      res.status(200).json({
        success: true,
        message_id: result.message_id,
        message: "WhatsApp message sent successfully"
      });
    } else {
      res.status(500).json({
        success: false,
        message: `Failed to send WhatsApp message: ${result.error}`
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Error sending WhatsApp message: ${error.message}`
    });
  }
}
