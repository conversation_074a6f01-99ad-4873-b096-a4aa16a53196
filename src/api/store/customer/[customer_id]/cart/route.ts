import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ICartModuleService } from "@camped-ai/framework/types";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartModuleService: ICartModuleService = req.scope.resolve(
      Modules.CART
    );

    // Example: Retrieve an customer ID from query params
    const customerId = req.params.customer_id;

    if (!customerId) {
      return res.status(400).json({ error: "customer_id is required" });
    }

    // Fetch customer cart details
    const customerCart = await cartModuleService.listCarts({
      customer_id: customerId.toString(), 
    },{relations: ["items", "shipping_address", "billing_address"], select: ["original_item_total"]});

    // Respond with the retrieved customer cart details
    return res.status(200).json({customerCart});
  } catch (error) {
    console.error("Error retrieving customer cart:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
