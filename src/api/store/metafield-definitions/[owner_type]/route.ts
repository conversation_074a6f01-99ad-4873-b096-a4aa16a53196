import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: metafieldDefinition } = await query.graph({
    entity: "metafield_definition",
    filters: {
      owner_type: [
          req.params.owner_type,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ metafieldDefinition });
};


