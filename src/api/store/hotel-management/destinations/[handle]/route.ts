import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const {
    limit = 20,
    offset = 0,
    is_featured,
    is_active,
    is_pets_allowed,
  } = req.query || {};
  const filters: Record<string, any> = {};

  const { data: destination } = await query.graph({
    entity: "destination",
    filters: {
      handle: req.params.handle,
    },
    fields: ["*", "images.*", "faqs.*"],
  });

  console.log("Destination:", destination);

  filters.destination_id = destination?.[0]?.id;
  if (is_featured !== undefined) {
    filters.is_featured = is_featured === "true";
  }
  if (is_active !== undefined) {
    filters.is_active = is_active === "true";
  }
  if (is_pets_allowed !== undefined) {
    filters.is_pets_allowed = is_pets_allowed === "true";
  }

  // // Get hotels for this destination
  const { data: hotels } = await query.graph({
    entity: "hotel",
    filters,
    fields: ["*", "images.*"],
  });

  res.json({ destination, hotels });
};
