import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");

    const { id } = req.params;

    // Get customer ID from session
    const customerId = req.user?.customer_id;

    if (!customerId) {
      return res.status(401).json({
        message: "Unauthorized",
      });
    }

    // Get booking
    const booking = await bookingOrderService.retrieveBookingOrder(id);

    // Check if booking belongs to customer
    if (booking.metadata?.customer_id !== customerId) {
      return res.status(403).json({
        message: "Forbidden",
      });
    }

    res.json({
      booking
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to get booking",
    });
  }
}

export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");
    const roomInventoryService = container.resolve("roomInventoryService");

    const { id } = req.params;
    const { reason } = req.body;

    // Get customer ID from session
    const customerId = req.user?.customer_id;

    if (!customerId) {
      return res.status(401).json({
        message: "Unauthorized",
      });
    }

    // Get booking
    const booking = await bookingOrderService.retrieveBookingOrder(id);

    // Check if booking belongs to customer
    if (booking.metadata?.customer_id !== customerId) {
      return res.status(403).json({
        message: "Forbidden",
      });
    }

    // Cancel booking
    const cancelledBooking = await bookingOrderService.cancelBookingOrder(id, {
      reason,
      cancelledBy: "customer"
    });

    // Update room inventory to mark as available
    try {
      await roomInventoryService.updateInventoryStatus(
        booking.room_id,
        new Date(booking.check_in_date),
        new Date(booking.check_out_date),
        "available",
        `Booking cancelled by customer: ${booking.id}`
      );
    } catch (inventoryError) {
      console.error("Failed to update inventory:", inventoryError);
      // Continue with the response even if inventory update fails
    }

    res.json({
      booking: cancelledBooking,
      success: true,
      message: "Booking cancelled successfully",
      // Include a flag to indicate that availability should be refreshed
      refresh_availability: true
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to cancel booking",
    });
  }
}
