import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import * as pdf from "html-pdf";

// Extended request type to include JWT token data
interface AuthenticatedRequest extends MedusaRequest {
  user?: {
    customer_id?: string;
    actor_id?: string;
    actor_type?: string;
    app_metadata?: {
      customer_id?: string;
    };
  };
}

// Helper function to format currency
function currencyFormatter(
  amount: number,
  currencyCode: string = "USD"
): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currencyCode.toUpperCase(),
  }).format(amount / 100); // Assuming amounts are in cents
}

// Helper function to format dates
function dateFormatter(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

// Helper function to replace placeholders in template content
function replacePlaceholders(
  content: string,
  dataContext: Record<string, any>
): string {
  let processedContent = content;

  // Replace placeholders recursively
  function replaceInObject(obj: any, prefix: string = ""): void {
    for (const [key, value] of Object.entries(obj)) {
      const placeholder = prefix ? `{{${prefix}.${key}}}` : `{{${key}}}`;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        // Recursively handle nested objects
        replaceInObject(value, prefix ? `${prefix}.${key}` : key);
      } else {
        // Replace the placeholder with the actual value
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          "g"
        );
        processedContent = processedContent.replace(regex, String(value || ""));
      }
    }
  }

  replaceInObject(dataContext);
  return processedContent;
}

/**
 * Download invoice for a booking (Store API)
 * Customers can download invoices for their completed bookings
 */
export async function GET(req: AuthenticatedRequest, res: MedusaResponse) {
  const { id: bookingId } = req.params;
  const INVOICE_TEMPLATE_EVENT_NAME = "booking.invoice.default";

  if (!bookingId) {
    return res.status(400).json({ message: "Booking ID is required." });
  }

  try {
    console.log("req.headers:", req.headers);

    // Try to get customer ID from different possible sources (same logic as bookings route)
    let customerId = req.session?.customer_id;

    // If not in session directly, try to get from JWT token data
    if (!customerId && req.user) {
      // Check if customer_id is in user object directly
      customerId = req.user.customer_id;

      // Or check if it's in the app_metadata
      if (!customerId && req.user.app_metadata?.customer_id) {
        customerId = req.user.app_metadata.customer_id;
      }

      // Or check if actor_id is a customer ID
      if (
        !customerId &&
        req.user.actor_id &&
        req.user.actor_type === "customer"
      ) {
        customerId = req.user.actor_id;
      }
    }

    // Try to extract from Authorization header (Bearer token)
    if (!customerId && req.headers.authorization) {
      try {
        const authHeader = req.headers.authorization;

        // Check if it's a Bearer token
        if (authHeader.startsWith("Bearer ")) {
          const token = authHeader.substring(7); // Remove 'Bearer ' prefix
          console.log("Found JWT token in Authorization header:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                } else if (payload.customer_id) {
                  customerId = payload.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error(
          "Error extracting customer ID from Authorization header:",
          e
        );
      }
    }

    // Try to extract from x-customer-id header directly
    if (!customerId && req.headers["x-customer-id"]) {
      customerId = req.headers["x-customer-id"] as string;
      console.log("Found customer ID in x-customer-id header:", customerId);
    }

    // Fallback: Try to extract from cookies if still no customer ID
    if (!customerId && req.headers.cookie) {
      try {
        // Find the JWT token in the cookies
        const cookies = req.headers.cookie.split(";").map((c) => c.trim());
        const jwtCookie = cookies.find((c) => c.startsWith("connect.sid="));

        if (jwtCookie) {
          const token = jwtCookie.split("=")[1];
          console.log("Found JWT token in cookies:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error("Error extracting customer ID from cookies:", e);
      }
    }

    console.log("Customer ID extracted:", customerId);

    if (!customerId) {
      return res.status(401).json({
        message:
          "Unauthorized. Please provide a valid customer ID via one of the following methods: Authorization header (Bearer token), x-customer-id header, or session cookie.",
        supportedAuthMethods: [
          "Authorization: Bearer <jwt_token>",
          "x-customer-id: <customer_id>",
          "Cookie: connect.sid=<session_cookie>",
        ],
      });
    }

    const orderService: any = req.scope.resolve(Modules.ORDER);
    const notificationTemplateService: NotificationTemplateService =
      req.scope.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // 1. Fetch the Order
    const order = await orderService.retrieveOrder(bookingId, {
      relations: ["billing_address", "items"],
    });

    if (!order) {
      return res.status(404).json({ message: "Booking not found." });
    }

    // 2. Verify the booking belongs to the customer
    if (order.customer_id !== customerId) {
      return res.status(403).json({
        message:
          "Forbidden. You can only download invoices for your own bookings.",
      });
    }

    // 3. Check if the booking is completed (has payment)
    const metadata = order.metadata || {};
    if (order.status !== "completed" && metadata.payment_status !== "paid") {
      return res.status(400).json({
        message: "Invoice is only available for completed bookings.",
      });
    }

    // 4. Get the invoice template
    let invoiceTemplate = null;
    try {
      const filters = {
        event_name: INVOICE_TEMPLATE_EVENT_NAME,
        is_active: true,
        channel: "pdf",
      };
      const config = { take: 1 };

      const templates =
        await notificationTemplateService.listNotificationTemplates(
          filters,
          config
        );

      if (templates && templates.length > 0) {
        invoiceTemplate = templates[0];
      } else {
        console.warn(
          `No active PDF template found for event: ${INVOICE_TEMPLATE_EVENT_NAME}`
        );
      }
    } catch (error) {
      console.error("Error fetching invoice template:", error);
    }

    // 5. Use fallback template if no template found
    if (!invoiceTemplate) {
      invoiceTemplate = {
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 30px; }
              .customer-details { margin-bottom: 30px; }
              .booking-details { margin-bottom: 30px; }
              .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
              .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .items-table th { background-color: #f2f2f2; }
              .total-section { text-align: right; }
              .total-line { margin: 5px 0; }
              .total-amount { font-weight: bold; font-size: 1.2em; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>INVOICE</h1>
            </div>

            <div class="invoice-details">
              <p><strong>Invoice #:</strong> {{order.display_id}}</p>
              <p><strong>Date:</strong> {{order.created_at_formatted}}</p>
              <p><strong>Status:</strong> {{order.status}}</p>
            </div>

            <div class="customer-details">
              <h3>Bill To:</h3>
              <p>{{customer.first_name}} {{customer.last_name}}</p>
              <p>{{customer.email}}</p>
            </div>

            <div class="booking-details">
              <h3>Booking Details:</h3>
              <p><strong>Hotel:</strong> {{booking.hotel_name}}</p>
              <p><strong>Room Type:</strong> {{booking.room_config_name}}</p>
              <p><strong>Check-in:</strong> {{booking.check_in_date}}</p>
              <p><strong>Check-out:</strong> {{booking.check_out_date}}</p>
              <p><strong>Guests:</strong> {{booking.number_of_guests}}</p>
            </div>

            <table class="items-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                {{items_html_block}}
              </tbody>
            </table>

            <div class="total-section">
              <div class="total-line">Subtotal: {{order.subtotal_formatted}}</div>
              <div class="total-line">Tax: {{order.tax_total_formatted}}</div>
              <div class="total-line total-amount">Total: {{order.total_formatted}}</div>
            </div>
          </body>
          </html>
        `,
      };
    }

    // 6. Create data context for placeholder replacement

    // Extract add-ons from metadata if they exist
    const addOns = metadata.add_ons || [];

    // Create add-ons list for display
    const addOnsList = addOns.length > 0
      ? addOns.map((addOn: any) => `✓ ${addOn.name || 'Service'}`).join('<br>')
      : 'No add-on services selected';

    const dataContext: Record<string, any> = {
      order: {
        display_id: order.display_id || order.id,
        created_at_formatted: dateFormatter(order.created_at),
        subtotal_formatted: currencyFormatter(
          order.subtotal || 0,
          order.currency_code
        ),
        shipping_total_formatted: currencyFormatter(
          order.shipping_total || 0,
          order.currency_code
        ),
        tax_total_formatted: currencyFormatter(
          order.tax_total || 0,
          order.currency_code
        ),
        total_formatted: currencyFormatter(
          order.total || 0,
          order.currency_code
        ),
        currency_code: order.currency_code,
        status: order.status,
        notes: order.notes || "",
        payment_method_name: order.payment_method_name || "N/A",
      },
      customer: {
        email: order.customer?.email || order.email || "N/A",
        guest_phone: metadata.guest_phone || "N/A",
        first_name:
          order.customer?.first_name ||
          metadata.guest_name?.split(" ")[0] ||
          "",
        last_name:
          order.customer?.last_name ||
          metadata.guest_name?.split(" ").slice(1).join(" ") ||
          "",
      },
      booking: {
        hotel_name: metadata.hotel_name || "Hotel",
        room_config_name:
          metadata.room_config_name || metadata.room_type || "Standard Room",
        check_in_date: metadata.check_in_date
          ? dateFormatter(metadata.check_in_date)
          : "N/A",
        check_out_date: metadata.check_out_date
          ? dateFormatter(metadata.check_out_date)
          : "N/A",
        check_in_time: metadata.check_in_time || "12:00",
        check_out_time: metadata.check_out_time || "12:00",
        number_of_guests: metadata.number_of_guests || 1,
        special_requests: metadata.special_requests || "None",
        total_amount: currencyFormatter(
          metadata.total_amount * 100 || 0,
          order.currency_code
        ),
      },
      // Add-ons information
      add_ons: addOns.map((addOn: any) => ({
        name: addOn.name || "Service",
      })),
      add_ons_list: addOnsList,
      items_html_block:
        order.items
          ?.map(
            (item: any) =>
              `<tr>
              <td>${item.title || "Hotel Booking"}</td>
              <td style="text-align: center;">${
                item.quantity?.toString() || "1"
              }</td>
              <td style="text-align: right;">${currencyFormatter(
                item.unit_price || 0,
                order.currency_code
              )}</td>
              <td style="text-align: right;">${currencyFormatter(
                item.total || item.unit_price * item.quantity || 0,
                order.currency_code
              )}</td>
            </tr>`
          )
          .join("") || `<tr><td colspan="4">No items found</td></tr>`,
    };

    // 7. Replace placeholders in the template content
    const processedHtmlContent = replacePlaceholders(
      invoiceTemplate.content,
      dataContext
    );

    // 8. Generate PDF
    const options: pdf.CreateOptions = {
      format: "A4",
      orientation: "portrait",
      border: { top: "0.5in", right: "0.5in", bottom: "0.5in", left: "0.5in" },
    };

    pdf.create(processedHtmlContent, options).toBuffer((err, buffer) => {
      if (err) {
        console.error("Error generating PDF with html-pdf:", err);
        return res.status(500).json({
          message: "Failed to generate PDF.",
          errorDetail: err.message,
        });
      }

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="invoice-${order.display_id || order.id}.pdf"`
      );
      res.send(buffer);
    });
  } catch (error: any) {
    console.error("Error in GET /store/bookings/[id]/invoice route:", error);
    let statusCode = 500;
    let message = "Failed to process invoice request.";

    if (
      error.message?.toLowerCase().includes("not found") ||
      error.name === "NotFoundError" ||
      error.type === "not_found"
    ) {
      statusCode = 404;
      message = error.message || "Booking not found.";
    }

    res.status(statusCode).json({
      message,
      errorDetail: error.message,
    });
  }
}
