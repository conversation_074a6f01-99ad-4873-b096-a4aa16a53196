import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for booking reservation
export const StoreBookingReservationSchema = z.object({
  room_id: z.string(),
  hotel_id: z.string(),
  start_date: z.string(),
  end_date: z.string(),
  guest_name: z.string().optional(),
  guest_email: z.string().email().optional(),
  guest_phone: z.string().optional(),
  number_of_guests: z.number().default(1),
  special_requests: z.string().optional(),
  customer_id: z.string().optional(),
  session_id: z.string().optional(),
  price_amount: z.number().optional(),
  price_currency_code: z.string().default("USD"),
  metadata: z.record(z.any()).optional(),
});

export type StoreBookingReservationType = z.infer<typeof StoreBookingReservationSchema>;

/**
 * Create a booking reservation
 *
 * @param req - The request object
 * @param res - The response object
 * @returns The created booking reservation
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartService = req.scope.resolve(Modules.CART);
    const lineItemService = req.scope.resolve(Modules.LINE_ITEM);

    // Validate request body
    const validationResult = StoreBookingReservationSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request body",
        errors: validationResult.error.format(),
      });
    }

    const data = validationResult.data;

    // Create a cart
    const cart = await cartService.create({
      customer_id: data.customer_id,
      metadata: {
        ...data.metadata,
        session_id: data.session_id || `session_${Date.now()}`,
        hotel_id: data.hotel_id,
        start_date: data.start_date,
        end_date: data.end_date,
        guest_name: data.guest_name,
        guest_email: data.guest_email,
        guest_phone: data.guest_phone,
        number_of_guests: data.number_of_guests,
        special_requests: data.special_requests,
      },
    });

    // Create a line item for the room
    const lineItem = await lineItemService.create({
      cart_id: cart.id,
      variant_id: data.room_id,
      quantity: 1,
      metadata: {
        start_date: data.start_date,
        end_date: data.end_date,
      },
    });

    // If price is provided, update the line item with the price
    if (data.price_amount) {
      await lineItemService.update(lineItem.id, {
        unit_price: data.price_amount,
        currency_code: data.price_currency_code,
      });
    }

    // Set expiration time (30 minutes from now)
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000);
    await cartService.update(cart.id, {
      metadata: {
        ...cart.metadata,
        expires_at: expiresAt.toISOString(),
      },
    });

    // Get the updated cart with the line item
    const updatedCart = await cartService.retrieveCart(cart.id, {
      relations: ["line_items"],
    });

    return res.status(201).json({
      reservation: {
        id: cart.id,
        cart_id: cart.id,
        room_id: data.room_id,
        hotel_id: data.hotel_id,
        start_date: data.start_date,
        end_date: data.end_date,
        guest_name: data.guest_name,
        guest_email: data.guest_email,
        guest_phone: data.guest_phone,
        number_of_guests: data.number_of_guests,
        special_requests: data.special_requests,
        price_amount: data.price_amount,
        price_currency_code: data.price_currency_code,
        expires_at: expiresAt.toISOString(),
        status: "reserved",
        metadata: updatedCart.metadata,
        created_at: updatedCart.created_at,
        updated_at: updatedCart.updated_at,
      },
    });

  } catch (error) {
    console.error("Error creating booking reservation:", error);
    return res.status(500).json({
      message: "Error creating booking reservation",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
