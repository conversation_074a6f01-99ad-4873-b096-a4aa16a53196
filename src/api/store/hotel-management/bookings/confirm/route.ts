import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingService = container.resolve("bookingService");
    const bookingOrderService = container.resolve("bookingOrderService");

    const {
      reservation_id,
      payment_method,
      shipping_address,
      billing_address,
      region_id,
      currency_code,
      total_amount
    } = req.body;

    // Validate required fields
    if (!reservation_id || !payment_method || !region_id || !currency_code || !total_amount) {
      return res.status(400).json({
        message: "Missing required fields",
      });
    }

    // Get customer ID from session
    const customerId = req.user?.customer_id;

    // Get the reservation
    const reservation = await bookingService.retrieveReservation(reservation_id);

    // Check if reservation is still valid
    if (reservation.status !== "pending") {
      return res.status(400).json({
        message: `Reservation is ${reservation.status}`,
      });
    }

    // Check if reservation has expired
    if (new Date(reservation.expires_at) < new Date()) {
      return res.status(400).json({
        message: "Reservation has expired",
      });
    }

    // Create booking data
    const bookingData = {
      hotel_id: reservation.hotel_id,
      room_id: reservation.room_id,
      check_in_date: reservation.start_date,
      check_out_date: reservation.end_date,
      check_in_time: reservation.check_in_time,
      check_out_time: reservation.check_out_time,
      guest_name: reservation.guest_name,
      guest_email: reservation.guest_email,
      guest_phone: reservation.guest_phone,
      number_of_guests: reservation.number_of_guests,
      special_requests: reservation.special_requests,
      total_amount,
      currency_code,
      region_id,
      shipping_address,
      billing_address,
      customer_id: customerId,
      metadata: {
        payment_method,
        customer_id: customerId,
        ...reservation.metadata
      }
    };

    // Create Medusa order and booking order
    const { order, bookingOrder } = await bookingOrderService.createFromReservation(
      reservation_id,
      bookingData
    );

    res.json({
      booking: bookingOrder,
      order
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to confirm booking",
    });
  }
}
