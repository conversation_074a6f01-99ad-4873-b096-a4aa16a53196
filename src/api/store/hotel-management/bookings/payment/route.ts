import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");
    const paymentService = container.resolve(Modules.PAYMENT);

    const {
      booking_id,
      payment_provider_id,
      payment_method,
      payment_data
    } = req.body;

    // Validate required fields
    if (!booking_id || !payment_provider_id) {
      return res.status(400).json({
        message: "Missing required fields",
      });
    }

    // Get customer ID from session
    const customerId = req.user?.customer_id;

    if (!customerId) {
      return res.status(401).json({
        message: "Unauthorized",
      });
    }

    // Get the booking
    const booking = await bookingOrderService.retrieveBookingOrder(booking_id);

    // Check if booking belongs to customer
    if (booking.metadata?.customer_id !== customerId) {
      return res.status(403).json({
        message: "Forbidden",
      });
    }

    // Get the order
    const orderService = container.resolve(Modules.ORDER);
    const order = await orderService.retrieveOrder(booking.order_id);

    // Create payment session
    const paymentSession = await paymentService.createSession({
      provider_id: payment_provider_id,
      amount: booking.total_amount,
      currency_code: booking.currency_code,
      context: {
        customer_id: customerId,
        email: booking.guest_email,
        booking_id: booking.id,
        order_id: booking.order_id,
        payment_method: payment_method || "card",
        ...payment_data
      }
    });

    // Update booking with payment session
    await bookingOrderService.updateBookingOrder(booking_id, {
      payment_status: "awaiting_payment",
      metadata: {
        ...booking.metadata,
        payment_session_id: paymentSession.id
      }
    });

    res.json({
      payment_session: paymentSession
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create payment session",
    });
  }
}
