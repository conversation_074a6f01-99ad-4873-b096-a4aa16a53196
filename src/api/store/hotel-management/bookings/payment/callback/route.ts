import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { container } = req.scope;
    const bookingOrderService = container.resolve("bookingOrderService");
    const paymentService = container.resolve(Modules.PAYMENT);

    const {
      payment_session_id,
      booking_id
    } = req.body;

    // Validate required fields
    if (!payment_session_id || !booking_id) {
      return res.status(400).json({
        message: "Missing required fields",
      });
    }

    // Get the booking
    const booking = await bookingOrderService.retrieveBookingOrder(booking_id);

    // Verify payment session
    const paymentSession = await paymentService.retrieveSession(payment_session_id);
    
    // Check payment status
    const paymentStatus = await paymentService.getStatus(paymentSession);
    
    if (paymentStatus === "authorized" || paymentStatus === "captured") {
      // Update booking payment status
      await bookingOrderService.updatePaymentStatus(booking_id, "paid");
      
      // Update booking status if it's pending
      if (booking.status === "pending") {
        await bookingOrderService.updateStatus(booking_id, "confirmed");
      }
      
      // Update order status
      const orderService = container.resolve(Modules.ORDER);
      await orderService.capturePayment(booking.order_id);
      
      res.json({
        success: true,
        booking_id,
        status: "paid"
      });
    } else {
      res.json({
        success: false,
        booking_id,
        status: paymentStatus
      });
    }
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to process payment callback",
    });
  }
}
