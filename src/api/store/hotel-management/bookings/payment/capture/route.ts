import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

/**
 * API endpoint to capture a payment for a booking
 * This endpoint takes a payment session ID and captures the payment
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
    const orderService = req.scope.resolve(Modules.ORDER);

    // Get the payment session ID from the request body
    const body = req.body as any;
    const payment_session_id = body?.payment_session_id as string;

    // Validate required fields
    if (!payment_session_id) {
      return res.status(400).json({
        success: false,
        message: "Payment session ID is required",
      });
    }

    // Retrieve the payment session to verify it exists and get associated data
    let paymentSession: any;
    try {
      paymentSession = await paymentModuleService.retrievePaymentSession(payment_session_id);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: "Payment session not found",
      });
    }


    // Step 1: Authorize the payment session
    let payment: any;
    try {
      payment = await paymentModuleService.authorizePaymentSession(
        payment_session_id,
        {}
      );

      if (!payment || !payment.id) {
        return res.status(400).json({
          success: false,
          message: "Failed to authorize payment session",
        });
      }
    } catch (error) {
      console.error("Error authorizing payment:", error);
      return res.status(400).json({
        success: false,
        message: error instanceof Error ?
          `Error authorizing payment: ${error.message}` :
          "Failed to authorize payment session",
      });
    }

    // Step 2: Capture the payment
    let capture: any;
    try {
      capture = await paymentModuleService.capturePayment({
        payment_id: payment.id,
      });
    } catch (error) {
      console.error("Error capturing payment:", error);
      return res.status(400).json({
        success: false,
        message: error instanceof Error ?
          `Error capturing payment: ${error.message}` :
          "Failed to capture payment",
        payment_id: payment.id,
      });
    }

    // Step 3: Update the order metadata to set payment_status to paid
    let order = null;
    let orderId = null;

    // Try to get order ID from payment session data
    if (paymentSession.data && paymentSession.data.order_id) {
      orderId = paymentSession.data.order_id;
    }

    if (orderId) {
      try {

        // Get the order
        order = await orderService.retrieveOrder(orderId);

        // Update the order metadata
        if (order) {
          // Prepare metadata update
          const metadata = order.metadata || {};
          metadata.payment_status = "paid";

         
            order = await (orderService as any).updateOrders(orderId, {
              status: "completed",
              metadata: metadata
            });


        }
      } catch (orderError) {
        console.error("Error updating order metadata:", orderError);
        // Continue with the response even if order update fails
      }
    } else {
      console.log("No order ID found in payment data, skipping order update");
    }

    // Return the successful response
    return res.json({
      success: true,
      payment: {
        id: payment.id,
        status: capture.status || "captured",
        amount: capture.amount || payment.amount,
        currency_code: capture.currency_code || payment.currency_code,
        captured_at: new Date().toISOString(),
      },
      order: order ? {
        id: order.id,
        status: order.status || "completed",
        payment_status: order.metadata?.payment_status || "paid"
      } : null,
    });
  } catch (error) {
    console.error("Error capturing payment:", error);
    return res.status(400).json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to capture payment",
    });
  }
}
