import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

/**
 * Simple test endpoint to check if the API is working
 * 
 * @param req - The request object
 * @param res - The response object
 * @returns A simple response
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    return res.json({
      success: true,
      message: "Hotel Management API is working",
      timestamp: new Date().toISOString(),
      query: req.query,
      headers: req.headers
    });
  } catch (error) {
    console.error("Error in test endpoint:", error);
    return res.status(500).json({
      success: false,
      message: "Error in test endpoint",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
