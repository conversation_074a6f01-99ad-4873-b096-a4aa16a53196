import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ROOM_INVENTORY_SERVICE } from "../../../../../modules/hotel-management";
import { z } from "zod";

// Validation schema for room availability check
export const StoreRoomAvailabilitySchema = z.object({
  room_id: z.string(),
  check_in: z.string(),
  check_out: z.string(),
});

export type StoreRoomAvailabilityType = z.infer<typeof StoreRoomAvailabilitySchema>;

/**
 * Public endpoint to check availability for a specific room
 * 
 * @param req - The request object
 * @param res - The response object
 * @returns Availability status for the room
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_SERVICE);
    
    // Validate query parameters
    const { room_id, check_in, check_out } = req.query;
    
    if (!room_id || !check_in || !check_out) {
      return res.status(400).json({
        message: "room_id, check_in, and check_out dates are required",
      });
    }
    
    // Parse dates
    const checkInDate = new Date(check_in as string);
    const checkOutDate = new Date(check_out as string);
    
    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }
    
    // Check availability
    const availability = await roomInventoryService.checkAvailability(
      room_id as string,
      checkInDate,
      checkOutDate,
      true // Include details
    );
    
    return res.json({
      room_id,
      check_in,
      check_out,
      ...availability
    });
    
  } catch (error) {
    console.error("Error checking room availability:", error);
    return res.status(500).json({
      message: "Error checking room availability",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
