import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../../../modules/hotel-management/room-inventory";
import { z } from "zod";

// Validation schema for room search
export const StoreSearchRoomsSchema = z.object({
  hotel_id: z.string().optional(),
  destination_id: z.string().optional(),
  check_in: z.string(),
  check_out: z.string(),
  adults: z.number().default(1),
  children: z.number().default(0),
  infants: z.number().default(0),
  rooms: z.number().default(1),
});

export type StoreSearchRoomsType = z.infer<typeof StoreSearchRoomsSchema>;

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve("query");
    const roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);

    // Validate query parameters
    const { check_in, check_out, hotel_id, destination_id, adults, children, infants, rooms } = req.query;

    if (!check_in || !check_out) {
      return res.status(400).json({
        message: "check_in and check_out dates are required",
      });
    }

    // Parse dates
    const checkInDate = new Date(check_in as string);
    const checkOutDate = new Date(check_out as string);

    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }

    if (checkInDate >= checkOutDate) {
      return res.status(400).json({
        message: "check_in date must be before check_out date",
      });
    }

    // Build filters for hotel search
    const hotelFilters: Record<string, any> = {};

    if (hotel_id) {
      hotelFilters.id = [hotel_id];
    }

    if (destination_id) {
      hotelFilters.destination_id = [destination_id];
    }

    if (!hotel_id && !destination_id) {
      return res.status(400).json({
        message: "Either hotel_id or destination_id is required",
      });
    }

    // Get hotels
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: hotelFilters,
      fields: ["id", "name", "category_id", "images.*"],
    });

    if (!hotels || hotels.length === 0) {
      return res.json({
        available_rooms: [],
        hotels: [],
      });
    }

    // Get all room configurations (products) for these hotels
    const hotelCategoryIds = hotels.map(hotel => hotel.category_id);

    const { data: products } = await query.graph({
      entity: "product",
      filters: {
        categories: hotelCategoryIds,
      },
      fields: ["id", "title", "description", "metadata", "thumbnail", "categories.*"],
    });

    // Get all room variants
    const productIds = products.map(product => product.id);

    const { data: variants } = await query.graph({
      entity: "product_variant",
      filters: {
        product_id: productIds,
      },
      fields: ["id", "title", "product_id", "metadata", "prices.*"],
    });

    // Check availability for each variant
    const availableRooms = [];

    for (const variant of variants) {
      try {
        // Get the product (room config) for this variant
        const product = products.find(p => p.id === variant.product_id);

        if (!product) continue;

        // Get the hotel for this product
        const hotel = hotels.find(h => h.category_id === product.categories[0]?.id);

        if (!hotel) continue;

        // Check if this room meets the occupancy requirements
        const maxAdults = product.metadata?.max_adults || 1;
        const maxChildren = product.metadata?.max_children || 0;
        const maxInfants = product.metadata?.max_infants || 0;

        if (
          maxAdults < (adults as unknown as number || 1) ||
          maxChildren < (children as unknown as number || 0) ||
          maxInfants < (infants as unknown as number || 0)
        ) {
          continue;
        }

        // Check room inventory availability
        const roomInventories = await roomInventoryService.listRoomInventories({
          inventory_item_id: variant.id,
          from_date: checkInDate,
          to_date: checkOutDate,
          status: "available",
        });

        // If we have inventory records with available_quantity > 0 for all dates, the room is available
        const isAvailable = roomInventories && roomInventories.length > 0 &&
          roomInventories.every(inventory => inventory.available_quantity > 0);

        if (isAvailable) {
          // Get the price for this room
          const price = variant.prices && variant.prices.length > 0
            ? variant.prices[0]
            : null;

          availableRooms.push({
            id: variant.id,
            name: variant.title,
            room_number: variant.metadata?.room_number,
            room_config: {
              id: product.id,
              name: product.title,
              description: product.description,
              room_size: product.metadata?.room_size,
              bed_type: product.metadata?.bed_type,
              max_adults: maxAdults,
              max_children: maxChildren,
              max_infants: maxInfants,
              amenities: product.metadata?.amenities,
            },
            hotel: {
              id: hotel.id,
              name: hotel.name,
              thumbnail: hotel.images?.find(img => img.metadata?.isThumbnail)?.url || null,
            },
            price: price ? {
              amount: price.amount,
              currency_code: price.currency_code,
            } : null,
          });
        }
      } catch (error) {
        // Skip rooms with no inventory
        continue;
      }
    }

    res.json({
      available_rooms: availableRooms,
      hotels: hotels.map(hotel => ({
        id: hotel.id,
        name: hotel.name,
        thumbnail: hotel.images?.find(img => img.metadata?.isThumbnail)?.url || null,
      })),
    });
  } catch (error) {
    console.error("Error searching rooms:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to search rooms",
    });
  }
};
