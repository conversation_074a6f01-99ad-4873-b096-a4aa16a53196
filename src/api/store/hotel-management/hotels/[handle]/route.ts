import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const { data: hotel } = await query.graph({
    entity: "hotel",
    filters: {
      handle: req.params.handle,
    },
    fields: [
      "*",
      "images.*",
      ...(req.validatedQuery?.fields.split(",") || []),
    ],
  });
  const { data: rooms } = await query.graph({
    entity: "product",
    filters: {
      categories: hotel?.map((hotel) => hotel?.category_id),
    },
    fields: ["*"],
  });
  res.json({ hotel, rooms });
};
