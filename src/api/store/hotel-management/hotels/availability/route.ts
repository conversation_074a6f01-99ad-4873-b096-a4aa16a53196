import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { parseISO, eachDayOfInterval, format } from "date-fns";

// Validation schema for cross-hotel availability search
export const StoreHotelsAvailabilitySchema = z.object({
  check_in: z.string(),
  check_out: z.string(),
  adults: z.coerce.number().default(1),
  children: z.coerce.number().default(0),
  infants: z.coerce.number().default(0),
  destination_id: z.string().optional(),
  currency_code: z.string().default("USD"),
  is_pets_allowed: z.string().optional(),
});

export type StoreHotelsAvailabilityType = z.infer<
  typeof StoreHotelsAvailabilitySchema
>;

/**
 * Endpoint to check availability across multiple hotels
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A list of hotels with available rooms
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("[Availability] Request started with query params:", req.query);
    const query = req.scope.resolve("query");
    // Resolve pricing module service for price calculations
    const pricingModuleService = req.scope.resolve(Modules.PRICING);
    console.log("[Availability] Services resolved successfully");

    // Validate and extract query parameters
    const {
      check_in,
      check_out,
      adults = 1,
      children = 0,
      infants = 0,
      destination_id,
      currency_code = "USD",
      is_pets_allowed,
    } = req.query;

    if (!check_in || !check_out) {
      return res.status(400).json({
        message: "check_in and check_out dates are required",
      });
    }

    // Parse dates
    const checkInDate = parseISO(check_in as string);
    const checkOutDate = parseISO(check_out as string);

    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }

    if (checkInDate >= checkOutDate) {
      return res.status(400).json({
        message: "check_in date must be before check_out date",
      });
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Log the search parameters for debugging
    console.log(
      `[Availability] Search parameters: check_in=${format(
        checkInDate,
        "yyyy-MM-dd"
      )}, check_out=${format(
        checkOutDate,
        "yyyy-MM-dd"
      )}, nights=${nights}, adults=${adults}, children=${children}, infants=${infants}, currency=${currency_code}`
    );

    // Build filters for hotel search
    const hotelFilters: Record<string, any> = {
      is_active: true,
    };

    if (destination_id) {
      hotelFilters.destination_id = destination_id;
    }

    if (is_pets_allowed !== undefined) {
      hotelFilters.is_pets_allowed = is_pets_allowed === "true";
    }

    // Get hotels
    console.log("[Availability] Fetching hotels with filters:", hotelFilters);
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: hotelFilters,
      fields: [
        "id",
        "name",
        "category_id",
        "handle",
        "description",
        "images.*",
        "metadata",
        "is_pets_allowed",
        "rating",
        "amenities",
        "destination_id",
      ],
    });
    console.log(
      "[Availability] Hotels found:",
      hotels ? hotels.length : 0,
      "hotels"
    );
    console.log("[Availability] Hotel IDs:", hotels?.map((h) => h.id) || []);

    if (!hotels || hotels.length === 0) {
      return res.json({
        hotels: [],
        message: "No hotels found matching the criteria",
      });
    }

    // Get destination information for hotels that have destination_id
    const destinationMap = new Map();
    const uniqueDestinationIds = [
      ...new Set(
        hotels.filter((h) => h.destination_id).map((h) => h.destination_id)
      ),
    ];

    if (uniqueDestinationIds.length > 0) {
      console.log(
        "[Availability] Fetching destination information for:",
        uniqueDestinationIds
      );
      try {
        const { data: destinations } = await query.graph({
          entity: "destination",
          filters: {
            id: uniqueDestinationIds,
          },
          fields: ["id", "name"],
        });

        if (destinations && destinations.length > 0) {
          destinations.forEach((dest) => {
            destinationMap.set(dest.id, dest.name);
          });
          console.log(
            "[Availability] Destination map created:",
            Object.fromEntries(destinationMap)
          );
        }
      } catch (error) {
        console.error("[Availability] Error fetching destinations:", error);
      }
    }

    // Process each hotel to find available rooms
    const hotelsWithAvailability = [];

    for (const hotel of hotels) {
      try {
        // Get room configurations for this hotel
        console.log(
          `[Availability] Fetching room configs for hotel ${hotel.id} (${hotel.name})`
        );
        const { data: roomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              hotel_id: hotel.id,
            },
          },
          fields: [
            "id",
            "title",
            "description",
            "thumbnail",
            "metadata",
            "categories.*",
          ],
        });
        console.log(
          `[Availability] Found ${
            roomConfigs?.length || 0
          } room configs for hotel ${hotel.id}`
        );

        if (!roomConfigs || roomConfigs.length === 0) {
          continue; // Skip hotels with no room configurations
        }

        // Get all room variants for these configurations
        const roomConfigIds = roomConfigs.map((config) => config.id);
        console.log(
          `[Availability] Fetching variants for ${roomConfigIds.length} room configs:`,
          roomConfigIds
        );

        const { data: variants } = await query.graph({
          entity: "product_variant",
          filters: {
            product_id: roomConfigIds,
          },
          fields: ["id", "title", "product_id", "metadata", "options"],
        });
        console.log(
          `[Availability] Found ${variants?.length || 0} room variants`
        );

        if (!variants || variants.length === 0) {
          continue; // Skip hotels with no room variants
        }

        // Group variants by their product (room configuration)
        const variantsByConfig = {};
        for (const variant of variants) {
          if (!variantsByConfig[variant.product_id]) {
            variantsByConfig[variant.product_id] = [];
          }
          variantsByConfig[variant.product_id].push(variant);
        }

        // Check availability for each room configuration
        const availableRoomConfigs = [];

        for (const config of roomConfigs) {
          const configVariants = variantsByConfig[config.id] || [];

          // Skip configurations that don't meet occupancy requirements
          const maxAdults = config.metadata?.max_adults || 1;
          const maxChildren = config.metadata?.max_children || 0;
          const maxInfants = config.metadata?.max_infants || 0;

          if (
            (maxAdults as number) < (adults as unknown as number) ||
            (maxChildren as number) < (children as unknown as number) ||
            (maxInfants as number) < (infants as unknown as number)
          ) {
            continue;
          }

          // Check availability for each variant
          const availableVariants = [];

          // Check availability for each variant using the room_inventory table
          for (const variant of configVariants) {
            try {
              // Query the room_inventory table for this variant
              console.log(
                `[Availability] Checking inventory for variant ${variant.id} (${variant.title})`
              );
              const { data: roomInventory } = await query.graph({
                entity: "room_inventory",
                filters: {
                  // Use inventory_item_id for the variant ID based on the database schema
                  inventory_item_id: variant.id,
                },
                fields: [
                  "id",
                  "inventory_item_id",
                  "from_date",
                  "to_date",
                  "status",
                  "available_quantity",
                ],
              });
              console.log(
                `[Availability] Found ${
                  roomInventory?.length || 0
                } inventory records for variant ${variant.id}`
              );

              // Generate all dates in the range
              const dateRange = eachDayOfInterval({
                start: checkInDate,
                end: new Date(checkOutDate.getTime() - 1), // Exclude checkout day
              });
              console.log(
                `[Availability] Checking availability for ${
                  dateRange.length
                } days: ${format(dateRange[0], "yyyy-MM-dd")} to ${format(
                  dateRange[dateRange.length - 1],
                  "yyyy-MM-dd"
                )}`
              );

              // Check if all dates in the range are covered by available inventory entries
              let hasAvailableEntry = false;
              let hasConflict = false;

              console.log(
                `[Availability] Starting day-by-day availability check for ${dateRange.length} days`
              );
              for (const day of dateRange) {
                let dayIsCovered = false;
                const dayDateOnly = new Date(
                  day.getFullYear(),
                  day.getMonth(),
                  day.getDate()
                );
                const formattedDay = format(dayDateOnly, "yyyy-MM-dd");
                console.log(`[Availability] Checking day: ${formattedDay}`);

                console.log(
                  `[Availability] Checking ${roomInventory.length} inventory entries for day ${formattedDay}`
                );
                for (const entry of roomInventory) {
                  const entryFromDate = new Date(entry.from_date);
                  const entryToDate = new Date(entry.to_date);

                  const entryFromDateOnly = new Date(
                    entryFromDate.getFullYear(),
                    entryFromDate.getMonth(),
                    entryFromDate.getDate()
                  );

                  const entryToDateOnly = new Date(
                    entryToDate.getFullYear(),
                    entryToDate.getMonth(),
                    entryToDate.getDate()
                  );

                  // Check if this entry covers this day and is available
                  const entryFromFormatted = format(
                    entryFromDateOnly,
                    "yyyy-MM-dd"
                  );
                  const entryToFormatted = format(
                    entryToDateOnly,
                    "yyyy-MM-dd"
                  );

                  const dateInRange =
                    entryFromDateOnly <= dayDateOnly &&
                    entryToDateOnly > dayDateOnly;
                  console.log(
                    `[Availability] Entry ${
                      entry.id
                    }: ${entryFromFormatted} to ${entryToFormatted}, status: ${
                      entry.status
                    }, covers day ${formattedDay}: ${
                      dateInRange ? "YES" : "NO"
                    }`
                  );

                  if (dateInRange) {
                    // Check if the entry status makes the room available
                    const isAvailable =
                      entry.status === "available" &&
                      (entry.available_quantity === undefined ||
                        entry.available_quantity > 0);

                    console.log(
                      `[Availability] Entry ${entry.id} availability: ${
                        isAvailable ? "AVAILABLE" : "NOT AVAILABLE"
                      } (status: ${entry.status}, quantity: ${
                        entry.available_quantity
                      })`
                    );

                    if (isAvailable) {
                      dayIsCovered = true;
                      console.log(
                        `[Availability] Day ${formattedDay} is covered by an available entry`
                      );
                      break;
                    } else {
                      // This day has a conflicting entry
                      hasConflict = true;
                      console.log(
                        `[Availability] Day ${formattedDay} has a conflicting entry (not available)`
                      );
                      break;
                    }
                  }
                }

                if (!dayIsCovered) {
                  hasAvailableEntry = false;
                  console.log(
                    `[Availability] Day ${formattedDay} is not covered by any available entry, marking room as unavailable`
                  );
                  break;
                } else if (hasConflict) {
                  console.log(
                    `[Availability] Conflict found for day ${formattedDay}, stopping check`
                  );
                  break;
                } else {
                  hasAvailableEntry = true;
                  console.log(
                    `[Availability] Day ${formattedDay} is available`
                  );
                }
              }

              // Room is available only if all days are covered by available entries and there are no conflicts
              const isAvailable = hasAvailableEntry && !hasConflict;
              console.log(
                `[Availability] Variant ${variant.id} availability result: ${
                  isAvailable ? "AVAILABLE" : "NOT AVAILABLE"
                } (hasAvailableEntry: ${hasAvailableEntry}, hasConflict: ${hasConflict})`
              );

              if (isAvailable) {
                availableVariants.push(variant);
              }
            } catch (error) {
              // Skip variants with errors
              continue;
            }
          }

          // If there are available variants for this configuration, add it to the list
          if (availableVariants.length > 0) {
            // Get the price for this room configuration
            try {
              // Get the price set for this product
              const priceSetId = config.metadata?.price_set_id;
              console.log(
                `[Availability] Getting price for room config ${
                  config.id
                }, price_set_id: ${priceSetId || "NOT SET"}`
              );

              // Default price to use when no price set is available
              let calculatedPrice = {
                amount: 10000, // Example price: $100.00
                currency_code: currency_code as string,
              };

              // Try to get the actual price if a price set is available
              if (priceSetId) {
                // Get the price set
                const { data: priceSet } = await query.graph({
                  entity: "price_set",
                  filters: {
                    id: priceSetId,
                  },
                  fields: ["id", "rules.*", "prices.*"],
                });
                console.log(
                  `[Availability] Price set found: ${
                    priceSet && priceSet.length > 0 ? "YES" : "NO"
                  }`
                );

                if (priceSet && priceSet.length > 0) {
                  // Calculate the price for this room
                  const priceContext = {
                    adults: adults as unknown as number,
                    children: children as unknown as number,
                    infants: infants as unknown as number,
                    nights: nights,
                    check_in: format(checkInDate, "yyyy-MM-dd"),
                    check_out: format(checkOutDate, "yyyy-MM-dd"),
                  };
                  console.log(
                    `[Availability] Calculating price with context:`,
                    priceContext
                  );

                  // Prepare pricing parameters
                  console.log(
                    `[Availability] Preparing to calculate price for variant ${availableVariants[0].id}`
                  );

                  // Log the pricing service structure to debug
                  console.log(
                    `[Availability] Pricing service methods:`,
                    Object.keys(pricingModuleService)
                  );

                  // In a real implementation, we would calculate the actual price here
                  // For now, we'll use the default price defined above
                  console.log(
                    `[Availability] Using price for debugging:`,
                    calculatedPrice
                  );
                }
              } else {
                console.log(
                  `[Availability] No price set found, using default price:`,
                  calculatedPrice
                );
              }

              // Always add the room configuration to the list of available rooms, even if no price set is found
              console.log(
                `[Availability] Adding room config ${config.id} to available rooms list`
              );
              availableRoomConfigs.push({
                id: config.id,
                title: config.title,
                description: config.description,
                thumbnail: config.thumbnail,
                metadata: config.metadata,
                available_rooms: availableVariants.length,
                price: calculatedPrice,
              });
            } catch (error) {
              // If price calculation fails, still include the room but with a default price
              console.error(
                `[Availability] Error calculating price for room config ${config.id}:`,
                error
              );

              // Use a default price for error cases
              const defaultPrice = {
                amount: 10000, // Example price: $100.00
                currency_code: currency_code as string,
              };

              console.log(
                `[Availability] Using default price due to error:`,
                defaultPrice
              );
              console.log(
                `[Availability] Adding room config ${config.id} to available rooms list (error recovery)`
              );

              availableRoomConfigs.push({
                id: config.id,
                title: config.title,
                description: config.description,
                thumbnail: config.thumbnail,
                metadata: config.metadata,
                available_rooms: availableVariants.length,
                price: defaultPrice, // Use default price instead of null
              });
            }
          }
        }

        // If there are available room configurations for this hotel, add it to the list
        console.log(
          `[Availability] Hotel ${hotel.id} has ${availableRoomConfigs.length} available room configurations`
        );
        if (availableRoomConfigs.length > 0) {
          hotelsWithAvailability.push({
            id: hotel.id,
            name: hotel.name,
            handle: hotel.handle,
            description: hotel.description,
            thumbnail:
              hotel.images?.find((img) => img.metadata?.isThumbnail)?.url ||
              null,
            images: hotel.images,
            is_pets_allowed: hotel.is_pets_allowed,
            rating: hotel.rating,
            amenities: hotel.amenities,
            destination_name: hotel.destination_id
              ? destinationMap.get(hotel.destination_id)
              : null,
            // Use any available metadata or empty object
            metadata: (hotel as any).metadata || {},
            available_rooms: availableRoomConfigs,
          });
        }
      } catch (error) {
        // Skip hotels with errors
        console.error(
          `[Availability] Error processing hotel ${hotel.id}:`,
          error
        );
        continue;
      }
    }

    // Return the results
    console.log(
      `[Availability] Returning ${hotelsWithAvailability.length} hotels with availability`
    );
    return res.json({
      check_in: check_in,
      check_out: check_out,
      nights: nights,
      adults: adults,
      children: children,
      infants: infants,
      currency_code: currency_code,
      hotels: hotelsWithAvailability,
      count: hotelsWithAvailability.length,
    });
  } catch (error) {
    console.error(
      "[Availability] Error checking availability across hotels:",
      error
    );
    return res.status(500).json({
      message: "Error checking availability across hotels",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
