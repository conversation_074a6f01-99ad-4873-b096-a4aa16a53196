import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import {
  ADD_ON_SERVICE,
  AddOnServiceLevel,
} from "../../../../../../modules/hotel-management/add-on-service";
import { registerAddOnServiceModule } from "../../../../../admin/hotel-management/add-on-services/register-module";

// Validation schema for add-ons query
export const StoreHotelAddOnsSchema = z.object({
  currency_code: z.string().default("USD"),
  is_active: z.boolean().optional().default(true),
  limit: z.coerce.number().optional().default(50),
  offset: z.coerce.number().optional().default(0),
});

export type StoreHotelAddOnsType = z.infer<typeof StoreHotelAddOnsSchema>;

/**
 * GET /store/hotel-management/hotels/[id]/add-ons
 *
 * Fetch available add-on services for a specific hotel
 * This includes both hotel-level and destination-level add-ons
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Available add-on services for the hotel
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");

    // Validate query parameters
    const validationResult = StoreHotelAddOnsSchema.safeParse(req.query);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: validationResult.error.errors,
      });
    }

    const { currency_code, is_active, limit, offset } = validationResult.data;

    // Register the add-on service module
    registerAddOnServiceModule(req.scope);
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // First, get the hotel to find its destination_id
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: { id: hotelId },
      fields: ["id", "name", "destination_id"],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(404).json({
        message: "Hotel not found",
      });
    }

    const hotel = hotels[0];
    const destinationId = hotel.destination_id;

    // Get hotel-level add-ons
    console.log("Searching for hotel add-ons with:", {
      service_level: AddOnServiceLevel.HOTEL,
      hotel_id: hotelId,
      is_active,
    });

    const [hotelAddOns] = await addOnServiceService.listAddOnServices(
      {
        service_level: AddOnServiceLevel.HOTEL,
        hotel_id: hotelId,
        is_active,
      },
      { skip: 0, take: limit }
    );

    console.log("Found hotel add-ons:", hotelAddOns.length);

    // Get destination-level add-ons (if hotel has a destination)
    let destinationAddOns = [];
    if (destinationId) {
      const [destAddOns] = await addOnServiceService.listAddOnServices(
        {
          service_level: AddOnServiceLevel.DESTINATION,
          destination_id: destinationId,
          is_active,
        },
        { skip: 0, take: limit }
      );
      destinationAddOns = destAddOns;
    }

    // Combine and format add-ons for customer response
    const allAddOns = [...hotelAddOns, ...destinationAddOns];

    // Filter out inactive add-ons and format for customer
    const formattedAddOns = allAddOns
      .filter((addOn) => addOn.is_active !== false)
      .map((addOn) => ({
        id: addOn.id,
        name: addOn.name,
        description: addOn.description || "",
        service_type: addOn.type || "general",
        service_level: addOn.service_level,
        // Pricing information
        pricing_type: addOn.pricing_type || "per_person",
        adult_price: addOn.adult_price ? addOn.adult_price/100 : 0,
        child_price: addOn.child_price ? addOn.child_price/100 : 0,
        package_price: addOn.package_price ? addOn.package_price/100 : null,
        currency_code: addOn.currency_code || currency_code,
        max_capacity: addOn.max_capacity,
        images: addOn.metadata?.images || [],
        hotel_id: addOn.service_level === "hotel" ? addOn.hotel_id : null,
        hotel_name: addOn.service_level === "hotel" ? addOn.hotel_name : null,
        destination_id:
          addOn.service_level === "destination" ? addOn.destination_id : null,
        destination_name:
          addOn.service_level === "destination" ? addOn.destination_name : null,
        // Variant IDs for frontend reference
        adult_variant_id: addOn.adult_variant_id,
        child_variant_id: addOn.child_variant_id,
        package_variant_id: addOn.package_variant_id,
      }))
      .slice(offset, offset + limit);

    return res.json({
      add_ons: formattedAddOns,
      count: formattedAddOns.length,
      hotel: {
        id: hotel.id,
        name: hotel.name,
        destination_id: destinationId,
      },
    });
  } catch (error) {
    console.error("Error fetching hotel add-ons:", error);
    return res.status(500).json({
      message: "Failed to fetch add-on services",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
