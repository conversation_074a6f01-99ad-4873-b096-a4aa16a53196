import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import { CANCELLATION_POLICY_SERVICE } from "src/modules/hotel-management/cancellation-policy";
import HotelModuleService from "src/modules/hotel-management/hotel/service";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";

/**
 * Public endpoint to get comprehensive hotel details for storefront
 * Includes hotel info, images, room configurations, and cancellation policies
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Comprehensive hotel details
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
    const cancellationPolicyService: CancellationPolicyService =
      req.scope.resolve(CANCELLATION_POLICY_SERVICE);

    // Get hotel details from hotel entity
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: {
        id: hotelId,
      },
      fields: [
        "id",
        "name",
        "handle",
        "description",
        "is_active",
        "website",
        "email",
        "destination_id",
        "rating",
        "total_reviews",
        "notes",
        "location",
        "address",
        "phone_number",
        "timezone",
        "available_languages",
        "tax_type",
        "tax_number",
        "tags",
        "amenities",
        "rules",
        "safety_measures",
        "currency",
        "check_in_time",
        "check_out_time",
        "is_featured",
        "category_id",
        "created_at",
        "updated_at",
      ],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(404).json({ message: "Hotel not found" });
    }

    const hotel = hotels[0];

    // Get hotel images
    const hotelImages = await hotelService.getHotelImages(hotelId);

    // Get room configurations for this hotel
    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters: {
        metadata: {
          hotel_id: hotelId,
        },
      },
      fields: [
        "id",
        "title",
        "subtitle",
        "description",
        "handle",
        "thumbnail",
        "status",
        "metadata",
        "images.id",
        "images.url",
      ],
    });

    // Get cancellation policies for this hotel
    const cancellationPolicies =
      await cancellationPolicyService.findCancellationPolicies(
        { hotel_id: hotelId, is_active: true },
        { order: { days_before_checkin: "DESC" } }
      );

    // Format room configurations for easier consumption
    const formattedRoomConfigs = roomConfigs?.map((roomConfig) => {
      // Extract hotel-specific room configuration data from metadata
      const roomConfigMetadata = roomConfig.metadata || {};

      // Get amenities with descriptions
      const amenities = roomConfigMetadata.amenities || [];
      const formattedAmenities = amenities.map((amenity) => ({
        name: amenity,
        description: getAmenityDescription(amenity),
      }));

      return {
        id: roomConfig.id,
        title: roomConfig.title,
        subtitle: roomConfig.subtitle,
        description: roomConfig.description,
        handle: roomConfig.handle,
        thumbnail: roomConfig.thumbnail,
        status: roomConfig.status,
        room_details: {
          room_size: roomConfigMetadata.room_size,
          bed_type: roomConfigMetadata.bed_type,
          max_adults: roomConfigMetadata.max_adults || 1,
          max_children: roomConfigMetadata.max_children || 0,
          max_infants: roomConfigMetadata.max_infants || 0,
          max_occupancy: roomConfigMetadata.max_occupancy || 1,
          max_extra_beds: roomConfigMetadata.max_extra_beds || 0,
        },
        amenities: formattedAmenities,
        images: roomConfig.images,
      };
    });

    /**
     * Helper function to get amenity descriptions
     */
    function getAmenityDescription(amenity: string): string {
      const amenityDescriptions: Record<string, string> = {
        Wifi: "High-speed wireless internet access throughout the room",
        TV: "Flat-screen TV with premium cable channels",
        "Air Conditioning": "Individual climate control system",
        "Mini Bar": "Fully stocked mini bar with premium beverages and snacks",
        Safe: "In-room electronic safe large enough for a laptop",
        Hairdryer: "Professional hairdryer in the bathroom",
        Bathtub: "Luxurious deep soaking bathtub",
        Shower: "Walk-in rainfall shower",
        Toiletries: "Premium bath amenities",
        Bathrobe: "Plush bathrobes and slippers",
        Desk: "Spacious work desk with ergonomic chair",
        "Coffee Machine": "Premium coffee and tea making facilities",
        Iron: "Iron and ironing board",
        Phone: "Direct dial telephone",
        Balcony: "Private balcony with seating area",
        View: "Scenic views from the room",
        "Room Service": "24-hour room service available",
        Gym: "Access to fitness center",
        Pool: "Access to swimming pool",
        Spa: "Access to spa facilities",
      };

      return amenityDescriptions[amenity] || `${amenity} available in room`;
    }

    /**
     * Helper function to get rule descriptions
     */
    function getRuleDescription(rule: string): string {
      const ruleDescriptions: Record<string, string> = {
        "No Smoking":
          "Smoking is strictly prohibited in all areas of the hotel",
        "Check-in after 3 PM": "Check-in is available from 3:00 PM onwards",
        "Check-out before 11 AM": "Check-out must be completed by 11:00 AM",
        "No Pets": "Pets are not allowed on the property",
        "Pets Allowed": "Well-behaved pets are welcome with prior arrangement",
        "Quiet Hours": "Quiet hours are enforced from 10 PM to 7 AM",
        "No Parties": "Parties and events are not permitted in guest rooms",
        "ID Required": "Valid government-issued ID required at check-in",
        "Credit Card Required":
          "Credit card required for incidentals and security deposit",
        "Age Restriction": "Guests must be 18 years or older to check in",
        "No Outside Food": "Outside food and beverages are not permitted",
        "Pool Hours": "Swimming pool has designated operating hours",
        "Dress Code": "Appropriate dress code required in public areas",
        "No Weapons": "Weapons of any kind are strictly prohibited",
        "Visitor Policy": "All visitors must register at the front desk",
        "Damage Policy":
          "Guests are responsible for any damage to hotel property",
        "Lost Key Fee": "Replacement fee applies for lost room keys",
        "Early Check-in Fee": "Additional charges may apply for early check-in",
        "Late Check-out Fee": "Additional charges may apply for late check-out",
        "Cancellation Policy":
          "Cancellation must be made according to hotel policy",
      };

      return (
        ruleDescriptions[rule] || `${rule} - Please follow this hotel policy`
      );
    }

    /**
     * Helper function to get safety measure descriptions
     */
    function getSafetyMeasureDescription(measure: string): string {
      const safetyMeasureDescriptions: Record<string, string> = {
        "24/7 Security": "Round-the-clock security personnel on premises",
        CCTV: "Closed-circuit television surveillance in public areas",
        "Fire Extinguishers":
          "Fire extinguishers placed throughout the property",
        "Smoke Detectors":
          "Smoke detection systems in all rooms and common areas",
        "First Aid Kit": "First aid supplies available for emergencies",
        "Emergency Exits":
          "Clearly marked emergency exits and evacuation routes",
        "Card Key Access": "Secure card key access to rooms and facilities",
        "Safe Deposit Box": "Safe deposit boxes available for valuables",
        Sanitization: "Regular sanitization of high-touch surfaces",
        "Health Screening": "Health screening protocols for guests and staff",
        "Social Distancing": "Measures in place to maintain social distancing",
        "Contactless Check-in": "Option for contactless check-in and check-out",
        "Medical Assistance": "Access to medical assistance when needed",
        AED: "Automated External Defibrillator available on premises",
        Lifeguard: "Lifeguard on duty at swimming areas",
        "Child Safety": "Child safety features and protocols",
      };

      return (
        safetyMeasureDescriptions[measure] ||
        `${measure} implemented for guest safety`
      );
    }

    // Format cancellation policies for easier consumption
    const formattedCancellationPolicies = cancellationPolicies.map(
      (policy: any) => ({
        id: policy.id,
        name: policy.name,
        description: policy.description,
        days_before_checkin: policy.days_before_checkin,
        refund_type: policy.refund_type,
        refund_amount: policy.refund_amount,
      })
    );

    // Format rules with descriptions
    const rules = hotel.rules || [];
    const formattedRules = Array.isArray(rules)
      ? rules.map((rule) => ({
          name: rule,
          description: getRuleDescription(rule),
        }))
      : [];

    // Format safety measures with descriptions
    const safetyMeasures = hotel.safety_measures || [];
    const formattedSafetyMeasures = Array.isArray(safetyMeasures)
      ? safetyMeasures.map((measure) => ({
          name: measure,
          description: getSafetyMeasureDescription(measure),
        }))
      : [];

    // Return comprehensive hotel details
    return res.json({
      hotel: {
        ...hotel,
        images: hotelImages,
        formatted_rules: formattedRules,
        formatted_safety_measures: formattedSafetyMeasures,
        cancellation_policies: formattedCancellationPolicies,
      },
      room_configurations: formattedRoomConfigs || [],
    });
  } catch (error) {
    console.error("Error fetching hotel details:", error);
    return res.status(500).json({
      message: "Error fetching hotel details",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
