import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../../../../modules/hotel-management/room-inventory";

/**
 * Public endpoint to get the best available price for a hotel
 *
 * @param req - The request object
 * @param res - The response object
 * @returns The best available price for the hotel
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const pricingModuleService = req.scope.resolve(Modules.PRICING);

    // Try to resolve the room inventory service, but handle the case where it might not be available
    let roomInventoryService = null;
    try {
      roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);
    } catch (error) {
      console.log("Room inventory service not available:", error.message);
    }

    console.log(`Searching for hotel with ID: ${hotelId}`);

    let hotel;
    try {
      // First, try to find the hotel directly using the hotel ID
      const hotelResult = await query.graph({
        entity: "hotel",
        filters: {
          id: hotelId,
        },
        fields: ["id", "name", "handle", "category_id", "description", "rating", "address", "amenities"],
      });

      if (hotelResult.data && hotelResult.data.length > 0) {
        hotel = hotelResult.data;
        console.log(`Hotel found by ID:`, hotel);
      } else {
        // Try searching by handle
        console.log(`Hotel not found by ID, trying to search by handle: ${hotelId}`);
        const handleResult = await query.graph({
          entity: "hotel",
          filters: {
            handle: hotelId,
          },
          fields: ["id", "name", "handle", "category_id", "description", "rating", "address", "amenities"],
        });

        if (handleResult.data && handleResult.data.length > 0) {
          hotel = handleResult.data;
          console.log(`Hotel found by handle:`, hotel);
        } else {
          // As a last resort, try searching in product_category
          console.log(`Hotel not found in hotel table, trying product_category: ${hotelId}`);
          const categoryResult = await query.graph({
            entity: "product_category",
            filters: {
              id: hotelId,
            },
            fields: ["id", "name", "handle", "metadata"],
          });

          hotel = categoryResult.data;
          console.log(`Product category search result:`, hotel);
        }
      }
    } catch (error) {
      console.error(`Error searching for hotel:`, error);
    }

    if (!hotel || hotel.length === 0) {
      // List all available hotels to help with debugging
      try {
        const allHotels = await query.graph({
          entity: "hotel",
          filters: {},
          fields: ["id", "name", "handle", "category_id"],
          take: 10
        });

        console.log(`Available hotels:`, allHotels.data);

        return res.status(404).json({
          message: "Hotel not found",
          hotel_id: hotelId,
          available_hotels: allHotels.data
        });
      } catch (error) {
        console.error(`Error listing hotels:`, error);
        return res.status(404).json({ message: "Hotel not found and could not list available hotels" });
      }
    }

    // Get query parameters with defaults
    const {
      check_in = new Date().toISOString().split('T')[0],
      check_out = new Date(Date.now() + 86400000).toISOString().split('T')[0],
      currency_code = "USD"
    } = req.query;

    // Parse dates
    const checkInDate = new Date(check_in as string);
    const checkOutDate = new Date(check_out as string);

    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }

    if (checkInDate >= checkOutDate) {
      return res.status(400).json({
        message: "check_in date must be before check_out date",
      });
    }

    // Calculate number of nights
    const nights = Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));

    // We already have the hotel details from the earlier query

    // Get the hotel ID from the hotel record
    const hotelRecordId = hotel[0].id;
    console.log(`Using hotel ID for room configurations: ${hotelRecordId}`);

    // Try to get room configurations using different methods
    let roomConfigs = [];

    // Method 1: Try using category_id
    try {
      const categoryId = hotel[0].category_id;
      console.log(`Trying category_id: ${categoryId}`);

      const { data: categoryRoomConfigs } = await query.graph({
        entity: "product",
        filters: {
          categories: { id: [categoryId] },
          // Filter for products that have price_set_id in metadata (room configs)
          metadata: {
            price_set_id: { exists: true }
          }
        },
        fields: ["id", "metadata", "title"],
      });

      if (categoryRoomConfigs && categoryRoomConfigs.length > 0) {
        console.log(`Found ${categoryRoomConfigs.length} room configs using category_id`);
        roomConfigs = categoryRoomConfigs;
      }
    } catch (error) {
      console.error(`Error getting room configs by category_id:`, error);
    }

    // Method 2: Try using hotel_id in metadata
    if (roomConfigs.length === 0) {
      try {
        console.log(`Trying hotel_id in metadata: ${hotelRecordId}`);

        const { data: metadataRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              hotel_id: hotelRecordId,
              price_set_id: { exists: true }
            }
          },
          fields: ["id", "metadata", "title"],
        });

        if (metadataRoomConfigs && metadataRoomConfigs.length > 0) {
          console.log(`Found ${metadataRoomConfigs.length} room configs using hotel_id in metadata`);
          roomConfigs = metadataRoomConfigs;
        }
      } catch (error) {
        console.error(`Error getting room configs by hotel_id in metadata:`, error);
      }
    }

    // Method 3: Try a more general search
    if (roomConfigs.length === 0) {
      try {
        console.log(`Trying general search for room configs with price_set_id`);

        const { data: allRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              price_set_id: { exists: true }
            }
          },
          fields: ["id", "metadata", "title"],
          take: 100
        });

        if (allRoomConfigs && allRoomConfigs.length > 0) {
          console.log(`Found ${allRoomConfigs.length} total room configs, filtering for this hotel`);

          // Filter room configs that might be related to this hotel
          const filteredRoomConfigs = allRoomConfigs.filter(config => {
            // Check if hotel_id in metadata matches
            if (config.metadata?.hotel_id === hotelRecordId) return true;

            // Check if the title or handle contains the hotel name or handle
            const hotelName = hotel[0].name.toLowerCase();
            const hotelHandle = hotel[0].handle.toLowerCase();
            const configTitle = config.title?.toLowerCase() || '';

            return configTitle.includes(hotelName) ||
                   configTitle.includes(hotelHandle);
          });

          if (filteredRoomConfigs.length > 0) {
            console.log(`Found ${filteredRoomConfigs.length} room configs after filtering`);
            roomConfigs = filteredRoomConfigs;
          }
        }
      } catch (error) {
        console.error(`Error in general search for room configs:`, error);
      }
    }

    if (!roomConfigs || roomConfigs.length === 0) {
      return res.json({
        hotel: hotel[0],
        best_price: null,
        message: "No room configurations found for this hotel"
      });
    }

    // Find the best price among all room configurations
    let bestPrice = null;
    let bestRoomConfig = null;

    for (const roomConfig of roomConfigs) {
      // Get the price set ID from metadata
      const priceSetId = roomConfig.metadata?.price_set_id;

      if (!priceSetId) continue;

      try {
        // Calculate price using the pricing module
        const calculatedPrices = await pricingModuleService.calculatePrices(
          { id: [priceSetId] },
          {
            context: {
              currency_code: currency_code as string,
              quantity: nights,
              occupancy_type: "base",
              product_id: roomConfig.id,
            },
          }
        );

        if (calculatedPrices && calculatedPrices.length > 0) {
          const calculatedPrice = calculatedPrices[0];

          // Check if we have a valid calculated price
          if (calculatedPrice.calculated_amount !== null) {
            // Update best price if this is the first price or if it's lower than the current best price
            if (bestPrice === null || calculatedPrice.calculated_amount < bestPrice.amount) {
              bestPrice = {
                amount: calculatedPrice.calculated_amount,
                original_amount: calculatedPrice.original_amount || calculatedPrice.calculated_amount,
                currency_code: currency_code as string,
                total_amount: calculatedPrice.calculated_amount * nights,
                per_night_amount: calculatedPrice.calculated_amount,
                nights: nights,
              };

              bestRoomConfig = {
                id: roomConfig.id,
                title: roomConfig.title,
              };
            }
          }
        }
      } catch (error) {
        console.error(`Error calculating price for room config ${roomConfig.id}:`, error);
        // Continue with next room config
      }
    }

    // Return the results
    return res.json({
      hotel: hotel[0],
      check_in: check_in,
      check_out: check_out,
      nights: nights,
      currency_code: currency_code,
      best_price: bestPrice,
      room_config: bestRoomConfig,
    });

  } catch (error) {
    console.error("Error getting best price for hotel:", error);
    return res.status(500).json({
      message: "Error getting best price for hotel",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
