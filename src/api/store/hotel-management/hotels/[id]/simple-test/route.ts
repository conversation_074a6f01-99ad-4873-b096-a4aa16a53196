import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

/**
 * Simple test endpoint that doesn't require any dependencies
 * 
 * @param req - The request object
 * @param res - The response object
 * @returns A simple response
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    
    // Return a simple response
    return res.json({
      success: true,
      message: "Simple test endpoint is working",
      hotel_id: hotelId,
      query_params: req.query,
      headers: req.headers,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Error in simple test endpoint:", error);
    return res.status(500).json({
      success: false,
      message: "Error in simple test endpoint",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
