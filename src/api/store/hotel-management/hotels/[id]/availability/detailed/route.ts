import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../../../../../modules/hotel-management/room-inventory";
import { z } from "zod";

// Validation schema for detailed availability check
export const StoreHotelDetailedAvailabilitySchema = z.object({
  check_in: z.string(),
  check_out: z.string(),
  adults: z.number().default(1),
  children: z.number().default(0),
  infants: z.number().default(0),
  currency_code: z.string().default("USD"),
  include_price_tiers: z.boolean().default(true),
  include_unavailable: z.boolean().default(false),
});

export type StoreHotelDetailedAvailabilityType = z.infer<
  typeof StoreHotelDetailedAvailabilitySchema
>;

/**
 * Public endpoint to check detailed availability and calculate prices for all room configurations in a hotel
 * Includes price tiers for different occupancy types and meal plans
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A detailed list of available room configurations with their prices and price tiers
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const pricingModuleService = req.scope.resolve(Modules.PRICING);

    // Try to resolve the room inventory service, but handle the case where it might not be available
    let roomInventoryService = null;
    try {
      roomInventoryService = req.scope.resolve(ROOM_INVENTORY_MODULE);
    } catch (error) {
      console.log("Room inventory service not available:", error.message);
    }

    console.log(`Searching for hotel with ID: ${hotelId}`);

    let hotel: any;
    try {
      // First, try to find the hotel directly using the hotel ID
      const hotelResult = await query.graph({
        entity: "hotel",
        filters: {
          id: hotelId,
        },
        fields: [
          "id",
          "name",
          "handle",
          "category_id",
          "description",
          "rating",
          "address",
          "amenities",
        ],
      });

      if (hotelResult.data && hotelResult.data.length > 0) {
        hotel = hotelResult.data;
        console.log(`Hotel found by ID:`, hotel);
      } else {
        // Try searching by handle
        console.log(
          `Hotel not found by ID, trying to search by handle: ${hotelId}`
        );
        const handleResult = await query.graph({
          entity: "hotel",
          filters: {
            handle: hotelId,
          },
          fields: [
            "id",
            "name",
            "handle",
            "category_id",
            "description",
            "rating",
            "address",
            "amenities",
          ],
        });

        if (handleResult.data && handleResult.data.length > 0) {
          hotel = handleResult.data;
          console.log(`Hotel found by handle:`, hotel);
        } else {
          // As a last resort, try searching in product_category
          console.log(
            `Hotel not found in hotel table, trying product_category: ${hotelId}`
          );
          const categoryResult = await query.graph({
            entity: "product_category",
            filters: {
              id: hotelId,
            },
            fields: ["id", "name", "handle", "metadata"],
          });

          hotel = categoryResult.data;
          console.log(`Product category search result:`, hotel);
        }
      }
    } catch (error) {
      console.error(`Error searching for hotel:`, error);
    }

    if (!hotel || hotel.length === 0) {
      // List all available hotels to help with debugging
      try {
        const allHotels = await query.graph({
          entity: "hotel",
          filters: {},
          fields: ["id", "name", "handle", "category_id"],
          // @ts-ignore - limit is supported by the query engine
          limit: 10,
        });

        console.log(`Available hotels:`, allHotels.data);

        return res.status(404).json({
          message: "Hotel not found",
          hotel_id: hotelId,
          available_hotels: allHotels.data,
        });
      } catch (error) {
        console.error(`Error listing hotels:`, error);
        return res.status(404).json({
          message: "Hotel not found and could not list available hotels",
        });
      }
    }

    // Validate query parameters
    const {
      check_in,
      check_out,
      adults = 1,
      children = 0,
      infants = 0,
      currency_code = "USD",
      include_price_tiers = true,
      include_unavailable = false,
    } = req.query;

    if (!check_in || !check_out) {
      return res.status(400).json({
        message: "check_in and check_out dates are required",
      });
    }

    // Parse dates
    const checkInDate = new Date(check_in as string);
    const checkOutDate = new Date(check_out as string);

    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }

    if (checkInDate >= checkOutDate) {
      return res.status(400).json({
        message: "check_in date must be before check_out date",
      });
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // We already have the hotel details from the earlier query

    // Get destination details if available
    let destination = null;
    if (hotel[0].destination_id) {
      try {
        const { data: destinationData } = await query.graph({
          entity: "hotel",
          filters: {
            id: hotel[0].destination_id,
          },
          fields: ["id", "name", "handle", "description"],
        });

        if (destinationData && destinationData.length > 0) {
          destination = destinationData[0];
        }
      } catch (error) {
        console.error(`Error fetching destination:`, error);
      }
    }

    // Get the hotel ID from the hotel record
    const hotelRecordId = hotel[0].id;
    console.log(`Using hotel ID for room configurations: ${hotelRecordId}`);

    // Try to get room configurations using different methods
    let roomConfigs = [];

    // Method 1: Try using category_id
    try {
      const categoryId = hotel[0].category_id;
      console.log(`Trying category_id: ${categoryId}`);

      const { data: categoryRoomConfigs } = await query.graph({
        entity: "product",
        filters: {
          categories: { id: [categoryId] },
          // Filter for products that have price_set_id in metadata (room configs)
          metadata: {
            price_set_id: { exists: true },
          },
        },
        fields: [
          "id",
          "title",
          "description",
          "thumbnail",
          "metadata",
          "handle",
          "images.*",
        ],
      });

      if (categoryRoomConfigs && categoryRoomConfigs.length > 0) {
        console.log(
          `Found ${categoryRoomConfigs.length} room configs using category_id`
        );
        roomConfigs = categoryRoomConfigs;
      }
    } catch (error) {
      console.error(`Error getting room configs by category_id:`, error);
    }

    // Method 2: Try using hotel_id in metadata
    if (roomConfigs.length === 0) {
      try {
        console.log(`Trying hotel_id in metadata: ${hotelRecordId}`);

        const { data: metadataRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              hotel_id: hotelRecordId,
              price_set_id: { exists: true },
            },
          },
          fields: [
            "id",
            "title",
            "description",
            "thumbnail",
            "metadata",
            "handle",
            "images.*",
          ],
        });

        if (metadataRoomConfigs && metadataRoomConfigs.length > 0) {
          console.log(
            `Found ${metadataRoomConfigs.length} room configs using hotel_id in metadata`
          );
          roomConfigs = metadataRoomConfigs;
        }
      } catch (error) {
        console.error(
          `Error getting room configs by hotel_id in metadata:`,
          error
        );
      }
    }

    // Method 3: Try a more general search
    if (roomConfigs.length === 0) {
      try {
        console.log(`Trying general search for room configs with price_set_id`);

        const { data: allRoomConfigs } = await query.graph({
          entity: "product",
          filters: {
            metadata: {
              price_set_id: { exists: true },
            },
          },
          fields: [
            "id",
            "title",
            "description",
            "thumbnail",
            "metadata",
            "handle",
            "images.*",
          ],
          // @ts-ignore - limit is supported by the query engine
          limit: 100,
        });

        if (allRoomConfigs && allRoomConfigs.length > 0) {
          console.log(
            `Found ${allRoomConfigs.length} total room configs, filtering for this hotel`
          );

          // Filter room configs that might be related to this hotel
          const filteredRoomConfigs = allRoomConfigs.filter((config) => {
            // Check if hotel_id in metadata matches
            if (config.metadata?.hotel_id === hotelRecordId) return true;

            // Check if the title or handle contains the hotel name or handle
            const hotelName = hotel[0].name.toLowerCase();
            const hotelHandle = hotel[0].handle.toLowerCase();
            const configTitle = config.title?.toLowerCase() || "";
            const configHandle = config.handle?.toLowerCase() || "";

            return (
              configTitle.includes(hotelName) ||
              configHandle.includes(hotelHandle) ||
              configTitle.includes(hotelHandle) ||
              configHandle.includes(hotelName)
            );
          });

          if (filteredRoomConfigs.length > 0) {
            console.log(
              `Found ${filteredRoomConfigs.length} room configs after filtering`
            );
            roomConfigs = filteredRoomConfigs;
          }
        }
      } catch (error) {
        console.error(`Error in general search for room configs:`, error);
      }
    }

    if (!roomConfigs || roomConfigs.length === 0) {
      return res.json({
        hotel: hotel[0],
        destination: destination,
        check_in: check_in,
        check_out: check_out,
        nights: nights,
        adults: adults,
        children: children,
        infants: infants,
        currency_code: currency_code,
        available_rooms: [],
        message: "No room configurations found for this hotel",
      });
    }

    // Get all room variants for these configurations
    const roomConfigIds = roomConfigs.map((config) => config.id);

    const { data: variants } = await query.graph({
      entity: "product_variant",
      filters: {
        product_id: roomConfigIds,
      },
      fields: ["id", "title", "product_id", "metadata", "options"],
    });

    // Process each room configuration
    const roomConfigResults = [];

    for (const roomConfig of roomConfigs) {
      // Get all variants (rooms) for this room configuration
      const configVariants = variants.filter(
        (v) => v.product_id === roomConfig.id
      );

      // Skip if no variants and we're not including unavailable rooms
      if (!configVariants.length && !include_unavailable) continue;

      // Check if this room configuration meets the occupancy requirements
      const maxAdults = roomConfig.metadata?.max_adults || 1;
      const maxChildren = roomConfig.metadata?.max_children || 0;
      const maxInfants = roomConfig.metadata?.max_infants || 0;

      const meetsOccupancyRequirements =
        maxAdults >= (adults as unknown as number) &&
        maxChildren >= (children as unknown as number) &&
        maxInfants >= (infants as unknown as number);

      if (!meetsOccupancyRequirements && !include_unavailable) {
        continue; // Skip this room configuration if it doesn't meet occupancy requirements
      }

      // Check availability for each variant
      const availableVariants = [];
      let isAvailable = false;

      // If room inventory service is not available, assume all variants are available
      if (!roomInventoryService) {
        console.log(
          "Room inventory service not available, assuming all variants are available"
        );
        availableVariants.push(...configVariants);
        isAvailable = configVariants.length > 0;
      } else {
        // Check availability using the room inventory service
        for (const variant of configVariants) {
          try {
            // Check availability for this variant
            const roomInventories = await roomInventoryService.listRoomInventories({
              inventory_item_id: variant.id,
              from_date: checkInDate,
              to_date: checkOutDate,
              status: "available",
            });

            // If we have inventory records with available_quantity > 0 for all dates, the room is available
            if (roomInventories && roomInventories.length > 0) {
              // Check if all dates in the range have available inventory
              const allDatesAvailable = roomInventories.every(inventory =>
                inventory.available_quantity > 0
              );

              if (allDatesAvailable) {
                availableVariants.push(variant);
                isAvailable = true;
              }
            }
          } catch (error) {
            console.error(
              `Error checking availability for variant ${variant.id}:`,
              error
            );
            // Continue with next variant
          }
        }
      }

      // Skip if no available variants and we're not including unavailable rooms
      if (!isAvailable && !include_unavailable) continue;

      // Calculate prices for this room configuration
      try {
        // Get the price set ID from metadata
        const priceSetId = roomConfig.metadata?.price_set_id;

        if (!priceSetId) {
          if (include_unavailable) {
            // Add room config without pricing
            roomConfigResults.push({
              id: roomConfig.id,
              title: roomConfig.title,
              description: roomConfig.description,
              thumbnail: roomConfig.thumbnail,
              images: roomConfig.images,
              handle: roomConfig.handle,
              room_size: roomConfig.metadata?.room_size,
              bed_type: roomConfig.metadata?.bed_type,
              max_adults: maxAdults,
              max_children: maxChildren,
              max_infants: maxInfants,
              amenities: roomConfig.metadata?.amenities,
              available_rooms: availableVariants.length,
              is_available: isAvailable,
              meets_occupancy: meetsOccupancyRequirements,
              price: null,
              price_tiers: [],
            });
          }
          continue;
        }

        // Try to get all prices for this price set
        const prices = await pricingModuleService.listPrices({
          price_set_id: priceSetId,
        });
        console.log(
          `Prices for price set ${priceSetId}:`,
          JSON.stringify(prices)
        );

        // Try to find prices for all meal plans based on the title pattern
        const numAdults = adults as unknown as number;
        const mealPlans = ["none", "bb", "hb", "fb", "ai"]; // none, bed & breakfast, half board, full board, all inclusive

        // Create an object to store prices for each meal plan
        const mealPlanPrices = {};
        let foundAnyPrice = false;

        // Find prices for each meal plan
        for (const mealPlan of mealPlans) {
          let totalPrice = 0;
          let foundBasePrice = false;

          // For 1 or 2 adults, just look for the base price
          if (numAdults <= 2) {
            const expectedPriceTitle = `base-${mealPlan}-${numAdults}-${numAdults}`;
            console.log(
              `Looking for price with title pattern: ${expectedPriceTitle}`
            );

            const matchingPrice = prices.find(
              (price) => price.title === expectedPriceTitle
            );
            if (matchingPrice) {
              console.log(
                `Found matching price for ${mealPlan}:`,
                JSON.stringify(matchingPrice)
              );
              foundAnyPrice = true;
              foundBasePrice = true;

              // Convert price from database (divide by 100 to match UI expectations)
              totalPrice = Number(matchingPrice.amount) / 100;
            }
          }
          // For 3+ adults, look for base price for 2 adults + extra adult price for each additional adult
          else {
            // First, find the base price for 2 adults
            const basePriceTitle = `base-${mealPlan}-2-2`;
            console.log(
              `Looking for base price with title pattern: ${basePriceTitle}`
            );

            const basePrice = prices.find(
              (price) => price.title === basePriceTitle
            );
            if (basePrice) {
              console.log(
                `Found base price for ${mealPlan}:`,
                JSON.stringify(basePrice)
              );
              foundBasePrice = true;

              // Convert price from database (divide by 100 to match UI expectations)
              totalPrice = Number(basePrice.amount) / 100;

              // Now find the extra adult price
              const extraAdultPriceTitle = `extra_adult-${mealPlan}-1-1`;
              console.log(
                `Looking for extra adult price with title pattern: ${extraAdultPriceTitle}`
              );

              const extraAdultPrice = prices.find(
                (price) => price.title === extraAdultPriceTitle
              );
              if (extraAdultPrice) {
                console.log(
                  `Found extra adult price for ${mealPlan}:`,
                  JSON.stringify(extraAdultPrice)
                );

                // Convert price from database (divide by 100 to match UI expectations)
                const extraAdultAmount = Number(extraAdultPrice.amount) / 100;

                // Add extra adult price for each additional adult beyond 2
                const extraAdults = numAdults - 2;
                totalPrice += extraAdultAmount * extraAdults;

                console.log(
                  `Total price after adding ${extraAdults} extra adults: ${totalPrice}`
                );
                foundAnyPrice = true;
              } else {
                console.log(
                  `No extra adult price found for ${mealPlan}, skipping extra adult pricing`
                );
                // If no extra adult price found, don't add any extra adult pricing
                console.log(
                  `Total price remains ${totalPrice} without extra adult pricing`
                );
                foundAnyPrice = true;
              }

              // Now find the child price if there are children
              const numChildren = children as unknown as number;
              if (numChildren > 0) {
                const childPriceTitle = `extra_child-${mealPlan}-1-1`;
                console.log(
                  `Looking for child price with title pattern: ${childPriceTitle}`
                );

                const childPrice = prices.find(
                  (price) => price.title === childPriceTitle
                );

                if (childPrice) {
                  console.log(
                    `Found child price for ${mealPlan}:`,
                    JSON.stringify(childPrice)
                  );

                  // Convert price from database (divide by 100 to match UI expectations)
                  const childAmount = Number(childPrice.amount) / 100;

                  // Add child price for each child
                  totalPrice += childAmount * numChildren;

                  console.log(
                    `Total price after adding ${numChildren} children: ${totalPrice}`
                  );
                } else {
                  console.log(
                    `No child price found for ${mealPlan}, skipping child pricing`
                  );
                  // If no child price found, don't add any child pricing
                  console.log(
                    `Total price remains ${totalPrice} without child pricing`
                  );
                }
              }

              console.log(
                `Final total price for ${numAdults} adults and ${numChildren} children with ${mealPlan} meal plan: ${totalPrice}`
              );
            }
          }

          // If we found a price, add it to the meal plan prices
          if (foundBasePrice) {
            mealPlanPrices[mealPlan] = {
              amount: totalPrice * nights,
              original_amount: totalPrice * nights,
              currency_code: currency_code as string,
              total_amount: totalPrice * nights,
              per_night_amount: totalPrice,
              nights: nights,
            };
          }
        }

        // If we didn't find any prices, skip this room configuration
        if (!foundAnyPrice) {
          console.log(
            `No prices found for room config ${roomConfig.id}, skipping`
          );
          continue;
        }

        // Use the 'none' meal plan as the default price if available, otherwise use the first available price
        const defaultMealPlan = mealPlanPrices["none"]
          ? "none"
          : Object.keys(mealPlanPrices)[0];
        const basePrice = mealPlanPrices[defaultMealPlan];

        // Calculate price tiers if requested
        const priceTiers = [];

        if (include_price_tiers && basePrice) {
          // Define occupancy types to check
          const occupancyTypes = [
            "single",
            "double",
            "triple",
            "quad",
            "child",
            "infant",
          ];

          // Define meal plans to check
          const mealPlans = ["BB", "HB", "FB", "AI"];

          // Calculate prices for each occupancy type and meal plan
          for (const occupancyType of occupancyTypes) {
            for (const mealPlan of mealPlans) {
              try {
                const tierPrices = await pricingModuleService.calculatePrices(
                  { id: [priceSetId] },
                  {
                    context: {
                      currency_code: currency_code as string,
                      quantity: nights,
                      occupancy_type: occupancyType,
                      meal_plan: mealPlan,
                      product_id: roomConfig.id,
                      min_occupancy: adults as unknown as number,
                      max_occupancy:
                        (adults as unknown as number) +
                        (children as unknown as number),
                    },
                  }
                );

                if (
                  tierPrices &&
                  tierPrices.length > 0 &&
                  tierPrices[0].calculated_amount !== null
                ) {
                  priceTiers.push({
                    occupancy_type: occupancyType,
                    meal_plan: mealPlan,
                    amount: tierPrices[0].calculated_amount,
                    total_amount:
                      Number(tierPrices[0].calculated_amount) * nights,
                    per_night_amount: tierPrices[0].calculated_amount,
                    currency_code: currency_code as string,
                  });
                }
              } catch (error) {
                console.error(
                  `Error calculating price tier for ${occupancyType} / ${mealPlan}:`,
                  error
                );
                // Continue with next tier
              }
            }
          }
        }

        // Add to room config results
        roomConfigResults.push({
          id: roomConfig.id,
          title: roomConfig.title,
          description: roomConfig.description,
          thumbnail: roomConfig.thumbnail,
          images: roomConfig.images,
          handle: roomConfig.handle,
          room_size: roomConfig.metadata?.room_size,
          bed_type: roomConfig.metadata?.bed_type,
          max_adults: maxAdults,
          max_children: maxChildren,
          max_infants: maxInfants,
          amenities: roomConfig.metadata?.amenities,
          available_rooms: availableVariants.length,
          is_available: isAvailable,
          meets_occupancy: meetsOccupancyRequirements,
          price: {
            ...basePrice,
            meal_plans: mealPlanPrices, // Include all meal plan prices
            selected_meal_plan: defaultMealPlan, // Default selected meal plan
          },
          price_tiers: priceTiers,
        });
      } catch (error) {
        console.error(
          `Error calculating price for room config ${roomConfig.id}:`,
          error
        );

        if (include_unavailable) {
          // Add room config without pricing
          roomConfigResults.push({
            id: roomConfig.id,
            title: roomConfig.title,
            description: roomConfig.description,
            thumbnail: roomConfig.thumbnail,
            images: roomConfig.images,
            handle: roomConfig.handle,
            room_size: roomConfig.metadata?.room_size,
            bed_type: roomConfig.metadata?.bed_type,
            max_adults: maxAdults,
            max_children: maxChildren,
            max_infants: maxInfants,
            amenities: roomConfig.metadata?.amenities,
            available_rooms: availableVariants.length,
            is_available: isAvailable,
            meets_occupancy: meetsOccupancyRequirements,
            price: null,
            price_tiers: [],
          });
        }
      }
    }

    // Return the results
    return res.json({
      hotel: hotel[0],
      destination: destination,
      check_in: check_in,
      check_out: check_out,
      nights: nights,
      adults: adults,
      children: children,
      infants: infants,
      currency_code: currency_code,
      available_rooms: roomConfigResults, // Changed from room_configs to match the regular availability endpoint
    });
  } catch (error) {
    console.error("Error checking detailed hotel availability:", error);
    return res.status(500).json({
      message: "Error checking detailed hotel availability",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
