import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import HotelModuleService from "src/modules/hotel-management/hotel/service";

/**
 * Public endpoint to list all hotels with basic details for storefront
 *
 * @param req - The request object
 * @param res - The response object
 * @returns List of hotels with basic details
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve("query");
    const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);

    // Get query parameters
    const {
      destination_id,
      is_featured,
      is_active,
      is_pets_allowed,
      limit = 100,
      offset = 0,
    } = req.query;

    // Build filters
    const filters: Record<string, any> = {};

    if (destination_id) {
      filters.destination_id = destination_id;
    }

    if (is_featured !== undefined) {
      filters.is_featured = is_featured === "true";
    }

    if (is_active !== undefined) {
      filters.is_active = is_active === "true";
    }

    if (is_pets_allowed !== undefined) {
      filters.is_pets_allowed = is_pets_allowed === "true";
    }

    // Get all hotels from the hotel entity
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters,
      fields: [
        "id",
        "name",
        "handle",
        "description",
        "destination_id",
        "rating",
        "total_reviews",
        "location",
        "address",
        "currency",
        "is_featured",
        "is_pets_allowed",
        "category_id",
        "amenities",
        "rules",
        "safety_measures",
      ],
    });

    if (!hotels || hotels.length === 0) {
      return res.json({ hotels: [] });
    }

    // Get thumbnail images for each hotel
    const hotelsWithImages = await Promise.all(
      hotels.map(async (hotel) => {
        const images = await hotelService.getHotelImages(hotel.id);

        // Find thumbnail image or use the first image
        const thumbnailImage =
          images.find((img) => img.isThumbnail) || images[0] || null;

        return {
          ...hotel,
          thumbnail: thumbnailImage ? thumbnailImage.url : null,
        };
      })
    );

    return res.json({
      hotels: hotelsWithImages,
      count: hotelsWithImages.length,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
    });
  } catch (error) {
    console.error("Error listing hotels:", error);
    return res.status(500).json({
      message: "Error listing hotels",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
