// src/api/stripe-payment-session.ts

import {
  createPaymentSessionsWorkflow,
  createPaymentCollectionForCartWorkflow,
  createPaymentCollectionsStep,
} from "@camped-ai/medusa/core-flows";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
} from "@camped-ai/framework/utils";
import { HttpTypes } from "@camped-ai/framework/types";

import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";

export const POST = async (
  req: AuthenticatedMedusaRequest<HttpTypes.StoreCreatePaymentCollection>,
  res: MedusaResponse<HttpTypes.StorePaymentCollectionResponse>
) => {
  const { cart_id, payment_provider_id } = req.body;

  if (!cart_id) {
    res.status(400).json({ message: "cart_id is required" } as any);
    return;
  }

  try {
    // First, get the cart to retrieve its total
    const queryService = req.scope.resolve(
      ContainerRegistrationKeys.REMOTE_QUERY
    );
    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cart_id } },
        fields: ["id", "total", "subtotal", "tax_total"],
      })
    );

    if (!cart) {
      res.status(404).json({ message: "Cart not found" } as any);
      return;
    }

    console.log("Cart total:", cart.total);

    // Initialize payment sessions for the cart with the correct amount
    await createPaymentCollectionForCartWorkflow(req.scope).run({
      input: {
        cart_id,
        // We can't directly set the amount in the workflow input
        // Instead, we'll use metadata to track the expected amount
        metadata: {
          cart_id: cart_id,
          cart_total: cart.total,
          // This is just for reference, the actual amount will be calculated by the workflow
          expected_amount: cart.total,
        },
      },
    });

    // Now get the payment collection
    const [cartCollectionRelation] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
        ],
      })
    );

    const paymentCollection = cartCollectionRelation?.payment_collection;

    console.log("Payment collection:", paymentCollection);
    console.log("Payment collection amount:", paymentCollection?.amount);

    const { result: paymentSessions } = await createPaymentSessionsWorkflow(
      req.scope
    ).run({
      input: {
        payment_collection_id: paymentCollection.id,
        provider_id: payment_provider_id,
      },
    });

    if (!paymentSessions) {
      res
        .status(404)
        .json({ message: "Stripe payment session not found" } as any);
      return;
    }

    // Return the Stripe payment session details
    res.status(200).json({ payment_session: paymentSessions } as any);
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: "An error occurred while creating the payment session",
    } as any);
  }
};
