import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { calculateCustomPriceWorkflow } from "../../../workflows/calculate-custom-price";

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  try {
    const { variant_id, currency_code, addons } = req.body;

    if (!variant_id || !currency_code) {
      return res.status(400).json({
        success: false,
        message: "variant_id and currency_code are required"
      });
    }
    
    const { result } = await calculateCustomPriceWorkflow(req.scope)
      .run({
        input: {
          variant_id,
          currency_code,
          addons
        }
      });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to calculate price"
    });
  }
}