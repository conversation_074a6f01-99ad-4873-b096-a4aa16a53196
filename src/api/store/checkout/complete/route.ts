import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
} from "@camped-ai/framework/utils";
import {
  completeCartWorkflow,
  createPaymentSessionsWorkflow,
  authorizePaymentSessionStep,
} from "@camped-ai/medusa/core-flows";
import { STRIPE_CHECKOUT_SERVICE } from "../../../../modules/stripe-checkout";
import { withClient } from "src/utils/db";
import Stripe from "stripe";

/**
 * Store API endpoint for completing a cart after Stripe checkout
 * This endpoint should be called after successful Stripe payment
 * Takes a cart_id and session_id to complete the cart
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Completing cart after Stripe checkout");

    // Check if req.scope exists
    if (!req.scope) {
      console.error("req.scope is undefined in cart completion");
      return res.status(400).json({
        message: "<PERSON>ope is not available, cannot complete cart",
      });
    }

    // Extract data from request body
    const { cart_id, session_id } = req.body as {
      cart_id: string;
      session_id: string;
    };

    // Validate required fields
    if (!cart_id) {
      return res.status(400).json({
        message: "cart_id is required",
      });
    }

    if (!session_id) {
      return res.status(400).json({
        message: "session_id is required",
      });
    }

    console.log(`Completing cart ${cart_id} with session ${session_id}`);

    // Verify the Stripe checkout session
    const stripeCheckoutService = req.scope.resolve(STRIPE_CHECKOUT_SERVICE);
    let checkoutSession: any;

    try {
      checkoutSession = await stripeCheckoutService.retrieveCheckoutSession(
        session_id
      );
    } catch (error) {
      console.error("Error retrieving Stripe session:", error);
      return res.status(400).json({
        message: "Invalid session_id or session not found",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    console.log("checkoutSession retieved", checkoutSession);

    // Verify the session is completed and matches the cart
    if (checkoutSession.payment_status !== "paid") {
      return res.status(400).json({
        message: "Payment not completed for this session",
        payment_status: checkoutSession.payment_status,
      });
    }

    if (checkoutSession.metadata?.cart_id !== cart_id) {
      return res.status(400).json({
        message: "Session does not match the provided cart_id",
      });
    }

    // Get the cart to verify it exists
    const queryService = req.scope.resolve(
      ContainerRegistrationKeys.REMOTE_QUERY
    );

    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cart_id } },
        fields: ["id", "total", "currency_code", "metadata"],
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
      });
    }

    console.log("Cart and session verified, creating payment session...");

    // Create payment session entry for checkout session
    try {
      const queryService = req.scope.resolve(
        ContainerRegistrationKeys.REMOTE_QUERY
      );

      // Get the payment collection for this cart
      const [cartCollectionRelation] = await queryService(
        remoteQueryObjectFromString({
          entryPoint: "cart_payment_collection",
          variables: { filters: { cart_id } },
          fields: [
            "payment_collection.id",
            "payment_collection.amount",
            "payment_collection.currency_code",
          ],
        })
      );

      const paymentCollection = cartCollectionRelation?.payment_collection;

      if (!paymentCollection) {
        console.error("No payment collection found for cart");
        return res.status(400).json({
          message: "Payment collection not found for cart",
        });
      }

      console.log("Found payment collection:", paymentCollection.id);

      // Generate payment session ID
      const generatePaymentSessionId = () => {
        const timestamp = Date.now().toString(36);
        const randomString = Math.random().toString(36).substring(2, 15);
        return `payses_${timestamp}${randomString}`;
      };

      const paymentSessionId = generatePaymentSessionId();
      const now = new Date();

      // Get the payment intent from the checkout session
      if (!checkoutSession.payment_intent) {
        console.error("No payment intent found in checkout session");
        return res.status(400).json({
          message: "No payment intent found in checkout session",
        });
      }

      console.log("Retrieving payment intent:", checkoutSession.payment_intent);

      // Retrieve the actual payment intent from Stripe
      let paymentIntent: any;
      try {
        // Create Stripe instance
        const stripe = new Stripe(process.env.STRIPE_API_KEY!, {
          apiVersion: "2024-04-10",
        });

        paymentIntent = await stripe.paymentIntents.retrieve(
          checkoutSession.payment_intent
        );
        console.log("Payment intent retrieved:", paymentIntent.id);
      } catch (error) {
        console.error("Error retrieving payment intent:", error);
        return res.status(400).json({
          message: "Failed to retrieve payment intent",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }

      // Prepare payment session data (using actual payment intent data)
      const paymentSessionData = {
        id: paymentIntent.id,
        object: "payment_intent",
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        created: paymentIntent.created,
        customer: paymentIntent.customer,
        metadata: {
          ...paymentIntent.metadata,
          checkout_session_id: checkoutSession.id, // Store reference to checkout session
        },
        payment_method: paymentIntent.payment_method,
        payment_method_types: paymentIntent.payment_method_types,
        client_secret: paymentIntent.client_secret,
        confirmation_method: paymentIntent.confirmation_method,
        capture_method: paymentIntent.capture_method,
        amount_received: paymentIntent.amount_received,
        amount_capturable: paymentIntent.amount_capturable,
        charges: paymentIntent.charges,
      };

      // Create payment session entry in database
      await withClient(async (client) => {
        const insertQuery = `
          INSERT INTO payment_session (
            id, currency_code, amount, raw_amount, provider_id, data,
            context, status, authorized_at, payment_collection_id,
            metadata, created_at, updated_at, deleted_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        `;

        const values = [
          paymentSessionId,
          paymentCollection.currency_code,
          paymentCollection.amount,
          JSON.stringify({
            value: paymentCollection.amount.toString(),
            precision: 20,
          }),
          "pp_stripe_stripe",
          JSON.stringify(paymentSessionData),
          JSON.stringify({}), // empty context
          "authorized",
          now,
          paymentCollection.id,
          JSON.stringify({}), // empty metadata
          now,
          now,
          null,
        ];

        await client.query(insertQuery, values);
        console.log("Payment session created successfully:", paymentSessionId);
      });
    } catch (error) {
      console.error("Error creating payment session:", error);
      return res.status(500).json({
        message: "Failed to create payment session",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    console.log("Proceeding to complete cart...");

    // Complete the cart using the workflow
    let completeResult: any;
    try {
      const { result } = await completeCartWorkflow(req.scope).run({
        input: {
          id: cart_id,
        },
      });
      completeResult = result;

      console.log("Cart completed successfully:", completeResult.id);
    } catch (error) {
      console.error("Error completing cart:", error);
      return res.status(500).json({
        message: "Failed to complete cart",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    // Update the order with Stripe session information
    try {
      const orderService = req.scope.resolve(Modules.ORDER);
      await orderService.updateOrders(completeResult.id, {
        metadata: {
          ...completeResult.metadata,
          stripe_checkout_session_id: session_id,
          stripe_payment_intent_id: checkoutSession.payment_intent,
          payment_status: "paid",
          payment_completed_at: new Date().toISOString(),
        },
      });

      console.log("Order updated with Stripe payment information");
    } catch (error) {
      console.error("Error updating order with Stripe info:", error);
      // Don't fail the request if this update fails
    }

    return res.json({
      order: completeResult,
      session_id: session_id,
      payment_status: "completed",
      success: true,
    });
  } catch (error) {
    console.error("Error completing cart after checkout:", error);
    return res.status(500).json({
      message: "An error occurred while completing the cart",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
