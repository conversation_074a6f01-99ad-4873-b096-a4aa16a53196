import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
} from "@camped-ai/framework/utils";
import { createPaymentCollectionForCartWorkflow } from "@camped-ai/medusa/core-flows";
import { STRIPE_CHECKOUT_SERVICE } from "../../../../modules/stripe-checkout";

/**
 * Store API endpoint for creating Stripe checkout sessions
 * Takes a cart_id and creates a Stripe Checkout Session
 * Returns the checkout URL for frontend redirection
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Creating Stripe checkout session");

    // Check if req.scope exists
    if (!req.scope) {
      console.error("req.scope is undefined in checkout session creation");
      return res.status(400).json({
        message: "Scope is not available, cannot create checkout session",
      });
    }

    // Extract data from request body
    const { cart_id, success_url, cancel_url } = req.body as {
      cart_id: string;
      success_url: string;
      cancel_url: string;
    };

    // Validate required fields
    if (!cart_id) {
      return res.status(400).json({
        message: "cart_id is required",
      });
    }

    if (!success_url) {
      return res.status(400).json({
        message: "success_url is required",
      });
    }

    if (!cancel_url) {
      return res.status(400).json({
        message: "cancel_url is required",
      });
    }

    console.log(`Creating checkout session for cart: ${cart_id}`);

    // Get the cart with line items and metadata
    const queryService = req.scope.resolve(
      ContainerRegistrationKeys.REMOTE_QUERY
    );

    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cart_id } },
        fields: [
          "id",
          "total",
          "subtotal",
          "tax_total",
          "currency_code",
          "email",
          "metadata",
          "items.*",
          "items.variant.*",
          "items.product.*",
        ],
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
      });
    }

    console.log("Cart found:", {
      id: cart.id,
      total: cart.total,
      currency_code: cart.currency_code,
      items_count: cart.items?.length || 0,
    });

    // Check if cart has items
    if (!cart.items || cart.items.length === 0) {
      return res.status(400).json({
        message: "Cart is empty",
      });
    }

    // Check if cart total is valid
    if (!cart.total || cart.total <= 0) {
      return res.status(400).json({
        message: "Cart total must be greater than 0",
      });
    }

    // Create payment collection for the cart if it doesn't exist
    console.log("Creating payment collection for cart...");
    try {
      await createPaymentCollectionForCartWorkflow(req.scope).run({
        input: {
          cart_id: cart.id,
          metadata: {
            cart_id: cart.id,
            cart_total: cart.total,
            checkout_session: true,
          },
        },
      });
      console.log("Payment collection created successfully");
    } catch (error) {
      // If payment collection already exists, that's fine
      if (error.message?.includes("already has a payment collection")) {
        console.log("Payment collection already exists for cart");
      } else {
        console.error("Error creating payment collection:", error);
        return res.status(500).json({
          message: "Failed to create payment collection",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Prepare line items for Stripe
    const lineItems = cart.items.map((item: any) => {
      const unitAmount = Math.round((item.unit_price || 0) * 100); // Convert to cents
      const productName = item.title || item.product?.title || "Hotel Booking";

      // Extract hotel and room information from metadata
      const hotelName =
        item.metadata?.hotel_name || cart.metadata?.hotel_name || "Hotel";
      const roomConfigName =
        item.metadata?.room_config_name ||
        cart.metadata?.room_config_name ||
        "Room";
      const checkInDate =
        item.metadata?.check_in_date || cart.metadata?.check_in_date;
      const checkOutDate =
        item.metadata?.check_out_date || cart.metadata?.check_out_date;

      let description = `${hotelName} - ${roomConfigName}`;
      if (checkInDate && checkOutDate) {
        const checkIn = new Date(checkInDate).toLocaleDateString();
        const checkOut = new Date(checkOutDate).toLocaleDateString();
        description += ` (${checkIn} - ${checkOut})`;
      }

      return {
        price_data: {
          currency: cart.currency_code.toLowerCase(),
          product_data: {
            name: productName,
            description: description,
          },
          unit_amount: unitAmount,
        },
        quantity: item.quantity || 1,
      };
    });

    // Use the URLs provided in the request payload
    // Note: Stripe will replace {CHECKOUT_SESSION_ID} with the actual session ID in success_url
    const successUrlWithSessionId = success_url.includes(
      "{CHECKOUT_SESSION_ID}"
    )
      ? success_url
      : `${success_url}${
          success_url.includes("?") ? "&" : "?"
        }session_id={CHECKOUT_SESSION_ID}`;

    console.log("Using URLs:", {
      success_url: successUrlWithSessionId,
      cancel_url: cancel_url,
    });

    // Prepare metadata for the session
    const sessionMetadata = {
      cart_id: cart.id,
      hotel_id: cart.metadata?.hotel_id || "",
      room_config_id: cart.metadata?.room_config_id || "",
      guest_email: cart.email || cart.metadata?.guest_email || "",
      guest_name: cart.metadata?.guest_name || "",
      check_in_date: cart.metadata?.check_in_date || "",
      check_out_date: cart.metadata?.check_out_date || "",
      number_of_guests: cart.metadata?.number_of_guests?.toString() || "1",
      number_of_rooms: cart.metadata?.number_of_rooms?.toString() || "1",
    };

    // Get the Stripe checkout service
    const stripeCheckoutService = req.scope.resolve(STRIPE_CHECKOUT_SERVICE);

    // Create the checkout session
    const checkoutSession = await stripeCheckoutService.createCheckoutSession({
      cart_id: cart.id,
      line_items: lineItems,
      customer_email: cart.email || cart.metadata?.guest_email,
      success_url: successUrlWithSessionId,
      cancel_url: cancel_url,
      metadata: sessionMetadata,
    });

    console.log("Checkout session created successfully:", {
      session_id: checkoutSession.id,
      cart_id: cart.id,
      checkout_url: checkoutSession.url,
    });

    // Note: We'll create the payment session during cart completion when we have the payment intent data
    console.log(
      "Checkout session created, payment session will be created during completion"
    );

    return res.json({
      checkout_url: checkoutSession.url,
      session_id: checkoutSession.id,
      cart_id: cart.id,
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return res.status(500).json({
      message: "An error occurred while creating the checkout session",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
