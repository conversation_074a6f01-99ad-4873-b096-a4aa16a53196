import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";
import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
    req.scope.resolve(METAFIELD_DEFINITION_MODULE);

  const { ids, fields } = req.query;
  const filters = ids ? { id: ids.split(",") } : undefined;
  const defaultFields = ["id", "title", "categories.*"];
  const requestedFields = fields ? fields.split(",") : [];
  const productFields = Array.from(new Set([...defaultFields, ...requestedFields]));

  const { data: products } = await query.graph({
    entity: "product",
    fields: productFields,
    filters,
  });

  if (!products.length) {
    return res.json({ products: [] });
  }

  // Fetch product metafield definitions
  const { data: productMetafieldDefinitions } = await query.graph({
    entity: "metafield_definition",
    filters: { owner_type: ["product"], scope: "store" },
    fields: ["id", "namespace", "key", "type", "label", "namespace_label"],
  });

  // Fetch product metafields
  const { data: productMetafields } = await query.graph({
    entity: "metafield",
    fields: ["definition_id", "value", "owner_id"],
  });

  // Organize product metafields
  const productMetafieldMap = {};
  productMetafields.forEach((field) => {
    const definition = productMetafieldDefinitions.find((def) => def.id === field.definition_id);
    if (!definition) return;

    if (!productMetafieldMap[field.owner_id]) {
      productMetafieldMap[field.owner_id] = {};
    }

    if (!productMetafieldMap[field.owner_id][definition.namespace]) {
      productMetafieldMap[field.owner_id][definition.namespace] = {
        fields: [],
      };
    }

    productMetafieldMap[field.owner_id][definition.namespace].fields.push({
      label: definition.label,
      type: definition.type,
      key: definition.key,
      value: field.value,
    });
  });

  // Fetch category metafield definitions
  const categoryMetafieldDefinitions = await metafieldDefinitionModuleService.listMetafieldDefinitions({
    owner_type: "category",
    scope: "store",
    categories: { $overlap: products.flatMap((p) => p.categories?.map((c) => c.id) || []) },
  });

  // Fetch category metafields
  const { data: categoryMetafields } = await query.graph({
    entity: "metafield",
    fields: ["definition_id", "value", "owner_id"],
  });

  // Organize category metafields
  const categoryMetafieldMap = {};
  categoryMetafieldDefinitions.forEach((definition) => {
    definition.categories.forEach((categoryId) => {
      if (!categoryMetafieldMap[categoryId]) {
        categoryMetafieldMap[categoryId] = {};
      }

      if (!categoryMetafieldMap[categoryId][definition.namespace]) {
        categoryMetafieldMap[categoryId][definition.namespace] = {
          fields: [],
        };
      }

      const value = categoryMetafields.find(
        (meta) => meta.definition_id === definition.id && meta.owner_id === products[0].id
      )?.value;

      categoryMetafieldMap[categoryId][definition.namespace].fields.push({
        label: definition.label,
        type: definition.type,
        key: definition.key,
        value: value 
      });
    });
  });

  // Attach custom_fields and category_fields to each product
  const updatedProducts = products.map((product) => ({
    ...product,
    custom_fields: productMetafieldMap[product.id] || {},
    category_fields: product.categories.reduce((acc, category) => {
      return { ...acc, ...(categoryMetafieldMap[category.id] || {}) };
    }, {}),
  }));

  res.json({ products: updatedProducts });
};
