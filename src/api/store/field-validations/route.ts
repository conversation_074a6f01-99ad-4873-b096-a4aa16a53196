import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: fieldValidation } = await query.graph({
    entity: "field_validation",
    // filters: {
    //     id: [
    //       req.params.id,
    //     ],
    //   },
    
    fields: ["*"],
  });

  res.json({ fieldValidation });
};
