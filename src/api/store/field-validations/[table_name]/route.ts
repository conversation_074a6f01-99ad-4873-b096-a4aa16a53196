import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: fieldValidation } = await query.graph({
    entity: "field_validation",
    filters: {
      table_name: [
          req.params.table_name,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ fieldValidation });
};


