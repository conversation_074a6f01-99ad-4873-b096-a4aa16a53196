import { 
    AuthenticatedMedusaRequest, 
    MedusaResponse
  } from "@camped-ai/framework";
  import { ContainerRegistrationKeys } from "@camped-ai/framework/utils"
  
  export const GET = async (
    req: AuthenticatedMedusaRequest,
    res: MedusaResponse
  ) => {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  
    const { data: [customer] } = await query.graph({
      entity: "customer",
      fields: [
        "subscriptions.*",
        "subscriptions.orders.items.product_title",
        "subscriptions.orders.items.thumbnail",
        "subscriptions.orders.items.quantity",
        "subscriptions.orders.items.detail.quantity",
        "subscriptions.orders.items.product_id",
        "subscriptions.orders.items.variant_title",
        "subscriptions.orders.items.unit_price",


      ],
      filters: {
        id: [req.auth_context.actor_id]
      }
    })
    const transformedData = transformData(customer.subscriptions);
  
    res.json({
      subscriptions: transformedData
    })
  }
  function transformData(data) {
    const subscriptions = data.map(({ orders, ...sub }) => {
        const firstItem = orders[0]?.items[0] ? { ...orders[0].items[0], detail: undefined } : {};
        return { ...sub, ...firstItem, quantity: orders[0]?.items[0]?.quantity };
        
    });
    return subscriptions;
}
