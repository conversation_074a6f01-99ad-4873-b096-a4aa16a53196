import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");

  const { data: metafield } = await query.graph({
    entity: "metafield",
    filters: {
      owner_id: [
          req.params.owner_id,
        ],
      },
    
    fields: ["*"],
  });

  res.json({ metafield });
};


