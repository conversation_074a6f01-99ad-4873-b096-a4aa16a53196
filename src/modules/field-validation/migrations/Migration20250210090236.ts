import { Migration } from '@mikro-orm/migrations';

export class Migration20250210090236 extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table if exists "field_validation" add column if not exists "is_visible" boolean not null default true;');
    this.addSql('alter table if exists "field_validation" rename column "required" to "is_required";');
  }

  async down(): Promise<void> {
    this.addSql('alter table if exists "field_validation" drop column if exists "is_visible";');
    this.addSql('alter table if exists "field_validation" rename column "is_required" to "required";');
  }

}
