{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "table_name": {"name": "table_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "field_name": {"name": "field_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "user_type": {"name": "user_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'both'", "enumItems": ["guest", "logged_in", "both"], "mappedType": "enum"}, "is_required": {"name": "is_required", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "is_visible": {"name": "is_visible", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "field_validation", "schema": "public", "indexes": [{"keyName": "IDX_field_validation_table_name", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_field_validation_table_name\" ON \"field_validation\" (table_name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_field_validation_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_field_validation_deleted_at\" ON \"field_validation\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_field_validation_unique", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_field_validation_unique\" ON \"field_validation\" (table_name, field_name, user_type) WHERE deleted_at IS NULL"}, {"keyName": "field_validation_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}