import { Migration } from '@mikro-orm/migrations';

export class Migration20250209132049 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "field_validation" ("id" text not null, "table_name" text not null, "field_name" text not null, "user_type" text check ("user_type" in (\'guest\', \'logged_in\', \'both\')) not null default \'both\', "required" boolean not null default false, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "field_validation_pkey" primary key ("id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_field_validation_table_name" ON "field_validation" (table_name) WHERE deleted_at IS NULL;');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_field_validation_deleted_at" ON "field_validation" (deleted_at) WHERE deleted_at IS NULL;');
    this.addSql('CREATE UNIQUE INDEX IF NOT EXISTS "IDX_field_validation_unique" ON "field_validation" (table_name, field_name, user_type) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "field_validation" cascade;');
  }

}
