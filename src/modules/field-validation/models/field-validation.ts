import { model } from "@camped-ai/framework/utils";

export const FieldValidation = model.define("field_validation", {
    id: model.id().primaryKey(),
    table_name: model.text().index(),
    field_name: model.text(),
    user_type: model.enum(["guest", "logged_in", "both"]).default("both"),
    is_required: model.boolean().default(false),
    is_visible: model.boolean().default(true),

}).indexes([{
    name: "IDX_field_validation_unique",
    on: ["table_name", "field_name", "user_type"],
    unique: true,
}]);