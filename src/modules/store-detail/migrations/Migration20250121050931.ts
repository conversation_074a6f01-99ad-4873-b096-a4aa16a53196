import { Migration } from '@mikro-orm/migrations';

export class Migration20250121050931 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table if not exists "store_detail" ("store_id" text not null, "id" text not null, "storefront_url" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "store_detail_pkey" primary key ("store_id"));');
    this.addSql('CREATE INDEX IF NOT EXISTS "IDX_store_detail_deleted_at" ON "store_detail" (deleted_at) WHERE deleted_at IS NULL;');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "store_detail" cascade;');
  }

}
