{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"store_id": {"name": "store_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "storefront_url": {"name": "storefront_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "store_detail", "schema": "public", "indexes": [{"keyName": "IDX_store_detail_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_store_detail_deleted_at\" ON \"store_detail\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "store_detail_pkey", "columnNames": ["store_id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}