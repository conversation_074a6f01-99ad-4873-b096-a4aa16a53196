import Stripe from "stripe";

export interface CheckoutSessionOptions {
  cart_id: string;
  line_items: Array<{
    price_data: {
      currency: string;
      product_data: {
        name: string;
        description?: string;
      };
      unit_amount: number;
    };
    quantity: number;
  }>;
  customer_email?: string;
  success_url: string;
  cancel_url: string;
  metadata?: Record<string, any>;
}

export interface CheckoutSessionResponse {
  id: string;
  url: string;
  payment_status: string;
  status: string;
}

class StripeCheckoutService {
  private stripe: Stripe;

  constructor(container) {
    try {
      // Initialize Stripe with API key from environment variables
      const apiKey = process.env.STRIPE_API_KEY;

      if (!apiKey) {
        throw new Error(
          "STRIPE_API_KEY is not defined in environment variables"
        );
      }

      this.stripe = new Stripe(apiKey, {
        apiVersion: "2024-04-10", // Use the latest API version
      });

      console.log("Stripe checkout service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize Stripe Checkout Service:", error);
      throw error;
    }
  }

  /**
   * Create a Stripe Checkout Session
   * @param options - Checkout session options
   * @returns The created checkout session
   */
  async createCheckoutSession(options: CheckoutSessionOptions): Promise<CheckoutSessionResponse> {
    const {
      cart_id,
      line_items,
      customer_email,
      success_url,
      cancel_url,
      metadata = {},
    } = options;

    try {
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items,
        mode: 'payment',
        success_url,
        cancel_url,
        customer_email,
        metadata: {
          cart_id,
          ...metadata,
        },
        // Enable automatic tax calculation if needed
        automatic_tax: {
          enabled: false,
        },
        // Set payment intent data for additional context
        payment_intent_data: {
          metadata: {
            cart_id,
            ...metadata,
          },
        },
      });

      console.log(
        `Created Stripe checkout session for cart ${cart_id}:`,
        JSON.stringify({
          id: session.id,
          url: session.url,
          payment_status: session.payment_status,
          status: session.status,
        }, null, 2)
      );

      return {
        id: session.id,
        url: session.url!,
        payment_status: session.payment_status,
        status: session.status,
      };
    } catch (error) {
      console.error(
        `Failed to create checkout session for cart ${cart_id}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Retrieve a checkout session by ID
   * @param sessionId - The checkout session ID
   * @returns The checkout session details
   */
  async retrieveCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
    try {
      const session = await this.stripe.checkout.sessions.retrieve(sessionId);

      console.log(
        `Retrieved checkout session ${sessionId}:`,
        JSON.stringify({
          id: session.id,
          payment_status: session.payment_status,
          status: session.status,
        }, null, 2)
      );

      return session;
    } catch (error) {
      console.error(`Failed to retrieve checkout session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * List checkout sessions with optional filters
   * @param options - List options
   * @returns List of checkout sessions
   */
  async listCheckoutSessions(options: {
    limit?: number;
    starting_after?: string;
    ending_before?: string;
  } = {}): Promise<Stripe.ApiList<Stripe.Checkout.Session>> {
    try {
      const sessions = await this.stripe.checkout.sessions.list(options);
      return sessions;
    } catch (error) {
      console.error("Failed to list checkout sessions:", error);
      throw error;
    }
  }
}

export default StripeCheckoutService;
