import { BaseEntity } from "@camped-ai/framework/utils";
import { Entity, Column, ManyToOne, JoinColumn, Index, OneToMany } from "typeorm";
import { RoomConfig } from "./room-config";

@Entity()
export class Hotel extends BaseEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  postal_code: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  website: string;

  @Index()
  @Column({ nullable: true })
  destination_id: string;

  @Index()
  @Column({ nullable: true })
  category_id: string;

  @OneToMany(() => RoomConfig, roomConfig => roomConfig.hotel)
  room_configs: RoomConfig[];

  @Column("simple-array", { nullable: true })
  images: string[];

  @Column({ type: "jsonb", nullable: true })
  metadata: Record<string, any>;
}
