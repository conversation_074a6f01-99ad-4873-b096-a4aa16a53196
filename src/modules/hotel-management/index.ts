import RoomConfigService from "./services/room-config";
import { Module } from "@camped-ai/framework/utils";
import { ADD_ON_SERVICE, ADD_ON_SERVICE_MODULE } from "./add-on-service";
import AddOnServiceModuleService from "./add-on-service/service";
import { ROOM_INVENTORY_MODULE } from "./room-inventory";

export const ROOM_CONFIG_SERVICE = "roomConfigService";

// Re-export from sub-modules
export * from "./room-inventory";
export * from "./add-on-service";

// Export the room config service module
export const roomConfigModule = Module(ROOM_CONFIG_SERVICE, {
  service: RoomConfigService,
});

// Export the main module
export default {
  scope: "hotel-management",
  resources: {
    [ROOM_CONFIG_SERVICE]: {
      instance: RoomConfigService,
      singleton: true,
    },
    [ADD_ON_SERVICE]: {
      instance: AddOnServiceModuleService,
      singleton: true,
    },
    [ADD_ON_SERVICE_MODULE]: {
      instance: AddOnServiceModuleService,
      singleton: true,
    },
  },
};
