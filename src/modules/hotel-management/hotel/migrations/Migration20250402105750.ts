import { Migration } from '@mikro-orm/migrations';

export class Migration20250402105750 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "hotel" ("id" text not null, "category_id" text not null, "name" text not null, "handle" text not null, "description" text null, "is_active" boolean not null default true, "website" text null, "email" text null, "destination_id" text not null, "rating" integer null, "total_reviews" integer null, "notes" text null, "location" text null, "address" text null, "phone_number" text null, "timezone" text null, "available_languages" jsonb null, "tax_type" text null, "tax_number" text null, "tags" jsonb null, "amenities" jsonb null, "currency" text null, "check_in_time" text null, "check_out_time" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "hotel_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_hotel_deleted_at" ON "hotel" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "hotel_image" ("id" text not null, "url" text not null, "metadata" jsonb null, "rank" integer not null default 0, "hotel_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "hotel_image_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_hotel_image_hotel_id" ON "hotel_image" (hotel_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_hotel_image_deleted_at" ON "hotel_image" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_Hotel_image_url" ON "hotel_image" (url) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "room_config" ("id" text not null, "name" text not null, "type" text not null, "description" text null, "room_size" text null, "amenities" text[] null, "bed_type" text null, "max_extra_beds" integer not null default 0, "max_adults" integer not null default 1, "max_children" integer not null default 0, "max_infants" integer not null default 0, "max_occupancy" integer not null default 1, "hotel_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "room_config_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_config_hotel_id" ON "room_config" (hotel_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_config_deleted_at" ON "room_config" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "hotel_image" add constraint "hotel_image_hotel_id_foreign" foreign key ("hotel_id") references "hotel" ("id") on update cascade;`);

    this.addSql(`alter table if exists "room_config" add constraint "room_config_hotel_id_foreign" foreign key ("hotel_id") references "hotel" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "hotel_image" drop constraint if exists "hotel_image_hotel_id_foreign";`);

    this.addSql(`alter table if exists "room_config" drop constraint if exists "room_config_hotel_id_foreign";`);

    this.addSql(`drop table if exists "hotel" cascade;`);

    this.addSql(`drop table if exists "hotel_image" cascade;`);

    this.addSql(`drop table if exists "room_config" cascade;`);
  }

}
