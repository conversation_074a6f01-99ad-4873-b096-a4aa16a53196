import { model } from "@camped-ai/framework/utils";
import { Hotel } from "./hotel";

export const HotelImage = model
  .define(
    { tableName: "hotel_image", name: "HotelImage" },
    {
      id: model.id({ prefix: "hot_img" }).primaryKey(),
      url: model.text(),
      metadata: model.json().nullable(),
      rank: model.number().default(0),
      hotel: model.belongsTo(() => Hotel, {
        mappedBy: "images",
      }),
    }
  )
  .indexes([
    {
      name: "IDX_Hotel_image_url",
      on: ["url"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
