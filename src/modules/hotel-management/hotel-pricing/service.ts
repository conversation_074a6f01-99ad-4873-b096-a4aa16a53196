import { MedusaService } from "@camped-ai/framework/utils";
import { DAL } from "@camped-ai/framework/types";
import {
  BasePriceRule,
  SeasonalPriceRule,
  ChannelPriceOverride,
  OccupancyConfig,
  MealPlan,
} from "./models";

type InjectedDependencies = {
  baseRepository: DAL.RepositoryService
}

/**
 * Service for managing hotel pricing
 */
class HotelPricingService extends MedusaService({
  BasePriceRule,
  SeasonalPriceRule,
  ChannelPriceOverride,
  OccupancyConfig,
  MealPlan,
}) {
  protected baseRepository_: DAL.RepositoryService

  constructor({ baseRepository }: InjectedDependencies) {
    super(...arguments)
    this.baseRepository_ = baseRepository
  }
}

export default HotelPricingService;