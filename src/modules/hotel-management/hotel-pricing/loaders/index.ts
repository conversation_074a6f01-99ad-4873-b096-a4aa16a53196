import { LoaderOptions } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import { BaseRepository } from "../repositories";
import {
  BasePriceRuleRepository,
  OccupancyConfigRepository,
  SeasonalPriceRuleRepository,
  ChannelPriceOverrideRepository,
  MealPlanRepository
} from "../repositories/hotel-pricing";

export default async ({ container }: LoaderOptions): Promise<void> => {
  container.register({
    baseRepository: asClass(BaseRepository).singleton(),
    basePriceRuleRepository: asClass(BasePriceRuleRepository).singleton(),
    occupancyConfigRepository: asClass(OccupancyConfigRepository).singleton(),
    seasonalPriceRuleRepository: asClass(SeasonalPriceRuleRepository).singleton(),
    channelPriceOverrideRepository: asClass(ChannelPriceOverrideRepository).singleton(),
    mealPlanRepository: asClass(MealPlanRepository).singleton(),
  });
}
