import { DALUtils } from "@camped-ai/framework/utils";
import { BasePriceRule, OccupancyConfig, SeasonalPriceRule, ChannelPriceOverride, MealPlan } from "../models";

export class BasePriceRuleRepository extends DALUtils.mikroOrmBaseRepositoryFactory(
  BasePriceRule
) {}

export class OccupancyConfigRepository extends DALUtils.mikroOrmBaseRepositoryFactory(
  OccupancyConfig
) {}

export class SeasonalPriceRuleRepository extends DALUtils.mikroOrmBaseRepositoryFactory(
  SeasonalPriceRule
) {}

export class ChannelPriceOverrideRepository extends DALUtils.mikroOrmBaseRepositoryFactory(
  ChannelPriceOverride
) {}

export class MealPlanRepository extends DALUtils.mikroOrmBaseRepositoryFactory(
  MealPlan
) {}
