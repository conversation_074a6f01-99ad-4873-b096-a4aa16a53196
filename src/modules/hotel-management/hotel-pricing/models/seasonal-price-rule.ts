import { model } from "@camped-ai/framework/utils";
import BasePriceRule from "./base-price-rule";

/**
 * SeasonalPriceRule model
 *
 * Defines seasonal price overrides for specific date ranges.
 */
const SeasonalPriceRule = model.define("seasonal_price_rule", {
  id: model.id({ prefix: "spr" }).primaryKey(),

  // Date range
  start_date: model.dateTime(),             // Start date of the override
  end_date: model.dateTime(),               // End date of the override

  // Price override
  amount: model.number(),                  // Override price in smallest currency unit
  currency_code: model.text().nullable(),   // Can be null to use base price currency

  // Description and priority
  name: model.text().nullable(),           // Season name (e.g., "Summer 2025")
  description: model.text().nullable(),     // Optional description
  priority: model.number().default(0),     // Rule priority for conflict resolution

  // Relationships
  base_price_rule: model.belongsTo(() => BasePriceRule, {
    joinColumn: "base_price_rule_id",
  }),

  // Additional constraints
  min_nights: model.number().nullable(),    // Minimum nights required for this price
  max_nights: model.number().nullable(),    // Maximum nights allowed for this price

  // Day of week constraints (e.g., only apply on weekends)
  day_of_week_constraints: model.json().nullable(),

  // Metadata for additional configuration
  metadata: model.json().nullable(),
})
.indexes([
  {
    on: ["base_price_rule_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["start_date", "end_date"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["priority"],
    where: "deleted_at IS NULL",
  },
]);

export default SeasonalPriceRule;
