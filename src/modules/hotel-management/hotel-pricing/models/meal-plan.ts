import { model } from "@camped-ai/framework/utils"

export enum MealPlanTypeEnum {
  NONE = "none",
  BED_BREAKFAST = "bb",
  HALF_BOARD = "hb",
  FULL_BOARD = "fb",
}

/**
 * MealPlan model
 *
 * Stores meal plan options for a hotel.
 */
const MealPlan = model.define("meal_plan", {
  id: model.id({ prefix: "mp" }).primaryKey(),
  name: model.text(),
  type: model.text(), // Using text instead of enum to allow custom types
  hotel_id: model.text(),
  is_default: model.boolean().default(false),
  metadata: model.json().nullable(),
})
.indexes([
  {
    on: ["hotel_id"],
    where: "deleted_at IS NULL",
  },
]);

export default MealPlan;
