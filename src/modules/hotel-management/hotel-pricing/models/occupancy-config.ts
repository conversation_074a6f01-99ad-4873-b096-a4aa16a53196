import { model } from "@camped-ai/framework/utils";

/**
 * Enum for occupancy types
 */
export enum OccupancyConfigTypeEnum {
  BASE_1 = "BASE_1",
  BASE_2 = "BASE_2",
  EXTRA_ADULT = "EXTRA_ADULT",
  CHILD = "CHILD",
  INFANT = "INFANT",
  CUSTOM = "CUSTOM",
}

/**
 * OccupancyConfig model
 *
 * Defines a specific occupancy type configuration for a hotel.
 */
const OccupancyConfig = model.define("occupancy_config", {
  id: model.id({ prefix: "occ" }).primaryKey(),

  // Basic information
  name: model.text(),                        // Display name (e.g., "Adult", "Child 2-12", "Infant")
  type: model.enum(OccupancyConfigTypeEnum), // Type of occupant (BASE_1, BASE_2, EXTRA_ADULT, CHILD, INFANT, CUSTOM)

  // Hotel this configuration belongs to
  hotel_id: model.text(),

  // Age range
  min_age: model.number(),                   // Minimum age for this occupancy type
  max_age: model.number(),                   // Maximum age for this occupancy type

  // Occupancy settings
  min_occupancy: model.number().default(1),  // Minimum number of this type of occupant
  max_occupancy: model.number().default(1),  // Maximum number of this type of occupant

  // Default flag
  is_default: model.boolean().default(false), // Whether this is a default occupancy type

  // Metadata for additional configuration
  metadata: model.json().nullable(),
})
.indexes([
  {
    on: ["hotel_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["hotel_id", "type"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["hotel_id", "min_age", "max_age"],
    where: "deleted_at IS NULL",
  },
]);

export default OccupancyConfig;
