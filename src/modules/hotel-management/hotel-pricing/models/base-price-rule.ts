import { model } from "@camped-ai/framework/utils";
import SeasonalPriceRule from "./seasonal-price-rule";
import ChannelPriceOverride from "./channel-price-override";

/**
 * BasePriceRule model
 *
 * Defines the base price for a room configuration with specific occupancy and meal plan,
 * including weekday-specific pricing.
 */
const BasePriceRule = model.define("base_price_rule", {
  id: model.id({ prefix: "bpr" }).primaryKey(),

  // Core pricing fields
  amount: model.number(),                  // Base price in smallest currency unit (e.g., cents)
  currency_code: model.text().nullable(),   // Inherits hotel's currency if null
  description: model.text().nullable(),     // Optional notes

  // Foreign keys
  hotel_id: model.text(),                  // Hotel this price rule belongs to
  room_config_id: model.text(),            // Room configuration this price rule applies to
  occupancy_type_id: model.text(),         // Occupancy type (e.g., base, extra adult, child)
  meal_plan_id: model.text().nullable(),   // Meal plan (e.g., BB, HB, FB) - nullable for backward compatibility

  // Occupancy constraints
  min_occupancy: model.number().default(1), // Minimum occupancy this price applies to
  max_occupancy: model.number().nullable(), // Maximum occupancy (null means no upper limit)

  // Weekday pricing - prices for specific days of the week
  monday_price: model.number().nullable(),  // Price for Monday (null means use base amount)
  tuesday_price: model.number().nullable(), // Price for Tuesday (null means use base amount)
  wednesday_price: model.number().nullable(), // Price for Wednesday (null means use base amount)
  thursday_price: model.number().nullable(), // Price for Thursday (null means use base amount)
  friday_price: model.number().nullable(),  // Price for Friday (null means use base amount)
  saturday_price: model.number().nullable(), // Price for Saturday (null means use base amount)
  sunday_price: model.number().nullable(),  // Price for Sunday (null means use base amount)

  // Relationships
  seasonal_rules: model.hasMany(() => SeasonalPriceRule, {
    mappedBy: "base_price_rule_id",
  }),

  channel_overrides: model.hasMany(() => ChannelPriceOverride, {
    mappedBy: "base_price_rule_id",
  }),

  // Metadata for additional configuration
  metadata: model.json().nullable(),
})
.indexes([
  {
    on: ["hotel_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["room_config_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["occupancy_type_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["meal_plan_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["room_config_id", "occupancy_type_id", "meal_plan_id"],
    where: "deleted_at IS NULL",
    unique: true,
  },
]);

export default BasePriceRule;
