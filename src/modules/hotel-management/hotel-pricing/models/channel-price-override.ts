import { model } from "@camped-ai/framework/utils";
import BasePriceRule from "./base-price-rule";

/**
 * ChannelPriceOverride model
 *
 * Defines price overrides for specific sales channels.
 */
const ChannelPriceOverride = model.define("channel_price_override", {
  id: model.id({ prefix: "cpo" }).primaryKey(),

  // Price override
  amount: model.number(),                  // Override price in smallest currency unit
  currency_code: model.text().nullable(),   // Can be null to use base price currency

  // Foreign keys
  sales_channel_id: model.text(),          // Sales channel this override applies to

  // Relationships
  base_price_rule: model.belongsTo(() => BasePriceRule, {
    joinColumn: "base_price_rule_id",
  }),

  // Metadata for additional configuration
  metadata: model.json().nullable(),
})
.indexes([
  {
    on: ["base_price_rule_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["sales_channel_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["base_price_rule_id", "sales_channel_id"],
    where: "deleted_at IS NULL",
    unique: true,
  },
]);

export default ChannelPriceOverride;
