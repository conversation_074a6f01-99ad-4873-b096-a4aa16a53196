import { Migration } from '@mikro-orm/migrations';

export class Migration20250502000000 extends Migration {

  override async up(): Promise<void> {
    // Drop the room_availability table as it's no longer used
    this.addSql(`drop table if exists "room_availability" cascade;`);
    
    // Drop the room_status_enum type if it exists
    this.addSql(`drop type if exists "room_status_enum";`);
  }

  override async down(): Promise<void> {
    // Re-create the room_status_enum type
    this.addSql(`
      CREATE TYPE IF NOT EXISTS "room_status_enum" AS ENUM (
        'available', 
        'booked', 
        'maintenance', 
        'reserved',
        'cleaning'
      );
    `);

    // Re-create the room_availability table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "room_availability" (
        "id" text NOT NULL,
        "room_id" text NOT NULL,
        "start_date" timestamptz NOT NULL,
        "end_date" timestamptz NOT NULL,
        "status" "room_status_enum" NOT NULL DEFAULT 'available',
        "price_override" numeric NULL,
        "metadata" jsonb NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "room_availability_pkey" PRIMARY KEY ("id")
      );
    `);

    // Re-create indexes
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_availability_room_id" ON "room_availability" ("room_id");`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_availability_start_date" ON "room_availability" ("start_date");`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_availability_end_date" ON "room_availability" ("end_date");`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_availability_deleted_at" ON "room_availability" (deleted_at) WHERE deleted_at IS NULL;`);
  }
}
