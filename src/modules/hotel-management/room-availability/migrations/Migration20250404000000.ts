import { Migration } from "@camped-ai/framework/utils";

export default Migration({
  name: "RoomAvailability",
  async up(db) {
    await db.schema.createEnum("room_status_enum", [
      "available",
      "booked",
      "maintenance",
      "blocked",
      "cleaning"
    ]);

    await db.schema.createTable("room_availability", (table) => {
      table.string("id").primary();
      table.string("room_id").notNullable();
      table.dateTime("start_date").notNullable();
      table.dateTime("end_date").notNullable();
      table.enum("status", ["available", "booked", "maintenance", "reserved", "cleaning"]).defaultTo("available").notNullable();
      table.decimal("price_override", 10, 2).nullable();
      table.jsonb("metadata").nullable();
      table.timestamps(true, true);

      // Add foreign key constraint
      table.foreign("room_id").references("id").inTable("product_variant").onDelete("CASCADE");

      // Add indexes
      table.index("room_id");
      table.index("start_date");
      table.index("end_date");
    });
  },

  async down(db) {
    await db.schema.dropTable("room_availability");
    await db.schema.dropEnum("room_status_enum");
  }
});
