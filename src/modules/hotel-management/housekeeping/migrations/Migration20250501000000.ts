import { Migration } from "@camped-ai/framework/utils";

export default Migration({
  name: "HousekeepingModule",
  async up(db) {
    // Create housekeeping_task table
    await db.schema.createTable("housekeeping_task", (table) => {
      table.string("id").primary();
      
      // Room information
      table.string("room_id").index();
      table.string("room_config_id").index();
      table.string("hotel_id").index();
      
      // Task details
      table.string("task_type").defaultTo("cleaning");
      table.enum("status", ["pending", "in_progress", "completed", "verified", "cancelled"]).defaultTo("pending");
      table.enum("priority", ["low", "medium", "high", "urgent"]).defaultTo("medium");
      table.enum("room_status", ["dirty", "cleaning", "clean", "inspected", "out_of_service"]).defaultTo("dirty");
      
      // Scheduling
      table.dateTime("scheduled_date").notNullable();
      table.string("due_time").nullable();
      
      // Assignment
      table.string("assigned_to").nullable();
      table.dateTime("assigned_at").nullable();
      
      // Completion
      table.dateTime("started_at").nullable();
      table.dateTime("completed_at").nullable();
      table.dateTime("verified_at").nullable();
      table.string("verified_by").nullable();
      
      // Additional information
      table.text("notes").nullable();
      table.boolean("guest_request").defaultTo(false);
      table.boolean("recurring").defaultTo(false);
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
    
    // Create housekeeping_staff table
    await db.schema.createTable("housekeeping_staff", (table) => {
      table.string("id").primary();
      
      // Staff information
      table.string("name").notNullable();
      table.string("email").nullable();
      table.string("phone").nullable();
      table.enum("role", ["housekeeper", "supervisor", "manager", "maintenance"]).defaultTo("housekeeper");
      
      // Work details
      table.string("hotel_id").index();
      table.boolean("is_active").defaultTo(true);
      table.integer("max_rooms_per_shift").defaultTo(15);
      
      // Schedule
      table.specificType("working_days", "text[]").defaultTo("{}");
      table.string("shift").defaultTo("morning");
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
    
    // Create housekeeping_assignment table
    await db.schema.createTable("housekeeping_assignment", (table) => {
      table.string("id").primary();
      
      // Staff information
      table.string("staff_id").index();
      table.string("staff_name");
      
      // Assignment details
      table.dateTime("date").notNullable();
      table.string("shift").defaultTo("morning");
      
      // Workload
      table.integer("max_rooms").defaultTo(15);
      table.integer("assigned_rooms_count").defaultTo(0);
      
      // Status
      table.boolean("is_active").defaultTo(true);
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
    
    // Create housekeeping_checklist table
    await db.schema.createTable("housekeeping_checklist", (table) => {
      table.string("id").primary();
      
      // Checklist information
      table.string("name").notNullable();
      table.text("description").nullable();
      
      // Applicability
      table.string("hotel_id").index();
      table.string("room_config_id").nullable();
      table.string("task_type").defaultTo("cleaning");
      
      // Status
      table.boolean("is_active").defaultTo(true);
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
    
    // Create housekeeping_checklist_item table
    await db.schema.createTable("housekeeping_checklist_item", (table) => {
      table.string("id").primary();
      
      // Checklist information
      table.string("checklist_id").index();
      
      // Item details
      table.string("name").notNullable();
      table.text("description").nullable();
      table.integer("order").defaultTo(0);
      table.boolean("is_required").defaultTo(true);
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
    
    // Create housekeeping_task_completion table
    await db.schema.createTable("housekeeping_task_completion", (table) => {
      table.string("id").primary();
      
      // Task information
      table.string("task_id").index();
      
      // Completion details
      table.string("completed_by").notNullable();
      table.dateTime("completed_at").notNullable();
      table.integer("duration_minutes").defaultTo(0);
      
      // Checklist completion
      table.string("checklist_id").nullable();
      table.specificType("completed_items", "text[]").defaultTo("{}");
      table.specificType("skipped_items", "text[]").defaultTo("{}");
      
      // Issues
      table.boolean("issues_found").defaultTo(false);
      table.text("issue_description").nullable();
      table.boolean("requires_maintenance").defaultTo(false);
      
      // Verification
      table.boolean("verified").defaultTo(false);
      table.string("verified_by").nullable();
      table.dateTime("verified_at").nullable();
      table.text("verification_notes").nullable();
      
      // Additional information
      table.text("notes").nullable();
      table.specificType("photos", "text[]").nullable();
      
      // Metadata
      table.jsonb("metadata").nullable();
      
      // Timestamps
      table.timestamps(true, true);
    });
  },
  
  async down(db) {
    // Drop tables in reverse order
    await db.schema.dropTableIfExists("housekeeping_task_completion");
    await db.schema.dropTableIfExists("housekeeping_checklist_item");
    await db.schema.dropTableIfExists("housekeeping_checklist");
    await db.schema.dropTableIfExists("housekeeping_assignment");
    await db.schema.dropTableIfExists("housekeeping_staff");
    await db.schema.dropTableIfExists("housekeeping_task");
  }
});
