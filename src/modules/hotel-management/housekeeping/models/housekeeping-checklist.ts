import { model } from "@camped-ai/framework/utils";

export const HousekeepingChecklistItem = model.define("housekeeping_checklist_item", {
  id: model.id({ prefix: "hk_check_item" }).primaryKey(),

  // Checklist information
  checklist_id: model.text().index(), // Reference to the checklist

  // Item details
  name: model.text(),
  description: model.text().nullable(),
  order: model.number().default(0),
  is_required: model.boolean().default(true),

  // Metadata
  metadata: model.json().nullable(),
});

export const HousekeepingChecklist = model.define("housekeeping_checklist", {
  id: model.id({ prefix: "hk_check" }).primaryKey(),

  // Checklist information
  name: model.text(),
  description: model.text().nullable(),

  // Applicability
  hotel_id: model.text().index(), // Reference to the hotel
  room_config_id: model.text().nullable(), // If specific to a room type
  task_type: model.text().default("cleaning"), // cleaning, turndown, inspection, etc.

  // Status
  is_active: model.boolean().default(true),

  // Items - we'll use a custom query instead of a direct relationship

  // Metadata
  metadata: model.json().nullable(),
});
