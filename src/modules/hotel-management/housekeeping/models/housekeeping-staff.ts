import { model } from "@camped-ai/framework/utils";
import { HousekeepingAssignment } from "./housekeeping-assignment";

export enum StaffRole {
  HOUSEKEEPER = "housekeeper",
  SUPERVISOR = "supervisor",
  MANAGER = "manager",
  MAINTENANCE = "maintenance"
}

export const HousekeepingStaff = model.define("housekeeping_staff", {
  id: model.id({ prefix: "hk_staff" }).primaryKey(),

  // Staff information
  name: model.text(),
  email: model.text().nullable(),
  phone: model.text().nullable(),
  role: model.enum(StaffRole).default(StaffRole.HOUSEKEEPER),

  // Work details
  hotel_id: model.text().index(), // Reference to the hotel
  is_active: model.boolean().default(true),
  max_rooms_per_shift: model.number().default(15),

  // Schedule
  working_days: model.array().default(["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]),
  shift: model.text().default("morning"), // morning, afternoon, evening, night

  // Assignments - we'll use a custom query instead of a direct relationship

  // Metadata
  metadata: model.json().nullable(),
});
