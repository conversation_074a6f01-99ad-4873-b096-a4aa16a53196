import { model } from "@camped-ai/framework/utils";
import { HousekeepingTask } from "./housekeeping-task";

export const HousekeepingAssignment = model.define("housekeeping_assignment", {
  id: model.id({ prefix: "hk_assign" }).primaryKey(),

  // Staff information
  staff_id: model.text().index(), // Reference to the staff member
  staff_name: model.text(), // Name of the staff member

  // Assignment details
  date: model.dateTime(), // Date of assignment
  shift: model.text().default("morning"), // morning, afternoon, evening, night

  // Workload
  max_rooms: model.number().default(15), // Maximum number of rooms to assign
  assigned_rooms_count: model.number().default(0), // Number of rooms assigned

  // Status
  is_active: model.boolean().default(true), // Whether the assignment is active

  // Tasks - we'll use a custom query instead of a direct relationship
  // since the tasks are linked through staff_id, not directly to the assignment

  // Metadata
  metadata: model.json().nullable(),
});
