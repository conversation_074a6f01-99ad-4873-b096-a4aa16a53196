import { model } from "@camped-ai/framework/utils";

export const HousekeepingTaskCompletion = model.define("housekeeping_task_completion", {
  id: model.id({ prefix: "hk_completion" }).primaryKey(),
  
  // Task information
  task_id: model.text().index(), // Reference to the housekeeping task
  
  // Completion details
  completed_by: model.text(), // Staff ID who completed the task
  completed_at: model.dateTime(),
  duration_minutes: model.number().default(0),
  
  // Checklist completion
  checklist_id: model.text().nullable(), // Reference to the checklist used
  completed_items: model.array().default([]), // Array of completed checklist item IDs
  skipped_items: model.array().default([]), // Array of skipped checklist item IDs
  
  // Issues
  issues_found: model.boolean().default(false),
  issue_description: model.text().nullable(),
  requires_maintenance: model.boolean().default(false),
  
  // Verification
  verified: model.boolean().default(false),
  verified_by: model.text().nullable(),
  verified_at: model.dateTime().nullable(),
  verification_notes: model.text().nullable(),
  
  // Additional information
  notes: model.text().nullable(),
  photos: model.array().nullable(), // Array of photo URLs
  
  // Metadata
  metadata: model.json().nullable(),
});
