import { model } from "@camped-ai/framework/utils";

export enum HousekeepingTaskStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  VERIFIED = "verified",
  CANCELLED = "cancelled"
}

export enum HousekeepingTaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent"
}

export enum RoomCleaningStatus {
  DIRTY = "dirty",           // Room needs cleaning
  CLEANING = "cleaning",     // Room is being cleaned
  CLEAN = "clean",           // Room has been cleaned but not inspected
  INSPECTED = "inspected",   // Room has been inspected and is ready
  OUT_OF_SERVICE = "out_of_service" // Room is not available for cleaning/booking
}

export const HousekeepingTask = model.define("housekeeping_task", {
  id: model.id({ prefix: "hk_task" }).primaryKey(),
  
  // Room information
  room_id: model.text().index(), // Reference to the product variant (room)
  room_config_id: model.text().index(), // Reference to the room configuration
  hotel_id: model.text().index(), // Reference to the hotel
  
  // Task details
  task_type: model.text().default("cleaning"), // cleaning, turndown, inspection, maintenance, etc.
  status: model.enum(HousekeepingTaskStatus).default(HousekeepingTaskStatus.PENDING),
  priority: model.enum(HousekeepingTaskPriority).default(HousekeepingTaskPriority.MEDIUM),
  room_status: model.enum(RoomCleaningStatus).default(RoomCleaningStatus.DIRTY),
  
  // Scheduling
  scheduled_date: model.dateTime(),
  due_time: model.text().nullable(), // Time when the task should be completed (HH:MM)
  
  // Assignment
  assigned_to: model.text().nullable(), // Staff member ID
  assigned_at: model.dateTime().nullable(),
  
  // Completion
  started_at: model.dateTime().nullable(),
  completed_at: model.dateTime().nullable(),
  verified_at: model.dateTime().nullable(),
  verified_by: model.text().nullable(),
  
  // Additional information
  notes: model.text().nullable(),
  guest_request: model.boolean().default(false), // Whether this was requested by a guest
  recurring: model.boolean().default(false), // Whether this is a recurring task
  
  // Metadata
  metadata: model.json().nullable(),
});
