import { MedusaService, Modules } from "@camped-ai/framework/utils";
import {
  HousekeepingTask,
  HousekeepingTaskStatus,
  RoomCleaningStatus,
  HousekeepingTaskPriority
} from "./models/housekeeping-task";
import { HousekeepingAssignment } from "./models/housekeeping-assignment";
import { HousekeepingStaff } from "./models/housekeeping-staff";
import { HousekeepingChecklist, HousekeepingChecklistItem } from "./models/housekeeping-checklist";
import { HousekeepingTaskCompletion } from "./models/housekeeping-task-completion";
import { startOfDay, endOfDay, format } from "date-fns";

class HousekeepingModuleService extends MedusaService({
  HousekeepingTask,
  HousekeepingAssignment,
  HousekeepingStaff,
  HousekeepingChecklist,
  HousekeepingChecklistItem,
  HousekeepingTaskCompletion,
}) {
  constructor(container, options) {
    super(container);
    try {
      // Try to resolve services if they exist
      if (container.hasRegistration("productVariantService")) {
        this.productVariantService = container.resolve("productVariantService");
      }

      if (container.hasRegistration("room_inventory")) {
        this.roomInventoryService = container.resolve("room_inventory");
      }
    } catch (error) {
      console.warn("Some services could not be resolved:", error.message);
    }
  }

  /**
   * Get tasks for an assignment
   */
  async getTasksForAssignment(assignmentId, options = {}) {
    try {
      // First get the assignment to get the staff_id
      const assignment = await this.retrieveHousekeepingAssignment(assignmentId);

      if (!assignment) {
        throw new Error(`Assignment with ID ${assignmentId} not found`);
      }

      // Then get all tasks assigned to this staff member on the assignment date
      const assignmentDate = format(new Date(assignment.date), "yyyy-MM-dd");

      const filters = {
        assigned_to: assignment.staff_id,
        scheduled_date: assignmentDate,
        ...options.filters,
      };

      return await this.listHousekeepingTasks(filters, options);
    } catch (error) {
      throw new Error(`Failed to get tasks for assignment: ${error.message}`);
    }
  }

  /**
   * Create a new housekeeping task
   */
  async createHousekeepingTask(data) {
    try {
      return await this.create(HousekeepingTask, {
        room_id: data.room_id,
        room_config_id: data.room_config_id,
        hotel_id: data.hotel_id,
        task_type: data.task_type || "cleaning",
        status: data.status || HousekeepingTaskStatus.PENDING,
        priority: data.priority || HousekeepingTaskPriority.MEDIUM,
        room_status: data.room_status || RoomCleaningStatus.DIRTY,
        scheduled_date: data.scheduled_date,
        due_time: data.due_time,
        assigned_to: data.assigned_to,
        assigned_at: data.assigned_to ? new Date() : null,
        notes: data.notes,
        guest_request: data.guest_request || false,
        recurring: data.recurring || false,
        metadata: data.metadata || {},
      });
    } catch (error) {
      throw new Error(`Failed to create housekeeping task: ${error.message}`);
    }
  }

  /**
   * Update a housekeeping task
   */
  async updateHousekeepingTask(id, data) {
    try {
      return await this.update(HousekeepingTask, id, data);
    } catch (error) {
      throw new Error(`Failed to update housekeeping task: ${error.message}`);
    }
  }

  /**
   * Get housekeeping tasks for a hotel
   */
  async getHousekeepingTasksByHotel(hotelId, options = {}) {
    try {
      const filters = {
        hotel_id: hotelId,
        ...options.filters,
      };

      return await this.listHousekeepingTasks(filters, options);
    } catch (error) {
      throw new Error(`Failed to get housekeeping tasks: ${error.message}`);
    }
  }

  /**
   * Get housekeeping tasks for a room
   */
  async getHousekeepingTasksByRoom(roomId, options = {}) {
    try {
      const filters = {
        room_id: roomId,
        ...options.filters,
      };

      return await this.listHousekeepingTasks(filters, options);
    } catch (error) {
      throw new Error(`Failed to get housekeeping tasks: ${error.message}`);
    }
  }

  /**
   * Get housekeeping tasks for a date range
   */
  async getHousekeepingTasksByDateRange(hotelId, startDate, endDate, options = {}) {
    try {
      const filters = {
        hotel_id: hotelId,
        scheduled_date: {
          gte: startOfDay(new Date(startDate)),
          lte: endOfDay(new Date(endDate)),
        },
        ...options.filters,
      };

      return await this.listHousekeepingTasks(filters, options);
    } catch (error) {
      throw new Error(`Failed to get housekeeping tasks: ${error.message}`);
    }
  }

  /**
   * Assign a task to a staff member
   */
  async assignTask(taskId, staffId) {
    try {
      const task = await this.retrieveHousekeepingTask(taskId);
      const staff = await this.retrieveHousekeepingStaff(staffId);

      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      if (!staff) {
        throw new Error(`Staff with ID ${staffId} not found`);
      }

      return await this.updateHousekeepingTask(taskId, {
        assigned_to: staffId,
        assigned_at: new Date(),
        status: HousekeepingTaskStatus.PENDING,
      });
    } catch (error) {
      throw new Error(`Failed to assign task: ${error.message}`);
    }
  }

  /**
   * Start a housekeeping task
   */
  async startTask(taskId, staffId) {
    try {
      const task = await this.retrieveHousekeepingTask(taskId);

      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      if (task.assigned_to !== staffId) {
        throw new Error(`Task is not assigned to staff member ${staffId}`);
      }

      // Update room status to cleaning
      if (task.room_id) {
        await this.updateRoomStatus(task.room_id, RoomCleaningStatus.CLEANING);
      }

      return await this.updateHousekeepingTask(taskId, {
        status: HousekeepingTaskStatus.IN_PROGRESS,
        started_at: new Date(),
        room_status: RoomCleaningStatus.CLEANING,
      });
    } catch (error) {
      throw new Error(`Failed to start task: ${error.message}`);
    }
  }

  /**
   * Complete a housekeeping task
   */
  async completeTask(taskId, staffId, completionData) {
    try {
      const task = await this.retrieveHousekeepingTask(taskId);

      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      if (task.assigned_to !== staffId) {
        throw new Error(`Task is not assigned to staff member ${staffId}`);
      }

      // Create task completion record
      const taskCompletion = await this.create(HousekeepingTaskCompletion, {
        task_id: taskId,
        completed_by: staffId,
        completed_at: new Date(),
        duration_minutes: completionData.duration_minutes || 0,
        checklist_id: completionData.checklist_id,
        completed_items: completionData.completed_items || [],
        skipped_items: completionData.skipped_items || [],
        issues_found: completionData.issues_found || false,
        issue_description: completionData.issue_description,
        requires_maintenance: completionData.requires_maintenance || false,
        notes: completionData.notes,
        photos: completionData.photos,
      });

      // Update room status to clean
      if (task.room_id) {
        await this.updateRoomStatus(task.room_id, RoomCleaningStatus.CLEAN);
      }

      // Update task status
      return await this.updateHousekeepingTask(taskId, {
        status: HousekeepingTaskStatus.COMPLETED,
        completed_at: new Date(),
        room_status: RoomCleaningStatus.CLEAN,
      });
    } catch (error) {
      throw new Error(`Failed to complete task: ${error.message}`);
    }
  }

  /**
   * Verify a completed housekeeping task
   */
  async verifyTask(taskId, supervisorId, verificationData) {
    try {
      const task = await this.retrieveHousekeepingTask(taskId);

      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      if (task.status !== HousekeepingTaskStatus.COMPLETED) {
        throw new Error(`Task with ID ${taskId} is not completed`);
      }

      // Get the task completion record
      const taskCompletion = await this.listHousekeepingTaskCompletions({
        task_id: taskId,
      }, { limit: 1 });

      if (taskCompletion.length === 0) {
        throw new Error(`Task completion record not found for task ${taskId}`);
      }

      // Update task completion record
      await this.update(HousekeepingTaskCompletion, taskCompletion[0].id, {
        verified: true,
        verified_by: supervisorId,
        verified_at: new Date(),
        verification_notes: verificationData.notes,
      });

      // Update room status to inspected
      if (task.room_id) {
        await this.updateRoomStatus(task.room_id, RoomCleaningStatus.INSPECTED);
      }

      // Update task status
      return await this.updateHousekeepingTask(taskId, {
        status: HousekeepingTaskStatus.VERIFIED,
        verified_at: new Date(),
        verified_by: supervisorId,
        room_status: RoomCleaningStatus.INSPECTED,
      });
    } catch (error) {
      throw new Error(`Failed to verify task: ${error.message}`);
    }
  }

  /**
   * Cancel a housekeeping task
   */
  async cancelTask(taskId, reason) {
    try {
      const task = await this.retrieveHousekeepingTask(taskId);

      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      return await this.updateHousekeepingTask(taskId, {
        status: HousekeepingTaskStatus.CANCELLED,
        notes: reason ? `${task.notes || ''}\nCancelled: ${reason}` : task.notes,
      });
    } catch (error) {
      throw new Error(`Failed to cancel task: ${error.message}`);
    }
  }

  /**
   * Update room cleaning status
   */
  async updateRoomStatus(roomId, status) {
    try {
      // Update room inventory status
      const today = format(new Date(), "yyyy-MM-dd");

      // Check if we have the room inventory service
      if (this.roomInventoryService) {
        // Find existing inventory for today
        const inventory = await this.roomInventoryService.listRoomInventories({
          inventory_item_id: roomId,
          from_date: today,
          to_date: today,
        });

        if (inventory.length > 0) {
          // Update existing inventory
          await this.roomInventoryService.updateRoomInventory(inventory[0].id, {
            status: status,
          });
        } else {
          // Create new inventory
          await this.roomInventoryService.createRoomInventory({
            inventory_item_id: roomId,
            from_date: today,
            to_date: today,
            status: status,
          });
        }
      }

      return true;
    } catch (error) {
      throw new Error(`Failed to update room status: ${error.message}`);
    }
  }

  /**
   * Get assignments for a staff member
   */
  async getAssignmentsForStaff(staffId, options = {}) {
    try {
      const filters = {
        staff_id: staffId,
        ...options.filters,
      };

      return await this.listHousekeepingAssignments(filters, options);
    } catch (error) {
      throw new Error(`Failed to get assignments for staff: ${error.message}`);
    }
  }

  /**
   * Create a housekeeping staff member
   */
  async createHousekeepingStaff(data) {
    try {
      return await this.create(HousekeepingStaff, {
        name: data.name,
        email: data.email,
        phone: data.phone,
        role: data.role,
        hotel_id: data.hotel_id,
        is_active: data.is_active !== undefined ? data.is_active : true,
        max_rooms_per_shift: data.max_rooms_per_shift || 15,
        working_days: data.working_days || ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        shift: data.shift || "morning",
        metadata: data.metadata || {},
      });
    } catch (error) {
      throw new Error(`Failed to create housekeeping staff: ${error.message}`);
    }
  }

  /**
   * Get items for a checklist
   */
  async getChecklistItems(checklistId, options = {}) {
    try {
      const filters = {
        checklist_id: checklistId,
        ...options.filters,
      };

      return await this.listHousekeepingChecklistItems(filters, options);
    } catch (error) {
      throw new Error(`Failed to get checklist items: ${error.message}`);
    }
  }

  /**
   * Create a housekeeping checklist
   */
  async createHousekeepingChecklist(data) {
    try {
      const checklist = await this.create(HousekeepingChecklist, {
        name: data.name,
        description: data.description,
        hotel_id: data.hotel_id,
        room_config_id: data.room_config_id,
        task_type: data.task_type || "cleaning",
        is_active: data.is_active !== undefined ? data.is_active : true,
        metadata: data.metadata || {},
      });

      // Create checklist items if provided
      if (data.items && Array.isArray(data.items)) {
        for (const item of data.items) {
          await this.create(HousekeepingChecklistItem, {
            checklist_id: checklist.id,
            name: item.name,
            description: item.description,
            order: item.order || 0,
            is_required: item.is_required !== undefined ? item.is_required : true,
            metadata: item.metadata || {},
          });
        }
      }

      return checklist;
    } catch (error) {
      throw new Error(`Failed to create housekeeping checklist: ${error.message}`);
    }
  }

  /**
   * Generate housekeeping tasks for a hotel
   * This can be used to automatically create tasks for rooms that need cleaning
   */
  async generateHousekeepingTasks(hotelId, date = new Date()) {
    try {
      const formattedDate = format(date, "yyyy-MM-dd");

      // Get all rooms for the hotel
      let rooms = [];
      try {
        if (this.container.hasRegistration(Modules.PRODUCT)) {
          const productModuleService = this.container.resolve(Modules.PRODUCT);
          rooms = await productModuleService.listProductVariants({
            metadata: {
              hotel_id: hotelId,
            },
          });
        } else {
          console.warn("Product module not available. Cannot fetch rooms.");
          return [];
        }
      } catch (error) {
        console.error("Error fetching rooms:", error.message);
        return [];
      }

      const tasks = [];

      // For each room, check if it needs cleaning
      for (const room of rooms) {
        // Check if there's already a task for this room on this date
        const existingTasks = await this.listHousekeepingTasks({
          room_id: room.id,
          scheduled_date: formattedDate,
        });

        if (existingTasks.length > 0) {
          // Skip if there's already a task
          continue;
        }

        // Check room status (if it's occupied, it needs cleaning)
        // This would typically come from your booking system
        // For now, we'll assume all rooms need cleaning
        const task = await this.create(HousekeepingTask, {
          room_id: room.id,
          room_config_id: room.product_id, // Assuming product_id is the room_config_id
          hotel_id: hotelId,
          task_type: "cleaning",
          status: HousekeepingTaskStatus.PENDING,
          priority: HousekeepingTaskPriority.MEDIUM,
          room_status: RoomCleaningStatus.DIRTY,
          scheduled_date: date,
          due_time: "14:00", // Default checkout time
          notes: "Automatically generated task",
        });

        tasks.push(task);
      }

      return tasks;
    } catch (error) {
      throw new Error(`Failed to generate housekeeping tasks: ${error.message}`);
    }
  }

  /**
   * Get housekeeping statistics for a hotel
   */
  async getHousekeepingStats(hotelId, date = new Date()) {
    try {
      const formattedDate = format(date, "yyyy-MM-dd");

      // Get all tasks for the hotel on this date
      const tasks = await this.listHousekeepingTasks({
        hotel_id: hotelId,
        scheduled_date: formattedDate,
      });

      // Calculate statistics
      const stats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === HousekeepingTaskStatus.PENDING).length,
        in_progress: tasks.filter(t => t.status === HousekeepingTaskStatus.IN_PROGRESS).length,
        completed: tasks.filter(t => t.status === HousekeepingTaskStatus.COMPLETED).length,
        verified: tasks.filter(t => t.status === HousekeepingTaskStatus.VERIFIED).length,
        cancelled: tasks.filter(t => t.status === HousekeepingTaskStatus.CANCELLED).length,

        // Room status counts
        dirty: tasks.filter(t => t.room_status === RoomCleaningStatus.DIRTY).length,
        cleaning: tasks.filter(t => t.room_status === RoomCleaningStatus.CLEANING).length,
        clean: tasks.filter(t => t.room_status === RoomCleaningStatus.CLEAN).length,
        inspected: tasks.filter(t => t.room_status === RoomCleaningStatus.INSPECTED).length,
        out_of_service: tasks.filter(t => t.room_status === RoomCleaningStatus.OUT_OF_SERVICE).length,

        // Priority counts
        low_priority: tasks.filter(t => t.priority === HousekeepingTaskPriority.LOW).length,
        medium_priority: tasks.filter(t => t.priority === HousekeepingTaskPriority.MEDIUM).length,
        high_priority: tasks.filter(t => t.priority === HousekeepingTaskPriority.HIGH).length,
        urgent_priority: tasks.filter(t => t.priority === HousekeepingTaskPriority.URGENT).length,
      };

      return stats;
    } catch (error) {
      throw new Error(`Failed to get housekeeping statistics: ${error.message}`);
    }
  }
}

export default HousekeepingModuleService;
