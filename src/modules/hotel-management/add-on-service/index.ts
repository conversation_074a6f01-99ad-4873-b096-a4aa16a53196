import { Module } from "@camped-ai/framework/utils";
import AddOnServiceModuleService from "./service";

export const ADD_ON_SERVICE_MODULE = "add_on_service";
export const ADD_ON_SERVICE = "addOnServiceService";

// Export types
export { AddOnServiceType, AddOnServiceLevel } from "./service";

// Create the module
const addOnServiceModule = Module(ADD_ON_SERVICE_MODULE, {
  service: AddOnServiceModuleService,
});

// Export the module
export default addOnServiceModule;
