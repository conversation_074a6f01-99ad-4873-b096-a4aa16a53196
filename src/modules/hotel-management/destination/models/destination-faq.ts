import { model } from "@camped-ai/framework/utils";
import { Destination } from "./destination";

export const DestinationFaq = model.define(
  { tableName: "destination_faq", name: "DestinationFaq" },
  {
    id: model.id({ prefix: "dest_faq" }).primaryKey(),
    question: model.text(),
    answer: model.text(),
    destination: model.belongsTo(() => Destination, {
      mappedBy: "faqs",
    }),
  }
);
