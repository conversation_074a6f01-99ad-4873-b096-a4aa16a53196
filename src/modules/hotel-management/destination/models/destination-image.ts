import { model } from "@camped-ai/framework/utils";
import { Destination } from "./destination";

export const DestinationImage = model
  .define(
    { tableName: "destination_image", name: "DestinationImage" },
    {
      id: model.id({ prefix: "dest_img" }).primaryKey(),
      url: model.text(),
      metadata: model.json().nullable(),
      rank: model.number().default(0),
      destination: model.belongsTo(() => Destination, {
        mappedBy: "images",
      }),
    }
  )
  .indexes([
    {
      name: "IDX_Destination_image_url",
      on: ["url"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);