import { Migration } from '@mikro-orm/migrations';

export class Migration20250402130149 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "destination" add column if not exists "is_featured" boolean not null default false;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "destination" drop column if exists "is_featured";`);
  }

}
