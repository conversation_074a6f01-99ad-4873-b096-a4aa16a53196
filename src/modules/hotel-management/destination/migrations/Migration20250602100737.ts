import { Migration } from '@mikro-orm/migrations';

export class Migration20250602100737 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "destination_faq" ("id" text not null, "question" text not null, "answer" text not null, "destination_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "destination_faq_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_destination_faq_destination_id" ON "destination_faq" (destination_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_destination_faq_deleted_at" ON "destination_faq" (deleted_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "destination_faq" add constraint "destination_faq_destination_id_foreign" foreign key ("destination_id") references "destination" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "destination_faq" cascade;`);
  }

}
