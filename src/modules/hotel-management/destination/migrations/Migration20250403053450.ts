import { Migration } from '@mikro-orm/migrations';

export class Migration20250403053450 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "destination_image" ("id" text not null, "url" text not null, "metadata" jsonb null, "rank" integer not null default 0, "destination_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "destination_image_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_destination_image_destination_id" ON "destination_image" (destination_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_destination_image_deleted_at" ON "destination_image" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_Destination_image_url" ON "destination_image" (url) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "destination_image" add constraint "destination_image_destination_id_foreign" foreign key ("destination_id") references "destination" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "destination_image" cascade;`);
  }

}
