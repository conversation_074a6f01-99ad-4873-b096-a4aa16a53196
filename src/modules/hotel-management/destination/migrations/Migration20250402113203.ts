import { Migration } from '@mikro-orm/migrations';

export class Migration20250402113203 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "destination" ("id" text not null, "category_id" text not null, "name" text not null, "handle" text not null, "description" text null, "is_active" boolean not null default true, "country" text not null, "currency" text not null, "location" text null, "tags" jsonb null, "website" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "destination_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_destination_deleted_at" ON "destination" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "destination" cascade;`);
  }

}
