import { Migration } from '@mikro-orm/migrations';

export class Migration20250328122527 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "campaign_extension" ("id" text not null, "booking_from_date" timestamptz not null, "booking_to_date" timestamptz not null, "campaign_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "campaign_extension_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_campaign_extension_deleted_at" ON "campaign_extension" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "campaign_extension" cascade;`);
  }

}
