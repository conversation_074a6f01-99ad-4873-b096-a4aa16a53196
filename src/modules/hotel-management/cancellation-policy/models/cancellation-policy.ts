import { model } from "@camped-ai/framework/utils";

export enum RefundType {
  PERCENTAGE = "percentage",
  FIXED = "fixed",
  NO_REFUND = "no_refund"
}

export const CancellationPolicy = model.define("cancellation_policy", {
  id: model.id({ prefix: "canc_pol" }).primaryKey(),
  name: model.text(),
  description: model.text().nullable(),
  hotel_id: model.text().index(),
  days_before_checkin: model.number().default(0),
  refund_type: model.enum(RefundType).default(RefundType.PERCENTAGE),
  refund_amount: model.number().default(0),
  is_active: model.boolean().default(true),
  metadata: model.json().nullable(),
});
