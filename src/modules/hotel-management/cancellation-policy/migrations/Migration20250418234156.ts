import { Migration } from '@mikro-orm/migrations';

export class Migration20250418234156 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "cancellation_policy" ("id" text not null, "name" text not null, "description" text null, "hotel_id" text not null, "days_before_checkin" integer not null default 0, "refund_type" text check ("refund_type" in ('percentage', 'fixed', 'no_refund')) not null default 'percentage', "refund_amount" integer not null default 0, "is_active" boolean not null default true, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "cancellation_policy_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cancellation_policy_hotel_id" ON "cancellation_policy" (hotel_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_cancellation_policy_deleted_at" ON "cancellation_policy" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "cancellation_policy" cascade;`);
  }

}
