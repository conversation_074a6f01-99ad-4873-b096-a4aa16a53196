import { Module } from "@camped-ai/framework/utils";
import CancellationPolicyService from "./service";
import { RefundType } from "./models/cancellation-policy";
import { joinerConfig } from "./joiner-config";

export const CANCELLATION_POLICY_SERVICE = "cancellationPolicyService";

// Export types from cancellation policy
export { RefundType } from "./models/cancellation-policy";
export type { CancellationPolicyData } from "./service";

export default Module(CANCELLATION_POLICY_SERVICE, {
  service: CancellationPolicyService,
  joinerConfig,
});
