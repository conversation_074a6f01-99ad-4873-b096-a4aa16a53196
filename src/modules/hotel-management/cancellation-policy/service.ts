import { MedusaError, MedusaService } from "@camped-ai/framework/utils";
import { CancellationPolicy, RefundType } from "./models/cancellation-policy";
import { EntityManager } from "typeorm";

export type CancellationPolicyData = {
  id?: string;
  name: string;
  description?: string;
  hotel_id: string;
  days_before_checkin: number;
  refund_type: RefundType;
  refund_amount: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at?: Date;
  updated_at?: Date;
};

class CancellationPolicyService extends MedusaService({
  CancellationPolicy,
}) {
  protected readonly manager_: EntityManager;
  protected readonly repository_: any;

  constructor(container: any) {
    super(arguments[0]);
    this.manager_ = container.manager;
    this.repository_ = container.cancellationPolicyRepository;

    // Debug log to see what methods are available on the repository
    console.log('Repository methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.repository_)));
  }

  /**
   * Create a new cancellation policy
   */
  async createCancellationPolicy(data: CancellationPolicyData) {
    try {
      // Create the policy using the service's create method
      const policy = await this.createCancellationPolicies({
        id: data.id || `canc_pol_${Date.now()}`, // Use provided ID or generate a new one
        name: data.name,
        description: data.description || null,
        hotel_id: data.hotel_id,
        days_before_checkin: data.days_before_checkin,
        refund_type: data.refund_type,
        refund_amount: data.refund_amount,
        is_active: data.is_active !== undefined ? data.is_active : true,
        metadata: data.metadata
      });

      return policy;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create cancellation policy: ${error.message}`
      );
    }
  }

  /**
   * Update a cancellation policy
   */
  async updateCancellationPolicy(id: string, data: Partial<CancellationPolicyData>) {
    try {
      console.log('Updating policy with ID:', id);

      // First check if the policy exists
      const policies = await this.repository_.find({ id });

      if (!policies || policies.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Cancellation policy with id ${id} not found`
        );
      }

      const existingPolicy = policies[0];

      // Delete the existing policy
      await this.repository_.delete(id);

      // Create a new policy with the same ID but updated fields
      const newPolicy = {
        id: id,
        name: data.name !== undefined ? data.name : existingPolicy.name,
        description: data.description !== undefined ? data.description : existingPolicy.description,
        hotel_id: existingPolicy.hotel_id,
        days_before_checkin: data.days_before_checkin !== undefined ? data.days_before_checkin : existingPolicy.days_before_checkin,
        refund_type: data.refund_type !== undefined ? data.refund_type : existingPolicy.refund_type,
        refund_amount: data.refund_amount !== undefined ? data.refund_amount : existingPolicy.refund_amount,
        is_active: data.is_active !== undefined ? data.is_active : existingPolicy.is_active,
        metadata: data.metadata !== undefined ? data.metadata : existingPolicy.metadata,
        created_at: existingPolicy.created_at,
        updated_at: new Date()
      };

      console.log('Creating new policy with same ID:', newPolicy);

      // Create the new policy
      const result = await this.repository_.create([newPolicy]);

      console.log('Create result:', result);

      // Return the newly created policy
      if (!result || result.length === 0) {
        throw new MedusaError(
          MedusaError.Types.DB_ERROR,
          `Failed to create new policy with id ${id}`
        );
      }

      return result[0];
    } catch (error) {
      console.error('Error updating cancellation policy:', error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update cancellation policy: ${error.message}`
      );
    }
  }

  /**
   * Get a cancellation policy by ID
   */
  async getCancellationPolicy(id: string) {
    try {
      if (!id) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Cancellation policy ID is required`
        );
      }

      // Find the policy using the repository
      const policies = await this.repository_.find({
        id,
        deleted_at: null
      });

      // Return the first policy if found, otherwise throw an error
      if (!policies || policies.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Cancellation policy with id ${id} not found`
        );
      }

      return policies[0];
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Cancellation policy with id ${id} not found`
      );
    }
  }

  /**
   * List cancellation policies with optional filters
   */
  async findCancellationPolicies(selector: Partial<CancellationPolicyData> = {}, _config: any = {}) {
    try {
      // Build the query selector
      const whereClause: any = {
        deleted_at: null
      };

      // Add filters
      if (selector.hotel_id) {
        whereClause.hotel_id = selector.hotel_id;
      }

      if (selector.is_active !== undefined) {
        whereClause.is_active = selector.is_active;
      }

      // Use the repository to find the policies
      const policies = await this.repository_.find({
        where: whereClause,
        orderBy: { days_before_checkin: 'DESC' }
      });

      return policies;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list cancellation policies: ${error.message}`
      );
    }
  }

  /**
   * Delete a cancellation policy
   */
  async deleteCancellationPolicy(id: string) {
    try {
      // Find the policy directly without throwing an error if not found
      const policies = await this.repository_.find({ id });

      if (!policies || policies.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Cancellation policy with id ${id} not found`
        );
      }

      // Hard delete the policy
      await this.repository_.delete(id);

      return { id, deleted: true };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete cancellation policy: ${error.message}`
      );
    }
  }
}

export default CancellationPolicyService;
