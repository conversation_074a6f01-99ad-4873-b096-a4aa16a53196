import { Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";
import { RoomInventoryStatus } from "../models/room-inventory";
import RoomInventoryModuleService from "../service";

/**
 * Utility function to find all variants that share the same room number
 * @param container - The DI container
 * @param roomId - The ID of the room variant being updated
 * @returns Array of variant IDs that share the same room number
 */
export async function findSharedRoomVariants(
  container: any,
  roomId: string
): Promise<string[]> {
  try {
    // Get the product module service
    const productModuleService = container.resolve<IProductModuleService>(
      Modules.PRODUCT
    );

    // Get the variant details to find the room number
    const variant = await productModuleService.retrieveProductVariant(roomId);

    console.log(`Retrieved variant for room ID ${roomId}:`, variant);

    if (!variant || !variant.metadata || !variant.metadata.room_number) {
      console.log(`No room number found in metadata for variant ${roomId}`);
      return [];
    }

    const roomNumber = variant.metadata.room_number;
    console.log(`Found room number ${roomNumber} for variant ${roomId}`);

    // Find all variants with the same room number in metadata
    // We need to get all variants and filter them manually since we can't query by metadata directly
    const allVariants = await productModuleService.listProductVariants({});

    console.log(`Retrieved ${allVariants.length} total variants to search for room number ${roomNumber}`);

    // Log all variants with room numbers for debugging
    allVariants.forEach((v: any) => {
      if (v.metadata && v.metadata.room_number) {
        console.log(`Variant ${v.id} has room number: ${v.metadata.room_number}`);
      }
    });

    // Filter variants that have the same room number in metadata
    const sharedVariants = allVariants.filter((v: any) => {
      const isShared = v.metadata &&
                      v.metadata.room_number &&
                      v.metadata.room_number === roomNumber;

      if (isShared) {
        console.log(`Found shared variant ${v.id} with matching room number ${roomNumber}`);
      }

      return isShared;
    });

    console.log(`Found ${sharedVariants.length} variants with room number ${roomNumber}`);

    if (!sharedVariants || sharedVariants.length <= 1) {
      return [];
    }

    // Return all variant IDs except the one being updated
    return sharedVariants
      .filter((v: any) => v.id !== roomId)
      .map((v: any) => v.id);
  } catch (error) {
    console.error("Error finding shared room variants:", error);
    return [];
  }
}

/**
 * Updates inventory status for all variants that share the same room number
 * @param container - The DI container
 * @param roomId - The ID of the room variant being updated
 * @param startDate - The start date of the period
 * @param endDate - The end date of the period
 * @param status - The new status
 * @param notes - Optional notes about the status change
 * @param orderId - Optional order ID to associate with this inventory entry
 * @param cartId - Optional cart ID to associate with this inventory entry
 * @param createIfNotExists - Whether to create new inventory records if none exist
 */
export async function updateSharedRoomInventory(
  container: any,
  roomId: string,
  startDate: Date | string,
  endDate: Date | string,
  status: string,
  notes: string | null = null,
  orderId: string | null = null,
  cartId: string | null = null,
  createIfNotExists: boolean = true
): Promise<void> {
  try {
    console.log(`Starting updateSharedRoomInventory for room ${roomId} with status ${status}`);
    console.log(`Date range: ${startDate} to ${endDate}`);
    console.log(`createIfNotExists: ${createIfNotExists}`);

    // Find all variants that share the same room number
    console.log(`Finding shared room variants for room ${roomId}...`);
    const sharedVariantIds = await findSharedRoomVariants(container, roomId);

    console.log(`Shared variant IDs:`, sharedVariantIds);

    if (sharedVariantIds.length === 0) {
      console.log(`No shared variants found for room ${roomId}, exiting.`);
      return;
    }

    console.log(`Found ${sharedVariantIds.length} shared room variants for room ${roomId}`);

    // Get the room inventory service
    const roomInventoryService = container.resolve(
      "roomInventoryService"
    ) as RoomInventoryModuleService;

    // Get the product module service to retrieve variant details
    const productModuleService = container.resolve(
      Modules.PRODUCT
    ) as IProductModuleService;

    // Update inventory for all shared variants
    for (const variantId of sharedVariantIds) {
      try {
        console.log(`Updating inventory for shared room variant ${variantId}`);

        try {
          // First try to update existing inventory records
          await roomInventoryService.updateInventoryStatus(
            variantId,
            startDate,
            endDate,
            status,
            `${notes || ''} (Shared room update from variant ${roomId})`,
            null, // bookingInfo
            null, // expiresAt
            orderId,
            cartId,
            true // skipSharedRooms to prevent infinite recursion
          );
        } catch (updateError) {
          // If update fails and createIfNotExists is true, create a new inventory record
          if (createIfNotExists) {
            console.log(`No existing inventory found for ${variantId}, creating new record`);
            console.log(`Update error:`, updateError);

            try {
              // Get variant details to include in the inventory record
              console.log(`Retrieving variant details for ${variantId}...`);
              const variant = await productModuleService.retrieveProductVariant(variantId);
              console.log(`Retrieved variant details:`, variant);

              // Create a new inventory record directly
              // We need to create a new record since there's no existing one to update

              // Convert dates to Date objects if they're strings
              const fromDate = typeof startDate === "string" ? new Date(startDate) : startDate;
              const toDate = typeof endDate === "string" ? new Date(endDate) : endDate;

              console.log(`Creating inventory record with dates: ${fromDate} to ${toDate}`);

              // Prepare inventory data
              const inventoryData = {
                inventory_item_id: variantId,
                from_date: fromDate,
                to_date: toDate,
                status: status,
                available_quantity: status === "available" ? 1 : 0,
                notes: `${notes || ''} (Shared room update from variant ${roomId})`,
                order_id: orderId || null,
                cart_id: cartId || null,
                check_in_time: "14:00", // Default check-in time
                check_out_time: "12:00", // Default check-out time
                is_noon_to_noon: true,
                metadata: {
                  room_number: variant?.metadata?.room_number || "",
                  floor: variant?.metadata?.floor || "",
                  hotel_id: variant?.metadata?.hotel_id || "",
                  shared_room: true,
                  original_room_id: roomId
                }
              };

              console.log(`Inventory data to create:`, inventoryData);

              // Create a new inventory entry
              try {
                console.log(`Calling createRoomInventories for ${variantId}...`);
                const result = await roomInventoryService.createRoomInventories([inventoryData]);
                console.log(`Successfully created inventory for shared room ${variantId}. Result:`, result);
              } catch (createError) {
                console.error(`Error creating inventory for shared room ${variantId}:`, createError);
                // Continue even if creation fails
              }
            } catch (variantError) {
              console.error(`Error retrieving variant ${variantId}:`, variantError);
              // Continue even if variant retrieval fails
            }
          } else {
            throw updateError;
          }
        }
      } catch (error) {
        console.error(`Error updating shared room variant ${variantId}:`, error);
        // Continue with other variants even if one fails
      }
    }
  } catch (error) {
    console.error("Error updating shared room inventory:", error);
  }
}
