import { RoomInventoryStatus } from "../models/room-inventory";

/**
 * Find an available room for a given room configuration
 * @param container - The Medusa container
 * @param roomConfigId - The ID of the room configuration
 * @param startDate - The start date of the stay
 * @param endDate - The end date of the stay
 * @returns The available room variant or null if none found
 */
export async function findAvailableRoomForConfig(
  container: any,
  roomConfigId: string,
  startDate: Date | string,
  endDate: Date | string
): Promise<any | null> {
  try {
    // Resolve necessary services
    const productService = container.resolve("productService");
    const roomInventoryService = container.resolve("roomInventoryService");

    // Get the product (room configuration)
    const roomConfig = await productService.retrieve(roomConfigId, {
      relations: ["variants"],
    });

    if (!roomConfig || !roomConfig.variants || roomConfig.variants.length === 0) {
      return null;
    }

    // Check each variant (room) for availability
    for (const variant of roomConfig.variants) {
      const availability = await roomInventoryService.checkAvailability(
        variant.id,
        startDate,
        endDate
      );

      if (availability.available) {
        return variant;
      }
    }

    // No available room found
    return null;
  } catch (error) {
    console.error("Error finding available room:", error);
    return null;
  }
}

/**
 * Find combinations of rooms that cover the entire stay when no single room is available
 * @param container - The Medusa container
 * @param roomConfigId - The ID of the room configuration
 * @param startDate - The start date of the stay
 * @param endDate - The end date of the stay
 * @returns Array of room assignments or null if no combination found
 */
export async function findRoomCombinationForStay(
  container: any,
  roomConfigId: string,
  startDate: Date | string,
  endDate: Date | string
): Promise<any[] | null> {
  try {
    // Resolve necessary services
    const productService = container.resolve("productService");
    const roomInventoryService = container.resolve("roomInventoryService");

    // Get the product (room configuration)
    const roomConfig = await productService.retrieve(roomConfigId, {
      relations: ["variants"],
    });

    if (!roomConfig || !roomConfig.variants || roomConfig.variants.length === 0) {
      return null;
    }

    // Create an array of dates for the stay
    const dates = [];
    let currentDate = new Date(startDate);
    const end = new Date(endDate);

    while (currentDate < end) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // For each date, find available rooms
    const availabilityMap: Record<string, string[]> = {};

    for (const date of dates) {
      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);

      const dateStr = date.toISOString().split('T')[0];
      availabilityMap[dateStr] = [];

      for (const variant of roomConfig.variants) {
        const availability = await roomInventoryService.checkAvailability(
          variant.id,
          date,
          nextDay
        );

        if (availability.available) {
          availabilityMap[dateStr].push(variant.id);
        }
      }

      // If no rooms available for a date, return null
      if (availabilityMap[dateStr].length === 0) {
        return null;
      }
    }

    // Find optimal room assignments with minimum switches
    return findOptimalRoomAssignments(availabilityMap, dates);
  } catch (error) {
    console.error("Error finding room combination:", error);
    return null;
  }
}

/**
 * Find the optimal room assignments with minimum room switches
 * @param availabilityMap - Map of dates to available room IDs
 * @param dates - Array of dates for the stay
 * @returns Array of room assignments
 */
function findOptimalRoomAssignments(
  availabilityMap: Record<string, string[]>,
  dates: Date[]
): any[] {
  const assignments = [];
  let currentRoom = null;
  let segmentStart = null;

  for (let i = 0; i < dates.length; i++) {
    const dateStr = dates[i].toISOString().split('T')[0];
    const availableRooms = availabilityMap[dateStr];

    // Try to keep the same room if possible
    if (currentRoom && availableRooms.includes(currentRoom)) {
      // Continue with the same room
      if (i === dates.length - 1) {
        // Last day, end the segment
        const segmentEnd = new Date(dates[i]);
        segmentEnd.setDate(segmentEnd.getDate() + 1); // End date is exclusive

        assignments.push({
          room_id: currentRoom,
          from_date: segmentStart,
          to_date: segmentEnd
        });
      }
    } else {
      // Need to switch rooms
      if (currentRoom) {
        // End the previous segment
        const segmentEnd = new Date(dates[i]);
        assignments.push({
          room_id: currentRoom,
          from_date: segmentStart,
          to_date: segmentEnd
        });
      }

      // Start a new segment
      currentRoom = availableRooms[0]; // Pick the first available room
      segmentStart = new Date(dates[i]);

      // If this is the last day, add the segment
      if (i === dates.length - 1) {
        const segmentEnd = new Date(dates[i]);
        segmentEnd.setDate(segmentEnd.getDate() + 1); // End date is exclusive

        assignments.push({
          room_id: currentRoom,
          from_date: segmentStart,
          to_date: segmentEnd
        });
      }
    }
  }

  return assignments;
}

/**
 * Reserve rooms for a booking
 * @param container - The Medusa container
 * @param roomConfigId - The ID of the room configuration
 * @param checkInDate - The check-in date
 * @param checkOutDate - The check-out date
 * @param guestName - The guest name
 * @param bookingId - Optional booking ID
 * @returns Reservation information
 */
export async function reserveRoomsForBooking(
  container: any,
  roomConfigId: string,
  checkInDate: Date | string,
  checkOutDate: Date | string,
  guestName: string,
  bookingId: string | null = null
): Promise<any> {
  try {
    const roomInventoryService = container.resolve("roomInventoryService");

    // First try to find a single room for the entire stay
    const availableRoom = await findAvailableRoomForConfig(
      container,
      roomConfigId,
      checkInDate,
      checkOutDate
    );

    // If a single room is available, reserve it
    if (availableRoom) {
      // Reserve this room in the inventory
      await roomInventoryService.updateInventoryStatus(
        availableRoom.id,
        checkInDate,
        checkOutDate,
        RoomInventoryStatus.RESERVED_UNASSIGNED,
        `Booking created (unassigned): ${guestName} | Booking ID: ${bookingId || 'N/A'} | Room Config: ${roomConfigId}`
      );

      // Return single room reservation
      return {
        type: "single_room",
        reservations: [{
          room_id: availableRoom.id,
          from_date: checkInDate,
          to_date: checkOutDate
        }],
        requires_assignment: false
      };
    }

    // If no single room is available, try to find a combination of rooms
    const roomCombination = await findRoomCombinationForStay(
      container,
      roomConfigId,
      checkInDate,
      checkOutDate
    );

    // If no combination is available, throw error
    if (!roomCombination) {
      throw new Error("No rooms available for the selected dates");
    }

    // Reserve each room segment
    for (const segment of roomCombination) {
      await roomInventoryService.updateInventoryStatus(
        segment.room_id,
        segment.from_date,
        segment.to_date,
        RoomInventoryStatus.RESERVED_UNASSIGNED,
        `Booking created (fragmented stay): ${guestName} | Booking ID: ${bookingId || 'N/A'} | Room Config: ${roomConfigId} | Segment: ${segment.from_date} to ${segment.to_date}`
      );
    }

    // Return fragmented reservation
    return {
      type: "fragmented",
      reservations: roomCombination,
      requires_assignment: true
    };
  } catch (error) {
    console.error("Error reserving rooms for booking:", error);
    throw error;
  }
}
