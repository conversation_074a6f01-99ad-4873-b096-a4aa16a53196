import { Migration } from '@mikro-orm/migrations';

export class Migration20250328104752 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "room_inventory" ("id" text not null, "inventory_item_id" text not null, "date" timestamptz not null, "available_quantity" integer not null default 1, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "room_inventory_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_room_inventory_deleted_at" ON "room_inventory" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "room_inventory" cascade;`);
  }

}
