{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "inventory_item_id": {"name": "inventory_item_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "from_date": {"name": "from_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "to_date": {"name": "to_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "check_in_time": {"name": "check_in_time", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'12:00'", "mappedType": "text"}, "check_out_time": {"name": "check_out_time", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'12:00'", "mappedType": "text"}, "is_noon_to_noon": {"name": "is_noon_to_noon", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "available_quantity": {"name": "available_quantity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "room_inventory", "schema": "public", "indexes": [{"keyName": "IDX_room_inventory_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_room_inventory_deleted_at\" ON \"room_inventory\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "room_inventory_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}], "nativeEnums": {}}