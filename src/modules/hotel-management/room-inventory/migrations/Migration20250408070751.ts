import { Migration } from '@mikro-orm/migrations';

export class Migration20250408070751 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "room_inventory" add column if not exists "check_in_time" text not null default '12:00', add column if not exists "check_out_time" text not null default '12:00', add column if not exists "is_noon_to_noon" boolean not null default true;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "room_inventory" drop column if exists "check_in_time", drop column if exists "check_out_time", drop column if exists "is_noon_to_noon";`);
  }

}
