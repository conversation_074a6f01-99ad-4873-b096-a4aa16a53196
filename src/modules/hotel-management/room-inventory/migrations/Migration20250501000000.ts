import { Migration } from '@mikro-orm/migrations';

export class Migration20250501000000 extends Migration {

  override async up(): Promise<void> {
    // Change from_date and to_date columns from timestamptz to date
    this.addSql(`alter table if exists "room_inventory" alter column "from_date" type date using ("from_date"::date);`);
    this.addSql(`alter table if exists "room_inventory" alter column "to_date" type date using ("to_date"::date);`);
  }

  override async down(): Promise<void> {
    // Change from_date and to_date columns back from date to timestamptz
    this.addSql(`alter table if exists "room_inventory" alter column "from_date" type timestamptz using ("from_date"::timestamptz);`);
    this.addSql(`alter table if exists "room_inventory" alter column "to_date" type timestamptz using ("to_date"::timestamptz);`);
  }

}
