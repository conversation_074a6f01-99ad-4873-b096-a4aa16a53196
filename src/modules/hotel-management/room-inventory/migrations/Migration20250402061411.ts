import { Migration } from '@mikro-orm/migrations';

export class Migration20250402061411 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "room_inventory" add column if not exists "to_date" timestamptz not null, add column if not exists "notes" text null;`);
    this.addSql(`alter table if exists "room_inventory" rename column "date" to "from_date";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "room_inventory" drop column if exists "to_date", drop column if exists "notes";`);

    this.addSql(`alter table if exists "room_inventory" rename column "from_date" to "date";`);
  }

}
