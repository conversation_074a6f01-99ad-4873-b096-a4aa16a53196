import { Migration } from '@mikro-orm/migrations';

export class Migration20250510000000 extends Migration {

  override async up(): Promise<void> {
    // Add order_id and cart_id columns to room_inventory table
    this.addSql(`
      ALTER TABLE IF EXISTS "room_inventory"
      ADD COLUMN IF NOT EXISTS "order_id" TEXT NULL,
      ADD COLUMN IF NOT EXISTS "cart_id" TEXT NULL;
    `);

    // Add indexes for the new columns
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_room_inventory_order_id"
      ON "room_inventory" ("order_id")
      WHERE "order_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_room_inventory_cart_id"
      ON "room_inventory" ("cart_id")
      WHERE "cart_id" IS NOT NULL;
    `);

    // Foreign key constraints — quote "order" table to avoid reserved keyword issues
    this.addSql(`
      ALTER TABLE IF EXISTS "room_inventory"
      ADD CONSTRAINT "room_inventory_order_id_foreign"
      FOREIGN KEY ("order_id") REFERENCES "order" ("id")
      ON UPDATE CASCADE ON DELETE SET NULL;
    `);

    this.addSql(`
      ALTER TABLE IF EXISTS "room_inventory"
      ADD CONSTRAINT "room_inventory_cart_id_foreign"
      FOREIGN KEY ("cart_id") REFERENCES "cart" ("id")
      ON UPDATE CASCADE ON DELETE SET NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop foreign key constraints
    this.addSql(`
      ALTER TABLE IF EXISTS "room_inventory"
      DROP CONSTRAINT IF EXISTS "room_inventory_order_id_foreign",
      DROP CONSTRAINT IF EXISTS "room_inventory_cart_id_foreign";
    `);

    // Drop indexes
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_room_inventory_order_id";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_room_inventory_cart_id";
    `);

    // Drop the columns
    this.addSql(`
      ALTER TABLE IF EXISTS "room_inventory"
      DROP COLUMN IF EXISTS "order_id",
      DROP COLUMN IF EXISTS "cart_id";
    `);
  }
}