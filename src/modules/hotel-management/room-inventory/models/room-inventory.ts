import { model } from "@camped-ai/framework/utils";

/**
 * Enum for room inventory status values
 */
export enum RoomInventoryStatus {
  AVAILABLE = "available",
  RESERVED = "reserved",
  BOOKED = "booked",
  MAINTENANCE = "maintenance",
  CLEANING = "cleaning",
  RESERVED_UNASSIGNED = "reserved_unassigned", // For bookings without specific room assignment
  CART_RESERVED = "cart_reserved", // For temporary cart reservations
}

export const RoomInventory = model.define("room_inventory", {
  id: model.id({ prefix: "room_avail" }).primaryKey(),
  inventory_item_id: model.text(),
  // These fields are stored as date columns in the database (not datetime)
  from_date: model.dateTime(),
  to_date: model.dateTime(),
  check_in_time: model.text().default("12:00"),
  check_out_time: model.text().default("12:00"),
  is_noon_to_noon: model.boolean().default(true),
  notes: model.text().nullable(),
  available_quantity: model.number().default(1),
  status: model.text().default(RoomInventoryStatus.AVAILABLE),
  metadata: model.json().nullable(),

  // Foreign keys
  order_id: model.text().nullable(),
  cart_id: model.text().nullable(),
})