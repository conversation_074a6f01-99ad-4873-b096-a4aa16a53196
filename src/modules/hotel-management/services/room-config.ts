import { MedusaService } from "@camped-ai/framework/utils";
import { RoomConfig } from "../models/room-config";
import { MedusaError } from "@camped-ai/framework/utils";

class RoomConfigService extends MedusaService({
  RoomConfig,
}) {
  /**
   * Create a new room configuration
   */
  async create(data: Partial<RoomConfig>): Promise<RoomConfig> {
    try {
      return await this.create({
        name: data.name,
        type: data.type,
        description: data.description,
        room_size: data.room_size,
        bed_type: data.bed_type,
        max_extra_beds: data.max_extra_beds || 0,
        max_adults: data.max_adults || 1,
        max_children: data.max_children || 0,
        max_infants: data.max_infants || 0,
        max_occupancy: data.max_occupancy || 1,
        amenities: data.amenities || [],
        hotel_id: data.hotel_id,
        metadata: data.metadata || {},
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create room configuration: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a room configuration by ID
   */
  async retrieve(id: string): Promise<RoomConfig> {
    const roomConfig = await this.retrieve(id);
    
    if (!roomConfig) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Room configuration with id ${id} not found`
      );
    }
    
    return roomConfig;
  }

  /**
   * Update a room configuration
   */
  async update(id: string, data: Partial<RoomConfig>): Promise<RoomConfig> {
    try {
      return await this.update(id, data);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update room configuration: ${error.message}`
      );
    }
  }

  /**
   * Delete a room configuration
   */
  async delete(id: string): Promise<void> {
    try {
      await this.delete(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete room configuration: ${error.message}`
      );
    }
  }

  /**
   * List room configurations
   */
  async list(selector: Record<string, any> = {}, config: Record<string, any> = {}): Promise<RoomConfig[]> {
    try {
      return await this.list(selector, config);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list room configurations: ${error.message}`
      );
    }
  }
}

export default RoomConfigService;
