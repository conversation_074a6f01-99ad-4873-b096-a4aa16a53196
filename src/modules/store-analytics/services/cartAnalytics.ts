import { PgConnectionType } from "../utils/types";

type InjectedDependencies = {
  __pg_connection__: PgConnectionType,
}
export class CartAnalyticsService {
  protected pgConnection: PgConnectionType;

  constructor({ __pg_connection__ }: InjectedDependencies) {
    this.pgConnection = __pg_connection__
  }

  async getAbandonedCarts(from?: Date, to?: Date) {

    const created = await this.pgConnection('cart').
    count('cart.id').whereBetween('cart.created_at', [from, to])
    

    const shipping = await this.pgConnection('cart_shipping_method')
    .count('cart_shipping_method.id').whereBetween('cart_shipping_method.created_at', [from,to])

    const payment = await this.pgConnection('payment_session')
    .count('payment_session.id').whereBetween('payment_session.created_at',[from,to])

    const placed  = await this.pgConnection('order')
    .count('order.id')
    .where('order.status', '=', 'pending').whereBetween('order.created_at',[from,to])
    const res1 = shipping as any;
    const res2 = payment as any;
    const res3 = placed as any;
    const res4 = created as any;

    const data = [
      {
        "value": res4[0].count,
        "name": "Cart Created",
        "fill": "#3B82F6"
      },
      {
        "value": res1[0].count,
        "name": "Shipping Created",
        "fill": "#4F46E5"
      },
      {
        "value": res2[0].count, 
        "name": "Payment Created",
        "fill": "#7C3AED"
      },
      {
        "value": res3[0].count, 
        "name": "Placed Order",
        "fill": "#9333EA"
      }
    ];
    return data;
  }

  async getCartValue(from?: Date, to?: Date){

    const value = await this.pgConnection('cart_line_item')
    .avg('unit_price')
    .whereBetween('cart_line_item.created_at',[from,to])

    const result = value as any;
    return result;
  }

 async getAbandonedCartValue(from?: Date, to?: Date) {
    const result = await this.pgConnection('cart_line_item')
    .join('cart_shipping_method','cart_shipping_method.cart_id','cart_line_item.cart_id')
    .where('cart_line_item.cart_id','<>','cart_shipping_method.cart_id')
    .avg('unit_price')
    .whereBetween('cart_line_item.created_at',[from,to])
        

    return result;
}



}
