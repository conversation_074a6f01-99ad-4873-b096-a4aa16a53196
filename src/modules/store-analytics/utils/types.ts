

import { ModulesSdkUtils } from "@camped-ai/framework/utils"

type OrdersHistory = {
  orderCount: string,
  date: string
}

export type OrdersHistoryResult = {
  dateRangeFrom?: number
  dateRangeTo?: number,
  dateRangeFromCompareTo?: number,
  dateRangeToCompareTo?: number,
  current: OrdersHistory[];
  previous: OrdersHistory[];
}

export type PgConnectionType = ReturnType<typeof ModulesSdkUtils.createPgConnection>;