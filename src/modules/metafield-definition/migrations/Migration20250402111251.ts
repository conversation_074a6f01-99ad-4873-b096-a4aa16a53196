import { Migration } from '@mikro-orm/migrations';

export class Migration20250402111251 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "metafield_definition" drop constraint if exists "metafield_definition_unique";`);
    this.addSql(`create table if not exists "metafield_definition" ("id" text not null, "owner_type" text check ("owner_type" in ('product', 'order', 'customer', 'store', 'category', 'room_config', 'hotel', 'destination')) not null, "namespace" text not null, "key" text not null, "namespace_label" text null, "label" text not null, "description" text null, "type" text check ("type" in ('text', 'number', 'float', 'boolean', 'json', 'dateTime')) not null default 'text', "required" boolean not null default false, "scope" text check ("scope" in ('admin', 'store')) not null default 'store', "categories" text[] null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "metafield_definition_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_definition_owner_type" ON "metafield_definition" (owner_type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_definition_deleted_at" ON "metafield_definition" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_metafield_definition_unique" ON "metafield_definition" (owner_type, namespace, key) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "metafield_definition" cascade;`);
  }

}
