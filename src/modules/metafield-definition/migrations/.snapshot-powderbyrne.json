{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "owner_type": {"name": "owner_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["product", "order", "customer", "store", "category", "room_config", "hotel", "destination"], "mappedType": "enum"}, "namespace": {"name": "namespace", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "key": {"name": "key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "namespace_label": {"name": "namespace_label", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "label": {"name": "label", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'text'", "enumItems": ["text", "number", "float", "boolean", "json", "dateTime"], "mappedType": "enum"}, "required": {"name": "required", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "scope": {"name": "scope", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'store'", "enumItems": ["admin", "store"], "mappedType": "enum"}, "categories": {"name": "categories", "type": "text[]", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "array"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "metafield_definition", "schema": "public", "indexes": [{"keyName": "IDX_metafield_definition_owner_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_metafield_definition_owner_type\" ON \"metafield_definition\" (owner_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_metafield_definition_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_metafield_definition_deleted_at\" ON \"metafield_definition\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_metafield_definition_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_metafield_definition_unique\" ON \"metafield_definition\" (owner_type, namespace, key) WHERE deleted_at IS NULL"}, {"keyName": "metafield_definition_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}], "nativeEnums": {}}