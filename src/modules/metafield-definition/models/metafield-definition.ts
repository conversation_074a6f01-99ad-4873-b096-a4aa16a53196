import { model } from "@camped-ai/framework/utils";

export const MetafieldDefinition = model
  .define("metafield_definition", {
    id: model.id().primaryKey(),
    owner_type: model
      .enum([
        "product",
        "order",
        "customer",
        "store",
        "category",
        "room_config",
        "hotel",
        "destination",
      ])
      .index(),
    namespace: model.text(),
    key: model.text(),
    namespace_label: model.text().nullable(),
    label: model.text(),
    description: model.text().nullable(),
    type: model
      .enum(["text", "number", "float", "boolean", "json", "dateTime"])
      .default("text"),
    // default_value: model.text().nullable(),
    required: model.boolean().default(false),
    scope: model.enum(["admin", "store"]).default("store"),
    categories: model.array().nullable(),
  })
  .indexes([
    {
      name: "IDX_metafield_definition_unique",
      on: ["owner_type", "namespace", "key"],
      unique: true,
    },
  ]);
