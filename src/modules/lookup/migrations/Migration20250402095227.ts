import { Migration } from '@mikro-orm/migrations';

export class Migration20250402095227 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "lookup" drop constraint if exists "lookup_unique";`);
    this.addSql(`create table if not exists "lookup" ("id" text not null, "entity_name" text not null, "value" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "lookup_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_lookup_entity_name" ON "lookup" (entity_name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_lookup_deleted_at" ON "lookup" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_lookup_unique" ON "lookup" (entity_name, value) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "lookup" cascade;`);
  }

}
