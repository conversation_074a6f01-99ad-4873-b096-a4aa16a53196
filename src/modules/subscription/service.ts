import { MedusaService } from "@camped-ai/framework/utils";
import Subscription from "./models/subscription";
import {
  CreateSubscriptionData,
  SubscriptionData,
  SubscriptionInterval,
  SubscriptionStatus,
} from "./types";
import moment from "moment";

class SubscriptionModuleService extends MedusaService({
  Subscription,
}) {
  // @ts-expect-error
  async createSubscriptions(
    data: CreateSubscriptionData | CreateSubscriptionData[]
  ): Promise<SubscriptionData[]> {
    const input = Array.isArray(data) ? data : [data];

    const subscriptions = await Promise.all(
      input.map(async (subscription) => {
        subscription.subscription_date = subscription.subscription_date
          ? new Date(subscription.subscription_date)
          : new Date();

        if (subscription.interval === SubscriptionInterval.DAILY) {
          subscription.subscription_date.setDate(
            subscription.subscription_date.getDate() + 1
          );
        }
        const subscriptionDate = subscription.subscription_date;
        const expirationDate = this.getExpirationDate({
          subscription_date: subscriptionDate,
          interval: subscription.interval,
        });
        const nextDate = new Date(subscriptionDate);
        nextDate.setDate(nextDate.getDate() + 1);

        return await super.createSubscriptions({
          ...subscription,
          subscription_date: subscriptionDate,
          last_order_date: subscriptionDate,
          next_order_date: this.getNextOrderDate({
            last_order_date: subscriptionDate,
            expiration_date: expirationDate,
            interval: subscription.interval,
            period: subscription.period,
          }),
          expiration_date: expirationDate,
        });
      })
    );

    return subscriptions;
  }

  async recordNewSubscriptionOrder(id: string) {
    const subscription = await this.retrieveSubscription(id);

    const orderDate = new Date();
    orderDate.setDate(orderDate.getDate() + 1);

    return await this.updateSubscriptions({
      id,
      last_order_date: orderDate,
      next_order_date: this.getNextOrderDate({
        last_order_date: orderDate,
        expiration_date: subscription.expiration_date,
        interval: subscription.interval,
        period: subscription.period,
      }),
    });
  }

  async expireSubscription(id: string | string[]): Promise<SubscriptionData[]> {
    const input = Array.isArray(id) ? id : [id];

    return await this.updateSubscriptions({
      selector: {
        id: input,
      },
      data: {
        next_order_date: null,
        status: SubscriptionStatus.EXPIRED,
      },
    });
  }

  async cancelSubscriptions(
    id: string | string[]
  ): Promise<SubscriptionData[]> {
    const input = Array.isArray(id) ? id : [id];

    return await this.updateSubscriptions({
      selector: {
        id: input,
      },
      data: {
        next_order_date: null,
        status: SubscriptionStatus.CANCELED,
      },
    });
  }

  getNextOrderDate({
    last_order_date,
    expiration_date,
    interval,
    period,
  }: {
    last_order_date: Date;
    expiration_date: Date;
    interval: SubscriptionInterval;
    period: number;
  }): Date | null {
    const nextOrderDate = moment(last_order_date).add(
      period,
      this.getIntervalUnit(interval)
    );
    const expirationMomentDate = moment(expiration_date);

    // if next order date is after the expiration date, return
    // null. Otherwise, return the next order date.
    return nextOrderDate.isAfter(expirationMomentDate)
      ? null
      : nextOrderDate.toDate();
  }

  getExpirationDate({
    subscription_date,
    interval,
  }: {
    subscription_date: Date;
    interval: SubscriptionInterval;
  }) {
    return moment(subscription_date)
      .add(this.getFixedPeriod(interval), this.getIntervalUnit(interval))
      .toDate();
  }

  private getFixedPeriod(interval: SubscriptionInterval): number {
    switch (interval) {
      case SubscriptionInterval.DAILY:
        return 30;
      case SubscriptionInterval.WEEKLY:
        return 10;
      case SubscriptionInterval.MONTHLY:
        return 12; // 1 year
      case SubscriptionInterval.YEARLY:
        return 5; // 5 years
      default:
        throw new Error(`Invalid interval: ${interval}`);
    }
  }

  private getIntervalUnit(
    interval: SubscriptionInterval
  ): moment.unitOfTime.DurationConstructor {
    switch (interval) {
      case SubscriptionInterval.DAILY:
        return "day";
      case SubscriptionInterval.WEEKLY:
        return "week";
      case SubscriptionInterval.MONTHLY:
        return "month";
      case SubscriptionInterval.YEARLY:
        return "year";
      default:
        throw new Error(`Invalid interval: ${interval}`);
    }
  }
}

export default SubscriptionModuleService;
