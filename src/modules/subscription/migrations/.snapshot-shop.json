{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'active'", "enumItems": ["active", "canceled", "expired", "failed"], "mappedType": "enum"}, "interval": {"name": "interval", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["daily", "weekly", "monthly", "yearly"], "mappedType": "enum"}, "period": {"name": "period", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "subscription_date": {"name": "subscription_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "last_order_date": {"name": "last_order_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "next_order_date": {"name": "next_order_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "expiration_date": {"name": "expiration_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "subscription", "schema": "public", "indexes": [{"keyName": "IDX_subscription_next_order_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_subscription_next_order_date\" ON \"subscription\" (next_order_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_subscription_expiration_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_subscription_expiration_date\" ON \"subscription\" (expiration_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_subscription_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_subscription_deleted_at\" ON \"subscription\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "subscription_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}], "nativeEnums": {}}