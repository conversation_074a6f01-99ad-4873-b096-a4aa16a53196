import { InferTypeOf } from "@camped-ai/framework/types";
import Subscription from "../models/subscription";

export enum SubscriptionStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  FAILED = "failed",
}

export enum SubscriptionInterval {
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  YEARLY = "yearly",
}

export type CreateSubscriptionData = {
  interval: SubscriptionInterval;
  period: number;
  status?: SubscriptionStatus;
  subscription_date?: Date;
  metadata?: Record<string, unknown>;
};

export type SubscriptionData = InferTypeOf<typeof Subscription>;
