import {
  AuthenticationInput,
  AuthIdentityProviderService,
  AuthenticationResponse,
  ICacheService,
  Logger,
  AuthIdentityDTO,
  MedusaContainer,
} from "@camped-ai/framework/types";
import {
  AbstractAuthModuleProvider,
  ContainerRegistrationKeys,
  isDefined,
  MedusaError,
  Modules,
} from "@camped-ai/framework/utils";
import { createHmac, randomBytes } from "node:crypto";
import { container } from "@camped-ai/framework";
import {
  createCustomerAccountWorkflow,
  updateCustomersWorkflow,
} from "@camped-ai/medusa/core-flows";
import pool from "src/utils/db";

type InjectedDependencies = {
  [Modules.CACHE]: ICacheService;
  [ContainerRegistrationKeys.LOGGER]: Logger;
  container: MedusaContainer;
};

type ProviderOptions = {
  /** the number of digits the OTP should have @default 6 */
  digits: number;
  /** the time to live of the OTP in seconds @default 60 * 5 (5 minutes) */
  ttl: number;
  /** maximum number of OTP emails allowed within the rate limit window @default 3 */
  maxOtpAttempts: number;
  /** time window in seconds for rate limiting OTP emails @default 60 * 5 (5 minutes) */
  rateLimitWindow: number;
};

export const OTP_RETURN_KEY = "otp_generated";

const domainName = process.env.S3_FILE_URL;
const projectName = process.env.PROJECT_NAME;

class OTPAuthProviderService extends AbstractAuthModuleProvider {
  static identifier = "otp";

  protected cacheService_: ICacheService;
  protected logger_: Logger;
  protected options_: ProviderOptions;

  constructor(
    container: InjectedDependencies,
    options: ProviderOptions = {
      digits: 6,
      ttl: 60 * 5,
      maxOtpAttempts: 3,
      rateLimitWindow: 60 * 5, // 5 minutes
    }
  ) {
    super();
    this.cacheService_ = container[Modules.CACHE];
    this.logger_ = container[ContainerRegistrationKeys.LOGGER];
    this.options_ = options;
  }

  /**
   * Generate a TOTP (Time-based One-Time Password)
   * @param secret The secret to generate the OTP from
   * @param timeStep The time step to use for the OTP
   * @returns {string} The OTP
   */
  generateTOTP(secret: string, timeStep: number): string {
    const time = Math.floor(Date.now() / 1000 / timeStep);

    const hmac = createHmac("sha1", Buffer.from(secret, "hex"));
    hmac.update(Buffer.from(time.toString(), "utf-8"));
    const hmacResult = hmac.digest();

    const offset = hmacResult[hmacResult.length - 1] & 0xf;
    const binary =
      ((hmacResult[offset] & 0x7f) << 24) |
      ((hmacResult[offset + 1] & 0xff) << 16) |
      ((hmacResult[offset + 2] & 0xff) << 8) |
      (hmacResult[offset + 3] & 0xff);

    return (binary % Math.pow(10, this.options_.digits))
      .toString()
      .padStart(this.options_.digits, "0");
  }

  /**
   * Verify the OTP
   * @param key The key to verify the OTP for
   * @param providedOtp The OTP to verify
   * @returns {boolean} True if the OTP is valid, false otherwise
   */
  async verify(key: string, providedOtp: string): Promise<boolean> {
    const storedOtp = await this.cacheService_.get(`totp:${key}`);

    const isValid = storedOtp === providedOtp;

    if (isValid) {
      await this.cacheService_.invalidate(`totp:${key}`);
    }

    return isValid;
  }

  /**
   * Check if the email has exceeded the rate limit for OTP sending
   * @param email The email to check
   * @returns {Promise<boolean>} True if rate limit is exceeded, false otherwise
   */
  async isRateLimitExceeded(email: string): Promise<boolean> {
    const rateLimitKey = `rate_limit:${email}`;
    const attempts = await this.cacheService_.get(rateLimitKey);

    if (!attempts) {
      return false;
    }

    const parsedAttempts = JSON.parse(attempts as string);
    const { count, timestamp } = parsedAttempts as {
      count: number;
      timestamp: number;
    };

    // Check if the rate limit window has expired
    const now = Date.now();
    const windowExpired =
      now - timestamp > this.options_.rateLimitWindow * 1000;

    if (windowExpired) {
      // If window expired, we'll reset the counter in updateRateLimit
      return false;
    }

    // Check if the number of attempts exceeds the limit
    return count >= this.options_.maxOtpAttempts;
  }

  /**
   * Update the rate limit counter for an email
   * @param email The email to update the rate limit for
   */
  async updateRateLimit(email: string): Promise<void> {
    const rateLimitKey = `rate_limit:${email}`;
    const attempts = await this.cacheService_.get(rateLimitKey);

    const now = Date.now();

    if (!attempts) {
      // First attempt
      await this.cacheService_.set(
        rateLimitKey,
        JSON.stringify({ count: 1, timestamp: now }),
        this.options_.rateLimitWindow
      );
      return;
    }

    const parsedAttempts = JSON.parse(attempts as string);
    const { count, timestamp } = parsedAttempts as {
      count: number;
      timestamp: number;
    };

    // Check if the rate limit window has expired
    const windowExpired =
      now - timestamp > this.options_.rateLimitWindow * 1000;

    if (windowExpired) {
      // Reset counter if window expired
      await this.cacheService_.set(
        rateLimitKey,
        JSON.stringify({ count: 1, timestamp: now }),
        this.options_.rateLimitWindow
      );
    } else {
      // Increment counter
      await this.cacheService_.set(
        rateLimitKey,
        JSON.stringify({ count: count + 1, timestamp }),
        this.options_.rateLimitWindow
      );
    }
  }

  async authenticate(
    data: AuthenticationInput,
    authIdentityProviderService: AuthIdentityProviderService
  ): Promise<AuthenticationResponse> {
    const identifier = data.body?.identifier;
    const otp = data.body?.otp;

    if (!isDefined(identifier)) {
      return {
        success: false,
        error: "Identifier is required",
      };
    }

    const authIdentity = await authIdentityProviderService
      .retrieve({
        entity_id: identifier,
      })
      .catch(() => null);

    if (!authIdentity) {
      // If there is no matching identity, return error for login
      this.logger_.warn(`No matching identity found for ${identifier}`);
      return {
        success: false,
        error: "User not registered. Please register first.",
      };
    }

    if (isDefined(identifier) && !isDefined(otp)) {
      // We're in a case we want to send the OTP to the user for login
      const otpSecret = authIdentity.provider_identities?.find(
        (provider: any) => provider.provider === this.identifier
      )?.provider_metadata?.otp_secret as string | undefined;

      if (!otpSecret) {
        this.logger_.error(
          `No OTP secret found for identity ${authIdentity.id}, make sure that you have registered the identity with the OTP auth module`
        );
        return {
          success: false,
          error: "Invalid request",
        };
      }

      // Check rate limit before sending OTP
      const isLimited = await this.isRateLimitExceeded(identifier);
      if (isLimited) {
        this.logger_.warn(
          `Rate limit exceeded for ${identifier}. Max ${
            this.options_.maxOtpAttempts
          } emails within ${this.options_.rateLimitWindow / 60} minutes.`
        );
        return {
          success: false,
          error: `Too many OTP requests. Please try again after ${Math.ceil(
            this.options_.rateLimitWindow / 60
          )} minutes.`,
        };
      }

      const totp = this.generateTOTP(otpSecret, this.options_.ttl);

      try {
        const response = await fetch(`${process.env.MAIL_URL}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "api-key": `${process.env.SMTP_PASS}`,
          },
          body: JSON.stringify({
            from: "<EMAIL>",
            to: identifier,
            subject: "Your Login OTP Code",
            htmlContent: `
              <!DOCTYPE html>
              <html lang="en">
              <head>
                  <meta charset="UTF-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Login OTP Code</title>
                  <style>
                      body {
                          font-family: Arial, sans-serif;
                          margin: 0;
                          padding: 0;
                          background-color: #f4f4f4;
                      }
                      .container {
                          max-width: 600px;
                          margin: 30px auto;
                          padding: 20px;
                          background-color: #ffffff;
                          border: 1px solid #e0e0e0;
                          border-radius: 8px;
                          box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                          text-align: center;
                      }
                      .logo {
                          text-align: center;
                          margin-bottom: 20px;
                      }
                      .logo img {
                          max-width: 150px;
                          height: auto;
                      }
                      h1 {
                          color: #333333;
                      }
                      p {
                          color: #555555;
                      }
                      .otp-code {
                          font-size: 32px;
                          font-weight: bold;
                          letter-spacing: 5px;
                          color: #007bff;
                          margin: 30px 0;
                          padding: 15px;
                          background-color: #f8f9fa;
                          border-radius: 8px;
                          display: inline-block;
                      }
                      .note {
                          font-size: 14px;
                          color: #777777;
                          margin-top: 20px;
                          font-style: italic;
                      }
                      hr {
                          border: 0;
                          height: 1px;
                          background-color: #e0e0e0;
                          margin: 30px 0;
                      }
                      .footer {
                          font-size: 12px;
                          color: #999999;
                          text-align: center;
                      }
                  </style>
              </head>
              <body>
                  <div class="container">
                      <div class="logo">
                          <img src="${domainName}/${projectName}/logo.png" alt="Perfect Piste Logo" style="max-width: 100px; height: auto;">
                      </div>

                      <h1>Your Login OTP Code</h1>
                      <p>Great to see you again!</p>
                      <p>Your account is just one step away. For your security, please use the verification code below to complete your login:</p>

                      <div class="otp-code">${totp}</div>

                      <p class="note">This code will expire in 5 minutes for security reasons.</p>

                      <hr>

                      <div class="footer">
                          <p>
                              If you didn't request this login, please ignore this email.<br>
                              This is an automated message, please do not reply to this email.
                          </p>
                      </div>
                  </div>
              </body>
              </html>
            `,
          }),
        });

        if (!response.ok) {
          console.error("Failed to send OTP email:", await response.text());
          return {
            success: false,
            error: "Failed to send OTP email",
          };
        }

        // Update rate limit counter after successful email sending
        await this.updateRateLimit(identifier);

        await this.cacheService_.set(
          `totp:${authIdentity.id}`,
          totp,
          this.options_.ttl
        );

        return {
          success: true,
          location: OTP_RETURN_KEY,
        };
      } catch (error) {
        this.logger_.error(`Error sending OTP email: ${error.message}`);
        return {
          success: false,
          error: "Failed to send OTP email",
        };
      }
    }

    if (isDefined(identifier) && isDefined(otp)) {
      // We're in a case we want to verify the OTP
      const isValid = await this.verify(authIdentity.id, otp);

      if (!isValid) {
        return {
          success: false,
          error: `Invalid OTP for ${identifier}`,
        };
      }

      return {
        success: true,
        authIdentity,
      };
    }

    return {
      success: false,
      error: "Invalid request",
    };
  }

  /**
   * Generate a random secret for the customer that will be used to generate the OTP
   * @returns {string} The secret
   */
  generateOTPSecret(): string {
    return randomBytes(32).toString("hex");
  }

  async updateCustomerHasAccount(id: string) {
    try {
      const client = await pool.connect();

      try {
        const query = `UPDATE CUSTOMER SET has_account = true WHERE id = $1`;
        const result = await client.query(query, [id]);

        return result.rowCount > 0;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error("Error updating customer:", error);
      return false;
    }
  }

  /**
   * Create a customer record for the user using Medusa's core workflow
   * @param email The email of the customer
   * @param authIdentityId The ID of the auth identity
   * @returns The created customer
   */
  private async createCustomer(
    email: string,
    authIdentityId: string,
    first_name?: string,
    last_name?: string,
    phone?: string
  ) {
    // Extract name from email (optional)
    const emailName = email.split("@")[0];
    const firstName = first_name || emailName || "Customer";

    // First try to find a guest customer with the same email
    try {
      const customerService = container.resolve(Modules.CUSTOMER);
      const existingCustomers = await customerService.listCustomers({ email });
      const guestCustomer = existingCustomers?.find(
        (customer: { has_account: boolean }) => !customer.has_account
      );

      if (guestCustomer) {
        this.logger_.info(
          `Found guest customer with email ${email}, ID: ${guestCustomer.id}`
        );

        // Step 1: Update the guest customer's metadata and other fields
        const updateWorkflow = updateCustomersWorkflow(container);
        const { result: updatedCustomer } = await updateWorkflow.run({
          input: {
            selector: { id: [guestCustomer.id] },
            update: {
              first_name: firstName || guestCustomer.first_name,
              last_name: last_name || guestCustomer.last_name,
              phone: phone || guestCustomer.phone,
              metadata: {
                ...(guestCustomer.metadata || {}),
                source: "otp_registration",
                converted_from_guest: true,
                converted_at: new Date().toISOString(),
              },
            },
          },
        });

        const hasAccount = this.updateCustomerHasAccount(guestCustomer.id);
        if (!hasAccount) {
          this.logger_.error(`Failed to update customer`);
          return null;
        }

        // Step 2: Use direct database query to update has_account field
        // This is necessary because has_account is not part of CustomerUpdatableFields
        // const manager = container.resolve("manager");
        // await manager.transaction(async (transactionManager: any) => {
        //   await transactionManager.query(
        //     `UPDATE "customer" SET "has_account" = true WHERE "id" = $1`,
        //     [guestCustomer.id]
        //   );
        // });

        // Step 3: Associate the auth identity with the customer
        const authService = container.resolve(Modules.AUTH);
        await authService.updateAuthIdentities({
          id: authIdentityId,
          app_metadata: {
            customer_id: guestCustomer.id,
          },
        });

        this.logger_.info(
          `Successfully updated guest customer ${guestCustomer.id} to registered customer`
        );
        return updatedCustomer[0]; // Return the first customer from the array
      } else {
        this.logger_.info(`No guest customer found with email ${email}`);
        const { result } = await createCustomerAccountWorkflow(
          this.container_
        ).run({
          input: {
            authIdentityId: authIdentityId,
            customerData: {
              first_name: firstName,
              last_name: last_name || "",
              email: email,
              phone: phone,
              metadata: {
                source: "otp_registration",
              },
            },
          },
        });

        this.logger_.info(
          `Created new customer with email ${email} using workflow`
        );
        return result;
      }
    } catch (error) {
      this.logger_.error(`Failed to create customer: ${error.message}`);
      return null;
    }
  }

  async register(
    data: AuthenticationInput,
    authIdentityProviderService: AuthIdentityProviderService
  ): Promise<AuthenticationResponse> {
    console.log("i am registering");
    if (!isDefined(data.body?.identifier)) {
      return {
        success: false,
        error: "Identifier is required",
      };
    }

    let authIdentity: AuthIdentityDTO | undefined;

    try {
      // Check if user already exists
      authIdentity = await authIdentityProviderService.retrieve({
        entity_id: data.body!.identifier,
      });
      console.log("existing user", { authIdentity });

      // For registration, if user already exists, return error
      return {
        success: false,
        error: "User already registered. Please use login instead.",
      };
    } catch (error: unknown) {
      if (!(error instanceof MedusaError))
        return { success: false, error: JSON.stringify(error) };

      if (error.type !== MedusaError.Types.NOT_FOUND)
        return { success: false, error: error.message };

      // If the identity is not found, we create it (new user)
      const otpSecret = this.generateOTPSecret();
      console.log({ otpSecret });
      authIdentity = await authIdentityProviderService.create({
        entity_id: data.body!.identifier,
        provider_metadata: {
          otp_secret: otpSecret,
        },
      });
      console.log("new user created", authIdentity);

      // Create a customer record for new users using the auth identity ID
      const customer = await this.createCustomer(
        data.body!.identifier,
        authIdentity.id,
        data.body?.first_name,
        data.body?.last_name,
        data.body?.phone
      );
      console.log("customer created:", customer?.id || "failed");
    }

    if (!authIdentity) {
      return {
        success: false,
        error: "Failed to create identity",
      };
    }

    // Send OTP to the user (only for new users)
    const otpSecret = authIdentity.provider_identities?.find(
      (provider: any) => provider.provider === this.identifier
    )?.provider_metadata?.otp_secret as string | undefined;

    if (!otpSecret) {
      this.logger_.error(
        `No OTP secret found for identity ${authIdentity.id}, make sure that you have registered the identity with the OTP auth module`
      );
      return {
        success: false,
        error: "Invalid request",
      };
    }

    // Check rate limit before sending OTP
    const isLimited = await this.isRateLimitExceeded(data.body!.identifier);
    if (isLimited) {
      this.logger_.warn(
        `Rate limit exceeded for ${data.body!.identifier}. Max ${
          this.options_.maxOtpAttempts
        } emails within ${this.options_.rateLimitWindow / 60} minutes.`
      );
      return {
        success: false,
        error: `Too many OTP requests. Please try again after ${Math.ceil(
          this.options_.rateLimitWindow / 60
        )} minutes.`,
      };
    }

    const totp = this.generateTOTP(otpSecret, this.options_.ttl);
    console.log({ totp });

    try {
      const response = await fetch(`${process.env.MAIL_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": `${process.env.SMTP_PASS}`,
        },
        body: JSON.stringify({
          from: "<EMAIL>",
          to: data.body!.identifier,
          subject: "Your Registration OTP Code",
          htmlContent: `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Registration OTP Code</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f4f4f4;
                    }
                    .container {
                        max-width: 600px;
                        margin: 30px auto;
                        padding: 20px;
                        background-color: #ffffff;
                        border: 1px solid #e0e0e0;
                        border-radius: 8px;
                        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                        text-align: center;
                    }
                    .logo {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .logo img {
                        max-width: 150px;
                        height: auto;
                    }
                    h1 {
                        color: #333333;
                    }
                    p {
                        color: #555555;
                    }
                    .otp-code {
                        font-size: 32px;
                        font-weight: bold;
                        letter-spacing: 5px;
                        color: #007bff;
                        margin: 30px 0;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: 8px;
                        display: inline-block;
                    }
                    .welcome {
                        font-size: 18px;
                        color: #28a745;
                        margin-bottom: 20px;
                    }
                    .note {
                        font-size: 14px;
                        color: #777777;
                        margin-top: 20px;
                        font-style: italic;
                    }
                    hr {
                        border: 0;
                        height: 1px;
                        background-color: #e0e0e0;
                        margin: 30px 0;
                    }
                    .footer {
                        font-size: 12px;
                        color: #999999;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="logo">
                        <img src="${domainName}/${projectName}/logo.png" alt="Brand Logo" style="max-width: 100px; height: auto;">
                    </div>

                    <h1>Your Registration OTP Code</h1>
                    <p class="welcome">Welcome, ${
                      data.body?.first_name ||
                      data.body!.identifier.split("@")[0] ||
                      "User"
                    }!</p>
                    <p>Thank you for registering with us. Please use the following OTP code to complete your registration:</p>

                    <div class="otp-code">${totp}</div>

                    <p class="note">This code will expire in 5 minutes for security reasons.</p>

                    <hr>

                    <div class="footer">
                        <p>
                            If you didn't request this registration, please ignore this email.<br>
                            This is an automated message, please do not reply to this email.
                        </p>
                    </div>
                </div>
            </body>
            </html>
          `,
        }),
      });

      if (!response.ok) {
        console.error("Failed to send OTP email:", await response.text());
        return {
          success: false,
          error: "Failed to send OTP email",
        };
      }

      // Update rate limit counter after successful email sending
      await this.updateRateLimit(data.body!.identifier);

      // Store the OTP in cache for verification
      await this.cacheService_.set(
        `totp:${authIdentity.id}`,
        totp,
        this.options_.ttl
      );

      // Return a response indicating OTP is required
      return {
        success: true,
        error: OTP_RETURN_KEY,
      };
    } catch (error) {
      console.error("Error sending OTP:", error);
      return {
        success: false,
        error: "Failed to send OTP",
      };
    }
  }
}

export default OTPAuthProviderService;
