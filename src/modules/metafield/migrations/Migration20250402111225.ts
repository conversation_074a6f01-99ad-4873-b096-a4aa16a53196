import { Migration } from '@mikro-orm/migrations';

export class Migration20250402111225 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "metafield" drop constraint if exists "metafield_unique";`);
    this.addSql(`create table if not exists "metafield" ("id" text not null, "owner_id" text not null, "owner_type" text check ("owner_type" in ('product', 'order', 'customer', 'store', 'category', 'room_config', 'hotel', 'destination')) not null, "namespace" text not null, "key" text not null, "definition_id" text not null, "value" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "metafield_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_owner_id" ON "metafield" (owner_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_owner_type" ON "metafield" (owner_type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_definition_id" ON "metafield" (definition_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_metafield_deleted_at" ON "metafield" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_metafield_unique" ON "metafield" (owner_id, owner_type, namespace, key) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "metafield" cascade;`);
  }

}
