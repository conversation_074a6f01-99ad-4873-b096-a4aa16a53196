import { model } from "@camped-ai/framework/utils";

export const Metafield = model
  .define("metafield", {
    id: model.id().primaryKey(),
    owner_id: model.text().index(), // ID of the entity this metafield belongs to
    owner_type: model
      .enum([
        "product",
        "order",
        "customer",
        "store",
        "category",
        "room_config",
        "hotel",
        "destination",
      ])
      .index(),
    namespace: model.text(),
    key: model.text(),
    definition_id: model.text().index(),
    // type: model
    //   .enum(["text", "number", "float", "boolean", "json", "dateTime"])
    //   .default("text"),
    value: model.text(), // Dynamic content
  })
  .indexes([
    {
      name: "IDX_metafield_unique",
      on: ["owner_id", "owner_type", "namespace", "key"],
      unique: true,
    },
  ]);
