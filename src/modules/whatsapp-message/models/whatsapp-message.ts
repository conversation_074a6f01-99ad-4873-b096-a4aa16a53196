import { model } from "@camped-ai/framework/utils";

export enum MessageDirection {
  INBOUND = "inbound",
  OUTBOUND = "outbound",
}

export enum MessageStatus {
  PENDING = "pending",
  SENT = "sent",
  DELIVERED = "delivered",
  READ = "read",
  FAILED = "failed",
}

export enum MessageType {
  TEXT = "text",
  TEMPLATE = "template",
  IMAGE = "image",
  VIDEO = "video",
  AUDIO = "audio",
  DOCUMENT = "document",
  LOCATION = "location",
  CONTACT = "contact",
}

export const WhatsAppMessage = model.define("whatsapp_message", {
  id: model.id({ prefix: "wa_msg" }).primaryKey(),
  whatsapp_message_id: model.text().nullable(),
  direction: model.enum(Object.values(MessageDirection)).default(MessageDirection.OUTBOUND),
  status: model.enum(Object.values(MessageStatus)).default(MessageStatus.PENDING),
  message_type: model.enum(Object.values(MessageType)).default(MessageType.TEXT),
  content: model.text(),
  order_id: model.text().nullable(),
  customer_id: model.text().nullable(),
  from_phone: model.text(),
  to_phone: model.text(),
  sent_at: model.dateTime(),
  delivered_at: model.dateTime().nullable(),
  read_at: model.dateTime().nullable(),
  metadata: model.json().nullable(),
  template_name: model.text().nullable(),
  template_language: model.text().nullable(),
  template_params: model.json().nullable(),
  media_url: model.text().nullable(),
  media_id: model.text().nullable(),
  media_mime_type: model.text().nullable(),
  retry_count: model.number().default(0),
  last_retry_at: model.dateTime().nullable(),
  error_message: model.text().nullable(),
})
.indexes([
  {
    on: ["order_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["customer_id"],
    where: "deleted_at IS NULL",
  },
  {
    on: ["whatsapp_message_id"],
    where: "deleted_at IS NULL",
  },
]);
