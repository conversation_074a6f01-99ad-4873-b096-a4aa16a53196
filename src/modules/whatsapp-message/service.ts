import {
  Context,
  FindConfig,
  Lo<PERSON>
} from "@camped-ai/framework/types";
import {
  InjectManager,
  MedusaContext,
  MedusaService
} from "@camped-ai/framework/utils";
import {
  MessageDirection,
  MessageStatus,
  MessageType,
  WhatsAppMessage
} from "./models/whatsapp-message";

type InjectedDependencies = {
  logger: Logger;
};

class WhatsAppMessageService extends MedusaService({
  WhatsAppMessage,
}) {
  protected readonly logger_: Logger;
  static identifier = "whatsapp-message";

  constructor(container: InjectedDependencies) {
    super(container);
    this.logger_ = container.logger;
  }

  /**
   * Save a sent WhatsApp message to the database
   * @param data - The WhatsApp message data
   */
  @InjectManager()
  async saveSentMessage(
    data: {
      whatsapp_message_id?: string;
      content: string;
      from_phone: string;
      to_phone: string;
      order_id?: string;
      customer_id?: string;
      message_type?: MessageType;
      template_name?: string;
      template_language?: string;
      template_params?: Record<string, any>;
      media_url?: string;
      media_id?: string;
      media_mime_type?: string;
      metadata?: Record<string, any>;
    },
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Saving sent WhatsApp message: ${JSON.stringify(data)}`);

      const messageData = {
        whatsapp_message_id: data.whatsapp_message_id,
        direction: MessageDirection.OUTBOUND,
        status: MessageStatus.SENT,
        message_type: data.message_type || MessageType.TEXT,
        content: data.content,
        from_phone: data.from_phone,
        to_phone: data.to_phone,
        order_id: data.order_id,
        customer_id: data.customer_id,
        sent_at: new Date(),
        template_name: data.template_name,
        template_language: data.template_language,
        template_params: data.template_params,
        media_url: data.media_url,
        media_id: data.media_id,
        media_mime_type: data.media_mime_type,
        metadata: data.metadata
      };

      // Create the WhatsApp message in the database
      const result = await this.createWhatsAppMessages([messageData], context);

      this.logger_.info(`WhatsApp message saved to database with ID: ${result[0].id}`);

      return result[0];
    } catch (error) {
      this.logger_.error(`Failed to save sent WhatsApp message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Save an incoming WhatsApp message to the database
   * @param data - The incoming WhatsApp message data
   */
  @InjectManager()
  async saveIncomingMessage(
    data: {
      whatsapp_message_id: string;
      content: string;
      from_phone: string;
      to_phone: string;
      order_id?: string;
      customer_id?: string;
      message_type?: MessageType;
      media_url?: string;
      media_id?: string;
      media_mime_type?: string;
      metadata?: Record<string, any>;
      timestamp?: number;
    },
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Saving incoming WhatsApp message: ${JSON.stringify(data)}`);

      const sentAt = data.timestamp ? new Date(data.timestamp * 1000) : new Date();

      const messageData = {
        whatsapp_message_id: data.whatsapp_message_id,
        direction: MessageDirection.INBOUND,
        status: MessageStatus.DELIVERED,
        message_type: data.message_type || MessageType.TEXT,
        content: data.content,
        from_phone: data.from_phone,
        to_phone: data.to_phone,
        order_id: data.order_id,
        customer_id: data.customer_id,
        sent_at: sentAt,
        delivered_at: new Date(),
        media_url: data.media_url,
        media_id: data.media_id,
        media_mime_type: data.media_mime_type,
        metadata: data.metadata
      };

      // Create the WhatsApp message in the database
      const result = await this.createWhatsAppMessages([messageData], context);

      this.logger_.info(`Incoming WhatsApp message saved to database with ID: ${result[0].id}`);

      return result[0];
    } catch (error) {
      this.logger_.error(`Failed to save incoming WhatsApp message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update the status of a WhatsApp message
   * @param whatsappMessageId - The WhatsApp message ID
   * @param status - The new status
   */
  @InjectManager()
  async updateMessageStatus(
    whatsappMessageId: string,
    status: MessageStatus,
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Updating WhatsApp message status: ${whatsappMessageId} to ${status}`);

      // Find the message by WhatsApp message ID
      const messages = await this.listWhatsAppMessages(
        { whatsapp_message_id: whatsappMessageId },
        { take: 1 },
        context
      );

      if (!messages.length) {
        throw new Error(`WhatsApp message with ID ${whatsappMessageId} not found`);
      }

      const message = messages[0];

      // Update status and related fields
      const updateData: Record<string, any> = { status };

      // Add additional fields based on status
      if (status === MessageStatus.DELIVERED) {
        updateData.delivered_at = new Date();
      } else if (status === MessageStatus.READ) {
        updateData.read_at = new Date();
      }

      // Update the message in the database
      const updated = await this.updateWhatsAppMessages(
        { id: message.id, ...updateData },
        context
      );

      this.logger_.info(`WhatsApp message status updated to ${status} for ID: ${message.id}`);

      return updated;
    } catch (error) {
      this.logger_.error(`Failed to update WhatsApp message status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get messages for a specific order
   * @param orderId - The order ID
   */
  @InjectManager()
  async getMessagesByOrderId(
    orderId: string,
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Getting WhatsApp messages for order: ${orderId}`);

      // Query the database for messages related to this order
      const messages = await this.listWhatsAppMessages(
        { order_id: orderId },
        {
          order: { created_at: "DESC" }
        },
        context
      );

      this.logger_.info(`Found ${messages.length} WhatsApp messages for order ${orderId}`);

      return messages;
    } catch (error) {
      this.logger_.error(`Failed to get WhatsApp messages for order ${orderId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get messages for a specific customer
   * @param customerId - The customer ID
   */
  @InjectManager()
  async getMessagesByCustomerId(
    customerId: string,
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Getting WhatsApp messages for customer: ${customerId}`);

      // Query the database for messages related to this customer
      const messages = await this.listWhatsAppMessages(
        { customer_id: customerId },
        {
          order: { created_at: "DESC" }
        },
        context
      );

      this.logger_.info(`Found ${messages.length} WhatsApp messages for customer ${customerId}`);

      return messages;
    } catch (error) {
      this.logger_.error(`Failed to get WhatsApp messages for customer ${customerId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a conversation between the business and a specific phone number
   * @param phoneNumber - The phone number to get conversation for
   */
  @InjectManager()
  async getConversationByPhone(
    phoneNumber: string,
    @MedusaContext() context: Context = {}
  ) {
    try {
      // Format phone number to ensure consistent matching
      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      this.logger_.info(`Getting WhatsApp conversation for phone: ${formattedPhone}`);

      // Query the database for messages where this phone number is either sender or receiver
      const messages = await this.listWhatsAppMessages(
        {
          $or: [
            { from_phone: formattedPhone },
            { to_phone: formattedPhone }
          ]
        },
        {
          order: { sent_at: "DESC" }
        },
        context
      );

      this.logger_.info(`Found ${messages.length} WhatsApp messages for phone ${formattedPhone}`);

      return messages;
    } catch (error) {
      this.logger_.error(`Failed to get WhatsApp conversation for ${phoneNumber}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get message by WhatsApp message ID
   * @param whatsappMessageId - The WhatsApp message ID
   */
  @InjectManager()
  async getMessageByWhatsAppId(
    whatsappMessageId: string,
    @MedusaContext() context: Context = {}
  ) {
    try {
      this.logger_.info(`Getting WhatsApp message by ID: ${whatsappMessageId}`);

      // Query the database for the message with this WhatsApp message ID
      const messages = await this.listWhatsAppMessages(
        { whatsapp_message_id: whatsappMessageId },
        { take: 1 },
        context
      );

      if (!messages.length) {
        this.logger_.warn(`WhatsApp message with ID ${whatsappMessageId} not found`);
        return null;
      }

      this.logger_.info(`Found WhatsApp message with ID ${whatsappMessageId}`);

      return messages[0];
    } catch (error) {
      this.logger_.error(`Failed to get WhatsApp message ${whatsappMessageId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Format phone number to ensure it's in the correct format for WhatsApp API
   */
  private formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters except the + sign
    let formattedNumber = phone.replace(/[^\d+]/g, "");

    // Ensure the number starts with a + sign
    if (!formattedNumber.startsWith("+")) {
      formattedNumber = "+" + formattedNumber;
    }

    return formattedNumber;
  }
}

export default WhatsAppMessageService;
