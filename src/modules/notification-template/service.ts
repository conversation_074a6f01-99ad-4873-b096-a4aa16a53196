import { MedusaService } from "@camped-ai/framework/utils";
import { NotificationTemplate } from "./models/notification-template";

// Import all template files directly
import { productCreated } from "../my-notification/email/templates/product-created";
import { inviteCreated } from "../my-notification/email/templates/invite-created";
import { orderPlaced } from "../my-notification/email/templates/order-placed";
import { orderShipped } from "../my-notification/email/templates/shipment-created";

export const NOTIFICATION_TEMPLATE_SERVICE = "notification_template";

class NotificationTemplateService extends MedusaService({
  NotificationTemplate,
}) {
  /**
   * Get a notification template by event name and channel
   * @param eventName - The event name
   * @param channel - The notification channel (email, feed)
   * @returns The notification template or null if not found
   */
  async getTemplate(eventName: string, channel: string) {
    try {
      const templates = await this.listNotificationTemplates({
        event_name: eventName,
        channel,
        is_active: true,
      });

      // Return the default template if available, otherwise the first one
      const defaultTemplate = templates.find((t) => t.is_default);
      return defaultTemplate || templates[0] || null;
    } catch (error) {
      console.error(`Error getting template for ${eventName} on ${channel}:`, error);
      return null;
    }
  }

  /**
   * Get or create a notification template by event name and channel
   * This ensures a template is always available, even if none exists in the database
   * @param eventName - The event name
   * @param channel - The notification channel (email, feed)
   * @param defaultSubject - Default subject to use if creating a new template
   * @param defaultContent - Default content to use if creating a new template
   * @returns The notification template
   */
  async getOrCreateTemplate(eventName: string, channel: string, defaultSubject: string, defaultContent: string) {
    try {
      // First try to get an existing template
      const existingTemplate = await this.getTemplate(eventName, channel);
      if (existingTemplate) {
        return existingTemplate;
      }

      // If no template exists, create a default one
      console.log(`No template found for ${eventName} on ${channel}, creating default template`);
      const newTemplate = await this.createNotificationTemplates({
        event_name: eventName,
        channel,
        subject: defaultSubject,
        content: defaultContent,
        is_default: true,
        is_active: true,
      });

      return newTemplate;
    } catch (error) {
      console.error(`Error getting or creating template for ${eventName} on ${channel}:`, error);
      // Return a fallback in-memory template
      return {
        id: 'fallback',
        event_name: eventName,
        channel,
        subject: defaultSubject,
        content: defaultContent,
        is_default: true,
        is_active: true,
      };
    }
  }

  /**
   * Check if notifications should be sent for a specific event and channel
   * @param eventName - The event name
   * @param channel - The notification channel (email, feed)
   * @returns Boolean indicating if notifications should be sent
   */
  async shouldSendNotification(eventName: string, channel: string): Promise<boolean> {
    try {
      const templates = await this.listNotificationTemplates({
        event_name: eventName,
        channel,
      });

      // Check if there's at least one active template
      return templates.some((t) => t.is_active);
    } catch (error) {
      console.error(`Error checking if should send notification for ${eventName} on ${channel}:`, error);
      return false;
    }
  }

  /**
   * Set a template as the default for its event and channel
   * @param templateId - The ID of the template to set as default
   */
  async setAsDefault(templateId: string): Promise<any> {
    const template = await this.retrieveNotificationTemplate(templateId);

    // First, unset default for all templates with the same event and channel
    const templatesWithSameEventAndChannel = await this.listNotificationTemplates({
      event_name: template.event_name,
      channel: template.channel,
      is_default: true,
    });

    for (const t of templatesWithSameEventAndChannel) {
      if (t.id !== templateId) {
        await this.updateNotificationTemplates([{
          id: t.id,
          is_default: false
        }]);
      }
    }

    // Then set this template as default
    return await this.updateNotificationTemplates([{
      id: templateId,
      is_default: true
    }]);
  }

  /**
   * Get the original template content from the code files
   * @param eventName - The event name
   * @param channel - The notification channel (email, feed)
   * @returns The original template content or null if not found
   */
  async getOriginalTemplateContent(eventName: string, channel: string) {
    try {
      console.log(`Getting original template content for event ${eventName} and channel ${channel}`);

      // Get the original template content based on the event name
      switch (eventName) {
        case "product.created":
          console.log('Using product.created template');
          return {
            subject: productCreated.subject,
            content: productCreated.body
          };

        case "invite.created":
          console.log('Using invite.created template');
          return {
            subject: inviteCreated.subject,
            content: inviteCreated.body
          };

        case "order.placed":
          console.log('Using order.placed template');
          return {
            subject: orderPlaced.subject,
            content: orderPlaced.body
          };

        case "order.shipped":
          console.log('Using order.shipped template');
          return {
            subject: orderShipped.subject,
            content: orderShipped.body
          };
        default:
          // Try to find a default template in the database
          const defaultTemplates = await this.listNotificationTemplates({
            event_name: eventName,
            channel: channel,
            is_default: true,
          });

          if (defaultTemplates.length > 0) {
            return {
              subject: defaultTemplates[0].subject,
              content: defaultTemplates[0].content
            };
          }
          return null;
      }
    } catch (error) {
      console.error(`Error getting original template for ${eventName} on ${channel}:`, error);
      return null;
    }
  }
}

export default NotificationTemplateService;
