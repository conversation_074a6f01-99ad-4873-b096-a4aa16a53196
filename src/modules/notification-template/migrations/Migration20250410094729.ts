import { Migration } from '@mikro-orm/migrations';

export class Migration20250410094729 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table if not exists "notification_template" ("id" text not null, "event_name" text not null, "channel" text not null, "is_default" boolean not null default false, "is_active" boolean not null default true, "subject" text null, "content" text not null, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "notification_template_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_notification_template_event_name" ON "notification_template" (event_name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_notification_template_channel" ON "notification_template" (channel) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_notification_template_deleted_at" ON "notification_template" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "notification_template" cascade;`);
  }

}
