import { model } from "@camped-ai/framework/utils";

export const NotificationTemplate = model.define("notification_template", {
  // Primary key with prefix for notification templates
  id: model.id({ prefix: "ntmpl" }).primaryKey(),
  
  // The event that triggers this notification (e.g., "product.created")
  event_name: model.text().index(),
  
  // The notification channel (email, feed)
  channel: model.text().index(),
  
  // Whether this template is the default for this event and channel
  is_default: model.boolean().default(false),
  
  // Whether notifications for this event and channel are active
  is_active: model.boolean().default(true),
  
  // Template content fields
  subject: model.text().nullable(), // For email channel
  content: model.text(), // The template content with placeholders
  
  // Optional metadata for additional configuration
  metadata: model.json().nullable(),
});
