import { AbstractNotificationProviderService } from "@camped-ai/framework/utils"
import { Logger, ProviderSendNotificationDTO, ProviderSendNotificationResultsDTO } from "@camped-ai/framework/types"
import nodemailer from "nodemailer";
import { config } from "./config";

type InjectedDependencies = {
  logger: Logger
}

type Options = {
  from: string
}

class MyNotificationProviderService extends AbstractNotificationProviderService {
  protected logger_: Logger
  protected options_: Options
  protected smtpTransporter: nodemailer.Transporter
  static identifier = "notification";


  constructor (
    { logger }: InjectedDependencies,
    options: Options
  ) {
    super()

    this.logger_ = logger
    this.options_ = options

    this.smtpTransporter = nodemailer.createTransport(config.smtp)

  }


  async send(notification: ProviderSendNotificationDTO): Promise<ProviderSendNotificationResultsDTO> {
    // Handle email notifications only
    try {
      const { html, subject } = notification.data;

      const payload = {
        from: this.options_.from,
        to: notification.to,
        subject: subject || "Notification",
        htmlContent: html,
      };

      const response = await fetch(`${process.env.MAIL_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": `${process.env.SMTP_PASS}`,
        },
        body: JSON.stringify(payload)
      });
      if (!response.ok) {
        throw new Error(`Failed to send email: ${response.statusText}`);
      }
      const result = await response.json();
      return { id: result.MessageId };
    } catch (error) {
      this.logger_.error(`Failed to send email: ${error.message}`);
      return {};
    }
  }


}

export default MyNotificationProviderService

