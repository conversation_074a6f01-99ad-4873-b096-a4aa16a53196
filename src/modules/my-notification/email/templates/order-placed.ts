export const orderPlaced = {
    subject: "Your Booking Confirmation at {{ booking.hotel_name }}",
    body: `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Hotel Booking Confirmation</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f4f4f4;
                    }
                    .container {
                        max-width: 600px;
                        margin: 30px auto;
                        padding: 20px;
                        background-color: #ffffff;
                        border: 1px solid #e0e0e0;
                        border-radius: 8px;
                        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                    }
                    .logo {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .logo img {
                        max-width: 150px;
                        height: auto;
                    }
                    h1 {
                        color: #333333;
                        text-align: center;
                    }
                    p {
                        color: #555555;
                        text-align: center;
                    }
                    hr {
                        border: 0;
                        height: 1px;
                        background: #ddd;
                        margin: 20px 0;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-top: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }
                    th {
                        background-color: #f4f4f4;
                        font-weight: bold;
                    }
                    td.right-align {
                        text-align: right;
                    }
                    img {
                        max-width: 50px;
                        height: auto;
                    }
                    .summary {
                        font-size: 16px;
                        margin-top: 20px;
                    }
                    .summary div {
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                        padding: 8px 0;
                    }
                    .summary span {
                        flex: 1;
                    }
                    .summary .right-align {
                        text-align: right;
                        white-space: nowrap;
                    }
                    .total {
                        font-weight: bold;
                        font-size: 18px;
                    }
                    .footer {
                        margin-top: 20px;
                        font-size: 12px;
                        color: #aaaaaa;
                        text-align: center;
                    }
                    .footer a {
                        color: #007bff;
                        text-decoration: none;
                    }
                    .footer a:hover {
                        text-decoration: underline;
                    }
                    .order-id {
                        text-align: center;
                        margin: 10px 0;
                    }
                    .order-id a {
                        color: #007bff;
                        text-decoration: none;
                        font-weight: bold;
                    }
                    .order-id a:hover {
                        text-decoration: underline;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <!-- Brand Logo -->
                    <div class="logo">
                        <img src="https://medusa-e-commerce.s3.ap-south-1.amazonaws.com/Flinkk/Logos/finalPoornayushlogo.png" alt="Brand Logo">
                    </div>

                    <h1>Your Booking at {{ booking.hotel_name }} is Confirmed!</h1>
                    <p>Hello <strong>{{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}</strong>,</p>

                    <!-- Booking Reference -->
                    <div class="order-id">
                        <p>Your booking <strong>(Reference: <a href="{{ frontendURL }}/account/orders/details/{{ order.id }}" target="_blank">{{ order.id }}</a>)</strong> has been successfully confirmed.</p>
                    </div>

                    <hr>

                    <!-- Booking Details -->
                    <h2>Booking Details</h2>
                    <div style="padding: 15px; background-color: #f9f9f9; border-radius: 5px; margin-bottom: 20px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Guest Name:</td>
                                <td style="padding: 8px;">{{ booking.guest_name }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Check-in Date:</td>
                                <td style="padding: 8px;">{{ booking.formatted_check_in_date }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Check-in Time:</td>
                                <td style="padding: 8px;">{{ booking.check_in_time }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Check-out Date:</td>
                                <td style="padding: 8px;">{{ booking.formatted_check_out_date }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Check-out Time:</td>
                                <td style="padding: 8px;">{{ booking.check_out_time }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Duration:</td>
                                <td style="padding: 8px;">{{ booking.nights }} night(s)</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Number of Guests:</td>
                                <td style="padding: 8px;">{{ booking.number_of_guests }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Number of Rooms:</td>
                                <td style="padding: 8px;">{{ booking.number_of_rooms }}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Room Type:</td>
                                <td style="padding: 8px;">{{ booking.room_type }}</td>
                            </tr>
                            {{#if booking.special_requests}}
                            <tr>
                                <td style="padding: 8px; width: 40%; font-weight: bold;">Special Requests:</td>
                                <td style="padding: 8px;">{{ booking.special_requests }}</td>
                            </tr>
                            {{/if}}
                        </table>
                    </div>



                    <hr>

                    <h2>Payment Summary</h2>
<div style="font-size: 16px; line-height: 1.6; color: #555555; margin-top: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
    <div style="display: block; width: 100%; padding: 5px 0;">
        <span style="float: left;">Room Charges:</span>
        <span style="float: right;">{{ currencySymbol }}{{ booking.total_amount }}</span>
        <div style="clear: both;"></div>
    </div>
    {{#if order.raw_shipping_total.value}}
    <div style="display: block; width: 100%; padding: 5px 0;">
        <span style="float: left;">Additional Services:</span>
        <span style="float: right;">{{ currencySymbol }}{{ order.raw_shipping_total.value }}</span>
        <div style="clear: both;"></div>
    </div>
    {{/if}}
    {{#if order.raw_item_tax_total.value}}
    <div style="display: block; width: 100%; padding: 5px 0;">
        <span style="float: left;">Taxes & Fees:</span>
        <span style="float: right;">{{ currencySymbol }}{{ order.raw_item_tax_total.value }}</span>
        <div style="clear: both;"></div>
    </div>
    {{/if}}
    <div style="border-top: 1px solid #ddd; margin: 10px 0;"></div>
    <div style="display: block; width: 100%; padding: 10px 0; font-weight: bold; font-size: 18px;">
        <span style="float: left;">Total Amount:</span>
        <span style="float: right;">{{ currencySymbol }}{{ booking.total_amount }}</span>
        <div style="clear: both;"></div>
    </div>
    <div style="display: block; width: 100%; padding: 5px 0; font-style: italic; font-size: 14px; color: #777;">
        <span>Payment Status: {{ payment_status }}</span>
    </div>
</div>


                    <hr>

                    <!-- Important Information -->
                    <div style="margin-top: 20px; padding: 15px; background-color: #f0f7ff; border-radius: 5px; border-left: 4px solid #0066cc;">
                        <h3 style="margin-top: 0; color: #0066cc;">Important Information</h3>
                        <ul style="padding-left: 20px; margin-bottom: 0;">
                            <li style="margin-bottom: 8px;">Please arrive at the hotel with a valid ID for check-in.</li>
                            <li style="margin-bottom: 8px;">Early check-in and late check-out may be subject to additional charges.</li>
                            <li style="margin-bottom: 8px;">For any changes to your booking, please contact the hotel directly.</li>
                            <li style="margin-bottom: 0;">Cancellation policy: Please refer to the terms and conditions of your booking.</li>
                        </ul>
                    </div>

                    <!-- Footer with Privacy Policy & Terms -->
                    <p class="footer">
                        If you have any questions, contact our <a href="${process.env.MEDUSA_STOREFRONT_URL}/contact-us" target="_blank">support team</a>.<br>
                        We hope you enjoy your stay!
                    </p>
                </div>
            </body>
            </html>
        `,
  };
