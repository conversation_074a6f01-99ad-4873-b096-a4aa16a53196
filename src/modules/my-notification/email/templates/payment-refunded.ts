export const refundProcessed = {
    subject: "Your Refund Has Been Processed!",
    body: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Refund Processed</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 30px auto;
                padding: 20px;
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                text-align: center;
            }
            .logo {
                text-align: center;
                margin-bottom: 20px;
            }
            .logo img {
                max-width: 150px;
                height: auto;
            }
            h1 {
                color: #333333;
            }
            p {
                color: #555555;
            }
            .details {
                text-align: left;
                background-color: #ffffff;
                padding: 10px;
                border-radius: 6px;
                margin-top: 15px;
                font-size: 14px;
                border: 1px solid #e0e0e0;
            }
            .order-id {
                text-align: center;
                margin: 10px 0;
            }
            .order-id a {
                color: #007bff;
                text-decoration: none;
                font-weight: bold;
            }
            .order-id a:hover {
                text-decoration: underline;
            }
            .footer {
                margin-top: 20px;
                font-size: 12px;
                color: #aaaaaa;
                text-align: center;
            }
            .footer a {
                color: #007bff;
                text-decoration: none;
            }
            .footer a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Brand Logo -->
            <div class="logo">
                <img src="https://medusa-e-commerce.s3.ap-south-1.amazonaws.com/Flinkk/Logos/finalPoornayushlogo.png" alt="Brand Logo">
            </div>

            <h1>Your Refund Has Been Processed!</h1>
            <p>Hello <strong>{{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}</strong>,</p>
            
            <!-- Clickable Order ID -->
            <div class="order-id">
                <p>Your refund for <strong>Order (ID: 
                <a href="{{ frontendURL }}/account/orders/details/{{ order.id }}" target="_blank">{{ order.id }}</a>)</strong> 
                has been processed successfully.</p>
            </div>

            <div class="details">
                <p><strong>Refund Amount:</strong> {{ currencySymbol }}{{ refundAmount }}</p>
            </div>

            <hr>

            <!-- Footer with Support & Terms -->
            <div class="footer">
                <p>
                    If you have any questions, contact our <a href="${process.env.MEDUSA_STOREFRONT_URL}/contact-us" target="_blank">support team</a>.<br>
                </p>
            </div>
        </div>
    </body>
    </html>
    `,
};
