export const productCreated = {
    subject: "New Product Created",
    body: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Product Created</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 30px auto;
                padding: 20px;
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                text-align: center;
            }
            .logo {
                text-align: center;
                margin-bottom: 20px;
            }
            .logo img {
                max-width: 150px;
                height: auto;
            }
            h1 {
                color: #333333;
            }
            p {
                color: #555555;
            }
            .details {
                text-align: left;
                background-color: #f9f9f9;
                padding: 10px;
                border-radius: 6px;
                margin-top: 15px;
                font-size: 14px;
            }
            .footer {
                margin-top: 20px;
                font-size: 12px;
                color: #aaaaaa;
                text-align: center;
            }
            .footer a {
                color: #007bff;
                text-decoration: none;
            }
            .footer a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Brand Logo -->
            <div class="logo">
                <img src="https://medusa-e-commerce.s3.ap-south-1.amazonaws.com/Flinkk/Logos/finalPoornayushlogo.png" alt="Brand Logo">
            </div>

            <h1>New Product Created!</h1>
            <p>A new product has been successfully added to the system.</p>

            <div class="details">
                <p><strong>Product Details:</strong></p>
                <ul>
                    <li><strong>ID:</strong> {{ product.id }}</li>
                </ul>
            </div>

            <hr>

            <!-- Footer with Privacy Policy & Terms -->
            <p class="footer">
                If you have any questions, contact our <a href="${process.env.MEDUSA_STOREFRONT_URL}/contact-us" target="_blank">support team</a>.<br>
                <a href="{{ frontendURL }}/privacy-policy" target="_blank">Privacy Policy</a> | 
                <a href="{{ frontendURL }}/terms" target="_blank">Terms & Conditions</a>
            </p>
        </div>
    </body>
    </html>
    `,
};
