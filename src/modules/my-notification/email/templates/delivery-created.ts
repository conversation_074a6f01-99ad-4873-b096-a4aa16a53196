export const orderDelivered = {
    subject: "Your Order Has Been Delivered!",
    body: `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Delivered</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 30px auto;
                padding: 20px;
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
                text-align: center;
            }
            .logo {
                text-align: center;
                margin-bottom: 20px;
            }
            .logo img {
                max-width: 150px;
                height: auto;
            }
            h1 {
                color: #333333;
            }
            p {
                color: #555555;
            }
            .details {
                text-align: left;
                background-color: #ffffff; /* White background */
                padding: 10px;
                border-radius: 6px;
                margin-top: 15px;
                font-size: 14px;
                border: 1px solid #e0e0e0;
            }
            .details table {
                width: 100%;
                border-collapse: collapse;
            }
            .details th, .details td {
                text-align: center;
                padding: 10px;
                border: 1px solid #ddd;
            }
            .details th {
                background-color: #ffffff; /* White background */
                color: #000000; /* Black text */
                font-weight: bold;
            }
            .details img {
                max-width: 100px;
                height: auto;
            }
            .order-id {
                text-align: center;
                margin: 10px 0;
            }
            .order-id a {
                color: #007bff;
                text-decoration: none;
                font-weight: bold;
            }
            .order-id a:hover {
                text-decoration: underline;
            }
            .footer {
                margin-top: 20px;
                font-size: 12px;
                color: #aaaaaa;
                text-align: center;
            }
            .footer a {
                color: #007bff;
                text-decoration: none;
            }
            .footer a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Brand Logo -->
            <div class="logo">
                <img src="https://medusa-e-commerce.s3.ap-south-1.amazonaws.com/Flinkk/Logos/finalPoornayushlogo.png" alt="Brand Logo">
            </div>

            <h1>Your Order Has Been Delivered!</h1>
            <p>Hello {{ fulfillment.delivery_address.first_name }} {{ fulfillment.delivery_address.last_name }},</p>
            
            <!-- Clickable Order ID -->
            <div class="order-id">
                <p>Your order <strong>(ID: <a href="{{ frontendURL }}/account/orders/details/{{ order.id }}" target="_blank">{{ order.id }}</a>)</strong> has been successfully delivered.</p>
            </div>
            <p>Below are the details:</p>

            <div class="details">
                <p><strong>Delivered Items:</strong></p>
                <table>
                    <thead>
                        <tr>
                            <th>Thumbnail</th>
                            <th>Product</th>
                            <th>Variant</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{#each order.items}}
                        <tr>
                            <td><img src="{{ this.thumbnail }}" alt="{{ this.product_title }}" style="max-width: 50px; height: auto;" /></td>
                            <td>{{ this.product_title }}</td>
                            <td>{{ this.variant_title }}</td>
                            <td>{{ this.quantity }}</td>
                        </tr>
                        {{/each}}
                    </tbody>
                </table>

                <p><strong>Delivery Address:</strong></p>
                <p>{{ fulfillment.delivery_address.first_name }} {{ fulfillment.delivery_address.last_name }}<br>
                   {{ fulfillment.delivery_address.address_1 }} {{ fulfillment.delivery_address.address_2 }}<br>
                   {{ fulfillment.delivery_address.city }}, {{ fulfillment.delivery_address.province }}<br>
                   {{ fulfillment.delivery_address.country_code }} - {{ fulfillment.delivery_address.postal_code }}<br>
                   Phone: {{ fulfillment.delivery_address.phone }}
                </p>
            </div>

            <hr>

            <!-- Footer with Privacy Policy & Terms -->
            <div class="footer">
                <p>
                    If you have any questions, contact our <a href="${process.env.MEDUSA_STOREFRONT_URL}/contact-us" target="_blank">support team</a>.<br>
                </p>
            </div>
        </div>
    </body>
    </html>
    `,
};
