# WhatsApp Notification Module

This module provides direct WhatsApp integration for sending notifications to customers. It uses the WhatsApp Business API to send messages without relying on third-party services like Twilio.

## Features

- Send WhatsApp notifications for booking confirmations
- Send WhatsApp notifications for payment confirmations
- Track message delivery status via webhooks
- Support for WhatsApp message templates

## Setup Instructions

### 1. WhatsApp Business API Setup

1. Create a Meta Business Account if you don't have one already
2. Set up a WhatsApp Business Account
3. Register a phone number for WhatsApp Business API
4. Create and submit message templates for approval

### 2. Environment Variables

Add the following environment variables to your `.env` file:

```
# WhatsApp Business API Configuration
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verification_token
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret
```

### 3. Message Templates

Create the following templates in the WhatsApp Business Manager:

#### Booking Confirmation Template

**Name:** booking_confirmation  
**Category:** TRANSACTIONAL  
**Language:** English  

**Header:** Your Booking Confirmation  
**Body:**
```
Hello {{1}},

Your booking at {{2}} is confirmed!

Check-in: {{3}} at {{5}}
Check-out: {{4}} at {{6}}
Booking ID: {{7}}
Duration: {{8}} nights
Guests: {{9}}
Room Type: {{10}}

We look forward to welcoming you!
```
**Footer:** Thank you for choosing us!

#### Payment Confirmation Template

**Name:** payment_confirmation  
**Category:** TRANSACTIONAL  
**Language:** English  

**Header:** Payment Confirmation  
**Body:**
```
Hello {{1}},

We've received your payment of {{2}} {{3}} for booking {{4}} on {{5}}.

Your reservation is now confirmed.
```
**Footer:** Thank you for your payment!

### 4. Webhook Setup

1. In the Meta Developer Portal, set up a webhook endpoint for your WhatsApp Business Account
2. Configure the webhook URL to point to your application's webhook endpoint: `https://your-domain.com/webhooks/whatsapp`
3. Set the verification token to match your `WHATSAPP_WEBHOOK_VERIFY_TOKEN` environment variable
4. Subscribe to the following webhook fields:
   - messages
   - message_status

## Usage

The module automatically sends WhatsApp notifications for the following events:

- `booking.order_created`: Sends a booking confirmation message
- `payment.captured`: Sends a payment confirmation message

## Troubleshooting

### Common Issues

1. **Message Not Delivered**: Check that the phone number is in the correct format with country code (e.g., +**********)
2. **Template Not Found**: Ensure the template name in the code matches exactly the approved template name in WhatsApp Business Manager
3. **API Errors**: Check the logs for detailed error messages from the WhatsApp API

### Debugging

Enable debug logging by setting the environment variable:

```
DEBUG=true
```

This will output detailed logs of the WhatsApp API requests and responses.

## Extending

To add new notification types:

1. Create a new template in WhatsApp Business Manager
2. Add the template name to the `WHATSAPP_TEMPLATES` object in `templates.ts`
3. Create a helper function to format the template data
4. Create a new subscriber for the event you want to send notifications for
