/**
 * WhatsApp Template Helper
 * 
 * This file contains helper functions and template definitions for WhatsApp messages.
 * WhatsApp Business API requires pre-approved templates for sending messages.
 * 
 * Template Guidelines:
 * 1. Templates must be approved by WhatsApp before use
 * 2. Templates can contain variables that are replaced with actual values
 * 3. Templates must follow WhatsApp's content policy
 * 
 * Example template structure in WhatsApp Business Manager:
 * 
 * Template Name: booking_confirmation
 * Category: TRANSACTIONAL
 * Language: English
 * 
 * Header: Your Booking Confirmation
 * Body: Hello {{1}},
 *       
 *       Your booking at {{2}} is confirmed!
 *       
 *       Check-in: {{3}} at {{5}}
 *       Check-out: {{4}} at {{6}}
 *       Booking ID: {{7}}
 *       Duration: {{8}} nights
 *       Guests: {{9}}
 *       Room Type: {{10}}
 *       
 *       We look forward to welcoming you!
 * Footer: Thank you for choosing us!
 */

/**
 * Template definitions
 * These should match the templates approved in your WhatsApp Business Manager
 */
export const WHATSAPP_TEMPLATES = {
  BOOKING_CONFIRMATION: "booking_confirmation",
  BOOKING_REMINDER: "booking_reminder",
  BOOKING_CANCELLED: "booking_cancelled",
  PAYMENT_CONFIRMATION: "payment_confirmation",
};

/**
 * Format booking data for WhatsApp template
 * @param booking Booking data
 * @param hotel Hotel data
 * @returns Formatted template data
 */
export function formatBookingTemplateData(booking: any, hotel: any) {
  // Format dates
  const checkInDate = new Date(booking.check_in_date).toLocaleDateString();
  const checkOutDate = new Date(booking.check_out_date).toLocaleDateString();
  
  // Calculate number of nights
  const nights = Math.ceil(
    (new Date(booking.check_out_date).getTime() - new Date(booking.check_in_date).getTime()) / 
    (1000 * 60 * 60 * 24)
  );
  
  return {
    1: booking.guest_name,
    2: hotel.name,
    3: checkInDate,
    4: checkOutDate,
    5: booking.check_in_time || "14:00",
    6: booking.check_out_time || "12:00",
    7: booking.id,
    8: nights.toString(),
    9: booking.adults ? `${booking.adults} adults` : "1 adult",
    10: booking.room_type || "Standard Room",
    footer: "Thank you for choosing us!"
  };
}

/**
 * Format payment confirmation data for WhatsApp template
 * @param booking Booking data
 * @param payment Payment data
 * @returns Formatted template data
 */
export function formatPaymentTemplateData(booking: any, payment: any) {
  return {
    1: booking.guest_name,
    2: payment.amount.toString(),
    3: payment.currency.toUpperCase(),
    4: booking.id,
    5: new Date().toLocaleDateString(),
    footer: "Thank you for your payment!"
  };
}
