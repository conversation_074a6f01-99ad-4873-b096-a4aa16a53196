import { AbstractNotificationProviderService } from "@camped-ai/framework/utils";
import {
  Logger,
  ProviderSendNotificationDTO,
  ProviderSendNotificationResultsDTO,
} from "@camped-ai/framework/types";
import { sendDirectWhatsAppMessage } from "./direct-message";

type InjectedDependencies = {
  logger: Logger;
};

type WhatsAppOptions = {
  from: string; // WhatsApp Phone Number ID
};

class DirectWhatsAppProviderService extends AbstractNotificationProviderService {
  protected logger_: Logger;
  protected options_: WhatsAppOptions;
  protected apiUrl: string;
  protected accessToken: string;
  static identifier = "direct-whatsapp";

  constructor({ logger }: InjectedDependencies, options: WhatsAppOptions) {
    super();
    this.logger_ = logger;
    this.options_ = options;

    // Initialize WhatsApp API configuration
    const phoneNumberId = options.from || process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.apiUrl = `https://graph.facebook.com/v22.0/${phoneNumberId}`;
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;

    if (!this.accessToken) {
      this.logger_.warn(
        "WhatsApp access token not configured. WhatsApp notifications will not work."
      );
    }

    if (!phoneNumberId) {
      this.logger_.warn(
        "WhatsApp phone number ID not configured. WhatsApp notifications will not work."
      );
    }
  }

  async send(
    notification: ProviderSendNotificationDTO
  ): Promise<ProviderSendNotificationResultsDTO> {
    try {
      this.logger_.info(
        `WhatsApp notification received: ${JSON.stringify(notification)}`
      );

      // If WhatsApp is not properly configured, log a warning and return
      if (!this.accessToken) {
        this.logger_.warn(
          "WhatsApp access token not configured. Skipping notification."
        );
        return {};
      }

      if (!this.options_.from && !process.env.WHATSAPP_PHONE_NUMBER_ID) {
        this.logger_.warn(
          "WhatsApp phone number ID not configured. Skipping notification."
        );
        return {};
      }

      // Get phone number ID from options or environment variable
      const phoneNumberId =
        this.options_.from || process.env.WHATSAPP_PHONE_NUMBER_ID;
      this.apiUrl = `https://graph.facebook.com/v22.0/${phoneNumberId}`;

      this.logger_.info(`Using WhatsApp API URL: ${this.apiUrl}`);
      this.logger_.info(
        `Using WhatsApp access token: ${
          this.accessToken ? "Configured" : "Not configured"
        }`
      );

      const phoneNumber = this.formatPhoneNumber(notification.to);

      // Check if this is a direct message or a template message
      if (notification.data.directMessage) {
        // This is a direct message
        const message = String(notification.data.message || "");

        this.logger_.info(`Sending direct WhatsApp message to ${phoneNumber}`);
        this.logger_.info(`Message: ${message}`);

        // Send direct message
        try {
          const result = await sendDirectWhatsAppMessage(
            phoneNumber,
            message,
            phoneNumberId,
            this.accessToken
          );

          this.logger_.info(
            `Direct WhatsApp message sent successfully. Result: ${JSON.stringify(
              result
            )}`
          );

          // Return early with the result
          return { id: result.messages?.[0]?.id };
        } catch (error) {
          this.logger_.error(
            `Failed to send direct WhatsApp message: ${error.message}`
          );
          throw error; // Re-throw to be caught by the outer try/catch
        }
      } else {
        // This is a template message
        const {
          templateName,
          templateData,
          language = "en_US",
        } = notification.data;

        this.logger_.info(
          `Sending WhatsApp template message to ${phoneNumber} using template ${templateName}`
        );
        this.logger_.info(`Template data: ${JSON.stringify(templateData)}`);

        // Format template components
        const components = this.formatTemplateComponents(templateData);
        this.logger_.info(
          `Formatted components: ${JSON.stringify(components)}`
        );

        // Prepare the message payload according to WhatsApp Business API format
        const payload = {
          messaging_product: "whatsapp",
          recipient_type: "individual",
          to: phoneNumber,
          type: "template",
          template: {
            name: templateName,
            language: {
              code: language,
            },
            components: components,
          },
        };

        this.logger_.info(`WhatsApp payload: ${JSON.stringify(payload)}`);

        // Send the message directly to WhatsApp Business API
        this.logger_.info(
          `Sending request to WhatsApp API: ${this.apiUrl}/messages`
        );

        // Send the template message
        const templateResponse = await fetch(`${this.apiUrl}/messages`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.accessToken}`,
          },
          body: JSON.stringify(payload),
        });

        const responseText = await templateResponse.text();
        this.logger_.info(
          `WhatsApp API response status: ${templateResponse.status}`
        );
        this.logger_.info(`WhatsApp API response: ${responseText}`);

        if (!templateResponse.ok) {
          let errorData: any;
          try {
            errorData = JSON.parse(responseText);
          } catch (e) {
            errorData = { error: responseText };
          }
          throw new Error(
            `WhatsApp API error (${templateResponse.status}): ${JSON.stringify(
              errorData
            )}`
          );
        }

        let result: any;
        try {
          result = JSON.parse(responseText);
        } catch (e) {
          this.logger_.warn(`Failed to parse response as JSON: ${e.message}`);
          result = { text: responseText };
        }

        this.logger_.info(
          `WhatsApp template message sent successfully. Result: ${JSON.stringify(
            result
          )}`
        );

        return { id: result.messages?.[0]?.id };
      }
    } catch (error) {
      this.logger_.error(`Failed to send WhatsApp message: ${error.message}`);
      this.logger_.error(`Error stack: ${error.stack}`);
      return {};
    }
  }

  /**
   * Format phone number to ensure it's in the correct format for WhatsApp API
   * @param phone Phone number to format
   * @returns Formatted phone number
   */
  private formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters except the + sign
    let formattedNumber = phone.replace(/[^\d+]/g, "");

    // Ensure the number starts with a + sign
    if (!formattedNumber.startsWith("+")) {
      formattedNumber = "+" + formattedNumber;
    }

    return formattedNumber;
  }

  /**
   * Format template components according to WhatsApp API requirements
   * @param templateData Template data to format
   * @returns Formatted template components
   */
  private formatTemplateComponents(templateData: Record<string, any>): any[] {
    const components = [];

    // Add header component if present
    if (templateData.header) {
      components.push({
        type: "header",
        parameters: [
          {
            type: templateData.header_type || "text",
            text: templateData.header,
          },
        ],
      });
    }

    // Add body component with parameters
    const bodyParameters = [];

    // Handle numeric keys (1, 2, 3, etc.) or named parameters
    for (const [key, value] of Object.entries(templateData)) {
      // Skip special keys
      if (["header", "header_type", "footer"].includes(key)) {
        continue;
      }

      // Add parameter
      bodyParameters.push({
        type: "text",
        text: String(value),
      });
    }

    if (bodyParameters.length > 0) {
      components.push({
        type: "body",
        parameters: bodyParameters,
      });
    }

    // Add footer component if present
    if (templateData.footer) {
      components.push({
        type: "footer",
        parameters: [
          {
            type: "text",
            text: templateData.footer,
          },
        ],
      });
    }

    return components;
  }

  /**
   * Retry sending a message with exponential backoff
   * @param notification Notification to send
   * @param maxRetries Maximum number of retries
   * @returns Result of the send operation
   */
  async sendWithRetry(
    notification: ProviderSendNotificationDTO,
    maxRetries = 3
  ): Promise<ProviderSendNotificationResultsDTO> {
    let retries = 0;

    while (retries < maxRetries) {
      try {
        return await this.send(notification);
      } catch (error) {
        retries++;
        this.logger_.warn(
          `WhatsApp send failed (attempt ${retries}/${maxRetries}): ${error.message}`
        );

        if (retries >= maxRetries) {
          this.logger_.error(
            `Failed to send WhatsApp after ${maxRetries} attempts`
          );
          throw error;
        }

        // Exponential backoff
        await new Promise((resolve) =>
          setTimeout(resolve, 1000 * Math.pow(2, retries))
        );
      }
    }

    return {};
  }
}

export default DirectWhatsAppProviderService;
