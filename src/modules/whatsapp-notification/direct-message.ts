/**
 * Helper functions for sending direct WhatsApp messages
 */

/**
 * Format a booking confirmation message
 * @param booking Booking data
 * @param hotel Hotel data
 * @returns Formatted message text
 */
export function formatBookingConfirmationMessage(booking: any, hotel: any): string {
  // Format dates
  const checkInDate = new Date(booking.check_in_date).toLocaleDateString();
  const checkOutDate = new Date(booking.check_out_date).toLocaleDateString();

  // Calculate number of nights
  const nights = Math.ceil(
    (new Date(booking.check_out_date).getTime() - new Date(booking.check_in_date).getTime()) /
    (1000 * 60 * 60 * 24)
  );

  // Get number of rooms from metadata or default to 1
  const numberOfRooms = booking.number_of_rooms || booking.metadata?.number_of_rooms || 1;

  return `
*Booking Confirmation - ${hotel.name || "Our Hotel"}*

Hello ${booking.guest_name || "Guest"},

Your booking at ${hotel.name || "our hotel"} is confirmed!

*Booking Details:*
• Booking ID: ${booking.id}
• Hotel: ${hotel.name || "Our Hotel"}
• Check-in: ${checkInDate} at ${booking.check_in_time || "14:00"}
• Check-out: ${checkOutDate} at ${booking.check_out_time || "12:00"}
• Duration: ${nights} nights
• Number of Rooms: ${numberOfRooms}
• Room Type: ${booking.room_type || "Standard Room"}
• Guests: ${booking.adults ? `${booking.adults} adults` : "1 adult"}${booking.children ? `, ${booking.children} children` : ""}

We look forward to welcoming you!

Thank you for choosing ${hotel.name || "us"}.
`;
}

/**
 * Format a payment confirmation message
 * @param booking Booking data
 * @param payment Payment data
 * @returns Formatted message text
 */
export function formatPaymentConfirmationMessage(booking: any, payment: any): string {
  return `
*Payment Confirmation*

Hello ${booking.guest_name || "Guest"},

We've received your payment of ${payment.amount} ${payment.currency.toUpperCase()} for booking ${booking.id}.

Your reservation is now confirmed.

Thank you for your payment!
`;
}

/**
 * Send a direct WhatsApp message
 * @param phoneNumber Recipient phone number
 * @param message Message text
 * @param phoneNumberId WhatsApp phone number ID
 * @param accessToken WhatsApp access token
 * @returns Response from WhatsApp API
 */
export async function sendDirectWhatsAppMessage(
  phoneNumber: string,
  message: string,
  phoneNumberId: string,
  accessToken: string
): Promise<any> {
  // Format phone number
  const formattedPhone = formatPhoneNumber(phoneNumber);

  // Prepare the message payload
  const payload = {
    messaging_product: "whatsapp",
    recipient_type: "individual",
    to: formattedPhone,
    type: "text",
    text: {
      preview_url: false,
      body: message
    }
  };

  // Send the message
  const response = await fetch(`https://graph.facebook.com/v22.0/${phoneNumberId}/messages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`
    },
    body: JSON.stringify(payload)
  });

  // Parse the response
  const responseText = await response.text();

  if (!response.ok) {
    throw new Error(`WhatsApp API error (${response.status}): ${responseText}`);
  }

  return JSON.parse(responseText);
}

/**
 * Format phone number to ensure it's in the correct format for WhatsApp API
 * @param phone Phone number to format
 * @returns Formatted phone number
 */
function formatPhoneNumber(phone: string): string {
  // Remove any non-digit characters except the + sign
  let formattedNumber = phone.replace(/[^\d+]/g, "");

  // Ensure the number starts with a + sign
  if (!formattedNumber.startsWith("+")) {
    formattedNumber = "+" + formattedNumber;
  }

  return formattedNumber;
}
