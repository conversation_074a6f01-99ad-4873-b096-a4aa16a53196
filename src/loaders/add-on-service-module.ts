import { MedusaContainer } from "@camped-ai/framework/types";
import {
  ADD_ON_SERVICE,
  ADD_ON_SERVICE_MODULE,
} from "../modules/hotel-management/add-on-service";
import AddOnServiceModuleService from "../modules/hotel-management/add-on-service/service";

/**
 * Manually register the add-on service module and its services
 * This ensures the services are available even if the module registration fails
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    // Register the module
    if (!container.hasRegistration(ADD_ON_SERVICE_MODULE)) {
      container.register({
        [ADD_ON_SERVICE_MODULE]: {
          resolve: () => new AddOnServiceModuleService(container),
        },
      });
      console.log("✅ Add-on service module registered successfully");
    }

    // Register the service
    if (!container.hasRegistration(ADD_ON_SERVICE)) {
      container.register({
        [ADD_ON_SERVICE]: {
          resolve: () => new AddOnServiceModuleService(container),
        },
      });
      console.log("✅ Add-on service registered successfully");
    }
  } catch (error) {
    console.error("❌ Failed to register add-on service module:", error);
  }
};
