import { MedusaContainer } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

/**
 * This loader is deprecated as we've moved from booking-core to order-based functionality.
 * It now provides mock implementations to prevent errors in code that still references booking-core.
 */
export default async (container: MedusaContainer): Promise<void> => {
  console.log("Booking module is deprecated. Using order module instead.");

  try {
    // Create mock services that redirect to order module
    const mockBookingService = {
      retrieveReservation: async (id) => {
        console.log(`[DEPRECATED] Attempted to use booking-core.retrieveReservation(${id})`);
        const orderService = container.resolve(Modules.ORDER);
        return orderService.retrieveOrder(id);
      },
      cancelReservation: async (id, options) => {
        console.log(`[DEPRECATED] Attempted to use booking-core.cancelReservation(${id})`);
        const orderService = container.resolve(Modules.ORDER);
        await orderService.cancel(id);
        return {
          reservation: await orderService.retrieveOrder(id),
          refund_amount: 0,
          refund_percentage: 0,
          cancellation_fee: 0,
          policy_applied: false,
          policy: null
        };
      },
      assignRoom: async (id, roomId, options) => {
        console.log(`[DEPRECATED] Attempted to use booking-core.assignRoom(${id}, ${roomId})`);
        const orderService = container.resolve(Modules.ORDER);
        const order = await orderService.retrieveOrder(id);

        // Update order metadata with room assignment
        await orderService.updateOrders(id, {
          metadata: {
            ...order.metadata,
            assigned_room_id: roomId,
            assigned_by: options?.assignedBy || 'system',
            assigned_at: new Date().toISOString(),
            notes: options?.notes
          }
        });

        return orderService.retrieveOrder(id);
      }
    };

    // Register mock services
    container.register("bookingService", mockBookingService);
    container.register("bookingOrderService", mockBookingService);
    container.register("bookingInvoiceService", {});
    container.register("bookingEmailService", {});
    container.register("bookingAIService", {
      refreshInventoryCache: async () => console.log("[DEPRECATED] Attempted to use bookingAIService.refreshInventoryCache")
    });

    // Also register with constants for backward compatibility
    container.register("BOOKING_SERVICE", mockBookingService);
    container.register("BOOKING_ORDER_SERVICE", mockBookingService);
    container.register("BOOKING_INVOICE_SERVICE", {});
    container.register("BOOKING_EMAIL_SERVICE", {});
    container.register("BOOKING_AI_SERVICE", {
      refreshInventoryCache: async () => console.log("[DEPRECATED] Attempted to use bookingAIService.refreshInventoryCache")
    });

    console.log("Mock booking services registered for backward compatibility");
  } catch (error) {
    console.error("Failed to register mock booking services:", error);
  }
};
