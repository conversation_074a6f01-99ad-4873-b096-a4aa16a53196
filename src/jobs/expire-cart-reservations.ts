import { MedusaContainer } from "@camped-ai/framework/types";
import { RoomInventoryStatus } from "../modules/hotel-management/room-inventory/models/room-inventory";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

/**
 * Job to find and release expired cart reservations
 * This job runs every 5 minutes to check for expired cart reservations
 * and release them back to available status
 */
export default async function expireCartReservationsJob(container: MedusaContainer) {
  try {
    // Resolve necessary services
    const roomInventoryService = container.resolve("roomInventoryService");
    const logger = container.resolve("logger");

    // Get current time
    const now = new Date();

    // Since we don't have metadata with expiration timestamps, we'll release all cart reservations
    // that are older than 30 minutes (assuming they're expired)
    const thirtyMinutesAgo = new Date();
    thirtyMinutesAgo.setMinutes(thirtyMinutesAgo.getMinutes() - 30);

    // Process cart reservations in smaller batches to avoid timeouts
    const batchSize = 20;
    let processed = 0;
    let hasMore = true;
    let attempts = 0;
    const maxAttempts = 10; // Limit the number of attempts to prevent infinite loops

    logger.info("Starting to process expired cart reservations");

    try {
      // Process in batches until we've processed all records or reached max attempts
      while (hasMore && attempts < maxAttempts) {
        attempts++;

        try {
          // Get a batch of cart reservations
          logger.info(`Processing batch ${attempts} (limit: ${batchSize})`);

          // Use listRoomInventories method which is available in the service
          const inventoryEntries = await roomInventoryService.listRoomInventories({
            status: RoomInventoryStatus.CART_RESERVED
          });

          // Manually limit the number of entries to process in this batch
          const batchEntries = inventoryEntries.slice(0, batchSize);

          if (!inventoryEntries || inventoryEntries.length === 0) {
            logger.info("No more cart reservations found to process");
            hasMore = false;
            break;
          }

          logger.info(`Found ${batchEntries.length} cart reservations in current batch (out of ${inventoryEntries.length} total)`);

          // Release each expired reservation in the current batch
          for (const entry of batchEntries) {
            try {
              // Use a timeout to prevent long-running operations
              const updatePromise = new Promise(async (resolve, reject) => {
                try {
                  const result = await roomInventoryService.updateRoomInventories({
                    id: entry.id,
                    status: RoomInventoryStatus.AVAILABLE,
                    available_quantity: 1,
                    notes: `Cart reservation expired on ${now.toISOString()}. Previous notes: ${entry.notes || 'None'}`
                  });
                  resolve(result);
                } catch (err) {
                  reject(err);
                }
              });

              // Set a timeout for the update operation
              const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error("Update operation timed out")), 10000)
              );

              // Race the update against the timeout
              await Promise.race([updatePromise, timeoutPromise]);

              logger.info(`Released expired cart reservation for room ${entry.inventory_item_id}`);
              processed++;
            } catch (error) {
              logger.error(`Error releasing expired cart reservation for room ${entry.inventory_item_id}: ${error.message}`);
            }
          }

          logger.info(`Completed processing batch ${attempts}. Total processed: ${processed}`);

          // If we've processed all available records, we're done
          if (processed >= inventoryEntries.length) {
            logger.info("All cart reservations have been processed");
            hasMore = false;
          }

          // Add a small delay between batches to prevent overloading the database
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          logger.error(`Error processing batch ${attempts}: ${error.message}`);

          // Add a longer delay after an error
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }

      if (attempts >= maxAttempts && hasMore) {
        logger.warn(`Reached maximum number of attempts (${maxAttempts}). Some cart reservations may not have been processed.`);
      }

      logger.info(`Successfully processed a total of ${processed} expired cart reservations`);
    } catch (error) {
      logger.error(`Error in cart reservation processing: ${error.message}`);
    }
  } catch (error) {
    const logger = container.resolve("logger");
    logger.error(`Error in expire-cart-reservations job: ${error.message}`);
  }
}

/**
 * Job configuration
 * This job runs every 5 minutes
 */
export const config = {
  name: "expire-cart-reservations",
  schedule: "*/5 * * * *", // Run every 5 minutes
  handler: expireCartReservationsJob,
};
