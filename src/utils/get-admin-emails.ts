import { MedusaContainer } from "@camped-ai/framework/types";
import { Modu<PERSON> } from "@camped-ai/framework/utils";

/**
 * Get all admin user emails
 * @param container The Medusa container
 * @returns Array of admin email addresses
 */
export async function getAdminEmails(container: MedusaContainer): Promise<string[]> {
  try {
    // Get the user service
    const userService = container.resolve(Modules.USER);

    // Get all users (they are all admins in this system)
    const users = await userService.listUsers({});

    // Extract emails
    const adminEmails = users.map((user: any) => user.email);

    // If no admin users found, use a default email
    if (!adminEmails.length) {
      return ["<EMAIL>"];
    }

    return adminEmails;
  } catch (error) {
    console.error("Error getting admin emails:", error);
    // Fallback to default email
    return ["<EMAIL>"];
  }
}
