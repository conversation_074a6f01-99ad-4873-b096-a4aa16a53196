/**
 * Simple event bus for handling application-wide events
 */

type EventCallback = (...args: any[]) => void;

interface EventMap {
  [eventName: string]: EventCallback[];
}

class EventBus {
  private events: EventMap = {};

  /**
   * Subscribe to an event
   * @param eventName - The name of the event to subscribe to
   * @param callback - The callback function to execute when the event is emitted
   * @returns A function to unsubscribe from the event
   */
  on(eventName: string, callback: EventCallback): () => void {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }

    this.events[eventName].push(callback);

    // Return a function to unsubscribe
    return () => {
      this.events[eventName] = this.events[eventName].filter(
        (cb) => cb !== callback
      );
    };
  }

  /**
   * Emit an event
   * @param eventName - The name of the event to emit
   * @param args - Arguments to pass to the callback functions
   */
  emit(eventName: string, ...args: any[]): void {
    if (this.events[eventName]) {
      this.events[eventName].forEach((callback) => {
        callback(...args);
      });
    }
  }

  /**
   * Remove all event listeners
   * @param eventName - Optional event name to clear only specific event listeners
   */
  clear(eventName?: string): void {
    if (eventName) {
      delete this.events[eventName];
    } else {
      this.events = {};
    }
  }
}

// Create a singleton instance
const eventBus = new EventBus();

// Define event names as constants
export const EVENTS = {
  BOOKING_CANCELLED: 'booking_cancelled',
  AVAILABILITY_UPDATED: 'availability_updated',
  INVENTORY_UPDATED: 'inventory_updated',
};

export default eventBus;
