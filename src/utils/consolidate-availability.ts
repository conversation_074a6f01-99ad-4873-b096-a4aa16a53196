import { format, parseISO, addDays, isEqual } from "date-fns";

/**
 * Type definition for individual date-based availability entry
 */
type DateBasedAvailability = {
  room_id: string;
  room_number: string;
  room_name: string;
  config_name: string;
  date: string;
  status: string;
  quantity: number;
  dynamic_price: number | null;
  notes: string;
  order_id: string;
};

/**
 * Type definition for consolidated date range-based availability entry
 */
type RangeBasedAvailability = {
  room_id: string;
  room_number: string;
  room_name: string;
  config_name: string;
  from_date: string;
  to_date: string;
  status: string;
  quantity: number;
  dynamic_price: number | null;
  notes: string;
  order_id: string;
};

/**
 * Checks if two dates are consecutive
 *
 * @param date1 - First date in 'YYYY-MM-DD' format
 * @param date2 - Second date in 'YYYY-MM-DD' format
 * @returns True if date2 is exactly one day after date1
 */
function areDatesConsecutive(date1: string, date2: string): boolean {
  const d1 = parseISO(date1);
  const d2 = parseISO(date2);

  // Create a date that's one day after d1
  const nextDay = addDays(d1, 1);

  // Check if d2 is the same as nextDay
  return isEqual(nextDay, d2);
}

/**
 * Consolidates individual date-based availability entries into date ranges
 *
 * @param availability - Array of individual date-based availability entries
 * @returns Array of consolidated date range-based availability entries
 */
export function consolidateAvailability(
  availability: DateBasedAvailability[]
): RangeBasedAvailability[] {
  if (!availability || !availability.length) {
    return [];
  }

  // Sort availability by room_id and date
  const sortedAvailability = [...availability].sort((a, b) => {
    if (a.room_id !== b.room_id) {
      return a.room_id.localeCompare(b.room_id);
    }
    return a.date.localeCompare(b.date);
  });

  const result: RangeBasedAvailability[] = [];
  let currentRange: RangeBasedAvailability | null = null;

  for (const entry of sortedAvailability) {
    // If this is the first entry or we're starting a new room
    if (!currentRange || currentRange.room_id !== entry.room_id) {
      // If we have a current range, add it to the result
      if (currentRange) {
        result.push(currentRange);
      }

      // Start a new range
      currentRange = {
        room_id: entry.room_id,
        room_number: entry.room_number,
        room_name: entry.room_name,
        config_name: entry.config_name,
        from_date: entry.date,
        to_date: entry.date,
        status: entry.status,
        quantity: entry.quantity,
        dynamic_price: entry.dynamic_price,
        notes: entry.notes,
        order_id: entry.order_id,
      };
      continue;
    }

    // Check if this entry can be added to the current range
    // 1. Properties must match
    // 2. Dates must be consecutive
    const propertiesMatch =
      currentRange.status === entry.status &&
      currentRange.quantity === entry.quantity &&
      currentRange.notes === entry.notes &&
      currentRange.dynamic_price === entry.dynamic_price;

    const datesAreConsecutive = areDatesConsecutive(
      currentRange.to_date,
      entry.date
    );

    if (propertiesMatch && datesAreConsecutive) {
      // Update the to_date of the current range
      currentRange.to_date = entry.date;
    } else {
      // Add the current range to the result and start a new one
      result.push(currentRange);
      currentRange = {
        room_id: entry.room_id,
        room_number: entry.room_number,
        room_name: entry.room_name,
        config_name: entry.config_name,
        from_date: entry.date,
        to_date: entry.date,
        status: entry.status,
        quantity: entry.quantity,
        dynamic_price: entry.dynamic_price,
        notes: entry.notes,
        order_id: entry.order_id,
      };
    }
  }

  // Add the last range if it exists
  if (currentRange) {
    result.push(currentRange);
  }

  return result;
}

/**
 * Adjusts the to_date for noon-to-noon concept
 * For noon-to-noon bookings, the to_date should be the day after the last day
 *
 * @param availability - Array of consolidated date range-based availability entries
 * @returns Array of adjusted date range-based availability entries
 */
export function adjustForNoonToNoon(
  availability: RangeBasedAvailability[]
): RangeBasedAvailability[] {
  return availability.map((entry) => {
    // Parse the to_date
    const toDate = parseISO(entry.to_date);

    // Check if this is a 1-day period (from_date equals to_date)
    const isOneDayPeriod = entry.from_date === entry.to_date;

    // Add one day to the to_date for noon-to-noon concept
    // This ensures the checkout is at noon on the day after the last night
    const nextDay = addDays(toDate, 1);

    // Log for debugging 1-day periods
    if (isOneDayPeriod) {
      console.log(
        `Adjusting 1-day period for room ${entry.room_id}: ${
          entry.from_date
        } to ${format(nextDay, "yyyy-MM-dd")}`
      );
    }

    return {
      ...entry,
      to_date: format(nextDay, "yyyy-MM-dd"),
    };
  });
}
