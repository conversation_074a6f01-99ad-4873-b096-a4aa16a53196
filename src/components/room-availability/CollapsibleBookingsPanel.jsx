import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from '@camped-ai/icons';
import { Calendar } from 'lucide-react';
import './CollapsibleBookingsPanel.css';

/**
 * A modern collapsible panel for displaying bookings
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Content to display inside the panel
 * @param {string} props.title - Title of the panel
 */
const CollapsibleBookingsPanel = ({ children, title = 'Bookings' }) => {
  // State to track if bookings panel is expanded
  const [isExpanded, setIsExpanded] = useState(false);

  // Load saved state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('bookingsPanelExpanded');
    if (savedState !== null) {
      setIsExpanded(savedState === 'true');
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('bookingsPanelExpanded', isExpanded);
  }, [isExpanded]);

  const togglePanel = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      {/* Overlay for when panel is expanded on mobile */}
      {isExpanded && (
        <div className="bookings-overlay" onClick={togglePanel}></div>
      )}

      {/* Bookings panel - modern slide-out */}
      <div className={`bookings-panel ${isExpanded ? 'expanded' : 'collapsed'}`}>
        <div className="bookings-toggle">
          <button
            className="toggle-button"
            onClick={togglePanel}
            aria-label={isExpanded ? "Collapse bookings panel" : "Expand bookings panel"}
          >
            {isExpanded ? <ChevronRight /> : <ChevronLeft />}
            <span className="toggle-label">{isExpanded ? '' : title}</span>
          </button>
        </div>

        <div className="bookings-content">
          <div className="bookings-header">
            <h3>{title}</h3>
          </div>

          <div className="bookings-body">
            {children}
          </div>
        </div>
      </div>
    </>
  );
};

/**
 * Component for displaying empty state when no bookings are available
 * @param {Object} props - Component props
 * @param {string} props.title - Main message to display
 * @param {string} props.subtitle - Secondary message to display
 * @param {React.ReactNode} props.icon - Icon to display
 */
export const EmptyBookingsState = ({
  title = 'No unallocated bookings found',
  subtitle = 'All bookings have been assigned to rooms',
  icon
}) => {
  return (
    <div className="empty-bookings">
      <div className="icon-container">
        {icon || <Calendar size={48} color="#ccc" />}
      </div>
      <p className="empty-title">{title}</p>
      <p className="empty-subtitle">{subtitle}</p>
    </div>
  );
};

export default CollapsibleBookingsPanel;
