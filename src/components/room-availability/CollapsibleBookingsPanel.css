/* Modern styling for Collapsible Bookings Panel */

/* Overlay for mobile when panel is expanded */
.bookings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  display: none;
}

/* Bookings panel styling */
.bookings-panel {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 320px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 20;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Panel states */
.bookings-panel.expanded {
  transform: translateX(0);
}

.bookings-panel.collapsed {
  transform: translateX(calc(100% - 40px));
}

/* Toggle button container */
.bookings-toggle {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%) translateX(-100%);
  z-index: 21;
}

/* Toggle button styling */
.toggle-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 8px 0 0 8px;
  padding: 12px 8px;
  cursor: pointer;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.toggle-button:hover {
  background-color: #f0f0f0;
}

.toggle-label {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform: rotate(180deg);
  margin-top: 8px;
  font-weight: 500;
  color: #555;
}

/* Bookings content */
.bookings-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.bookings-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eaeaea;
}

.bookings-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.bookings-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* Empty state styling */
.empty-bookings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.icon-container {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtitle {
  font-size: 14px;
  color: #777;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bookings-overlay {
    display: block;
  }
  
  .bookings-panel {
    width: 280px;
  }
}
