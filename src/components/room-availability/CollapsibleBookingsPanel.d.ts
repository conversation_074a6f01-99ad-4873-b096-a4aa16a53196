import React from 'react';

interface CollapsibleBookingsPanelProps {
  children: React.ReactNode;
  title?: string;
}

interface EmptyBookingsStateProps {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
}

declare const CollapsibleBookingsPanel: React.FC<CollapsibleBookingsPanelProps>;
export const EmptyBookingsState: React.FC<EmptyBookingsStateProps>;

export default CollapsibleBookingsPanel;
