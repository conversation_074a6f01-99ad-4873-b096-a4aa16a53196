import { Modules, MedusaError } from "@camped-ai/framework/utils";
import { HOTEL_PRICING_MODULE } from "../modules/hotel-management/hotel-pricing";

/**
 * Interface for price selection context
 */
interface PriceSelectionContext {
  currency_code?: string;
  check_in_date?: string;
  check_out_date?: string;
  guest_count?: number;
  children?: Array<{ age: number; count: number }>;
  sales_channel_id?: string; // Added sales_channel_id for channel-specific pricing
  [key: string]: any;
}

/**
 * Interface for price selection result
 */
interface PriceSelectionResult {
  originalPrice: number;
  calculatedPrice: number;
  prices: Array<{
    id: string;
    currency_code: string;
    amount: number;
    min_quantity: number;
    max_quantity: number;
  }>;
  originalPriceIncludesTax: boolean;
  calculatedPriceIncludesTax: boolean;
}

/**
 * HotelPriceSelectionStrategy
 *
 * Custom price selection strategy for hotel room pricing.
 */
export default class HotelPriceSelectionStrategy {
  static identifier = "hotel-pricing";

  // Services
  private productService_: any;
  private currencyService_: any;
  private hotelPricingService_: any;
  private salesChannelService_: any;

  /**
   * Constructor
   */
  constructor(container: any) {
    this.productService_ = container.resolve(Modules.PRODUCT);
    this.currencyService_ = container.resolve(Modules.CURRENCY);
    this.hotelPricingService_ = container.resolve(HOTEL_PRICING_MODULE);
    this.salesChannelService_ = container.resolve(Modules.SALES_CHANNEL);
  }

  /**
   * Calculate prices for product variants
   * @param {Array<{variantId: string, quantity?: number}>} data - The variants to calculate prices for
   * @param {PriceSelectionContext} context - The pricing context
   * @returns {Promise<Map<string, PriceSelectionResult>>} A map of variant IDs to price selection results
   */
  async calculateVariantPrice(
    data: { variantId: string; quantity?: number }[],
    context: PriceSelectionContext
  ): Promise<Map<string, PriceSelectionResult>> {
    const results = new Map<string, PriceSelectionResult>();

    for (const { variantId, quantity = 1 } of data) {
      try {
        // 1. Retrieve the linked room configuration
        const roomConfig = await this.hotelPricingService_.getRoomConfigByVariant(variantId);

        // 2. Retrieve the base price for this roomConfig
        const basePrice = await this.hotelPricingService_.getBasePrice(roomConfig, context);

        // 3. Apply seasonal overrides if the booking dates match any
        const priceWithSeasonalOverrides = await this.hotelPricingService_.applySeasonalOverrides(
          roomConfig,
          basePrice,
          context
        );

        // 4. Apply person pricing rules (e.g., extra guest surcharges)
        const priceWithPersonRules = await this.hotelPricingService_.applyPersonRules(
          roomConfig,
          priceWithSeasonalOverrides,
          context
        );

        // 5. Apply child age bracket pricing
        const priceWithChildRules = await this.hotelPricingService_.applyChildAgeBrackets(
          roomConfig,
          priceWithPersonRules,
          context
        );

        // 6. Apply sales channel overrides if a sales channel is specified
        let finalPrice = priceWithChildRules;
        if (context.sales_channel_id) {
          finalPrice = await this.applySalesChannelOverrides(
            roomConfig,
            basePrice,
            priceWithChildRules,
            context
          );
        }

        // 7. Convert price to order currency if needed
        let priceInCurrency = finalPrice;
        if (context.currency_code && basePrice.currency_code !== context.currency_code) {
          priceInCurrency = await this.hotelPricingService_.convertCurrency(
            finalPrice,
            context.currency_code
          );
        }

        // Prepare the result object
        const priceSelectionResult: PriceSelectionResult = {
          originalPrice: basePrice.amount,
          calculatedPrice: priceInCurrency,
          prices: [
            {
              id: basePrice.id,
              currency_code: context.currency_code || basePrice.currency_code,
              amount: priceInCurrency,
              min_quantity: quantity,
              max_quantity: quantity,
            },
          ],
          originalPriceIncludesTax: false,
          calculatedPriceIncludesTax: false,
        };

        results.set(variantId, priceSelectionResult);
      } catch (error) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Failed to calculate price for variant ${variantId}: ${error.message}`
        );
      }
    }

    return results;
  }

  /**
   * Apply sales channel overrides to the price
   * @param {any} roomConfig - The room configuration
   * @param {any} basePrice - The base price object
   * @param {number} currentPrice - The current calculated price
   * @param {PriceSelectionContext} context - The pricing context
   * @returns {Promise<number>} The price with sales channel overrides applied
   */
  private async applySalesChannelOverrides(
    roomConfig: any,
    basePrice: any,
    currentPrice: number,
    context: PriceSelectionContext
  ): Promise<number> {
    try {
      // Get all channel price overrides for this base price rule
      const channelOverrides = await this.hotelPricingService_.listChannelPriceOverrides({
        base_price_rule_id: basePrice.id,
        sales_channel_id: context.sales_channel_id,
      });

      // If no overrides found, return the current price
      if (!channelOverrides || channelOverrides.length === 0) {
        return currentPrice;
      }

      // Get the first override (there should only be one per sales channel)
      const override = channelOverrides[0];

      // Check if this is a percentage-based override
      if (override.metadata && override.metadata.percentage_override !== undefined) {
        const percentageOverride = Number(override.metadata.percentage_override);

        // Calculate the new price based on the percentage
        // If percentage is 10, add 10% to the base price
        // If percentage is -10, subtract 10% from the base price
        const percentageFactor = 1 + (percentageOverride / 100);
        return Math.round(currentPrice * percentageFactor);
      }

      // Otherwise, use the fixed price override
      return override.amount;
    } catch (error) {
      console.error("Error applying sales channel overrides:", error);
      // If there's an error, return the current price
      return currentPrice;
    }
  }
}
