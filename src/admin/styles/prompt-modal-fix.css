/**
 * CSS fixes for Prompt modal z-index issues
 * This ensures the delete confirmation dialog appears above all other elements
 */

/* Target the NativePrompt component */
.camped-ui-dialog,
[data-dialog-content],
[role="dialog"] {
  z-index: 10000 !important; /* Higher than other modals */
}

.camped-ui-dialog-overlay,
[data-dialog-overlay] {
  z-index: 9999 !important; /* Higher than other modal overlays */
}

/* Target specific dialog elements */
.camped-ui-dialog-content {
  z-index: 10000 !important;
  position: relative !important;
}

/* Ensure the dialog appears above everything else */
[data-radix-popper-content-wrapper] {
  z-index: 10000 !important;
}
