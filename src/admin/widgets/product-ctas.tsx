import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Button, Toaster, toast } from "@camped-ai/ui";
import { BroomSparkle, ArrowUpRightOnBox } from "@camped-ai/icons";
import { useQuery, useMutation } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { useLocation } from "react-router-dom";

const ProductCTAsWidget = () => {
  const location = useLocation();
  const productId = location.pathname.split("/").pop() || "";

  const {
    data: storeData,
    isLoading: isStoreLoading,
    isError: isStoreError,
  } = useQuery<any>({
    queryFn: () => sdk.client.fetch(`/admin/stores`, {}),
    queryKey: [["store"]],
  });

  const storeId = storeData?.stores?.[0]?.id;

  const {
    data: storeDetailsData,
    isLoading: isStoreDetailsLoading,
    isError: isStoreDetailsError,
  } = useQuery<any>({
    queryFn: () => sdk.client.fetch(`/admin/store-details/${storeId}`, {}),
    queryKey: [["store_details", storeId]],
    enabled: !!storeId,
  });

  const storefrontUrl = storeDetailsData?.storeDetails?.[0]?.storefront_url;

  // Fetch product details
  const { data: product, isLoading } = useQuery({
    queryKey: ["product", productId],
    queryFn: async () => {
      if (!productId) return { id: "", name: "" };
      return await sdk.client.fetch(`/admin/products/${productId}`);
    },
  });

  const deployHookURL = import.meta.env.VITE_VERCEL_DEPLOY_HOOK_URL ?? "";

  // Revalidate API call using useMutation
  const revalidateMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(deployHookURL, { method: "POST" });

      if (!response.ok) throw new Error("Failed to revalidate");
      return response.json();
    },
    onSuccess: () => {
      toast.success("Cleared Cache!", {
        description: "Wait for 2 to 3 mins for the changes to take effect.",
      });
    },
    onError: (error) => {
      console.error("Error triggering redeploy:", error);

      toast.error("Failed to clear cache.", {
        description: "An error occurred while clearing the cache.",
      });
    },
  });

  return (
    <Container className="divide-y p-0">
      <Toaster />
      <div className="flex items-center justify-end px-6 py-4 gap-4">
        <Button
          variant="secondary"
          onClick={() => revalidateMutation.mutate()}
          isLoading={revalidateMutation.isPending}
          className="h-6"
        >
          <BroomSparkle /> Clear Cache
        </Button>
        <Button
          variant="secondary"
          onClick={() =>
            window.open(`${storefrontUrl}/products/${product?.product?.handle}`)
          }
          isLoading={revalidateMutation.isPending}
          className="h-6"
        >
          <ArrowUpRightOnBox /> Preview
        </Button>
      </div>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "product.details.before",
});

export default ProductCTAsWidget;
