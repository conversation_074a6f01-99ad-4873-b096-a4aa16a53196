import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import NotificationFeed from "../components/notification-feed";
import { Button } from "@camped-ai/ui";
import { Link } from "react-router-dom";

const NotificationFeedWidget = () => {
  return (
    <div>
      <NotificationFeed />
      <div className="mt-2 text-center">
        <Link to="/app/notifications">
          <Button variant="secondary" size="small">View All Notifications</Button>
        </Link>
      </div>
    </div>
  );
};

export const config = defineWidgetConfig({
  zone: "layout.top-nav.after",
});

export default NotificationFeedWidget;
