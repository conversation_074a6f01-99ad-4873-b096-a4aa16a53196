import {
  Container,
  Heading,
  Text,
  Input,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON>kel<PERSON>,
  <PERSON><PERSON>,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState } from "react";
import { PencilSquare } from "@camped-ai/icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { ActionMenu } from "../common/components/action-menu";
import { useLocation } from "react-router-dom";

interface MetafieldDefinition {
  id: string;
  namespace: string;
  key: string;
  label: string;
}

interface DestinationMetafield {
  id?: string;
  definition_id: string;
  value: string;
}

interface AllDestinationMetaFieldsData {
  metafieldDefinition: MetafieldDefinition[];
}

interface DestinationMetaFieldsData {
  metafield: DestinationMetafield[];
}

interface CombinedField extends MetafieldDefinition {
  metafieldId?: string;
  value?: string;
}

const DestinationMetafieldsWidget = () => {
  const queryClient = useQueryClient();
  const location = useLocation();
  const destination_id = location.pathname.split("/").pop() || "";

  const allMetaQuery = useQuery<AllDestinationMetaFieldsData>({
    queryFn: () =>
      sdk.client.fetch(`/admin/metafield-definitions?owner_type=destination`),
    queryKey: ["metafield-definitions"],
  });

  const metaQuery = useQuery<DestinationMetaFieldsData>({
    queryFn: () =>
      destination_id
        ? sdk.client.fetch(`/admin/metafields/${destination_id}`)
        : Promise.resolve({ metafield: [] }),
    queryKey: ["metafields", destination_id],
  });

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [editedValues, setEditedValues] = useState<Record<string, string>>({});

  const definitions = allMetaQuery.data?.metafieldDefinition || [];
  const fields = metaQuery.data?.metafield || [];

  const combinedFields: CombinedField[] = definitions.map((def) => {
    const matchingField = fields.find(
      (field) => field.definition_id === def.id
    );
    return {
      ...def,
      metafieldId: matchingField?.id,
      value: matchingField?.value || "",
    };
  });

  const openDrawer = () => {
    setEditedValues(
      Object.fromEntries(
        combinedFields.map((field) => [field.id, field.value || ""])
      )
    );
    setIsDrawerOpen(true);
  };

  const handleChange = (id: string, value: string) => {
    setEditedValues((prev) => ({ ...prev, [id]: value }));
  };

  const updateMutation = useMutation({
    mutationFn: async () => {
      const promises = combinedFields.map(async (field) => {
        const newValue = editedValues[field.id];
        if (newValue === field.value) return; // Skip if unchanged

        if (field.metafieldId) {
          // Update existing metafield
          return sdk.client.fetch(`/admin/metafields/${field.metafieldId}`, {
            method: "PUT",
            body: JSON.stringify({
              value: newValue,
            }),
          });
        } else if (newValue) {
          // Create new metafield
          return sdk.client.fetch(`/admin/metafields`, {
            method: "POST",
            body: JSON.stringify({
              owner_id: destination_id,
              owner_type: "destination",
              definition_id: field.id,
              value: newValue,
            }),
          });
        }
      });

      await Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["metafields", destination_id],
      });
      setIsDrawerOpen(false);
      toast.success("Success", {
        description: "Metafields updated successfully",
      });
    },
    onError: () => {
      toast.error("Error", {
        description: "Failed to update metafields",
      });
    },
  });

  const handleSave = () => {
    updateMutation.mutate();
  };

  return (
    <Container className="p-0">
      <Toaster />
      <div className="flex items-center justify-between px-6 py-4 border-b">
        <Heading level="h2" className="text-lg font-medium">
          Metafields
        </Heading>
        <ActionMenu
          groups={[
            {
              actions: [
                {
                  label: "Edit",
                  icon: <PencilSquare className="w-4 h-4" />,
                  onClick: openDrawer,
                },
              ],
            },
          ]}
        />
      </div>
      {allMetaQuery.isLoading || metaQuery.isLoading ? (
        <Container className="p-6">
          <Skeleton className="mb-4 h-10" />
          <Skeleton className="mb-2 h-5" />
        </Container>
      ) : allMetaQuery.isError || metaQuery.isError ? (
        <Container className="p-4">
          <Alert>An error occurred while fetching metafields data.</Alert>
        </Container>
      ) : (
        <div className="divide-y divide-primary">
          {combinedFields.map((field) => (
            <div
              key={field.id}
              className="flex items-center justify-between px-6 py-4"
            >
              <Text className=" font-medium">{field.label}</Text>
              <Text>{field.value || "-"}</Text>
            </div>
          ))}
        </div>
      )}

      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Edit Metafields</Drawer.Title>
          </Drawer.Header>
          <Drawer.Body className="flex flex-col gap-4">
            {combinedFields.map((field) => (
              <div key={field.id} className="flex flex-col gap-2">
                <Text className="font-medium">{field.label}</Text>
                <Input
                  value={editedValues[field.id] || ""}
                  onChange={(e) => handleChange(field.id, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                />
              </div>
            ))}
          </Drawer.Body>
          <Drawer.Footer className="flex gap-2">
            <Button variant="secondary" onClick={() => setIsDrawerOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave} isLoading={updateMutation.isLoading}>
              Save
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>
    </Container>
  );
};

export default DestinationMetafieldsWidget;
