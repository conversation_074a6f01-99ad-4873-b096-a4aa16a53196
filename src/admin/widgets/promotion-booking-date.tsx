import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  DatePicker,
  toast,
} from "@camped-ai/ui";
import { PencilSquare } from "@camped-ai/icons";
import { ActionMenu } from "../components/ActionMenu";
import { useEffect, useState } from "react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { DateRangeDisplay } from "../components/date-range-display";
import { sdk } from "../lib/sdk";
import { useForm } from "react-hook-form";
import { Form } from "../components/form";

const PromotionBookingDateAsWidget = ({ data }: any) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  const {
    data: campaignDetails,
    isLoading: isCampaignDetailsLoading,
    isError: isCampaignDetailsError,
  } = useQuery({
    queryFn: () => sdk.client.fetch(`/admin/campaign-extension/${data.id}`),
    queryKey: ["campaign-extension", data?.id],
    enabled: !!data?.id,
  });
  const queryClient = useQueryClient();

  const id = (campaignDetails as any)?.campaign_extension?.[0]?.id;
  const booking_from_date = (campaignDetails as any)?.campaign_extension?.[0]
    ?.booking_from_date;
  const booking_to_date = (campaignDetails as any)?.campaign_extension?.[0]
    ?.booking_to_date;

  const form = useForm({
    defaultValues: {
      booking_from_date: booking_from_date
        ? new Date(booking_from_date)
        : undefined,
      booking_to_date: booking_to_date ? new Date(booking_to_date) : undefined,
    },
  });
  useEffect(() => {
    form.reset({
      booking_from_date: booking_from_date
        ? new Date(booking_from_date)
        : undefined,
      booking_to_date: booking_to_date ? new Date(booking_to_date) : undefined,
    });
  }, [booking_from_date, booking_to_date]);

  const updateMutation = useMutation({
    mutationFn: async (updatedFields: Record<string, string>) => {
      let campaign = {};
      if (id) {
        campaign = await sdk.client.fetch(`/admin/campaign-extension`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: {
            ...updatedFields,
            id,
            campaign_id: data.id,
          },
        });

        return campaign;
      } else {
        campaign = await sdk.client.fetch(`/admin/campaign-extension`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: {
            ...updatedFields,
            campaign_id: data.id,
          },
        });
      }
      return campaign;
    },
    onSuccess: () => {
      toast.success("Updated Successfully!", {
        description: "Metafields have been updated.",
      });

      // @ts-ignore
      queryClient.invalidateQueries(["campaign-extension", data?.id]);
      setIsDrawerOpen(false);
    },
    onError: (error) => {
      toast.error("Update Failed", {
        description: "An error occurred while updating the metafields.",
      });
    },
  });
  const handleSave = (data: any) => {
    if (Object.values(data).every((val) => val === null)) {
      toast.warning("Empty Fields", {
        description: "Please fill in at least one field before saving.",
      });
      return;
    }
    updateMutation.mutate(data);
  };

  return (
    <Container className="px-6">
      <div className="flex items-center justify-between py-2">
        <Heading level="h2">Booking Date</Heading>
        <ActionMenu
          groups={[
            {
              actions: [
                {
                  icon: <PencilSquare />,
                  label: "Edit",
                  onClick: openDrawer,
                },
              ],
            },
          ]}
        />
      </div>

      {isCampaignDetailsLoading ? (
        <>Loading...</>
      ) : (
        <DateRangeDisplay
          startsAt={booking_from_date ?? undefined}
          endsAt={booking_to_date ?? undefined}
          showTime
        />
      )}
      <Drawer
        open={isDrawerOpen}
        // @ts-ignore
        onClose={() => setIsDrawerOpen(false)}
        onOpenChange={setIsDrawerOpen}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Edit Booking Date</Drawer.Title>
          </Drawer.Header>{" "}
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSave)}
              className="flex flex-col gap-y-4"
            >
              <Drawer.Body className="p-4 space-y-4">
                <Form.Field
                  control={form.control}
                  name="booking_from_date"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Booking Start Date</Form.Label>

                        <Form.Control>
                          <DatePicker
                            granularity="minute"
                            hourCycle={12}
                            shouldCloseOnSelect={false}
                            {...field}
                          />
                        </Form.Control>

                        <Form.ErrorMessage />
                      </Form.Item>
                    );
                  }}
                />

                <Form.Field
                  control={form.control}
                  name="booking_to_date"
                  render={({ field }) => {
                    return (
                      <Form.Item>
                        <Form.Label>Booking End Date</Form.Label>

                        <Form.Control>
                          <DatePicker
                            granularity="minute"
                            shouldCloseOnSelect={false}
                            {...field}
                          />
                        </Form.Control>

                        <Form.ErrorMessage />
                      </Form.Item>
                    );
                  }}
                />
              </Drawer.Body>
              <Drawer.Footer className="flex gap-2">
                <Button
                  variant="secondary"
                  onClick={() => setIsDrawerOpen(false)}
                >
                  Cancel
                </Button>
                {/* @ts-ignore */}
                <Button type="submit">Save</Button>
              </Drawer.Footer>{" "}
            </form>
          </Form>
        </Drawer.Content>
      </Drawer>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "campaign.details.side.after",
});

export default PromotionBookingDateAsWidget;
