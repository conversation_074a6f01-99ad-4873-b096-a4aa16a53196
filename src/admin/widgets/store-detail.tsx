import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import {
  clx,
  Container,
  Heading,
  Text,
  Input,
  Button,
  Skeleton,
  Alert,
  Drawer,
} from "@camped-ai/ui";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { useState } from "react";
import { PencilSquare } from "@camped-ai/icons";
import { ActionMenu } from "../common/components/action-menu";

const StorefrontWidget = () => {
  const queryClient = useQueryClient();
  const {
    data: storeData,
    isLoading: isStoreLoading,
    isError: isStoreError,
  } = useQuery({
    queryFn: () => sdk.client.fetch(`/admin/stores`),
    queryKey: ["store"],
  });

  // @ts-ignore
  const storeId = storeData?.stores?.[0]?.id;

  const {
    data: storeDetailsData,
    isLoading: isStoreDetailsLoading,
    isError: isStoreDetailsError,
  } = useQuery({
    queryFn: () => sdk.client.fetch(`/admin/store-details/${storeId}`),
    queryKey: ["store_details", storeId],
    enabled: !!storeId,
  });

  // @ts-ignore
  const storefrontUrl = storeDetailsData?.storeDetails?.[0]?.storefront_url;
  const [isEditing, setEditing] = useState(false);
  const [newStorefrontURL, setNewStorefrontURL] = useState(storefrontUrl || "");
  const authToken = localStorage.getItem("medusa_auth_token");

  const mutation = useMutation({
    mutationFn: async (updatedURL: string) => {
      await sdk.client.fetch(`/admin/store-details/${storeId}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: { storefront_url: updatedURL },
      });

      return updatedURL;
    },
    onSuccess: (updatedURL) => {
      setEditing(false);
      queryClient.setQueryData(["store_details", storeId], (prevData: any) => ({
        ...prevData,
        storeDetails: [
          { ...prevData?.storeDetails?.[0], storefront_url: updatedURL },
        ],
      }));
      // @ts-ignore
      queryClient.invalidateQueries(["store_details", storeId]);
    },
  });

  const handleSave = () => {
    if (!newStorefrontURL.trim()) return;
    mutation.mutate(newStorefrontURL);
  };

  const handleEditClick = () => {
    setNewStorefrontURL(storefrontUrl || "");
    setEditing(true);
  };

  if (isStoreLoading || isStoreDetailsLoading) {
    return (
      <Container className="p-6">
        <Skeleton className="mb-4 h-10" />
        <Skeleton className="mb-2 h-5" />
      </Container>
    );
  }

  if (isStoreError || isStoreDetailsError) {
    return (
      <Container className="p-4">
        <Alert>An error occurred while fetching data.</Alert>
      </Container>
    );
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Storefront</Heading>
        <ActionMenu
          groups={[
            {
              actions: [
                {
                  icon: <PencilSquare />,
                  label: "Edit",
                  onClick: handleEditClick,
                },
              ],
            },
          ]}
        />
      </div>

      <div
        className={clx(
          "grid grid-cols-2 items-center px-6 py-4 text-ui-fg-subtle"
        )}
      >
        <Text size="small" weight="plus" leading="compact">
          Storefront URL
        </Text>
        <Text
          size="small"
          leading="compact"
          className="whitespace-pre-line text-pretty"
        >
          {storefrontUrl || "-"}
        </Text>
      </div>

      <Drawer
        open={isEditing}
        // @ts-ignore
        onClose={() => setEditing(false)}
        onOpenChange={setEditing}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Edit Storefront URL</Drawer.Title>
          </Drawer.Header>
          <Drawer.Body className="p-4">
            <Input
              value={newStorefrontURL}
              onChange={(e) => setNewStorefrontURL(e.target.value)}
              placeholder="Enter new Storefront URL"
              // @ts-ignore
              disabled={mutation.isLoading}
            />
          </Drawer.Body>
          <Drawer.Footer>
            <Button
              variant="secondary"
              onClick={() => setEditing(false)}
              // @ts-ignore
              disabled={mutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              // @ts-ignore
              isLoading={mutation.isLoading}
              disabled={!newStorefrontURL.trim()}
            >
              Save
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "store.details.after",
});

export default StorefrontWidget;
