import {
  Container,
  <PERSON><PERSON>,
  Text,
  Input,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState } from "react";
import { PencilSquare } from "@camped-ai/icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { ActionMenu } from "../common/components/action-menu";
import { useLocation } from "react-router-dom";

interface MetafieldDefinition {
  id: string;
  namespace: string;
  key: string;
  label: string;
}

interface RoomTypeMetafield {
  id?: string;
  definition_id: string;
  value: string;
}

interface AllRoomTypeMetaFieldsData {
  metafieldDefinition: MetafieldDefinition[];
}

interface RoomTypeMetaFieldsData {
  metafield: RoomTypeMetafield[];
}

interface CombinedField extends MetafieldDefinition {
  metafieldId?: string;
  value?: string;
}

const RoomTypeMetafieldsWidget = () => {
  const queryClient = useQueryClient();
  const location = useLocation();
  const room_type_id = location.pathname.split("/").pop() || "";

  const allMetaQuery = useQuery<AllRoomTypeMetaFieldsData>({
    queryFn: () =>
      sdk.client.fetch(`/admin/metafield-definitions?owner_type=room_type`),
    queryKey: ["metafield-definitions"],
  });

  const metaQuery = useQuery<RoomTypeMetaFieldsData>({
    queryFn: () =>
      room_type_id
        ? sdk.client.fetch(`/admin/metafields/${room_type_id}`)
        : Promise.resolve({ metafield: [] }),
    queryKey: ["metafields", room_type_id],
  });

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [editedValues, setEditedValues] = useState<Record<string, string>>({});

  const definitions = allMetaQuery.data?.metafieldDefinition || [];
  const fields = metaQuery.data?.metafield || [];

  const combinedFields: CombinedField[] = definitions.map((def) => {
    const matchingField = fields.find(
      (field) => field.definition_id === def.id
    );
    return {
      ...def,
      metafieldId: matchingField?.id,
      value: matchingField?.value || "",
    };
  });

  const openDrawer = () => {
    setEditedValues(
      Object.fromEntries(
        combinedFields.map((field) => [field.id, field.value || ""])
      )
    );
    setIsDrawerOpen(true);
  };

  const handleChange = (id: string, value: string) => {
    setEditedValues((prev) => ({ ...prev, [id]: value }));
  };

  const updateMutation = useMutation({
    mutationFn: async (updatedFields: Record<string, string>) => {
      const updatePayload = [];

      for (const [id, value] of Object.entries(updatedFields)) {
        const field = combinedFields.find((f) => f.id === id);

        if (field?.metafieldId) {
          updatePayload.push({ id: field.metafieldId, value });
        } else if (field?.id) {
          updatePayload.push({
            owner_id: room_type_id,
            definition_id: field.id,
            value,
          });
        }
      }

      if (updatePayload.length > 0) {
        await sdk.client.fetch(`/admin/metafields`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: updatePayload,
        });
      }
    },
    onSuccess: () => {
      toast.success("Updated Successfully!", {
        description: "Metafields have been updated.",
      });

      // @ts-ignore
      queryClient.invalidateQueries(["metafields", room_type_id]);
      setIsDrawerOpen(false);
    },
    onError: (error) => {
      toast.error("Update Failed", {
        description: "An error occurred while updating the metafields.",
      });
    },
  });

  const handleSave = () => {
    if (Object.values(editedValues).every((val) => val.trim() === "")) {
      toast.warning("Empty Fields", {
        description: "Please fill in at least one field before saving.",
      });
      return;
    }

    updateMutation.mutate(editedValues);
  };

  return (
    <Container className="divide-y p-0">
      <Toaster />
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Room Type Metafields</Heading>
        <ActionMenu
          groups={[
            {
              actions: [
                {
                  icon: <PencilSquare />,
                  label: "Edit",
                  onClick: openDrawer,
                },
              ],
            },
          ]}
        />
      </div>
      {allMetaQuery.isLoading || metaQuery.isLoading ? (
        <Container className="p-6">
          <Skeleton className="mb-4 h-10" />
          <Skeleton className="mb-2 h-5" />
        </Container>
      ) : allMetaQuery.isError || metaQuery.isError ? (
        <Container className="p-4">
          <Alert>An error occurred while fetching metafields data.</Alert>
        </Container>
      ) : (
        <div className="divide-y divide-primary">
          {combinedFields.map((field) => (
            <div
              key={field.id}
              className="flex items-center justify-between px-6 py-4"
            >
              <Text className=" font-medium">{field.label}</Text>
              <Text>{field.value || "-"}</Text>
            </div>
          ))}
        </div>
      )}
      <Drawer
        open={isDrawerOpen}
        // @ts-ignore
        onClose={() => setIsDrawerOpen(false)}
        onOpenChange={setIsDrawerOpen}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Edit Room Type Metafields</Drawer.Title>
          </Drawer.Header>
          <Drawer.Body className="p-4 space-y-4">
            {combinedFields.map((field) => (
              <div key={field.id}>
                <Text className=" font-medium mb-1">{field.label}</Text>
                <Input
                  value={editedValues[field.id] || ""}
                  onChange={(e) => handleChange(field.id, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                />
              </div>
            ))}
          </Drawer.Body>
          <Drawer.Footer className="flex gap-2">
            <Button variant="secondary" onClick={() => setIsDrawerOpen(false)}>
              Cancel
            </Button>
            {/* @ts-ignore */}
            <Button onClick={handleSave} isLoading={updateMutation.isLoading}>
              Save
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>
    </Container>
  );
};

export default RoomTypeMetafieldsWidget;
