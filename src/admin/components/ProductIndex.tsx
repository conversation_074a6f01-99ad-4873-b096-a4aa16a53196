import { useNavigate } from "react-router-dom";
import { Tag, TagSolid } from "@camped-ai/icons";
import { Container, Heading, Label } from "@camped-ai/ui";

const data = [
  { name: "Product", count: 1, tag: <Tag /> },
  // {name: "Categories", count:2, tag:<TagSolid />}
];

const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M8.25 4.5l7.5 7.5-7.5 7.5"
    />
  </svg>
);

const CustomDataPage = () => {
  const navigate = useNavigate();

  const handleNavigation = (name: string) => {
    const route = name.toLowerCase();
    navigate(`/settings/custom/${route}`);
  };

  return (
    <Container>
      <div className="py-5 border-b">
        <Heading className="font-semibold">Metafield definitions</Heading>
        <Label className="text-sm ">
          Add a custom piece of data to a specific part of your store.
        </Label>
      </div>

      {data.map((item, index) => (
        <div key={item.name}>
          <div
            className="flex justify-between items-center w-full py-2 text-left cursor-pointer rounded-md"
            onClick={() => handleNavigation(item.name)}
          >
            <div className="flex flex-row items-center gap-2">
              {item.tag}
              <Heading className="text-base" level="h3">
                {item.name}
              </Heading>
            </div>
            <div className="flex items-center gap-2">
              <ArrowIcon />
            </div>
          </div>
          {index !== data.length - 1 && <hr className="border-b" />}
        </div>
      ))}
    </Container>
  );
};

export default CustomDataPage;
