import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Heading,
  Text,
  Button,
  Label,
} from "@camped-ai/ui";
import { Download, X } from "lucide-react";
import { DestinationData } from "../../types";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
}

const ExportModal: React.FC<ExportModalProps> = ({ open, onClose }) => {
  // State for selected fields
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>({
    id: true,
    name: true,
    handle: true,
    description: true,
    is_active: true,
    is_featured: true,
    country: true,
    currency: true,
    location: true,
    tags: true,
    website: true,
    created_at: true,
    updated_at: true,
  });

  // State for filters
  const [filters, setFilters] = useState({
    is_active: "all", // "all", "true", "false"
    is_featured: "all", // "all", "true", "false"
    country: "all", // "all" means all countries
  });

  // State for file format
  const [fileFormat, setFileFormat] = useState<"csv" | "xlsx">("xlsx");

  // State for available countries
  const [countries, setCountries] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch available countries when modal opens
  useEffect(() => {
    if (open) {
      fetchCountries();
    }
  }, [open]);

  // Fetch unique countries from destinations
  const fetchCountries = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/admin/hotel-management/destinations', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch destinations');
      }

      const { destinations } = await response.json();

      // Extract unique countries
      const uniqueCountries = Array.from(
        new Set(destinations.map((dest: any) => dest.country).filter(Boolean))
      ).sort();

      setCountries(uniqueCountries);
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle field selection
  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  // Handle select all fields
  const handleSelectAll = () => {
    const allFields = { ...selectedFields };
    const allSelected = Object.values(allFields).every((value) => value);

    Object.keys(allFields).forEach((key) => {
      allFields[key] = !allSelected;
    });

    setSelectedFields(allFields);
  };

  // Handle filter changes
  const handleFilterChange = (
    filterName: string,
    value: string | boolean
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add fields
      const fields = Object.entries(selectedFields)
        .filter(([_, selected]) => selected)
        .map(([field]) => field);

      queryParams.append("fields", fields.join(","));

      // Add filters
      if (filters.is_active !== "all") {
        queryParams.append("is_active", filters.is_active);
      }

      if (filters.is_featured !== "all") {
        queryParams.append("is_featured", filters.is_featured);
      }

      if (filters.country && filters.country !== "all") {
        queryParams.append("country", filters.country);
      }

      // Add format
      queryParams.append("format", fileFormat);

      // Create a link to download the file
      const downloadLink = document.createElement("a");
      downloadLink.href = `/admin/destinations/export?${queryParams.toString()}`;
      downloadLink.target = "_blank";
      downloadLink.download = `destinations-export.${fileFormat}`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // Close the modal
      onClose();
    } catch (error) {
      console.error("Error exporting destinations:", error);
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[90vh]">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between items-center px-6 py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Export Destinations
            </Heading>
          </div>
        </FocusModal.Header>
        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Fields Section */}
            <div className="space-y-4 border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <Heading level="h3" className="text-lg font-medium">
                  Select Fields to Export
                </Heading>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={handleSelectAll}
                >
                  {Object.values(selectedFields).every((v) => v)
                    ? "Deselect All"
                    : "Select All"}
                </Button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-[300px] overflow-y-auto pr-2">
                {Object.keys(selectedFields).map((field) => (
                  <div key={field} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`field-${field}`}
                      checked={selectedFields[field]}
                      onChange={(e) => handleFieldChange(field, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded"
                    />
                    <Label htmlFor={`field-${field}`} className="cursor-pointer">
                      {field
                        .replace(/_/g, " ")
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              {/* Filters Section */}
              <div className="space-y-4 border rounded-lg p-4">
                <Heading level="h3" className="text-lg font-medium">
                  Filter Destinations
                </Heading>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="is_active">Active Status</Label>
                    <select
                      id="is_active"
                      value={filters.is_active}
                      onChange={(e) => handleFilterChange("is_active", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">All</option>
                      <option value="true">Active</option>
                      <option value="false">Inactive</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="is_featured">Featured Status</Label>
                    <select
                      id="is_featured"
                      value={filters.is_featured}
                      onChange={(e) => handleFilterChange("is_featured", e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">All</option>
                      <option value="true">Featured</option>
                      <option value="false">Not Featured</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="country">Country</Label>
                    {isLoading ? (
                      <div className="flex items-center justify-center py-2">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
                        <span className="text-sm text-gray-500">Loading countries...</span>
                      </div>
                    ) : (
                      <select
                        id="country"
                        value={filters.country}
                        onChange={(e) => handleFilterChange("country", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="all">All Countries</option>
                        {countries.map((country) => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>

              {/* Format Section */}
              <div className="space-y-4 border rounded-lg p-4">
                <Heading level="h3" className="text-lg font-medium">
                  Export Format
                </Heading>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="format-xlsx"
                      name="fileFormat"
                      value="xlsx"
                      checked={fileFormat === "xlsx"}
                      onChange={() => setFileFormat("xlsx")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="format-xlsx" className="cursor-pointer">
                      Excel (.xlsx)
                    </Label>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      id="format-csv"
                      name="fileFormat"
                      value="csv"
                      checked={fileFormat === "csv"}
                      onChange={() => setFileFormat("csv")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="format-csv" className="cursor-pointer">
                      CSV (.csv)
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </FocusModal.Body>
        <div className="flex-shrink-0 py-4 px-6 bg-white border-t border-gray-200 flex gap-2 justify-end">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default ExportModal;
