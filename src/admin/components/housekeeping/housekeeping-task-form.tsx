import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Select,
  Textarea,
  DatePicker,
  Switch,
  Toaster,
  toast,
  Label,
} from "@camped-ai/ui";
import {
  Calendar,
  Clock,
  User,
  Bed,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { format } from "date-fns";
import Spinner from "../shared/spinner";


type HousekeepingTaskFormProps = {
  hotelId: string;
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: any;
  initialDate?: Date;
};

const HousekeepingTaskForm: React.FC<HousekeepingTaskFormProps> = ({
  hotelId,
  onSubmit,
  onCancel,
  initialData,
  initialDate,
}) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    room_id: "",
    room_config_id: "",
    hotel_id: hotelId,
    task_type: "cleaning",
    status: "pending",
    priority: "medium",
    room_status: "dirty",
    scheduled_date: initialDate ? format(initialDate, "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd"),
    due_time: "14:00",
    assigned_to: "none",
    notes: "",
    guest_request: false,
    recurring: false,
  });

  const [rooms, setRooms] = useState<any[]>([]);
  const [roomConfigs, setRoomConfigs] = useState<any[]>([]);
  const [staff, setStaff] = useState<any[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState<boolean>(true);
  const [isLoadingStaff, setIsLoadingStaff] = useState<boolean>(true);

  // Fetch rooms from API or generate sample data
  const fetchRooms = async () => {
    try {
      setIsLoadingRooms(true);

      // Try different endpoints to fetch rooms
      const roomEndpoints = [
        `/admin/direct-rooms?hotel_id=${hotelId}`,
        `/admin/hotel-management/rooms?hotel_id=${hotelId}`
      ];

      let roomsData = null;
      let roomsSuccess = false;

      for (const endpoint of roomEndpoints) {
        try {
          console.log(`Trying to fetch rooms from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (response.ok) {
            const data = await response.json();

            // Check for different response formats
            if (data.rooms && data.rooms.length > 0) {
              roomsData = data.rooms;
              roomsSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length > 0) {
              roomsData = data;
              roomsSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // Try to fetch room configurations
      const roomConfigEndpoints = [
        `/admin/direct-room-configs?hotel_id=${hotelId}`,
        `/admin/hotel-room-configs?hotel_id=${hotelId}`,
        `/admin/room-configs?hotel_id=${hotelId}`
      ];

      let roomConfigsData = null;
      let roomConfigsSuccess = false;

      for (const endpoint of roomConfigEndpoints) {
        try {
          console.log(`Trying to fetch room configurations from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (response.ok) {
            const data = await response.json();

            // Check for different response formats
            if (data.roomConfigs && data.roomConfigs.length > 0) {
              roomConfigsData = data.roomConfigs;
              roomConfigsSuccess = true;
              break;
            } else if (data.room_configs && data.room_configs.length > 0) {
              roomConfigsData = data.room_configs;
              roomConfigsSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length > 0) {
              roomConfigsData = data;
              roomConfigsSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If we have real data, use it
      if (roomsSuccess && roomsData) {
        setRooms(roomsData);
        console.log('Loaded rooms:', roomsData);
      } else {
        // Otherwise, create sample rooms
        const sampleRooms = Array.from({ length: 10 }, (_, i) => ({
          id: `sample_room_${i}`,
          name: `Room ${101 + i}`,
          room_number: `${101 + i}`,
          status: i % 5 === 0 ? 'dirty' : i % 4 === 0 ? 'cleaning' : i % 3 === 0 ? 'maintenance' : 'clean',
          floor: `${Math.floor(i / 5) + 1}`,
          notes: `Sample room ${101 + i}`,
          is_active: true,
          room_config_id: 'sample_config',
          hotel_id: hotelId
        }));

        setRooms(sampleRooms);
        console.log('Using sample rooms:', sampleRooms);
      }

      // If we have real room configs, use them
      if (roomConfigsSuccess && roomConfigsData) {
        setRoomConfigs(roomConfigsData);
        console.log('Loaded room configs:', roomConfigsData);
      } else {
        // Otherwise, create sample room configs
        const sampleRoomConfigs = [
          {
            id: 'sample_config',
            name: 'Standard Room',
            description: 'A standard hotel room',
            hotel_id: hotelId
          },
          {
            id: 'sample_config_2',
            name: 'Deluxe Room',
            description: 'A deluxe hotel room',
            hotel_id: hotelId
          },
          {
            id: 'sample_config_3',
            name: 'Suite',
            description: 'A luxury suite',
            hotel_id: hotelId
          }
        ];

        setRoomConfigs(sampleRoomConfigs);
        console.log('Using sample room configs:', sampleRoomConfigs);
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      toast.error("Failed to load rooms");
    } finally {
      setIsLoadingRooms(false);
    }
  };

  // Fetch staff from API or generate sample data
  const fetchStaff = async () => {
    try {
      setIsLoadingStaff(true);

      // Try to fetch staff from API
      try {
        const response = await fetch(`/admin/hotel-management/housekeeping/staff?hotel_id=${hotelId}`);

        if (response.ok) {
          const data = await response.json();
          if (data.staff && data.staff.length > 0) {
            setStaff(data.staff);
            console.log('Loaded staff:', data.staff);
            setIsLoadingStaff(false);
            return;
          }
        }
      } catch (apiError) {
        console.warn('API for staff not available yet:', apiError);
      }

      // If API fails or returns no data, generate sample staff
      const sampleStaff = [
        {
          id: 'staff_1',
          name: 'John Doe',
          role: 'Housekeeper',
          status: 'available',
          hotel_id: hotelId
        },
        {
          id: 'staff_2',
          name: 'Jane Smith',
          role: 'Supervisor',
          status: 'busy',
          hotel_id: hotelId
        },
        {
          id: 'staff_3',
          name: 'Bob Johnson',
          role: 'Maintenance',
          status: 'available',
          hotel_id: hotelId
        }
      ];

      setStaff(sampleStaff);
      console.log('Using sample staff:', sampleStaff);
    } catch (error) {
      console.error("Error fetching staff:", error);
      toast.error("Failed to load staff");
      setStaff([]); // Set empty array to prevent errors
    } finally {
      setIsLoadingStaff(false);
    }
  };

  // Initialize form data from initialData if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        room_id: initialData.room_id || "",
        room_config_id: initialData.room_config_id || "",
        hotel_id: hotelId,
        task_type: initialData.task_type || "cleaning",
        status: initialData.status || "pending",
        priority: initialData.priority || "medium",
        room_status: initialData.room_status || "dirty",
        scheduled_date: initialData.scheduled_date
          ? format(new Date(initialData.scheduled_date), "yyyy-MM-dd")
          : format(new Date(), "yyyy-MM-dd"),
        due_time: initialData.due_time || "14:00",
        assigned_to: initialData.assigned_to || "",
        notes: initialData.notes || "",
        guest_request: initialData.guest_request || false,
        recurring: initialData.recurring || false,
      });
    } else if (initialDate) {
      setFormData(prev => ({
        ...prev,
        scheduled_date: format(initialDate, "yyyy-MM-dd"),
      }));
    }
  }, [initialData, initialDate]);

  // Fetch data on component mount
  useEffect(() => {
    fetchRooms();
    fetchStaff();
  }, [hotelId]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch change
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        scheduled_date: format(date, "yyyy-MM-dd")
      }));
    }
  };

  // Handle form submission with mock data
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Log the form data that would be sent to the API
      console.log('Form data submitted:', {
        ...formData,
        id: initialData?.id || `hk_task_${Date.now()}`,
      });

      toast.success(initialData ? "Task updated successfully" : "Task created successfully");
      onSubmit();
    } catch (error) {
      console.error("Error submitting task:", error);
      toast.error(initialData ? "Failed to update task" : "Failed to create task");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get room name by ID
  const getRoomName = (roomId: string) => {
    const room = rooms.find(r => r.id === roomId);
    return room ? `${room.room_number} - ${room.name}` : roomId;
  };

  // Get room config name by ID
  const getRoomConfigName = (configId: string) => {
    const config = roomConfigs.find(c => c.id === configId);
    return config ? config.name : configId;
  };

  // Get staff name by ID
  const getStaffName = (staffId: string) => {
    const staffMember = staff.find(s => s.id === staffId);
    return staffMember ? staffMember.name : staffId;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="p-4 bg-blue-50 border-l-4 border-blue-500 text-blue-700 mb-4">
        <p className="font-bold">Housekeeping Task</p>
        <p>Create a new housekeeping task for room cleaning or maintenance.</p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Room Selection */}
        <div>
          <Label htmlFor="room_id">Room</Label>
          <Select
            id="room_id"
            name="room_id"
            value={formData.room_id}
            onValueChange={(value) => setFormData(prev => ({ ...prev, room_id: value }))}
            disabled={isLoadingRooms || isSubmitting}
            required
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select a room">
                {formData.room_id ? getRoomName(formData.room_id) : "Select a room"}
              </Select.Value>
            </Select.Trigger>
            <Select.Content>
              {isLoadingRooms ? (
                <div className="flex items-center justify-center p-2">
                  <Spinner size="small" />
                  Loading rooms...
                </div>
              ) : rooms.length === 0 ? (
                <div className="p-2 text-center text-gray-500">No rooms found</div>
              ) : (
                rooms.map(room => (
                  <Select.Item key={room.id} value={room.id}>
                    {room.room_number} - {room.name}
                  </Select.Item>
                ))
              )}
            </Select.Content>
          </Select>
        </div>

        {/* Room Config Selection */}
        <div>
          <Label htmlFor="room_config_id">Room Type</Label>
          <Select
            id="room_config_id"
            name="room_config_id"
            value={formData.room_config_id}
            onValueChange={(value) => setFormData(prev => ({ ...prev, room_config_id: value }))}
            disabled={isLoadingRooms || isSubmitting}
            required
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select a room type">
                {formData.room_config_id ? getRoomConfigName(formData.room_config_id) : "Select a room type"}
              </Select.Value>
            </Select.Trigger>
            <Select.Content>
              {isLoadingRooms ? (
                <div className="flex items-center justify-center p-2">
                  <Spinner size="small" />
                  Loading room types...
                </div>
              ) : roomConfigs.length === 0 ? (
                <div className="p-2 text-center text-gray-500">No room types found</div>
              ) : (
                roomConfigs.map(config => (
                  <Select.Item key={config.id} value={config.id}>
                    {config.name}
                  </Select.Item>
                ))
              )}
            </Select.Content>
          </Select>
        </div>

        {/* Task Type */}
        <div>
          <Label htmlFor="task_type">Task Type</Label>
          <Select
            id="task_type"
            name="task_type"
            value={formData.task_type}
            onValueChange={(value) => setFormData(prev => ({ ...prev, task_type: value }))}
            disabled={isSubmitting}
            required
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select task type" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="cleaning">Regular Cleaning</Select.Item>
              <Select.Item value="deep_cleaning">Deep Cleaning</Select.Item>
              <Select.Item value="turndown">Turndown Service</Select.Item>
              <Select.Item value="inspection">Inspection</Select.Item>
              <Select.Item value="maintenance">Maintenance</Select.Item>
            </Select.Content>
          </Select>
        </div>

        {/* Priority */}
        <div>
          <Label htmlFor="priority">Priority</Label>
          <Select
            id="priority"
            name="priority"
            value={formData.priority}
            onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
            disabled={isSubmitting}
            required
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select priority" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="low">Low</Select.Item>
              <Select.Item value="medium">Medium</Select.Item>
              <Select.Item value="high">High</Select.Item>
              <Select.Item value="urgent">Urgent</Select.Item>
            </Select.Content>
          </Select>
        </div>

        {/* Room Status */}
        <div>
          <Label htmlFor="room_status">Room Status</Label>
          <Select
            id="room_status"
            name="room_status"
            value={formData.room_status}
            onValueChange={(value) => setFormData(prev => ({ ...prev, room_status: value }))}
            disabled={isSubmitting}
            required
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select room status" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="dirty">Dirty</Select.Item>
              <Select.Item value="cleaning">Cleaning</Select.Item>
              <Select.Item value="clean">Clean</Select.Item>
              <Select.Item value="inspected">Inspected</Select.Item>
              <Select.Item value="out_of_service">Out of Service</Select.Item>
            </Select.Content>
          </Select>
        </div>

        {/* Scheduled Date */}
        <div>
          <Label htmlFor="scheduled_date">Scheduled Date</Label>
          <div className="relative">
            <DatePicker
              date={formData.scheduled_date ? new Date(formData.scheduled_date) : undefined}
              onSelect={handleDateChange}
              disabled={isSubmitting}
            >
              <DatePicker.Trigger>
                <Input
                  id="scheduled_date"
                  name="scheduled_date"
                  value={formData.scheduled_date}
                  onChange={handleChange}
                  disabled={isSubmitting}
                  required
                  className="w-full"
                />
              </DatePicker.Trigger>
              <DatePicker.Content />
            </DatePicker>
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={16} />
          </div>
        </div>

        {/* Due Time */}
        <div>
          <Label htmlFor="due_time">Due Time</Label>
          <div className="relative">
            <Input
              id="due_time"
              name="due_time"
              type="time"
              value={formData.due_time}
              onChange={handleChange}
              disabled={isSubmitting}
              className="w-full"
            />
            <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={16} />
          </div>
        </div>

        {/* Assigned To */}
        <div>
          <Label htmlFor="assigned_to">Assign To</Label>
          <Select
            id="assigned_to"
            name="assigned_to"
            value={formData.assigned_to}
            onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_to: value }))}
            disabled={isLoadingStaff || isSubmitting}
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Select staff member">
                {formData.assigned_to ? getStaffName(formData.assigned_to) : "Select staff member"}
              </Select.Value>
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="none">Not assigned</Select.Item>
              {isLoadingStaff ? (
                <div className="flex items-center justify-center p-2">
                  <Spinner size="small" />
                  Loading staff...
                </div>
              ) : staff.length === 0 ? (
                <div className="p-2 text-center text-gray-500">No staff found</div>
              ) : (
                staff.map(member => (
                  <Select.Item key={member.id} value={member.id}>
                    {member.name} ({member.role})
                  </Select.Item>
                ))
              )}
            </Select.Content>
          </Select>
        </div>

        {/* Status (only for editing) */}
        {initialData && (
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              id="status"
              name="status"
              value={formData.status}
              onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
              disabled={isSubmitting}
              required
            >
              <Select.Trigger className="w-full">
                <Select.Value placeholder="Select status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="pending">Pending</Select.Item>
                <Select.Item value="in_progress">In Progress</Select.Item>
                <Select.Item value="completed">Completed</Select.Item>
                <Select.Item value="verified">Verified</Select.Item>
                <Select.Item value="cancelled">Cancelled</Select.Item>
              </Select.Content>
            </Select>
          </div>
        )}
      </div>

      {/* Notes */}
      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          disabled={isSubmitting}
          placeholder="Add any notes or special instructions"
          className="w-full"
          rows={3}
        />
      </div>

      {/* Switches */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="guest_request"
            checked={formData.guest_request}
            onCheckedChange={(checked) => handleSwitchChange("guest_request", checked)}
            disabled={isSubmitting}
          />
          <Label htmlFor="guest_request">Guest Request</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="recurring"
            checked={formData.recurring}
            onCheckedChange={(checked) => handleSwitchChange("recurring", checked)}
            disabled={isSubmitting}
          />
          <Label htmlFor="recurring">Recurring Task</Label>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-2 mt-6">
        <Button
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
          type="button"
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          type="submit"
          disabled={isSubmitting || !formData.room_id || !formData.room_config_id}
        >
          {isSubmitting ? (
            <>
              <Spinner size="small" />
              {initialData ? "Updating..." : "Creating..."}
            </>
          ) : (
            initialData ? "Update Task" : "Create Task"
          )}
        </Button>
      </div>

      <Toaster />
    </form>
  );
};

export default HousekeepingTaskForm;
