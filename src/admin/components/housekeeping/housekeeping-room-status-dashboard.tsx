import React, { useState, useEffect } from "react";
import {
  Container,
  Head<PERSON>,
  <PERSON>,
  But<PERSON>,
  Badge,
  Select,
  Toaster,
  toast,
  FocusModal,
} from "@camped-ai/ui";
import {
  Bed,

  RefreshCw,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  Al<PERSON>Triangle,
  Brush,
} from "lucide-react";
import { format } from "date-fns";
import Spinner from "../shared/spinner";

import HousekeepingTaskFormComponent from "./housekeeping-task-form";

// Mock function to generate housekeeping tasks
const getMockHousekeepingTasks = (hotelId: string, date: Date) => {
  // Create tasks that match our sample room IDs
  return [
    {
      id: 'task_1',
      room_id: 'sample_room_1',
      status: 'completed',
      room_status: 'clean',
      created_at: new Date().toISOString(),
    },
    {
      id: 'task_2',
      room_id: 'sample_room_2',
      status: 'pending',
      room_status: 'dirty',
      created_at: new Date().toISOString(),
    },
    {
      id: 'task_3',
      room_id: 'sample_room_3',
      status: 'in_progress',
      room_status: 'maintenance',
      created_at: new Date().toISOString(),
    },
    {
      id: 'task_4',
      room_id: 'sample_room_4',
      status: 'in_progress',
      room_status: 'cleaning',
      created_at: new Date().toISOString(),
    },
    {
      id: 'task_5',
      room_id: 'sample_room_5',
      status: 'completed',
      room_status: 'booked',
      created_at: new Date().toISOString(),
    },
  ];
};

type HousekeepingRoomStatusDashboardProps = {
  hotelId: string;
  onTaskCreated?: () => void;
};

const HousekeepingRoomStatusDashboard: React.FC<HousekeepingRoomStatusDashboardProps> = ({
  hotelId,
  onTaskCreated,
}) => {
  const [rooms, setRooms] = useState<any[]>([]);
  const [roomConfigs, setRoomConfigs] = useState<any[]>([]);
  const [roomInventories, setRoomInventories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [floors, setFloors] = useState<string[]>([]);
  const [showCreateTaskDialog, setShowCreateTaskDialog] = useState<boolean>(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);

  // Fetch real rooms and room configs from the API
  const fetchRooms = async () => {
    try {
      setIsLoading(true);

      // Fetch room configurations for this hotel
      console.log(`Fetching room configurations for hotel: ${hotelId}`);

      // Try different endpoints to fetch room configurations
      const roomConfigEndpoints = [
        `/admin/direct-room-configs?hotel_id=${hotelId}`,
        `/admin/hotel-room-configs?hotel_id=${hotelId}`,
        `/admin/simple-room-configs?hotel_id=${hotelId}`,
        `/admin/room-configs?hotel_id=${hotelId}`,
        `/app/admin/direct-room-configs?hotel_id=${hotelId}`,
        `/app/admin/hotel-room-configs?hotel_id=${hotelId}`,
        `/app/admin/simple-room-configs?hotel_id=${hotelId}`,
        `/app/admin/room-configs?hotel_id=${hotelId}`
      ];

      let roomConfigsData = null;
      let roomConfigsSuccess = false;

      for (const endpoint of roomConfigEndpoints) {
        try {
          console.log(`Trying to fetch room configurations from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          console.log(`Room configs response status from ${endpoint}: ${response.status} ${response.statusText}`);

          if (response.ok) {
            const data = await response.json();
            console.log(`Room configs API response from ${endpoint}:`, data);

            // Check for different response formats
            if (data.roomConfigs && data.roomConfigs.length > 0) {
              roomConfigsData = data.roomConfigs;
              roomConfigsSuccess = true;
              break;
            } else if (data.room_configs && data.room_configs.length > 0) {
              roomConfigsData = data.room_configs;
              roomConfigsSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length > 0) {
              roomConfigsData = data;
              roomConfigsSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      if (roomConfigsSuccess && roomConfigsData) {
        setRoomConfigs(roomConfigsData);
        console.log('Loaded room configs:', roomConfigsData);
      } else {
        // If all endpoints fail, use sample room configs
        console.warn('Failed to fetch room configurations from any endpoint, using sample data');
        const sampleRoomConfigs = [
          { id: 'sample_config', name: 'Standard Room', description: 'A comfortable standard room', hotel_id: hotelId },
          { id: 'deluxe_config', name: 'Deluxe Room', description: 'A spacious deluxe room with premium amenities', hotel_id: hotelId },
          { id: 'suite_config', name: 'Suite', description: 'A luxurious suite with separate living area', hotel_id: hotelId },
        ];
        setRoomConfigs(sampleRoomConfigs);
        console.log('Using sample room configs:', sampleRoomConfigs);
      }

      // Fetch rooms for this hotel
      console.log(`Fetching rooms for hotel: ${hotelId}`);

      // Try different endpoints to fetch rooms
      const roomEndpoints = [
        `/admin/direct-rooms?hotel_id=${hotelId}`,
        `/admin/hotel-management/rooms?hotel_id=${hotelId}`,
        `/app/admin/direct-rooms?hotel_id=${hotelId}`,
        `/app/admin/hotel-management/rooms?hotel_id=${hotelId}`
      ];

      let roomsData = null;
      let roomsSuccess = false;

      for (const endpoint of roomEndpoints) {
        try {
          console.log(`Trying to fetch rooms from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          console.log(`Rooms response status from ${endpoint}: ${response.status} ${response.statusText}`);

          if (response.ok) {
            const data = await response.json();
            console.log(`Rooms API response from ${endpoint}:`, data);

            // Check for different response formats
            if (data.rooms && data.rooms.length > 0) {
              roomsData = data.rooms;
              roomsSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length > 0) {
              roomsData = data;
              roomsSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If we have room configs but no rooms, try to fetch rooms for each room config
      if (!roomsSuccess && roomConfigsSuccess && roomConfigsData && roomConfigsData.length > 0) {
        console.log('Trying to fetch rooms from room configs...');

        const allRooms = [];

        for (const config of roomConfigsData) {
          try {
            console.log(`Fetching rooms for room config: ${config.id}`);

            // Try different endpoints for room config rooms
            const roomConfigEndpoints = [
              `/admin/room-configs/${config.id}/rooms`,
              `/app/admin/room-configs/${config.id}/rooms`
            ];

            let roomConfigSuccess = false;

            for (const endpoint of roomConfigEndpoints) {
              try {
                console.log(`Trying to fetch rooms from ${endpoint}`);
                const response = await fetch(endpoint, {
                  headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                  }
                });

                if (response.ok) {
                  const data = await response.json();
                  if (data.rooms && data.rooms.length > 0) {
                    allRooms.push(...data.rooms);
                    roomConfigSuccess = true;
                    break;
                  }
                }
              } catch (endpointError) {
                console.warn(`Error fetching from ${endpoint}:`, endpointError);
              }
            }

            // Skip the rest of the loop if we already found rooms
            if (roomConfigSuccess) {
              continue;
            }
          } catch (configError) {
            console.warn(`Error fetching rooms for config ${config.id}:`, configError);
          }
        }

        if (allRooms.length > 0) {
          roomsData = allRooms;
          roomsSuccess = true;
        }
      }

      if (roomsSuccess && roomsData) {
        setRooms(roomsData);
        console.log('Loaded rooms:', roomsData);
      } else {
        // If all endpoints fail, use empty array and generate sample data
        console.warn('Failed to fetch rooms from any endpoint, using sample data');

        // Generate sample rooms for demo purposes with more variety in statuses
        const sampleRooms = [
          { id: 'sample_room_1', name: 'Standard Room', room_number: '101', status: 'clean', floor: '1', notes: 'Ready for guests', is_active: true, room_config_id: 'sample_config', hotel_id: hotelId },
          { id: 'sample_room_2', name: 'Standard Room', room_number: '102', status: 'dirty', floor: '1', notes: 'Needs cleaning', is_active: true, room_config_id: 'sample_config', hotel_id: hotelId },
          { id: 'sample_room_3', name: 'Deluxe Room', room_number: '103', status: 'maintenance', floor: '1', notes: 'AC repair needed', is_active: true, room_config_id: 'deluxe_config', hotel_id: hotelId },
          { id: 'sample_room_4', name: 'Deluxe Room', room_number: '104', status: 'cleaning', floor: '1', notes: 'Currently being cleaned', is_active: true, room_config_id: 'deluxe_config', hotel_id: hotelId },
          { id: 'sample_room_5', name: 'Suite', room_number: '201', status: 'booked', floor: '2', notes: 'VIP guest', is_active: true, room_config_id: 'suite_config', hotel_id: hotelId },
          { id: 'sample_room_6', name: 'Suite', room_number: '202', status: 'reserved', floor: '2', notes: 'Reserved for tomorrow', is_active: true, room_config_id: 'suite_config', hotel_id: hotelId },
          { id: 'sample_room_7', name: 'Standard Room', room_number: '203', status: 'available', floor: '2', notes: 'Ready for booking', is_active: true, room_config_id: 'sample_config', hotel_id: hotelId },
          { id: 'sample_room_8', name: 'Deluxe Room', room_number: '204', status: 'inspected', floor: '2', notes: 'Recently inspected', is_active: true, room_config_id: 'deluxe_config', hotel_id: hotelId },
          { id: 'sample_room_9', name: 'Standard Room', room_number: '301', status: 'out_of_service', floor: '3', notes: 'Major renovation', is_active: false, room_config_id: 'sample_config', hotel_id: hotelId },
          { id: 'sample_room_10', name: 'Deluxe Room', room_number: '302', status: 'clean', floor: '3', notes: 'Ready for guests', is_active: true, room_config_id: 'deluxe_config', hotel_id: hotelId },
        ];

        setRooms(sampleRooms);
        console.log('Using sample rooms with various statuses:', sampleRooms);
      }

      // Extract unique floors from rooms (either real or sample)
      const uniqueFloors = [...new Set(rooms.map((room: any) => room.floor))].filter(Boolean);
      setFloors(uniqueFloors);

      // Fetch room inventories or create temporary ones based on room status
      try {
        // Try to fetch real inventory data
        const inventoriesPromises = roomsData ? roomsData.map(async (room: any) => {
          try {
            const inventoryResponse = await fetch(`/admin/hotel-management/room-inventory/rooms/${room.id}`);
            if (inventoryResponse.ok) {
              const inventoryData = await inventoryResponse.json();
              return inventoryData.roomInventory || [];
            }
            return null;
          } catch (err) {
            console.warn(`Could not fetch inventory for room ${room.id}:`, err);
            return null;
          }
        }) : [];

        const inventoriesResults = await Promise.all(inventoriesPromises);
        const fetchedInventories = inventoriesResults
          .flat()
          .filter(Boolean);

        // If we have real inventory data, use it
        if (fetchedInventories.length > 0) {
          setRoomInventories(fetchedInventories);
          console.log('Loaded room inventories:', fetchedInventories);
        } else {
          // Otherwise, create temporary inventory objects based on room status
          const tempInventories = rooms.map(room => ({
            id: `inv_${room.id}`,
            inventory_item_id: room.id,
            status: room.status || 'available',
            from_date: format(new Date(), "yyyy-MM-dd"),
            to_date: format(new Date(), "yyyy-MM-dd"),
            notes: room.notes || "",
          }));
          setRoomInventories(tempInventories);
          console.log('Created temporary room inventories:', tempInventories);
        }
      } catch (inventoryError) {
        console.error("Error fetching room inventories:", inventoryError);
        // Create temporary inventory objects based on room status
        const tempInventories = rooms.map(room => ({
          id: `inv_${room.id}`,
          inventory_item_id: room.id,
          status: room.status || 'available',
          from_date: format(new Date(), "yyyy-MM-dd"),
          to_date: format(new Date(), "yyyy-MM-dd"),
          notes: room.notes || "",
        }));
        setRoomInventories(tempInventories);
      }
    } catch (error) {
      console.error("Error fetching rooms:", error);
      toast.error("Failed to load rooms");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch housekeeping tasks for today using mock data
  const fetchHousekeepingTasks = async () => {
    try {
      // Use mock data directly
      const tasks = getMockHousekeepingTasks(hotelId, new Date());

      // Create a map of room IDs to their latest task
      const roomTaskMap = new Map();
      tasks.forEach((task: any) => {
        if (!roomTaskMap.has(task.room_id) || new Date(task.created_at) > new Date(roomTaskMap.get(task.room_id).created_at)) {
          roomTaskMap.set(task.room_id, task);
        }
      });

      // Update rooms with housekeeping status
      setRooms(prevRooms => prevRooms.map(room => {
        const task = roomTaskMap.get(room.id);
        if (task) {
          return {
            ...room,
            housekeeping_status: task.status,
            room_status: task.room_status,
            task_id: task.id,
          };
        }
        return room;
      }));

      console.log('Updated rooms with housekeeping tasks');

    } catch (error) {
      console.error("Error fetching housekeeping tasks:", error);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (hotelId) {
      console.log('HousekeepingRoomStatusDashboard: Fetching rooms for hotel ID:', hotelId);
      fetchRooms();
    }
  }, [hotelId]);

  // Log when rooms are updated
  useEffect(() => {
    console.log('HousekeepingRoomStatusDashboard: Rooms updated, count:', rooms.length);
  }, [rooms]);

  // Fetch housekeeping tasks after rooms are loaded
  useEffect(() => {
    if (rooms.length > 0) {
      fetchHousekeepingTasks();
    }
  }, [rooms.length]);

  // Get room config name
  const getRoomConfigName = (configId: string) => {
    const config = roomConfigs.find(c => c.id === configId);
    return config ? config.name : "Unknown";
  };

  // Get room inventory status
  const getRoomInventoryStatus = (roomId: string) => {
    const inventory = roomInventories.find(inv => inv.inventory_item_id === roomId);
    return inventory ? inventory.status : "unavailable";
  };

  // Get combined room status (from inventory and housekeeping)
  const getRoomStatus = (room: any) => {
    // First check if there's a housekeeping status
    if (room.room_status) {
      return room.room_status;
    }

    // Then check inventory status
    return getRoomInventoryStatus(room.id);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "dirty":
        return <Badge color="red"><XCircle className="w-3 h-3 mr-1" /> Dirty</Badge>;
      case "cleaning":
        return <Badge color="orange"><Brush className="w-3 h-3 mr-1" /> Cleaning</Badge>;
      case "clean":
        return <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Clean</Badge>;
      case "inspected":
        return <Badge color="purple"><CheckCircle className="w-3 h-3 mr-1" /> Inspected</Badge>;
      case "out_of_service":
        return <Badge color="gray"><AlertTriangle className="w-3 h-3 mr-1" /> Out of Service</Badge>;
      case "unavailable":
        return <Badge color="gray"><XCircle className="w-3 h-3 mr-1" /> Unavailable</Badge>;
      case "available":
        return <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Available</Badge>;
      case "booked":
        return <Badge color="blue"><Clock className="w-3 h-3 mr-1" /> Booked</Badge>;
      case "reserved":
        return <Badge color="yellow"><Clock className="w-3 h-3 mr-1" /> Reserved</Badge>;
      case "maintenance":
        return <Badge color="orange"><AlertTriangle className="w-3 h-3 mr-1" /> Maintenance</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Get housekeeping status badge
  const getHousekeepingStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge color="blue"><Clock className="w-3 h-3 mr-1" /> Pending</Badge>;
      case "in_progress":
        return <Badge color="orange"><Brush className="w-3 h-3 mr-1" /> In Progress</Badge>;
      case "completed":
        return <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Completed</Badge>;
      case "verified":
        return <Badge color="purple"><CheckCircle className="w-3 h-3 mr-1" /> Verified</Badge>;
      case "cancelled":
        return <Badge color="red"><XCircle className="w-3 h-3 mr-1" /> Cancelled</Badge>;
      default:
        return null;
    }
  };

  // Use all rooms since we removed filters
  const filteredRooms = rooms;

  // Create a new housekeeping task
  const handleCreateTask = (room: any) => {
    setSelectedRoom(room);
    setShowCreateTaskDialog(true);
  };

  // Handle task form submission
  const handleTaskSubmit = () => {
    setShowCreateTaskDialog(false);
    toast.success("Task created successfully");

    // Refresh data
    fetchRooms();

    // Notify parent component
    if (onTaskCreated) {
      onTaskCreated();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <Heading level="h2">Room Housekeeping Status</Heading>
        <Button
          variant="secondary"
          size="small"
          onClick={fetchRooms}
          disabled={isLoading}
        >
          {isLoading ? (
            <Spinner size="small" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Refresh Rooms
        </Button>
      </div>

      {/* Room Status Legend */}
      <div className="flex flex-wrap gap-3 bg-gray-50 p-3 rounded-md">
        <div className="flex items-center gap-2">
          <Text className="text-sm font-medium">Room Status:</Text>
        </div>
        <div className="flex flex-wrap gap-2">
          <Badge color="red"><XCircle className="w-3 h-3 mr-1" /> Dirty</Badge>
          <Badge color="orange"><Brush className="w-3 h-3 mr-1" /> Cleaning</Badge>
          <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Clean</Badge>
          <Badge color="purple"><CheckCircle className="w-3 h-3 mr-1" /> Inspected</Badge>
          <Badge color="gray"><AlertTriangle className="w-3 h-3 mr-1" /> Out of Service</Badge>
        </div>
      </div>

      {/* Room Grid - Simplified for Housekeeping */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner size="medium" />
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredRooms.map(room => {
            const status = getRoomStatus(room);

            return (
              <Container key={room.id} className="overflow-hidden border border-gray-200 rounded-lg bg-white">
                <div className={`p-3 ${
                  status === "dirty" ? "bg-red-50" :
                  status === "cleaning" ? "bg-orange-50" :
                  status === "clean" || status === "available" ? "bg-green-50" :
                  status === "inspected" ? "bg-purple-50" :
                  status === "out_of_service" ? "bg-gray-100" :
                  "bg-gray-50"
                }`}>
                  <div className="flex justify-between items-start">
                    <div>
                      <Text className="font-bold text-lg">{room.room_number}</Text>
                      <Text className="text-sm text-gray-600">{room.name}</Text>
                    </div>
                    <div>
                      {getStatusBadge(status)}
                    </div>
                  </div>
                </div>

                <div className="p-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <Text className="text-xs text-gray-500">Floor {room.floor || "N/A"}</Text>
                      {room.housekeeping_status && (
                        <div className="mt-1">{getHousekeepingStatusBadge(room.housekeeping_status)}</div>
                      )}
                    </div>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => handleCreateTask(room)}
                    >
                      {status === "dirty" ? "Clean Room" : "Create Task"}
                    </Button>
                  </div>
                </div>
              </Container>
            );
          })}
        </div>
      )}

      {/* Create Task Dialog */}
      {showCreateTaskDialog && selectedRoom && (
        <FocusModal open={showCreateTaskDialog} onOpenChange={setShowCreateTaskDialog}>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>
                Create Housekeeping Task for Room {selectedRoom.room_number}
              </FocusModal.Title>
            </FocusModal.Header>
            <HousekeepingTaskFormComponent
              hotelId={hotelId}
              onSubmit={handleTaskSubmit}
              onCancel={() => setShowCreateTaskDialog(false)}
              initialData={{
                room_id: selectedRoom.id,
                room_config_id: selectedRoom.product_id,
                hotel_id: hotelId,
                task_type: "cleaning",
                room_status: "dirty",
              }}
            />
          </FocusModal.Content>
        </FocusModal>
      )}

      <Toaster />
    </div>
  );
};

export default HousekeepingRoomStatusDashboard;
