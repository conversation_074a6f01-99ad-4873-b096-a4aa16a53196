import React, { useState, useEffect } from "react";
import {
  Table,
  But<PERSON>,
  Badge,
  Text,
  Heading,
  Toaster,
  toast,
  FocusModal,
  DropdownMenu,
} from "@camped-ai/ui";
import {
  Edit,
  Trash,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Play,
  CheckSquare,
  User,

  Plus,
} from "lucide-react";
import { format } from "date-fns";
import Spinner from "../shared/spinner";

import HousekeepingTaskFormComponent from "./housekeeping-task-form";
import HousekeepingTaskDetailsComponent from "./housekeeping-task-details";

type HousekeepingTaskListProps = {
  hotelId: string;
  selectedDate: Date;
  onTaskUpdated?: () => void;
};

const HousekeepingTaskList: React.FC<HousekeepingTaskListProps> = ({
  hotelId,
  selectedDate,
  onTaskUpdated,
}) => {
  const [tasks, setTasks] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [showEditForm, setShowEditForm] = useState<boolean>(false);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);

  // Fetch tasks from API or generate sample tasks if none exist
  const fetchTasks = async () => {
    try {
      setIsLoading(true);

      // Try to fetch real tasks from API
      try {
        // Try different endpoints for housekeeping tasks
        const taskEndpoints = [
          `/admin/hotel-management/housekeeping/tasks?hotel_id=${hotelId}&date=${format(selectedDate, 'yyyy-MM-dd')}`,
          `/app/admin/hotel-management/housekeeping/tasks?hotel_id=${hotelId}&date=${format(selectedDate, 'yyyy-MM-dd')}`
        ];

        let taskResponse = null;

        for (const endpoint of taskEndpoints) {
          try {
            console.log(`Trying to fetch tasks from ${endpoint}`);
            const response = await fetch(endpoint, {
              headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            });

            if (response.ok) {
              taskResponse = response;
              break;
            }
          } catch (endpointError) {
            console.warn(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        if (taskResponse) {
          const data = await taskResponse.json();
          if (data.tasks && data.tasks.length > 0) {
            setTasks(data.tasks);
            console.log('Loaded housekeeping tasks:', data.tasks);
            setIsLoading(false);
            return;
          }
        }

      } catch (apiError) {
        console.warn('API for housekeeping tasks not available yet:', apiError);
      }

      // If API fails or returns no data, generate sample tasks based on rooms
      // Try different endpoints to fetch rooms
      const roomEndpoints = [
        `/admin/direct-rooms?hotel_id=${hotelId}`,
        `/admin/hotel-management/rooms?hotel_id=${hotelId}`,
        `/app/admin/direct-rooms?hotel_id=${hotelId}`,
        `/app/admin/hotel-management/rooms?hotel_id=${hotelId}`
      ];

      let roomsData = null;
      let roomsSuccess = false;

      for (const endpoint of roomEndpoints) {
        try {
          console.log(`Trying to fetch rooms from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (response.ok) {
            const data = await response.json();

            // Check for different response formats
            if (data.rooms && data.rooms.length > 0) {
              roomsData = data.rooms;
              roomsSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length > 0) {
              roomsData = data;
              roomsSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, use sample data
      const rooms = roomsSuccess && roomsData ? roomsData : [
        { id: 'sample_room_1', room_number: '101', name: 'Standard Room 101' },
        { id: 'sample_room_2', room_number: '102', name: 'Standard Room 102' },
        { id: 'sample_room_3', room_number: '103', name: 'Standard Room 103' },
        { id: 'sample_room_4', room_number: '104', name: 'Standard Room 104' },
        { id: 'sample_room_5', room_number: '105', name: 'Standard Room 105' },
      ];

      // Generate sample tasks based on room data
      const sampleTasks = rooms
        .filter((room, index) => index < 10) // Limit to 10 tasks for demo
        .map((room, index) => ({
          id: `task_${index}_${room.id}`,
          title: `Clean room ${room.room_number}`,
          description: `Regular cleaning for ${room.name}`,
          status: index % 5 === 0 ? 'completed' :
                 index % 4 === 0 ? 'in_progress' :
                 index % 3 === 0 ? 'verified' : 'pending',
          priority: index % 3 === 0 ? 'high' : index % 7 === 0 ? 'urgent' : 'normal',
          room_id: room.id,
          room_number: room.room_number,
          room_name: room.name,
          room_status: room.status,
          assigned_to: 'Staff Member',
          created_at: new Date().toISOString(),
          due_date: format(selectedDate, 'yyyy-MM-dd'),
          hotel_id: hotelId,
        }));

      setTasks(sampleTasks);
      console.log('Generated sample housekeeping tasks:', sampleTasks);
    } catch (error) {
      console.error("Error fetching housekeeping tasks:", error);
      toast.error("Failed to load housekeeping tasks");
    } finally {
      setIsLoading(false);
    }
  };

  // Delete task using mock data
  const deleteTask = async () => {
    if (!selectedTask) return;

    try {
      // Simulate deletion by filtering out the task
      setTasks(prevTasks => prevTasks.filter(task => task.id !== selectedTask.id));
      toast.success("Task deleted successfully");
      if (onTaskUpdated) onTaskUpdated();
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error("Error deleting task:", error);
      toast.error("Failed to delete task");
    }
  };

  // Start task using mock data
  const startTask = async (taskId: string, staffId: string) => {
    try {
      // Update the task status in the local state
      setTasks(prevTasks => prevTasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            status: "in_progress",
            room_status: "cleaning",
            started_at: new Date().toISOString(),
          };
        }
        return task;
      }));
      toast.success("Task started successfully");
      if (onTaskUpdated) onTaskUpdated();
    } catch (error) {
      console.error("Error starting task:", error);
      toast.error("Failed to start task");
    }
  };

  // Complete task using mock data
  const completeTask = async (taskId: string, staffId: string) => {
    try {
      // Update the task status in the local state
      setTasks(prevTasks => prevTasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            status: "completed",
            room_status: "clean",
            completed_at: new Date().toISOString(),
          };
        }
        return task;
      }));
      toast.success("Task completed successfully");
      if (onTaskUpdated) onTaskUpdated();
    } catch (error) {
      console.error("Error completing task:", error);
      toast.error("Failed to complete task");
    }
  };

  // Verify task using mock data
  const verifyTask = async (taskId: string, supervisorId: string) => {
    try {
      // Update the task status in the local state
      setTasks(prevTasks => prevTasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            status: "verified",
            room_status: "inspected",
            verified_at: new Date().toISOString(),
            verified_by: supervisorId,
          };
        }
        return task;
      }));
      toast.success("Task verified successfully");
      if (onTaskUpdated) onTaskUpdated();
    } catch (error) {
      console.error("Error verifying task:", error);
      toast.error("Failed to verify task");
    }
  };

  // Cancel task using mock data
  const cancelTask = async (taskId: string, reason: string = "") => {
    try {
      // Update the task status in the local state
      setTasks(prevTasks => prevTasks.map(task => {
        if (task.id === taskId) {
          return {
            ...task,
            status: "cancelled",
            notes: task.notes + (reason ? ` | Cancelled: ${reason}` : ' | Cancelled'),
          };
        }
        return task;
      }));
      toast.success("Task cancelled successfully");
      if (onTaskUpdated) onTaskUpdated();
    } catch (error) {
      console.error("Error cancelling task:", error);
      toast.error("Failed to cancel task");
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge color="blue"><Clock className="w-3 h-3 mr-1" /> Pending</Badge>;
      case "in_progress":
        return <Badge color="orange"><Play className="w-3 h-3 mr-1" /> In Progress</Badge>;
      case "completed":
        return <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Completed</Badge>;
      case "verified":
        return <Badge color="purple"><CheckSquare className="w-3 h-3 mr-1" /> Verified</Badge>;
      case "cancelled":
        return <Badge color="red"><XCircle className="w-3 h-3 mr-1" /> Cancelled</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "low":
        return <Badge color="gray">Low</Badge>;
      case "medium":
        return <Badge color="blue">Medium</Badge>;
      case "high":
        return <Badge color="orange">High</Badge>;
      case "urgent":
        return <Badge color="red">Urgent</Badge>;
      default:
        return <Badge color="gray">{priority}</Badge>;
    }
  };

  // Get room status badge
  const getRoomStatusBadge = (status: string) => {
    switch (status) {
      case "dirty":
        return <Badge color="red">Dirty</Badge>;
      case "cleaning":
        return <Badge color="orange">Cleaning</Badge>;
      case "clean":
        return <Badge color="green">Clean</Badge>;
      case "inspected":
        return <Badge color="purple">Inspected</Badge>;
      case "out_of_service":
        return <Badge color="gray">Out of Service</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (hotelId) {
      console.log('HousekeepingTaskList: Fetching tasks for hotel ID:', hotelId, 'and date:', format(selectedDate, 'yyyy-MM-dd'));
      fetchTasks();
    }
  }, [hotelId, selectedDate]);

  // Log when tasks are updated
  useEffect(() => {
    console.log('HousekeepingTaskList: Tasks updated, count:', tasks.length);
  }, [tasks]);

  // Handle form submission
  const handleFormSubmit = () => {
    fetchTasks();
    if (onTaskUpdated) onTaskUpdated();
    setShowCreateForm(false);
    setShowEditForm(false);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center mb-4">
        <Heading level="h2">Housekeeping Tasks</Heading>
        <Button
          variant="primary"
          size="small"
          onClick={() => setShowCreateForm(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Task
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-32">
          <Spinner size="medium" />
        </div>
      ) : tasks.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-32 bg-gray-50 rounded-md">
          <Text className="text-gray-500">No tasks found for this date</Text>
          <Button
            variant="secondary"
            size="small"
            className="mt-2"
            onClick={() => setShowCreateForm(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Task
          </Button>
        </div>
      ) : (
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Room</Table.HeaderCell>
              <Table.HeaderCell>Task Type</Table.HeaderCell>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Priority</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {tasks.map((task) => (
              <Table.Row key={task.id}>
                <Table.Cell>
                  <div className="font-medium">{task.room_id}</div>
                </Table.Cell>
                <Table.Cell>{task.task_type}</Table.Cell>
                <Table.Cell>{getStatusBadge(task.status)}</Table.Cell>

                <Table.Cell>{getPriorityBadge(task.priority)}</Table.Cell>

                <Table.Cell>
                  <DropdownMenu>
                    <DropdownMenu.Trigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content align="end">
                      <DropdownMenu.Item
                        onClick={() => {
                          setSelectedTask(task);
                          setShowDetails(true);
                        }}
                      >
                        View Details
                      </DropdownMenu.Item>
                      <DropdownMenu.Item
                        onClick={() => {
                          setSelectedTask(task);
                          setShowEditForm(true);
                        }}
                      >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </DropdownMenu.Item>
                      {task.status === "pending" && task.assigned_to && (
                        <DropdownMenu.Item
                          onClick={() => startTask(task.id, task.assigned_to)}
                        >
                          <Play className="w-4 h-4 mr-2" />
                          Start Task
                        </DropdownMenu.Item>
                      )}
                      {task.status === "in_progress" && task.assigned_to && (
                        <DropdownMenu.Item
                          onClick={() => completeTask(task.id, task.assigned_to)}
                        >
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Complete Task
                        </DropdownMenu.Item>
                      )}
                      {task.status === "completed" && (
                        <DropdownMenu.Item
                          onClick={() => verifyTask(task.id, "supervisor_id")}
                        >
                          <CheckSquare className="w-4 h-4 mr-2" />
                          Verify Task
                        </DropdownMenu.Item>
                      )}
                      {(task.status === "pending" || task.status === "in_progress") && (
                        <DropdownMenu.Item
                          onClick={() => cancelTask(task.id)}
                        >
                          <XCircle className="w-4 h-4 mr-2" />
                          Cancel Task
                        </DropdownMenu.Item>
                      )}

                    </DropdownMenu.Content>
                  </DropdownMenu>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      )}

      {/* Create Task Form */}
      {showCreateForm && (
        <FocusModal open={showCreateForm} onOpenChange={setShowCreateForm}>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Create Housekeeping Task</FocusModal.Title>
            </FocusModal.Header>
            <HousekeepingTaskFormComponent
              hotelId={hotelId}
              onSubmit={handleFormSubmit}
              onCancel={() => setShowCreateForm(false)}
              initialDate={selectedDate}
            />
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* Edit Task Form */}
      {showEditForm && selectedTask && (
        <FocusModal open={showEditForm} onOpenChange={setShowEditForm}>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Edit Housekeeping Task</FocusModal.Title>
            </FocusModal.Header>
            <HousekeepingTaskFormComponent
              hotelId={hotelId}
              onSubmit={handleFormSubmit}
              onCancel={() => setShowEditForm(false)}
              initialData={selectedTask}
            />
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* Task Details */}
      {showDetails && selectedTask && (
        <FocusModal open={showDetails} onOpenChange={setShowDetails}>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Task Details</FocusModal.Title>
            </FocusModal.Header>
            <HousekeepingTaskDetailsComponent
              task={selectedTask}
              onClose={() => setShowDetails(false)}
            />
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* Delete Confirmation */}
      {showDeleteConfirm && selectedTask && (
        <FocusModal open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>Delete Task</FocusModal.Title>
            </FocusModal.Header>
            <Text>
              Are you sure you want to delete this task? This action cannot be undone.
            </Text>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="secondary"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={deleteTask}
              >
                Delete
              </Button>
            </div>
          </FocusModal.Content>
        </FocusModal>
      )}

      <Toaster />
    </div>
  );
};

export default HousekeepingTaskList;
