import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Badge,
  Text,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import {
  Calendar,
  Clock,
  User,
  Bed,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  CheckSquare,
  Play,
} from "lucide-react";
import { format, parseISO } from "date-fns";
import Spinner from "../shared/spinner";


type HousekeepingTaskDetailsProps = {
  task: any;
  onClose: () => void;
  onTaskUpdated?: () => void;
};

const HousekeepingTaskDetails: React.FC<HousekeepingTaskDetailsProps> = ({
  task,
  onClose,
  onTaskUpdated,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [room, setRoom] = useState<any>(null);
  const [roomConfig, setRoomConfig] = useState<any>(null);
  const [staffMember, setStaffMember] = useState<any>(null);
  const [taskCompletion, setTaskCompletion] = useState<any>(null);
  const [checklist, setChecklist] = useState<any>(null);

  // Fetch additional data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);

      try {
        // Fetch room details
        if (task.room_id) {
          const roomResponse = await fetch(`/admin/direct-rooms/${task.room_id}`);
          if (roomResponse.ok) {
            const roomData = await roomResponse.json();
            setRoom(roomData.room);
          }
        }

        // Fetch room config details
        if (task.room_config_id) {
          const configResponse = await fetch(`/admin/room-configs/${task.room_config_id}`);
          if (configResponse.ok) {
            const configData = await configResponse.json();
            setRoomConfig(configData.room_config);
          }
        }

        // Fetch staff details
        if (task.assigned_to) {
          try {
            const staffResponse = await fetch(`/admin/hotel-management/housekeeping/staff/${task.assigned_to}`);
            if (staffResponse.ok) {
              const staffData = await staffResponse.json();
              setStaffMember(staffData.staff);
            }
          } catch (error) {
            console.error("Error fetching staff details:", error);
          }
        }

        // Fetch task completion details
        try {
          const completionResponse = await fetch(`/admin/hotel-management/housekeeping/tasks/${task.id}/completion`);
          if (completionResponse.ok) {
            const completionData = await completionResponse.json();
            if (completionData.completion) {
              setTaskCompletion(completionData.completion);

              // If there's a checklist, fetch it
              if (completionData.completion.checklist_id) {
                const checklistResponse = await fetch(`/admin/hotel-management/housekeeping/checklists/${completionData.completion.checklist_id}`);
                if (checklistResponse.ok) {
                  const checklistData = await checklistResponse.json();
                  setChecklist(checklistData.checklist);
                }
              }
            }
          }
        } catch (error) {
          console.error("Error fetching task completion:", error);
        }
      } catch (error) {
        console.error("Error fetching task details:", error);
        toast.error("Failed to load task details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [task]);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge color="blue"><Clock className="w-3 h-3 mr-1" /> Pending</Badge>;
      case "in_progress":
        return <Badge color="orange"><Play className="w-3 h-3 mr-1" /> In Progress</Badge>;
      case "completed":
        return <Badge color="green"><CheckCircle className="w-3 h-3 mr-1" /> Completed</Badge>;
      case "verified":
        return <Badge color="purple"><CheckSquare className="w-3 h-3 mr-1" /> Verified</Badge>;
      case "cancelled":
        return <Badge color="red"><XCircle className="w-3 h-3 mr-1" /> Cancelled</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "low":
        return <Badge color="gray">Low</Badge>;
      case "medium":
        return <Badge color="blue">Medium</Badge>;
      case "high":
        return <Badge color="orange">High</Badge>;
      case "urgent":
        return <Badge color="red">Urgent</Badge>;
      default:
        return <Badge color="gray">{priority}</Badge>;
    }
  };

  // Get room status badge
  const getRoomStatusBadge = (status: string) => {
    switch (status) {
      case "dirty":
        return <Badge color="red">Dirty</Badge>;
      case "cleaning":
        return <Badge color="orange">Cleaning</Badge>;
      case "clean":
        return <Badge color="green">Clean</Badge>;
      case "inspected":
        return <Badge color="purple">Inspected</Badge>;
      case "out_of_service":
        return <Badge color="gray">Out of Service</Badge>;
      default:
        return <Badge color="gray">{status}</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "PPP");
    } catch (error) {
      return dateString;
    }
  };

  // Format time
  const formatTime = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "p");
    } catch (error) {
      return dateString;
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "PPp");
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-32">
          <Spinner size="medium" />
        </div>
      ) : (
        <>
          {/* Task Header */}
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              {getStatusBadge(task.status)}
              {getPriorityBadge(task.priority)}
              {task.guest_request && <Badge color="indigo">Guest Request</Badge>}
            </div>
            <Heading level="h3" className="text-xl">
              {task.task_type === "cleaning" ? "Room Cleaning" :
               task.task_type === "deep_cleaning" ? "Deep Cleaning" :
               task.task_type === "turndown" ? "Turndown Service" :
               task.task_type === "inspection" ? "Room Inspection" :
               task.task_type === "maintenance" ? "Maintenance" : task.task_type}
            </Heading>
          </div>

          {/* Room Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Bed className="w-5 h-5 text-gray-600" />
              <Heading level="h4" className="text-lg">Room Information</Heading>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-gray-500 text-sm">Room</Text>
                <Text className="font-medium">
                  {room ? `${room.room_number} - ${room.name}` : task.room_id}
                </Text>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Room Type</Text>
                <Text className="font-medium">
                  {roomConfig ? roomConfig.name : task.room_config_id}
                </Text>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Room Status</Text>
                <div>{getRoomStatusBadge(task.room_status)}</div>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Floor</Text>
                <Text className="font-medium">{room?.floor || "N/A"}</Text>
              </div>
            </div>
          </div>

          {/* Task Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-5 h-5 text-gray-600" />
              <Heading level="h4" className="text-lg">Task Details</Heading>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Text className="text-gray-500 text-sm">Scheduled Date</Text>
                <Text className="font-medium">{formatDate(task.scheduled_date)}</Text>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Due Time</Text>
                <Text className="font-medium">{task.due_time || "N/A"}</Text>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Assigned To</Text>
                <Text className="font-medium">
                  {staffMember ? staffMember.name : (task.assigned_to || "Not assigned")}
                </Text>
              </div>
              <div>
                <Text className="text-gray-500 text-sm">Recurring</Text>
                <Text className="font-medium">{task.recurring ? "Yes" : "No"}</Text>
              </div>
            </div>
          </div>

          {/* Task Timeline */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-gray-600" />
              <Heading level="h4" className="text-lg">Task Timeline</Heading>
            </div>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                  <Calendar className="w-3 h-3 text-blue-600" />
                </div>
                <div>
                  <Text className="font-medium">Created</Text>
                  <Text className="text-sm text-gray-500">{formatDateTime(task.created_at)}</Text>
                </div>
              </div>

              {task.assigned_at && (
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                    <User className="w-3 h-3 text-blue-600" />
                  </div>
                  <div>
                    <Text className="font-medium">Assigned</Text>
                    <Text className="text-sm text-gray-500">{formatDateTime(task.assigned_at)}</Text>
                  </div>
                </div>
              )}

              {task.started_at && (
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center mt-0.5">
                    <Play className="w-3 h-3 text-orange-600" />
                  </div>
                  <div>
                    <Text className="font-medium">Started</Text>
                    <Text className="text-sm text-gray-500">{formatDateTime(task.started_at)}</Text>
                  </div>
                </div>
              )}

              {task.completed_at && (
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
                    <CheckCircle className="w-3 h-3 text-green-600" />
                  </div>
                  <div>
                    <Text className="font-medium">Completed</Text>
                    <Text className="text-sm text-gray-500">{formatDateTime(task.completed_at)}</Text>
                    {taskCompletion && (
                      <Text className="text-sm text-gray-500">
                        Duration: {taskCompletion.duration_minutes} minutes
                      </Text>
                    )}
                  </div>
                </div>
              )}

              {task.verified_at && (
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center mt-0.5">
                    <CheckSquare className="w-3 h-3 text-purple-600" />
                  </div>
                  <div>
                    <Text className="font-medium">Verified</Text>
                    <Text className="text-sm text-gray-500">{formatDateTime(task.verified_at)}</Text>
                    {task.verified_by && (
                      <Text className="text-sm text-gray-500">
                        By: {task.verified_by}
                      </Text>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Task Completion Details */}
          {taskCompletion && (
            <div className="border rounded-md overflow-hidden mt-4">
              <div className="flex items-center gap-2 p-3 bg-gray-100 cursor-pointer">
                <CheckSquare className="w-5 h-5 text-gray-600" />
                <span className="font-medium">Completion Details</span>
              </div>
              <div className="p-4 bg-gray-50">
                  <div className="space-y-4">
                    {taskCompletion.issues_found && (
                      <div className="bg-red-50 p-3 rounded-md">
                        <div className="flex items-center gap-2 mb-1">
                          <AlertTriangle className="w-4 h-4 text-red-600" />
                          <Text className="font-medium text-red-600">Issues Found</Text>
                        </div>
                        <Text className="text-sm text-red-600">{taskCompletion.issue_description}</Text>
                        {taskCompletion.requires_maintenance && (
                          <Badge color="orange" className="mt-2">Requires Maintenance</Badge>
                        )}
                      </div>
                    )}

                    {checklist && checklist.items && checklist.items.length > 0 && (
                      <div>
                        <Text className="font-medium mb-2">Checklist: {checklist.name}</Text>
                        <div className="space-y-2">
                          {checklist.items.map((item: any) => {
                            const isCompleted = taskCompletion.completed_items?.includes(item.id);
                            const isSkipped = taskCompletion.skipped_items?.includes(item.id);

                            return (
                              <div key={item.id} className="flex items-start gap-2">
                                {isCompleted ? (
                                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                                ) : isSkipped ? (
                                  <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                                ) : (
                                  <div className="w-4 h-4 border border-gray-300 rounded-full mt-0.5" />
                                )}
                                <div>
                                  <Text className="font-medium">{item.name}</Text>
                                  {item.description && (
                                    <Text className="text-sm text-gray-500">{item.description}</Text>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {taskCompletion.notes && (
                      <div>
                        <Text className="font-medium mb-1">Notes</Text>
                        <Text className="text-sm">{taskCompletion.notes}</Text>
                      </div>
                    )}

                    {taskCompletion.photos && taskCompletion.photos.length > 0 && (
                      <div>
                        <Text className="font-medium mb-2">Photos</Text>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {taskCompletion.photos.map((photo: string, index: number) => (
                            <div key={index} className="aspect-square bg-gray-200 rounded-md overflow-hidden">
                              <img src={photo} alt={`Photo ${index + 1}`} className="w-full h-full object-cover" />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {task.notes && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="w-5 h-5 text-gray-600" />
                <Heading level="h4" className="text-lg">Notes</Heading>
              </div>
              <Text>{task.notes}</Text>
            </div>
          )}
        </>
      )}

      {/* Actions */}
      <div className="flex justify-end">
        <Button
          variant="secondary"
          onClick={onClose}
        >
          Close
        </Button>
      </div>

      <Toaster />
    </div>
  );
};

export default HousekeepingTaskDetails;
