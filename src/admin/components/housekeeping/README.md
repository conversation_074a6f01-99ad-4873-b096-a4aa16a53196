# Housekeeping Module Mock Data

This directory contains mock data and API handlers for the housekeeping module. These files allow you to test and develop the housekeeping module UI without requiring a backend implementation.

## Files

- `mock-housekeeping-data.ts` - Contains all the mock data structures for housekeeping tasks, staff, checklists, etc.
- `mock-api-handler.ts` - Handles API requests and returns appropriate mock responses
- `register-mock-api.ts` - Utility to register the mock API handler with the fetch API
- `mock-api-initializer.tsx` - React component to initialize the mock API and display a notification

## Features

The mock data implementation provides the following features:

### Housekeeping Tasks
- List tasks with filtering by date
- Create, update, and delete tasks
- Task lifecycle management (start, complete, verify, cancel)
- Task assignment to staff members

### Housekeeping Staff
- List staff members
- Create, update, and delete staff
- Staff role management (housekeeper, supervisor, manager, maintenance)

### Housekeeping Checklists
- List checklists with their items
- Create, update, and delete checklists and checklist items
- Checklist assignment to specific room types or task types

### Room Status Dashboard
- View room status (dirty, cleaning, clean, inspected, out of service)
- Filter rooms by floor, room type, and status
- Create housekeeping tasks for rooms

### Statistics
- View housekeeping statistics (tasks by status, room status, priority)

## Usage

The mock API is automatically initialized when the housekeeping dashboard is loaded. You'll see a notification in the bottom right corner indicating that the mock API is active.

All API requests to the housekeeping endpoints will be intercepted and handled by the mock API handler, returning appropriate mock data.

## Data Structure

### Housekeeping Tasks
```typescript
{
  id: string;
  room_id: string;
  room_config_id: string;
  hotel_id: string;
  task_type: string; // "cleaning", "deep_cleaning", "inspection", "maintenance", etc.
  status: HousekeepingTaskStatus; // "pending", "in_progress", "completed", "verified", "cancelled"
  priority: HousekeepingTaskPriority; // "low", "medium", "high", "urgent"
  room_status: RoomCleaningStatus; // "dirty", "cleaning", "clean", "inspected", "out_of_service"
  scheduled_date: string; // ISO date string
  due_time: string; // "HH:MM"
  assigned_to: string | null; // Staff ID
  assigned_at: string | null; // ISO date string
  started_at: string | null; // ISO date string
  completed_at: string | null; // ISO date string
  verified_at: string | null; // ISO date string
  verified_by: string | null; // Staff ID
  notes: string | null;
  guest_request: boolean;
  recurring: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}
```

### Housekeeping Staff
```typescript
{
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  role: StaffRole; // "housekeeper", "supervisor", "manager", "maintenance"
  hotel_id: string;
  is_active: boolean;
  max_rooms_per_shift: number;
  working_days: string[]; // ["Monday", "Tuesday", ...]
  shift: string; // "morning", "afternoon", "evening", "night"
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}
```

### Housekeeping Checklists
```typescript
{
  id: string;
  name: string;
  description: string | null;
  hotel_id: string;
  room_config_id: string | null; // If specific to a room type
  task_type: string; // "cleaning", "deep_cleaning", "inspection", etc.
  is_active: boolean;
  items: HousekeepingChecklistItem[];
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}
```

### Housekeeping Checklist Items
```typescript
{
  id: string;
  checklist_id: string;
  name: string;
  description: string | null;
  order: number;
  is_required: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
}
```

## Extending the Mock Data

To add more mock data or modify existing data, edit the `mock-housekeeping-data.ts` file. To add new API endpoints or modify the behavior of existing endpoints, edit the `mock-api-handler.ts` file.
