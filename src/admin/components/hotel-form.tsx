import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Select,
  Textarea,
  Checkbox,
  DropdownMenu,
} from "@camped-ai/ui";
import { ChevronDown } from "@camped-ai/icons";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import HotelMediaSection from "./hotel/hotel-media-section";
import { RoomTypeData } from "../types";

export type MediaField = {
  isThumbnail: boolean;
  url: string;
  id?: string | undefined;
  file?: File;
  field_id: string;
};

export type HotelFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  website: string | null;
  email: string | null;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  safety_measures?: string[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  roomTypes?: string[];
  id?: string;
};

type HotelFormProps = {
  formData: HotelFormData;
  roomTypes: RoomTypeData[];
  setFormData?: (data: HotelFormData) => void;
  onSubmit: (data: HotelFormData) => Promise<boolean>;
  closeModal: () => void;
};

const HotelForm = ({
  formData,
  setFormData = () => {}, // Default no-op function
  onSubmit,
  closeModal,
  roomTypes,
}: HotelFormProps) => {
  const [destinations, setDestinations] = useState<
    Array<{ id: string; name: string; location: string | null }>
  >([]);

  const [, setIsLoading] = useState(false); // isLoading state is used in handleSubmit

  const [isEditMode, setIsEditMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const form = useForm<HotelFormData>({
    defaultValues: {
      ...formData,
      destination_id: formData.destination_id || "",
      email: formData.email || "",
      rating: formData.rating || undefined,
      total_reviews: formData.total_reviews || undefined,
      notes: formData.notes || "",
      location: formData.location || "",
      address: formData.address || "",
      phone_number: formData.phone_number || "",
      timezone: formData.timezone || "",
      available_languages: formData.available_languages || [],
      tax_type: formData.tax_type || "",
      tax_number: formData.tax_number || "",
      tags: formData.tags || [],
      amenities: formData.amenities || [],
      currency: formData.currency || "",
      check_in_time: formData.check_in_time || "",
      check_out_time: formData.check_out_time || "",
      roomTypes: formData.roomTypes?.map((rt: any) => rt.id) ?? [],
      media: formData.media ?? [], // Ensure media is always an array
    },
  });

  useEffect(() => {
    setIsEditMode(!!formData.id);

    if (formData.media && formData.media.length > 0) {
      form.setValue(
        "media",
        formData.media.map((media) => ({
          ...media,
          field_id: media.field_id || crypto.randomUUID(),
        })),
        {
          shouldDirty: true,
          shouldTouch: true,
        }
      );
    }
  }, [formData, form.setValue]);

  const handleSubmit = async (data: HotelFormData) => {
    setIsLoading(true);
    try {
      const result = await onSubmit(data);
      if (result) {
        setFormData(data);
        closeModal();
      }
      return result;
    } catch (error) {
      console.error("Hotel submission error:", error);
      throw new Error(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetch("/admin/hotel-management/destinations", { credentials: "include" })
      .then((res) => res.json())
      .then((data) => setDestinations(data.destinations))
      .catch((error) => console.error("Error fetching destinations:", error));
  }, []);

  return (
    <FocusModal.Content>
      <FocusModal.Header>
        <div className="flex justify-between items-center">
          <Button
            variant="primary"
            onClick={form.handleSubmit(handleSubmit)}
            disabled={
              !form.watch("name") ||
              !form.watch("handle") ||
              (!isEditMode && !form.watch("destination_id"))
            }
          >
            {isEditMode ? "Update" : "Save"}
          </Button>
        </div>
      </FocusModal.Header>
      <FocusModal.Body className="flex flex-col items-center py-16 gap-4 overflow-y-auto ">
        <Heading level="h1">
          {isEditMode ? "Edit Hotel" : "Create Hotel"}
        </Heading>
        <div className="flex w-full max-w-lg flex-col gap-y-6">
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Hotel Name <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={form.watch("name")}
              onChange={(e) => {
                form.setValue("name", e.target.value);
                form.setValue(
                  "handle",
                  e.target.value.toLowerCase().replace(/\s+/g, "-")
                );
              }}
              placeholder="Enter hotel name"
            />
          </div>
          <div>
            <Text className="mb-2">Description</Text>
            <Textarea
              value={form.watch("description")}
              onChange={(e) => form.setValue("description", e.target.value)}
              placeholder="Enter description"
            />
          </div>
          {!isEditMode && (
            <div>
              <Text className="mb-2 flex items-center gap-1">
                Destination <span className="text-red-500 text-[0.6em]">★</span>
              </Text>
              <Select
                required={true}
                value={form.watch("destination_id") || ""}
                onValueChange={(value) => {
                  form.setValue("destination_id", value);
                }}
              >
                <Select.Trigger className="w-full rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none">
                  <Select.Value placeholder="Select a destination" />
                </Select.Trigger>
                <Select.Content>
                  {destinations.map((destination) => (
                    <Select.Item key={destination.id} value={destination.id}>
                      {destination.name}{" "}
                      {destination.location ? `(${destination.location})` : ""}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          )}
          <div className="flex gap-x-8">
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-active"
                checked={form.watch("is_active")}
                onCheckedChange={(checked) =>
                  form.setValue("is_active", checked)
                }
              />
              <Label htmlFor="is-active">Active</Label>
            </div>
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-pets-allowed"
                checked={form.watch("is_pets_allowed") || false}
                onCheckedChange={(checked) =>
                  form.setValue("is_pets_allowed", checked)
                }
              />
              <Label htmlFor="is-pets-allowed">Pets Allowed</Label>
            </div>
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Handle <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={form.watch("handle")}
              onChange={(e) => form.setValue("handle", e.target.value)}
              placeholder="Enter handle"
            />
          </div>

          <div>
            <Text className="mb-2">Website</Text>
            <Input
              value={form.watch("website") || ""}
              onChange={(e) => form.setValue("website", e.target.value)}
              placeholder="Enter website URL"
            />
          </div>
          <div>
            <Text className="mb-2">Room Types</Text>
            <DropdownMenu>
              <DropdownMenu.Trigger className="w-full">
                <Button variant="secondary" className="w-full justify-between">
                  <span className="truncate">
                    {form.watch("roomTypes")?.length
                      ? roomTypes
                          ?.filter((rt: any) =>
                            form.watch("roomTypes")?.includes(rt.id)
                          )
                          .map((rt: any) => rt.name)
                          .join(", ")
                      : "Select room types"}
                  </span>
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content className="w-[200px]">
                <div className="p-2">
                  <Input
                    placeholder="Search room types..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full"
                  />
                </div>
                {roomTypes
                  ?.filter((rt) =>
                    rt.name.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((roomType) => (
                    <DropdownMenu.Item
                      key={roomType.id}
                      onSelect={(event) => event.preventDefault()}
                      className="gap-2"
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Checkbox
                          checked={form
                            .watch("roomTypes")
                            ?.includes(roomType.id)}
                          onCheckedChange={() => {
                            const currentValues = form.watch("roomTypes") || [];
                            const newValues = currentValues.includes(
                              roomType.id
                            )
                              ? currentValues.filter((v) => v !== roomType.id)
                              : [...currentValues, roomType.id];
                            form.setValue("roomTypes", newValues);
                          }}
                        />
                        <span>{roomType.name}</span>
                      </div>
                    </DropdownMenu.Item>
                  ))}
              </DropdownMenu.Content>
            </DropdownMenu>
          </div>
          <div>
            <Text className="mb-2">Rating</Text>
            <Input
              type="number"
              value={form.watch("rating")}
              onChange={(e) => form.setValue("rating", Number(e.target.value))}
              placeholder="Enter rating"
            />
          </div>
          <div>
            <Text className="mb-2">Notes</Text>
            <Textarea
              value={form.watch("notes")}
              onChange={(e) => form.setValue("notes", e.target.value)}
              placeholder="Enter notes"
            />
          </div>
          <div>
            <Text className="mb-2">Email</Text>
            <Input
              value={form.watch("email") || ""}
              onChange={(e) => form.setValue("email", e.target.value)}
              placeholder="Enter email"
            />
          </div>
          <div>
            <Text className="mb-2">Location</Text>
            <Input
              value={form.watch("location") || ""}
              onChange={(e) => form.setValue("location", e.target.value)}
              placeholder="Enter location"
            />
          </div>
          <div>
            <Text className="mb-2">Address</Text>
            <Textarea
              value={form.watch("address") || ""}
              onChange={(e) => form.setValue("address", e.target.value)}
              placeholder="Enter address"
            />
          </div>
          <HotelMediaSection form={form} />
        </div>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default HotelForm;
