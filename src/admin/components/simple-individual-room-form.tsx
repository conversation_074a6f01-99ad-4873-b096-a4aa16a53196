import React, { useState, useEffect } from "react";
import {
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Switch,
  Toaster,
  toast,
} from "@camped-ai/ui";
import CustomSelect from "./custom-select";
import { TextareaField } from "./ai-enhanced-inputs";
import { <PERSON><PERSON>ini, XMark, Spinner } from "@camped-ai/icons";
import { Edit, XCircle } from "lucide-react";

interface SimpleIndividualRoomFormProps {
  hotelId: string;
  roomConfigId?: string;
  onComplete: (success: boolean) => void;
  initialData?: any;
  isEdit?: boolean;
  renderFooterButtons?: boolean;
  onSubmit?: (submitFn: () => void) => void;
  onSubmittingChange?: (isSubmitting: boolean) => void;
}

interface RoomOption {
  key: string;
  value: string;
}

const SimpleIndividualRoomForm: React.FC<SimpleIndividualRoomFormProps> = ({
  hotelId,
  roomConfigId,
  onComplete,
  initialData,
  isEdit = false,
  renderFooterButtons = true,
  onSubmit,
  onSubmittingChange,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [roomConfigs, setRoomConfigs] = useState<any[]>([]);
  const [isLoadingRoomConfigs, setIsLoadingRoomConfigs] = useState(true);
  const [openDropdown, setOpenDropdown] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    room_number: "",
    floor: "",
    status: "available",
    notes: "",
    is_active: true,
    // Room relationships
    left_room: "",
    opposite_room: "",
    connected_room: "",
    right_room: "",
    // References
    room_config_id: roomConfigId || "",
    hotel_id: hotelId || "hotel_id_missing",
    options: {} as Record<string, string>,
  });

  // State for existing rooms (for relationship dropdowns)
  const [existingRooms, setExistingRooms] = useState<any[]>([]);

  // For managing the UI state of options
  const [roomOptions, setRoomOptions] = useState<RoomOption[]>(
    initialData?.options
      ? Object.entries(initialData.options).map(([key, value]) => ({
          key,
          value: value as string,
        }))
      : []
  );
  const [newOptionKey, setNewOptionKey] = useState("");
  const [newOptionValue, setNewOptionValue] = useState("");

  useEffect(() => {
    const fetchRoomConfigs = async () => {
      setIsLoadingRoomConfigs(true);
      try {
        // Fetch room configurations from the API
        const timestamp = new Date().getTime();
        const response = await fetch(
          `/admin/direct-room-configs?hotel_id=${hotelId}&_=${timestamp}`,
          {
            credentials: "include",
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        const data = await response.json();
        console.log("Room configurations response:", data);

        if (data.roomConfigs && Array.isArray(data.roomConfigs)) {
          setRoomConfigs(data.roomConfigs);

          // If no room config is selected and we have configs, select the first one
          if (!formData.room_config_id && data.roomConfigs.length > 0) {
            setFormData((prev) => ({
              ...prev,
              room_config_id: data.roomConfigs[0].id,
            }));
          }
        } else {
          // Use sample data if API returns no results
          const sampleRoomConfigs = [
            {
              id: "room_config_1",
              name: "Deluxe King Room",
              type: "deluxe",
              hotel_id: hotelId,
            },
            {
              id: "room_config_2",
              name: "Family Suite",
              type: "suite",
              hotel_id: hotelId,
            },
          ];
          setRoomConfigs(sampleRoomConfigs);

          // If no room config is selected and we have configs, select the first one
          if (!formData.room_config_id) {
            setFormData((prev) => ({
              ...prev,
              room_config_id: sampleRoomConfigs[0].id,
            }));
          }
        }
      } catch (error) {
        console.error("Error fetching room configurations:", error);
        toast.error("Error", {
          description: "Failed to load room configurations",
        });

        // Use sample data if API fails
        const sampleRoomConfigs = [
          {
            id: "room_config_1",
            name: "Deluxe King Room",
            type: "deluxe",
            hotel_id: hotelId,
          },
          {
            id: "room_config_2",
            name: "Family Suite",
            type: "suite",
            hotel_id: hotelId,
          },
        ];
        setRoomConfigs(sampleRoomConfigs);

        // If no room config is selected and we have configs, select the first one
        if (!formData.room_config_id) {
          setFormData((prev) => ({
            ...prev,
            room_config_id: sampleRoomConfigs[0].id,
          }));
        }
      } finally {
        setIsLoadingRoomConfigs(false);
      }
    };

    // Fetch existing rooms for relationship dropdowns
    const fetchExistingRooms = async () => {
      try {
        // If we're editing a room, we want to fetch all rooms except the current one
        const currentRoomId = initialData?.id || "";
        const timestamp = new Date().getTime();
        const response = await fetch(
          `/admin/direct-rooms?hotel_id=${hotelId}&_=${timestamp}`,
          {
            credentials: "include",
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              Pragma: "no-cache",
              Expires: "0",
            },
          }
        );

        const data = await response.json();
        console.log("Existing rooms response:", data);

        if (data.rooms && Array.isArray(data.rooms)) {
          // Filter out the current room if we're editing
          const filteredRooms = data.rooms.filter(
            (room: any) => room.id !== currentRoomId
          );
          setExistingRooms(filteredRooms);
        } else {
          setExistingRooms([]);
        }
      } catch (error) {
        console.error("Error fetching existing rooms:", error);
        setExistingRooms([]);
      }
    };

    fetchRoomConfigs();
    fetchExistingRooms();
  }, [hotelId, initialData?.id]);

  // Initialize form data when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log("Setting initial data:", initialData);
      setFormData({
        name: initialData.name || "",
        room_number: initialData.room_number || "",
        floor: initialData.floor || "",
        status: initialData.status || "available",
        notes: initialData.notes || "",
        is_active: initialData.is_active !== false,
        // Room relationships
        left_room: initialData.left_room || "",
        opposite_room: initialData.opposite_room || "",
        connected_room: initialData.connected_room || "",
        right_room: initialData.right_room || "",
        // References
        room_config_id: initialData.room_config_id || roomConfigId || "",
        hotel_id: initialData.hotel_id || hotelId || "hotel_id_missing",
        options: initialData.options || ({} as Record<string, string>),
      });
    }
  }, [initialData, roomConfigId, hotelId]);

  // Register the submit function with the parent component
  useEffect(() => {
    if (onSubmit) {
      onSubmit(handleSubmit);
    }
  }, [onSubmit, formData, isSubmitting]);

  // Add click outside handler to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (openDropdown && !target.closest(".dropdown-container")) {
        setOpenDropdown("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openDropdown]);

  const roomStatuses = [
    { value: "available", label: "Available" },
    { value: "occupied", label: "Occupied" },
    { value: "maintenance", label: "Maintenance" },
    { value: "cleaning", label: "Cleaning" },
  ];

  const handleInputChange = (field: string, value: any) => {
    console.log(`Updating field ${field} to value:`, value);
    setFormData((prevData) => {
      const newData = { ...prevData, [field]: value };
      console.log("New form data:", newData);
      return newData;
    });
  };

  const addOption = () => {
    if (newOptionKey && newOptionValue) {
      // Add to UI state
      setRoomOptions([
        ...roomOptions,
        { key: newOptionKey, value: newOptionValue },
      ]);

      // Add to form data
      const updatedOptions = {
        ...formData.options,
        [newOptionKey]: newOptionValue,
      };
      setFormData({ ...formData, options: updatedOptions });

      // Reset inputs
      setNewOptionKey("");
      setNewOptionValue("");
    }
  };

  const removeOption = (keyToRemove: string) => {
    // Remove from UI state
    setRoomOptions(roomOptions.filter((option) => option.key !== keyToRemove));

    // Remove from form data
    const updatedOptions = { ...formData.options };
    delete updatedOptions[keyToRemove];
    setFormData({ ...formData, options: updatedOptions });
  };

  const handleSubmit = async () => {
    console.log("Submitting form with hotelId:", hotelId);
    console.log("Current formData:", formData);
    setIsSubmitting(true);
    if (onSubmittingChange) {
      onSubmittingChange(true);
    }
    try {
      // Prepare the data for API submission
      const apiData = isEdit
        ? {
            id: initialData.id,
            ...formData,
            hotel_id: hotelId,
            // Ensure these fields are explicitly included
            floor: formData.floor,
            room_number: formData.room_number,
            name: formData.name,
            status: formData.status,
            notes: formData.notes,
            is_active: formData.is_active,
            left_room: formData.left_room,
            opposite_room: formData.opposite_room,
            connected_room: formData.connected_room,
            right_room: formData.right_room,
          }
        : { ...formData, hotel_id: hotelId };

      console.log("Submitting room data:", apiData);

      // Send the data to the API
      const endpoint = "/admin/direct-rooms";
      const method = isEdit ? "PUT" : "POST";

      console.log(
        `Sending ${method} request to ${endpoint} with data:`,
        apiData
      );

      const response = await fetch(endpoint, {
        method,
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(apiData),
      });

      const data = await response.json();
      console.log("API response:", data);

      if (response.status >= 400) {
        throw new Error(data.message || "Failed to save room");
      }

      toast.success("Success", {
        description: isEdit
          ? "Room updated successfully"
          : "Room created successfully",
      });
      onComplete(true);
    } catch (error) {
      console.error("Error saving room:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to save room",
      });
    } finally {
      setIsSubmitting(false);
      if (onSubmittingChange) {
        onSubmittingChange(false);
      }
    }
  };

  return (
    <div className="p-6">
      <Toaster />
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Room Type *
          </label>
          <CustomSelect
            value={formData.room_config_id}
            onChange={(value) => handleInputChange("room_config_id", value)}
            options={roomConfigs.map((config) => ({
              value: config.id,
              label: config.name
            }))}
            placeholder={isLoadingRoomConfigs
              ? "Loading room types..."
              : roomConfigs.length === 0
              ? "No room types available"
              : "Select a room type"}
            disabled={isLoadingRoomConfigs || roomConfigs.length === 0}
            className="w-full h-9 text-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Room Number *
          </label>
          <Input
            value={formData.room_number}
            onChange={(e) => handleInputChange("room_number", e.target.value)}
            placeholder="e.g., 101"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Room Name *
          </label>
          <Input
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="e.g., Deluxe King 101"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Floor
          </label>
          <Input
            value={formData.floor}
            onChange={(e) => handleInputChange("floor", e.target.value)}
            placeholder="e.g., 1"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <CustomSelect
            value={formData.status}
            onChange={(value) => handleInputChange("status", value)}
            options={roomStatuses.map((status) => ({
              value: status.value,
              label: status.label
            }))}
            placeholder="Select a status"
            className="w-full h-9 text-sm"
          />
        </div>

        <div>
          <TextareaField
            id="notes"
            label="Notes"
            value={formData.notes}
            onChange={(value) => handleInputChange("notes", value)}
            placeholder="Any special notes about this room..."
            rows={3}
            contentType="description"
            context={{
              name: formData.name,
              type: "room notes",
              room_number: formData.room_number,
              room_type: formData.room_type,
            }}
            helpText="Add any special instructions or information about this room"
          />
        </div>

        <div className="border-t border-gray-200 pt-4 mt-4">
          <Heading level="h3" className="text-md font-semibold mb-3">
            Room Relationships
          </Heading>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Left Room
              </label>
              <CustomSelect
                value={formData.left_room}
                onChange={(value) => handleInputChange("left_room", value)}
                options={[
                  { value: "", label: "None" },
                  ...existingRooms.map((room) => ({
                    value: room.id,
                    label: room.name || `Room ${room.room_number}`
                  }))
                ]}
                placeholder="Select a room"
                className="w-full h-9 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Right Room
              </label>
              <CustomSelect
                value={formData.right_room}
                onChange={(value) => handleInputChange("right_room", value)}
                options={[
                  { value: "", label: "None" },
                  ...existingRooms.map((room) => ({
                    value: room.id,
                    label: room.name || `Room ${room.room_number}`
                  }))
                ]}
                placeholder="Select a room"
                className="w-full h-9 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Opposite Room
              </label>
              <CustomSelect
                value={formData.opposite_room}
                onChange={(value) => handleInputChange("opposite_room", value)}
                options={[
                  { value: "", label: "None" },
                  ...existingRooms.map((room) => ({
                    value: room.id,
                    label: room.name || `Room ${room.room_number}`
                  }))
                ]}
                placeholder="Select a room"
                className="w-full h-9 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Connected Room
              </label>
              <CustomSelect
                value={formData.connected_room}
                onChange={(value) => handleInputChange("connected_room", value)}
                options={[
                  { value: "", label: "None" },
                  ...existingRooms.map((room) => ({
                    value: room.id,
                    label: room.name || `Room ${room.room_number}`
                  }))
                ]}
                placeholder="Select a room"
                className="w-full h-9 text-sm"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Text className="font-medium">Active</Text>
            <Text className="text-sm text-gray-500">
              Is this room available for booking?
            </Text>
          </div>
          <Switch
            checked={formData.is_active}
            onCheckedChange={(checked) =>
              handleInputChange("is_active", checked)
            }
          />
        </div>

        {/* Room Options Section */}
        <div className="mt-6">
          <Text className="font-medium mb-2">Room Options</Text>
          <Text className="text-sm text-gray-500 mb-4">
            Add specific attributes for this room (e.g., View, Bed Type, etc.)
          </Text>

          {/* Current Options */}
          {roomOptions.length > 0 && (
            <div className="mb-4 space-y-2">
              {roomOptions.map((option) => (
                <div
                  key={option.key}
                  className="p-3 border rounded-md flex justify-between items-center"
                >
                  <div>
                    <Text className="font-medium">{option.key}</Text>
                    <Text className="text-sm text-gray-500">
                      {option.value}
                    </Text>
                  </div>
                  <Button
                    onClick={() => removeOption(option.key)}
                    variant="secondary"
                    size="small"
                    className="h-8 w-8 p-0 flex items-center justify-center"
                  >
                    <XMark className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Add New Option */}
          <div className="flex gap-2 mb-2">
            <Input
              placeholder="Option name (e.g., View)"
              value={newOptionKey}
              onChange={(e) => setNewOptionKey(e.target.value)}
              className="flex-1"
            />
            <Input
              placeholder="Value (e.g., Ocean)"
              value={newOptionValue}
              onChange={(e) => setNewOptionValue(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={addOption}
              disabled={!newOptionKey || !newOptionValue}
              variant="secondary"
              size="small"
              className="h-9 flex items-center"
            >
              <PlusMini className="w-4 h-4 mr-2" />
              Add
            </Button>
          </div>
        </div>

        {renderFooterButtons && (
          <div className="flex justify-end mt-6">
            <Button
              variant="secondary"
              size="small"
              onClick={() => onComplete(false)}
              className="h-9 flex items-center mr-2"
            >
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={handleSubmit}
              disabled={
                isSubmitting ||
                !formData.name ||
                !formData.room_number ||
                !formData.room_config_id
              }
              className="h-9 flex items-center"
            >
              {isSubmitting ? (
                <>
                  <Spinner className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : isEdit ? (
                <>
                  <Edit className="w-4 h-4 mr-2" />
                  Update Room
                </>
              ) : (
                <>
                  <PlusMini className="w-4 h-4 mr-2" />
                  Create Room
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleIndividualRoomForm;
