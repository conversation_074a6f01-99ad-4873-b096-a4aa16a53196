import React from "react";
import { ContentType } from "../hooks/useAIGenerate";
import AIEnhance from "./ai-enhance";

interface WithAIGenerateProps {
  contentType?: ContentType;
  context?: Record<string, any>;
  value: string;
  onChange: (value: string) => void;
  buttonVariant?: "primary" | "secondary" | "transparent";
  buttonSize?: "small" | "base" | "large";
  tooltipText?: string;
  buttonText?: string;
  disabled?: boolean;
}

/**
 * A higher-order component that adds AI generation capabilities to any input component
 * 
 * @example
 * const InputWithAI = withAIGenerate(Input);
 * 
 * // Then in your component:
 * <InputWithAI
 *   contentType="name"
 *   context={{ type: "product" }}
 *   value={name}
 *   onChange={setName}
 *   placeholder="Enter name"
 * />
 */
function withAIGenerate<P extends { value?: string; onChange?: (e: any) => void }>(
  Component: React.ComponentType<P>
) {
  return function WithAIGenerate({
    contentType = "general",
    context = {},
    value,
    onChange,
    buttonVariant = "transparent",
    buttonSize = "small",
    tooltipText,
    buttonText,
    disabled,
    ...props
  }: WithAIGenerateProps & Omit<P, "value" | "onChange">) {
    const handleChange = (e: any) => {
      if (typeof e === "string") {
        onChange(e);
      } else if (e && e.target && e.target.value !== undefined) {
        onChange(e.target.value);
      }
    };

    return (
      <div className="relative">
        <Component
          {...(props as P)}
          value={value}
          onChange={handleChange}
          className="pr-10"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
          <AIEnhance
            contentType={contentType}
            context={context}
            onGenerate={onChange}
            buttonVariant={buttonVariant}
            buttonSize={buttonSize}
            tooltipText={tooltipText}
            buttonText={buttonText}
            disabled={disabled}
          />
        </div>
      </div>
    );
  };
}

export default withAIGenerate;
