import React, { useState } from "react";
import {
  Container,
  <PERSON>ing,
  Text,
  Button,
  Input,
  Textarea,
  Switch,
  Select,
  Tabs,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { Form } from "../components/form/simple-form";

interface NotificationTemplateFormData {
  event_name: string;
  channel: string;
  subject?: string;
  content: string;
  is_default?: boolean;
  is_active?: boolean;
}

interface NotificationTemplateFormProps {
  initialData?: Partial<NotificationTemplateFormData> & { id?: string };
  onSuccess: () => void;
}

const NotificationTemplateForm: React.FC<NotificationTemplateFormProps> = ({
  initialData,
  onSuccess,
}) => {
  const queryClient = useQueryClient();
  const isEdit = !!initialData?.id;
  const [activeTab, setActiveTab] = useState("content");

  const form = useForm<NotificationTemplateFormData>({
    defaultValues: {
      event_name: initialData?.event_name || "",
      channel: initialData?.channel || "email",
      subject: initialData?.subject || "",
      content: initialData?.content || "",
      is_default: initialData?.is_default || false,
      is_active: initialData?.is_active !== undefined ? initialData.is_active : true,
    },
  });

  // Common event names for reference
  const commonEvents = [
    "order.placed",
    "order.shipped",
    "order.delivered",
    "order.cancelled",
    "product.created",
    "user.created",
    "user.password_reset",
    "payment.succeeded",
    "payment.failed",
  ];

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: NotificationTemplateFormData) => {
      return await sdk.client.fetch("/admin/notification-templates", {
        method: "POST",
        body: data,
      });
    },
    onSuccess: () => {
      toast.success("Notification template created successfully");
      queryClient.invalidateQueries({ queryKey: ["notification-templates"] });
      onSuccess();
    },
    onError: (error) => {
      toast.error(`Error creating notification template: ${error.message}`);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async (data: NotificationTemplateFormData & { id: string }) => {
      return await sdk.client.fetch(`/admin/notification-templates/${data.id}`, {
        method: "PUT",
        body: {
          subject: data.subject,
          content: data.content,
          is_default: data.is_default,
          is_active: data.is_active,
        },
      });
    },
    onSuccess: () => {
      toast.success("Notification template updated successfully");
      queryClient.invalidateQueries({ queryKey: ["notification-templates"] });
      onSuccess();
    },
    onError: (error) => {
      toast.error(`Error updating notification template: ${error.message}`);
    },
  });

  const onSubmit = (data: NotificationTemplateFormData) => {
    if (isEdit && initialData?.id) {
      updateMutation.mutate({ ...data, id: initialData.id });
    } else {
      createMutation.mutate(data);
    }
  };

  // Show subject field only for email channel
  const showSubject = form.watch("channel") === "email";

  return (
    <Container className="max-w-4xl mx-auto p-6">
      <Toaster />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {!isEdit && (
              <>
                <Form.Field
                  control={form.control}
                  name="event_name"
                  rules={{ required: "Event name is required" }}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Event Name</Form.Label>
                      <Form.Control>
                        <Input
                          {...field}
                          placeholder="e.g., order.placed"
                          list="event-suggestions"
                        />
                        <datalist id="event-suggestions">
                          {commonEvents.map((event) => (
                            <option key={event} value={event} />
                          ))}
                        </datalist>
                      </Form.Control>
                      <Form.Hint>
                        The event that triggers this notification (e.g., order.placed)
                      </Form.Hint>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )}
                />

                <Form.Field
                  control={form.control}
                  name="channel"
                  rules={{ required: "Channel is required" }}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Channel</Form.Label>
                      <Form.Control>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <Select.Content>
                            <Select.Item value="email">Email</Select.Item>
                            <Select.Item value="feed">In-App Feed</Select.Item>
                          </Select.Content>
                        </Select>
                      </Form.Control>
                      <Form.Hint>
                        The channel through which the notification will be sent
                      </Form.Hint>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )}
                />
              </>
            )}

            <Form.Field
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <Form.Item className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <Form.Label>Active</Form.Label>
                    <Form.Hint>
                      Enable or disable this notification template
                    </Form.Hint>
                  </div>
                  <Form.Control>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </Form.Control>
                </Form.Item>
              )}
            />

            <Form.Field
              control={form.control}
              name="is_default"
              render={({ field }) => (
                <Form.Item className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <Form.Label>Default Template</Form.Label>
                    <Form.Hint>
                      Set as the default template for this event and channel
                    </Form.Hint>
                  </div>
                  <Form.Control>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </Form.Control>
                </Form.Item>
              )}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Trigger value="content">Content</Tabs.Trigger>
              <Tabs.Trigger value="preview">Preview</Tabs.Trigger>
              <Tabs.Trigger value="placeholders">Available Placeholders</Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value="content" className="pt-4 space-y-4">
              {showSubject && (
                <Form.Field
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Subject</Form.Label>
                      <Form.Control>
                        <Input
                          {...field}
                          placeholder="Enter email subject"
                        />
                      </Form.Control>
                      <Form.Hint>
                        The subject line for email notifications
                      </Form.Hint>
                      <Form.ErrorMessage />
                    </Form.Item>
                  )}
                />
              )}

              <Form.Field
                control={form.control}
                name="content"
                rules={{ required: "Content is required" }}
                render={({ field }) => (
                  <Form.Item>
                    <Form.Label>Content</Form.Label>
                    <Form.Control>
                      <Textarea
                        {...field}
                        placeholder={
                          form.watch("channel") === "email"
                            ? "Enter HTML content with placeholders like {{user.name}}"
                            : "Enter plain text content with placeholders like {{user.name}}"
                        }
                        className="min-h-[300px] font-mono"
                      />
                    </Form.Control>
                    <Form.Hint>
                      {form.watch("channel") === "email"
                        ? "HTML content with placeholders. Use {{placeholder}} syntax."
                        : "Plain text content with placeholders. Use {{placeholder}} syntax."}
                    </Form.Hint>
                    <Form.ErrorMessage />
                  </Form.Item>
                )}
              />
            </Tabs.Content>
            <Tabs.Content value="preview" className="pt-4">
              <div className="border rounded-lg p-4 min-h-[300px] bg-white">
                {form.watch("channel") === "email" ? (
                  <div>
                    {showSubject && (
                      <div className="mb-2 pb-2 border-b">
                        <strong>Subject:</strong> {form.watch("subject")}
                      </div>
                    )}
                    <div dangerouslySetInnerHTML={{ __html: form.watch("content") }} />
                  </div>
                ) : (
                  <div>{form.watch("content")}</div>
                )}
              </div>
              <Text className="text-ui-fg-subtle mt-2">
                Note: Placeholders will not be replaced in this preview.
              </Text>
            </Tabs.Content>
            <Tabs.Content value="placeholders" className="pt-4">
              <div className="border rounded-lg p-4">
                <Heading level="h3" className="mb-4">Common Placeholders</Heading>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Heading level="h3" className="mb-2">User</Heading>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><code>{"{{user.id}}"}</code> - User ID</li>
                      <li><code>{"{{user.email}}"}</code> - User email</li>
                      <li><code>{"{{user.first_name}}"}</code> - User first name</li>
                      <li><code>{"{{user.last_name}}"}</code> - User last name</li>
                    </ul>
                  </div>
                  <div>
                    <Heading level="h3" className="mb-2">Order</Heading>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><code>{"{{order.id}}"}</code> - Order ID</li>
                      <li><code>{"{{order.display_id}}"}</code> - Order display ID</li>
                      <li><code>{"{{order.status}}"}</code> - Order status</li>
                      <li><code>{"{{order.total}}"}</code> - Order total</li>
                    </ul>
                  </div>
                  <div>
                    <Heading level="h3" className="mb-2">Product</Heading>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><code>{"{{product.id}}"}</code> - Product ID</li>
                      <li><code>{"{{product.title}}"}</code> - Product title</li>
                      <li><code>{"{{product.description}}"}</code> - Product description</li>
                      <li><code>{"{{product.handle}}"}</code> - Product handle</li>
                    </ul>
                  </div>
                  <div>
                    <Heading level="h3" className="mb-2">Other</Heading>
                    <ul className="list-disc pl-5 space-y-1">
                      <li><code>{"{{store.name}}"}</code> - Store name</li>
                      <li><code>{"{{frontendURL}}"}</code> - Frontend URL</li>
                      <li><code>{"{{currencySymbol}}"}</code> - Currency symbol</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Tabs.Content>
          </Tabs>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="secondary"
              onClick={onSuccess}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={createMutation.isPending || updateMutation.isPending}
            >
              {isEdit ? "Update Template" : "Create Template"}
            </Button>
          </div>
        </form>
      </Form>
    </Container>
  );
};

export default NotificationTemplateForm;
