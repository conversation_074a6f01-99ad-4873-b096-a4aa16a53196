import React, { useState } from "react";
import {
  Container,
  <PERSON><PERSON>,
  Text,
  Button,
  Input,
  Textarea,
  Switch,
  Select,
  Tabs,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";

interface NotificationTemplateFormData {
  event_name: string;
  channel: string;
  subject?: string;
  content: string;
  is_active?: boolean;
}

interface NotificationTemplateFormProps {
  initialData?: Partial<NotificationTemplateFormData> & { id?: string };
  onSuccess: () => void;
}

const NotificationTemplateForm = React.forwardRef<
  HTMLFormElement,
  NotificationTemplateFormProps
>(({ initialData, onSuccess }, ref) => {
  const queryClient = useQueryClient();
  const isEdit = !!initialData?.id;
  const [activeTab, setActiveTab] = useState("content");

  // Common event names for reference
  const commonEvents = [
    "order.placed",
    "order.shipped",
    "order.delivered",
    "order.cancelled",
    "product.created",
    "user.created",
    "user.password_reset",
    "payment.succeeded",
    "payment.failed",
  ];

  const form = useForm<NotificationTemplateFormData>({
    mode: "onChange",
    defaultValues: {
      event_name: initialData?.event_name || (isEdit ? "" : commonEvents[0]),
      channel: initialData?.channel || "email",
      subject: initialData?.subject || "",
      content: initialData?.content || "",
      is_active:
        initialData?.is_active !== undefined ? initialData.is_active : true,
    },
  });

  // Event-specific placeholders
  const eventPlaceholders = {
    "order.placed": [
      {
        category: "Order",
        placeholders: [
          { code: "{{order.id}}", description: "Order ID" },
          { code: "{{order.display_id}}", description: "Order display ID" },
          { code: "{{order.status}}", description: "Order status" },
          { code: "{{order.total}}", description: "Order total" },
          { code: "{{order.items}}", description: "Order items" },
          { code: "{{order.created_at}}", description: "Order creation date" },
        ],
      },
      {
        category: "Customer",
        placeholders: [
          {
            code: "{{customer.first_name}}",
            description: "Customer first name",
          },
          { code: "{{customer.last_name}}", description: "Customer last name" },
          { code: "{{customer.email}}", description: "Customer email" },
        ],
      },
    ],
    "order.shipped": [
      {
        category: "Order",
        placeholders: [
          { code: "{{order.id}}", description: "Order ID" },
          { code: "{{order.display_id}}", description: "Order display ID" },
          { code: "{{order.tracking_number}}", description: "Tracking number" },
          { code: "{{order.tracking_url}}", description: "Tracking URL" },
          { code: "{{order.shipping_method}}", description: "Shipping method" },
        ],
      },
      {
        category: "Customer",
        placeholders: [
          {
            code: "{{customer.first_name}}",
            description: "Customer first name",
          },
          { code: "{{customer.last_name}}", description: "Customer last name" },
        ],
      },
    ],
    "product.created": [
      {
        category: "Product",
        placeholders: [
          { code: "{{product.id}}", description: "Product ID" },
          { code: "{{product.title}}", description: "Product title" },
          {
            code: "{{product.description}}",
            description: "Product description",
          },
          { code: "{{product.handle}}", description: "Product handle" },
          {
            code: "{{product.thumbnail}}",
            description: "Product thumbnail URL",
          },
          { code: "{{product.price}}", description: "Product price" },
          {
            code: "{{product.created_at}}",
            description: "Product creation date",
          },
        ],
      },
    ],
    "user.created": [
      {
        category: "User",
        placeholders: [
          { code: "{{user.id}}", description: "User ID" },
          { code: "{{user.email}}", description: "User email" },
          { code: "{{user.first_name}}", description: "User first name" },
          { code: "{{user.last_name}}", description: "User last name" },
        ],
      },
    ],
    "payment.succeeded": [
      {
        category: "Payment",
        placeholders: [
          { code: "{{payment.id}}", description: "Payment ID" },
          { code: "{{payment.amount}}", description: "Payment amount" },
          { code: "{{payment.currency}}", description: "Payment currency" },
          { code: "{{payment.method}}", description: "Payment method" },
        ],
      },
      {
        category: "Order",
        placeholders: [
          { code: "{{order.id}}", description: "Order ID" },
          { code: "{{order.display_id}}", description: "Order display ID" },
        ],
      },
    ],
  };

  // We've removed channel-specific and common placeholders to simplify the UI
  const channelPlaceholders = {};
  const commonPlaceholders = [];

  // Get available placeholders based on selected event and channel
  const getAvailablePlaceholders = () => {
    const eventName = form.watch("event_name");
    const channel = form.watch("channel");

    let placeholders = [];

    // First, add event-specific placeholders if available
    if (eventName && eventPlaceholders[eventName]) {
      placeholders = [...placeholders, ...eventPlaceholders[eventName]];
    }

    // Then, add channel-specific placeholders
    if (channel && channelPlaceholders[channel]) {
      placeholders = [...placeholders, ...channelPlaceholders[channel]];
    }

    // Finally, add common placeholders
    placeholders = [...placeholders, ...commonPlaceholders];

    return placeholders;
  };

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: NotificationTemplateFormData) => {
      // Create a new body without is_default
      const createBody = {
        event_name: data.event_name,
        channel: data.channel,
        subject: data.subject,
        content: data.content,
        is_active: data.is_active,
      };

      return await sdk.client.fetch("/admin/notification-templates", {
        method: "POST",
        body: createBody,
      });
    },
    onSuccess: () => {
      toast.success("Notification template created successfully");
      queryClient.invalidateQueries({ queryKey: ["notification-templates"] });
      onSuccess();
    },
    onError: (error) => {
      toast.error(`Error creating notification template: ${error.message}`);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async (data: NotificationTemplateFormData & { id: string }) => {
      // Create update body without is_default
      const updateBody = {
        subject: data.subject,
        content: data.content,
        is_active: data.is_active,
      };

      return await sdk.client.fetch(
        `/admin/notification-templates/${data.id}`,
        {
          method: "PUT",
          body: updateBody,
        }
      );
    },
    onSuccess: () => {
      toast.success("Notification template updated successfully");
      queryClient.invalidateQueries({ queryKey: ["notification-templates"] });
      onSuccess();
    },
    onError: (error) => {
      toast.error(`Error updating notification template: ${error.message}`);
    },
  });

  const validateForm = () => {
    // Custom validation for event_name
    const eventName = form.getValues("event_name");
    if (!eventName || eventName === "") {
      form.setError("event_name", {
        type: "required",
        message: "Event name is required",
      });
      return false;
    }

    // Custom validation for content
    if (!form.getValues("content")) {
      form.setError("content", {
        type: "required",
        message: "Content is required",
      });
      setActiveTab("content"); // Switch to content tab if there's an error
      return false;
    }

    // For email channel, subject is recommended
    if (form.getValues("channel") === "email" && !form.getValues("subject")) {
      toast.info(
        "Email notifications typically have a subject line. You can still proceed without one."
      );
    }

    return true;
  };

  const onSubmit = (data: NotificationTemplateFormData) => {
    if (!validateForm()) {
      return;
    }

    if (isEdit && initialData?.id) {
      updateMutation.mutate({ ...data, id: initialData.id });
    } else {
      createMutation.mutate(data);
    }
  };

  // Show subject field only for email channel
  const showSubject = form.watch("channel") === "email";

  // Expose submit method to parent component
  React.useImperativeHandle(ref, () => ({
    ...ref.current,
    submitForm: () => {
      form.handleSubmit(onSubmit)();
    },
    isSubmitting: createMutation.isPending || updateMutation.isPending,
  }));

  return (
    <Container className="max-w-5xl mx-auto p-6">
      <Toaster />
      <form
        ref={ref}
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6 relative"
      >
        <div className="grid grid-cols-2 gap-4">
          {!isEdit && (
            <>
              <div className="space-y-2">
                <label htmlFor="event_name" className="text-sm font-medium">
                  Event Name <span className="text-red-500">*</span>
                </label>
                <div className="relative w-full">
                  <div className="relative">
                    <select
                      id="event_name"
                      className="w-full h-10 px-3 py-2 text-left bg-white border border-ui-border-base rounded-md appearance-none"
                      value={form.watch("event_name") || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        form.setValue("event_name", value);
                        // If we have placeholders for this event, show them
                        if (value && eventPlaceholders[value]) {
                          // No need to change tabs now that placeholders are always visible
                        }
                      }}
                    >
                      <option value="" disabled>
                        Select event
                      </option>
                      <optgroup label="Available Events">
                        {commonEvents.map((event) => (
                          <option key={event} value={event}>
                            {event}
                          </option>
                        ))}
                      </optgroup>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg
                        className="h-5 w-5 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>

                <p className="text-sm text-ui-fg-subtle">
                  The event that triggers this notification (e.g., order.placed)
                </p>
                {form.formState.errors.event_name && (
                  <p className="text-sm text-ui-fg-error">
                    {form.formState.errors.event_name.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="channel" className="text-sm font-medium">
                  Channel <span className="text-red-500">*</span>
                </label>
                <div className="relative w-full">
                  <div className="relative">
                    <select
                      id="channel"
                      className="w-full h-10 px-3 py-2 text-left bg-white border border-ui-border-base rounded-md appearance-none"
                      value={form.watch("channel") || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        form.setValue("channel", value);

                        // If switching to feed channel, clear subject
                        if (value === "feed") {
                          form.setValue("subject", "");
                        }

                        // Force re-render to update UI based on channel
                        setActiveTab("content");
                      }}
                    >
                      <option value="" disabled>
                        Select channel
                      </option>
                      <optgroup label="Available Channels">
                        <option value="email">Email</option>
                        <option value="feed">In-App Feed</option>
                      </optgroup>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg
                        className="h-5 w-5 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-ui-fg-subtle">
                  The channel through which the notification will be sent
                </p>
                {form.formState.errors.channel && (
                  <p className="text-sm text-ui-fg-error">
                    {form.formState.errors.channel.message}
                  </p>
                )}
              </div>
            </>
          )}

          <div className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <label htmlFor="is_active" className="text-sm font-medium">
                Active
              </label>
              <p className="text-sm text-ui-fg-subtle">
                Enable or disable this notification template
              </p>
            </div>
            <Switch
              checked={form.watch("is_active")}
              onCheckedChange={(checked) => form.setValue("is_active", checked)}
            />
          </div>

          {/* Default template toggle removed */}
        </div>

        {/* Only show content and preview tabs for email channel */}
        {form.watch("channel") === "email" ||
        form.watch("channel") === "pdf" ? (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <Tabs.List>
              <Tabs.Trigger value="content">Content</Tabs.Trigger>
              <Tabs.Trigger value="preview">Preview</Tabs.Trigger>
            </Tabs.List>

            <Tabs.Content value="content" className="pt-4">
              <div className="flex flex-col md:flex-row gap-6">
                {/* Content Editor */}
                <div className="flex-1 space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium">
                      Subject
                    </label>
                    <Input
                      id="subject"
                      {...form.register("subject")}
                      placeholder="Enter email subject"
                    />
                    <p className="text-sm text-ui-fg-subtle">
                      The subject line for email notifications
                    </p>
                    {form.formState.errors.subject && (
                      <p className="text-sm text-ui-fg-error">
                        {form.formState.errors.subject.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="content" className="text-sm font-medium">
                      Content <span className="text-red-500">*</span>
                    </label>
                    <Textarea
                      id="content"
                      {...form.register("content", {
                        required: "Content is required",
                      })}
                      placeholder="Enter HTML content with placeholders like {{user.name}}"
                      className="min-h-[300px] font-mono"
                    />
                    <p className="text-sm text-ui-fg-subtle">
                      HTML content with placeholders. Use placeholder syntax.
                    </p>
                    {form.formState.errors.content && (
                      <p className="text-sm text-ui-fg-error">
                        {form.formState.errors.content.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Placeholders Panel */}
                <div className="w-full md:w-96 border rounded-lg p-4 bg-ui-bg-subtle h-fit sticky top-4">
                  <div className="flex items-center justify-between mb-4">
                    <Heading level="h3">Available Placeholders</Heading>
                  </div>

                  <Text className="text-ui-fg-subtle mb-4 text-sm">
                    Based on event:{" "}
                    <strong>{form.watch("event_name") || "Any"}</strong> and
                    channel: <strong>{form.watch("channel")}</strong>
                  </Text>

                  <div className="max-h-[400px] overflow-y-auto pr-2">
                    {/* Get all placeholders */}
                    {(() => {
                      const allPlaceholders = getAvailablePlaceholders();
                      const eventName = form.watch("event_name");
                      const channel = form.watch("channel");

                      // Separate event-specific placeholders
                      const eventSpecific =
                        eventName && eventPlaceholders[eventName]
                          ? allPlaceholders.filter((cat) =>
                              eventPlaceholders[eventName].some(
                                (ec) => ec.category === cat.category
                              )
                            )
                          : [];

                      // Separate channel-specific placeholders
                      const channelSpecific =
                        channel && channelPlaceholders[channel]
                          ? allPlaceholders.filter(
                              (cat) =>
                                channelPlaceholders[channel].some(
                                  (cc) => cc.category === cat.category
                                ) &&
                                !eventSpecific.some(
                                  (ec) => ec.category === cat.category
                                )
                            )
                          : [];

                      // Get common placeholders (not event or channel specific)
                      const common = allPlaceholders.filter(
                        (cat) =>
                          !eventSpecific.some(
                            (ec) => ec.category === cat.category
                          ) &&
                          !channelSpecific.some(
                            (cc) => cc.category === cat.category
                          )
                      );

                      return (
                        <>
                          {/* Event-specific placeholders with highlight */}
                          {eventSpecific.length > 0 && (
                            <div className="mb-6 p-2 bg-ui-bg-highlight rounded-md border border-ui-border-interactive">
                              <Heading
                                level="h3"
                                className="mb-2 text-sm font-medium text-ui-fg-interactive"
                              >
                                {eventName} Placeholders
                              </Heading>
                              {eventSpecific.map((category, index) => (
                                <div key={`event-${index}`} className="mb-3">
                                  <Heading
                                    level="h3"
                                    className="mb-1 text-sm font-medium"
                                  >
                                    {category.category}
                                  </Heading>
                                  <ul className="list-disc pl-5 space-y-1">
                                    {category.placeholders.map(
                                      (placeholder, pIndex) => (
                                        <li key={pIndex}>
                                          <code
                                            className="bg-white px-1 py-0.5 rounded cursor-pointer hover:bg-ui-bg-base-hover"
                                            onClick={() => {
                                              // Get the current cursor position
                                              const textarea =
                                                document.getElementById(
                                                  "content"
                                                ) as HTMLTextAreaElement;
                                              if (textarea) {
                                                const start =
                                                  textarea.selectionStart;
                                                const end =
                                                  textarea.selectionEnd;
                                                const currentContent =
                                                  form.getValues("content") ||
                                                  "";

                                                // Insert the placeholder at cursor position
                                                const newContent =
                                                  currentContent.substring(
                                                    0,
                                                    start
                                                  ) +
                                                  placeholder.code +
                                                  currentContent.substring(end);

                                                form.setValue(
                                                  "content",
                                                  newContent
                                                );

                                                // Set focus back to textarea and position cursor after the inserted placeholder
                                                setTimeout(() => {
                                                  textarea.focus();
                                                  textarea.setSelectionRange(
                                                    start +
                                                      placeholder.code.length,
                                                    start +
                                                      placeholder.code.length
                                                  );
                                                }, 0);
                                              } else {
                                                // If textarea not found, just append to the end
                                                const currentContent =
                                                  form.getValues("content") ||
                                                  "";
                                                form.setValue(
                                                  "content",
                                                  currentContent +
                                                    placeholder.code
                                                );
                                                toast.success(
                                                  `Added ${placeholder.code} to content`
                                                );
                                              }
                                            }}
                                          >
                                            {placeholder.code}
                                          </code>
                                          <span className="text-ui-fg-subtle text-xs">
                                            {" "}
                                            - {placeholder.description}
                                          </span>
                                        </li>
                                      )
                                    )}
                                  </ul>
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Channel-specific and common placeholders removed */}
                        </>
                      );
                    })()}
                  </div>

                  <div className="mt-4 pt-4 border-t border-ui-border-base">
                    <Text className="text-sm text-ui-fg-subtle">
                      Click on a placeholder to insert it at cursor position in
                      your content.
                    </Text>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            <Tabs.Content value="preview" className="pt-4">
              <div className="border rounded-lg p-4 min-h-[300px] bg-white">
                {form.watch("channel") === "email" ? (
                  <div>
                    {showSubject && (
                      <div className="mb-2 pb-2 border-b">
                        <strong>Subject:</strong> {form.watch("subject")}
                      </div>
                    )}
                    <div
                      dangerouslySetInnerHTML={{
                        __html: form.watch("content"),
                      }}
                    />
                  </div>
                ) : (
                  <div>{form.watch("content")}</div>
                )}
              </div>
              <Text className="text-ui-fg-subtle mt-2">
                Note: Placeholders will not be replaced in this preview.
              </Text>
            </Tabs.Content>
          </Tabs>
        ) : (
          <div className="pt-4">
            <Text className="text-ui-fg-subtle">
              Feed notifications only require active status configuration.
            </Text>
          </div>
        )}
      </form>
    </Container>
  );
});

export default NotificationTemplateForm;
