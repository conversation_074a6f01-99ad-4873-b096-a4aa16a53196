# Room Management with Options

This document explains how the room management system works with options in the hotel booking system.

## Overview

The hotel booking system uses Medusa's product and variant system to manage rooms:

- **Room Configuration**: Each room configuration is stored as a product in Medusa.
- **Room**: Each individual room is stored as a product variant of the corresponding room configuration product.
- **Room Options**: Options like "View", "Bed Type", etc. are stored as product options and option values.

## Components

### SimpleIndividualRoomForm

This component provides a form for creating or editing a room with options. It includes:
- Basic room details (name, room number, floor, status, etc.)
- Room options management (add, remove options)
- Form validation and submission

#### Usage

```tsx
<SimpleIndividualRoomForm
  hotelId="hotel_123456"
  roomConfigId="room_cfg_123456" // Optional
  initialData={existingRoom} // Optional, for editing
  isEdit={false} // Set to true for editing
  onComplete={(success) => {
    // Handle completion
  }}
/>
```

## API Endpoints

### Create/Update Room with Options

**Endpoint:** `POST /admin/direct-rooms` or `PUT /admin/direct-rooms`

This endpoint creates or updates a room as a product variant with options. It handles:
- Creating product options if they don't exist
- Creating option values if they don't exist
- Creating/updating the product variant with the specified options
- Setting up inventory for the room

**Request Body (Create):**
```json
{
  "name": "Deluxe Room 101",
  "room_number": "101",
  "status": "available",
  "floor": "1",
  "notes": "Corner room with ocean view",
  "is_active": true,
  "room_config_id": "room_cfg_123456",
  "hotel_id": "hotel_123456",
  "options": {
    "View": "Ocean",
    "Bed Type": "King",
    "Smoking": "No"
  },
  "price": 15000,
  "currency_code": "usd"
}
```

**Request Body (Update):**
```json
{
  "id": "variant_123456",
  "name": "Deluxe Room 101 Updated",
  "status": "maintenance",
  "options": {
    "View": "Garden",
    "Bed Type": "Queen"
  }
}
```

## Implementation Details

When a room is created with options:

1. The system first checks if a product exists for the room configuration
2. If not, it creates a product for the room configuration
3. For each option:
   - It checks if the option exists for the product
   - If not, it creates the option
   - It checks if the option value exists
   - If not, it creates the option value
4. It creates a product variant with the specified options
5. It sets up inventory for the room

This approach allows for flexible room configurations and leverages Medusa's existing product and variant system.

## Room Display

Rooms with options are displayed in the UI with their options shown as badges. This makes it easy to see the specific attributes of each room at a glance.

## Benefits of Using Options

1. **Better Search Capabilities**: You can search for rooms with specific options (e.g., all rooms with ocean view)
2. **Flexible Pricing**: You can set different prices based on options (e.g., ocean view costs more)
3. **Improved Filtering**: Guests can filter rooms based on their preferences
4. **Better Inventory Management**: You can track availability of specific room types with specific options
