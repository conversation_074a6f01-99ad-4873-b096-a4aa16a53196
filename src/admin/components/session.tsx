import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import { Container, Heading, Select } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";

const timeRanges = [
  { label: "Yesterday", value: "yesterday" },
  { label: "Last Week", value: "last_week" },
  { label: "Last 14 Days", value: "last_14_days" },
  { label: "Last 30 Days", value: "last_30_days" },
];

export default function UserAcquisitionChart() {
  const [selectedRange, setSelectedRange] = useState("last_week");
  const [sessionData, setSessionData] = useState<
    { channelGroup: string; sessions: number }[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSessionData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `/admin/user-analytics/session?range=${selectedRange}`,
          { credentials: "include" }
        );
        if (!response.ok) throw new Error("Failed to fetch data");
        const data = await response.json();
        setSessionData(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSessionData();
  }, [selectedRange]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <Container className="p-4 rounded-lg shadow-lg border border-gray-200">
          <p className=" font-semibold">{payload[0].payload.channelGroup}</p>
          <p className="text-blue-600 font-medium">{`Sessions: ${payload[0].value}`}</p>
        </Container>
      );
    }
    return null;
  };

  return (
    <div>
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <div>
        <Heading level="h2">Session Sources</Heading>
          <p className="text-sm text-gray-500">{timeRanges.find(range => range.value === selectedRange)?.label}</p>
        </div>

        {/* Right-Aligned Select Dropdown */}
        <Select
          value={selectedRange}
          onValueChange={(value) => setSelectedRange(value)}
        >
          <Select.Trigger className="w-full md:w-40 px-3 py-2 rounded-md shadow-sm flex justify-between items-center cursor-pointer hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:outline-none">
            <span>
              {timeRanges.find((range) => range.value === selectedRange)
                ?.label || "Select Range"}
            </span>
          </Select.Trigger>
          <Select.Content className="bg-white border border-gray-300 rounded-md shadow-lg right-0">
            {timeRanges.map((range) => (
              <Select.Item
                key={range.value}
                value={range.value}
                className="px-3 py-2 hover:bg-blue-100 cursor-pointer"
              >
                {range.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>

      {/* Loader */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <CircularProgress size={24} />
        </div>
      ) : error ? (
        <p className="text-red-500 text-center">{error}</p>
      ) : (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={sessionData}
            layout="vertical"
            margin={{ top: 10, right: 30, left: 10, bottom: 5 }}
          >
            <XAxis type="number" tick={{ fontSize: 12 }} />
            <YAxis
              dataKey="channelGroup"
              type="category"
              tick={{ fontSize: 12 }}
              width={120}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="sessions" fill="#4285F4" barSize={30} />
          </BarChart>
        </ResponsiveContainer>
      )}
    </div>
  );
}
