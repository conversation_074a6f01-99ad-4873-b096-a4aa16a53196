import React from "react";
import { Input, Textarea, Label, Text } from "@camped-ai/ui";
import withA<PERSON>enerate from "./with-ai-generate";
import { ContentType } from "../hooks/useAIGenerate";

// Create enhanced versions of Input and Textarea with AI generation capabilities
export const InputWithAI = withAIGenerate(Input);
export const TextareaWithAI = withAIGenerate(Textarea);

// Props for the FormField components
interface FormFieldProps {
  id?: string;
  label?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  contentType?: ContentType;
  context?: Record<string, any>;
  required?: boolean;
  helpText?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
}

// Additional props for TextareaField
interface TextareaFieldProps extends FormFieldProps {
  rows?: number;
}

/**
 * A form field with an Input and AI generation capabilities
 * 
 * @example
 * <InputField
 *   label="Name"
 *   value={name}
 *   onChange={setName}
 *   contentType="name"
 *   context={{ type: "product" }}
 *   placeholder="Enter name"
 *   required
 *   helpText="The name of the product as it will appear to users"
 * />
 */
export const InputField: React.FC<FormFieldProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder,
  contentType = "general",
  context = {},
  required = false,
  helpText,
  error,
  disabled = false,
  className = "",
}) => {
  return (
    <div className={className}>
      {label && (
        <Label htmlFor={id} className="block mb-1 font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <InputWithAI
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        contentType={contentType}
        context={context}
        disabled={disabled}
      />
      
      {helpText && !error && (
        <Text className="text-xs text-gray-500 mt-1">{helpText}</Text>
      )}
      
      {error && (
        <Text className="text-xs text-red-500 mt-1">{error}</Text>
      )}
    </div>
  );
};

/**
 * A form field with a Textarea and AI generation capabilities
 * 
 * @example
 * <TextareaField
 *   label="Description"
 *   value={description}
 *   onChange={setDescription}
 *   contentType="description"
 *   context={{ name: "Product Name", type: "product" }}
 *   placeholder="Enter description"
 *   rows={5}
 * />
 */
export const TextareaField: React.FC<TextareaFieldProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder,
  contentType = "description",
  context = {},
  required = false,
  helpText,
  error,
  disabled = false,
  className = "",
  rows = 4,
}) => {
  return (
    <div className={className}>
      {label && (
        <Label htmlFor={id} className="block mb-1 font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      
      <TextareaWithAI
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        contentType={contentType}
        context={context}
        disabled={disabled}
        rows={rows}
      />
      
      {helpText && !error && (
        <Text className="text-xs text-gray-500 mt-1">{helpText}</Text>
      )}
      
      {error && (
        <Text className="text-xs text-red-500 mt-1">{error}</Text>
      )}
    </div>
  );
};

export default {
  InputWithAI,
  TextareaWithAI,
  InputField,
  TextareaField,
};
