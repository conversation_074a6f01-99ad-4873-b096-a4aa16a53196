import { Container, Heading, Select, Text } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
} from "recharts";

interface MetricData {
  activeUsers: number;
  eventCount: string;
  newUsers: number;
  totalUsers: number;
}

interface ActiveUserData {
  date: string;
  value: number | string;
}

const MetricCard = ({ title, value }: { title: string; value: string }) => (
  <div className="p-4 rounded-lg flex flex-col items-center justify-center">
    <p className=" text-base text-blue-500">{title}</p>
    <p className="text-xl font-semibold">{value}</p>
  </div>
);

const timeRanges = [
    { label: "Yesterday", value: "yesterday" },
    { label: "Last Week", value: "last_week" },
    { label: "Last 14 Days", value: "last_14_days" },
    { label: "Last 30 Days", value: "last_30_days" },
  ];

const AnalyticsCard = () => {
    const [selectedRange, setSelectedRange] = useState("last_week");
  const [metrics, setMetrics] = useState<MetricData | null>(null);
  const [activeUsers, setActiveUsers] = useState<ActiveUserData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);

    const fetchData = async () => {
      try {
        const [metricsRes, activeUsersRes] = await Promise.all([
          fetch(`/admin/user-analytics/metrics?range=${selectedRange}`, { credentials: "include" }),
          fetch(`/admin/user-analytics/activeuser?range=${selectedRange}`, { credentials: "include" }),
        ]);

        const metricsData = await metricsRes.json();
        const activeUsersData = await activeUsersRes.json();

        setMetrics(metricsData[0]);
        setActiveUsers(activeUsersData);
      } catch (err) {
        console.error("Error fetching data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedRange]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Container className="p-5 rounded-lg shadow-lg border border-gray-200">
          <Text className=" font-bold text-base">{label}</Text>
          <Text className="text-blue-500 font-medium">{`Value: ${payload[0].value}`}</Text>
        </Container>
      );
    }
    return null;
  };
  return (
    <>
       <div className="flex justify-between items-center mb-4">
        <div>
        <Heading level="h2">User Activity</Heading>
        <p className="text-sm text-gray-500 mb-2">
  {timeRanges.find((range) => range.value === selectedRange)?.label} engagement
</p>
        </div>
        <Select value={selectedRange} onValueChange={setSelectedRange}>
          <Select.Trigger className="w-full md:w-40 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none">
            <Select.Value>
              {timeRanges.find((range) => range.value === selectedRange)?.label || "Select Range"}
            </Select.Value>
          </Select.Trigger>
          <Select.Content>
            {timeRanges.map((range) => (
              <Select.Item key={range.value} value={range.value}>
                {range.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>


      {/* Metric Cards Section */}
      <div className="grid grid-cols-4 gap-4 mb-4">
        <MetricCard title="Active Users" value={metrics?.activeUsers || "0"} />
        <MetricCard title="Event Count" value={metrics?.eventCount || "0"} />
        <MetricCard title="New Users" value={metrics?.newUsers || "0"} />
        <MetricCard title="Total Users" value={metrics?.totalUsers || "0"} />
      </div>

      {/* Loading State */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
        <CircularProgress size={20} />
      </div>
      ) : (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={activeUsers}
            margin={{ top: 5, right: 20, left: 0, bottom: 5 }}
          >
            <XAxis dataKey="date" tick={{ fontSize: 12 }} />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip content={CustomTooltip} />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#3b82f6"
              strokeWidth={3}
            />
          </LineChart>
        </ResponsiveContainer>
      )}
    </>
  );
};

export default AnalyticsCard;
