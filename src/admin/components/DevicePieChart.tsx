import { Heading } from "@camped-ai/ui";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend, ResponsiveContainer } from "recharts";

const COLORS = ["#2563eb", "#3b82f6", "#60a5fa", "#93c5fd", "#bfdbfe"];

const DeviceDonutChart = () => {
    const [deviceData, setdeviceData] = useState([]);
    const [loading, setLoading] = useState(true);
  
  
    useEffect(() => {
      const fetchCityData = async () => {
        try {
          const response = await fetch("/admin/user-analytics/overview/device-category",{
            credentials: "include"
          });
          const data = await response.json();
          const totalUsers = data.reduce((sum, device) => sum + device.value, 0);
        
          // Convert values into percentages
          const percentageData = data.map((device) => ({
            ...device,
            percentage: ((device.value / totalUsers) * 100).toFixed(1), // 1 decimal place
          }));
  
          setdeviceData(percentageData);
        } catch (error) {
          console.error("Failed to fetch browser data:", error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchCityData();
    }, []);
  return (
    <>
      <Heading level="h2">Active Users by Device</Heading>
      <span className="text-sm">Last 30 days</span>
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={deviceData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={80}
            innerRadius={50} // Donut effect
            stroke="white"
            strokeWidth={2}
            paddingAngle={5} // Slight gap between segments
          >
            {deviceData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index]} />
            ))}
          </Pie>

          <Tooltip contentStyle={{ backgroundColor: "white", borderRadius: "8px" }} />
          <Legend iconSize={12} />
        </PieChart>
      </ResponsiveContainer>
    </>
  );
};

export default DeviceDonutChart;
