import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Textarea,
  Tooltip,
} from "@camped-ai/ui";
import CustomSelect from "./custom-select";
import { useForm } from "react-hook-form";
import HotelMediaSection from "./hotel/hotel-media-section";
import { MediaField } from "./hotel/media-item";
import { useState, useEffect } from "react";
import { TextareaField, InputField as AIInput } from "./ai-enhanced-inputs";
import {
  Info,
  Globe,
  MapPin,
  Mail,
  Phone,
  Clock,
  DollarSign,
  Tag,
  Building,
  Image,
} from "lucide-react";
import { RoomTypeData } from "../types";

export type HotelFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured?: boolean;
  website: string | null;
  email: string | null;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  roomTypes?: string[];
  id?: string;
};

type HotelFormProps = {
  formData: HotelFormData;
  roomTypes: RoomTypeData[];
  onSubmit: (data: HotelFormData) => Promise<boolean>;
  closeModal: () => void;
};

const HotelFormModern = ({
  formData,
  roomTypes,
  onSubmit,
  closeModal,
}: HotelFormProps) => {
  const [destinations, setDestinations] = useState<
    Array<{ id: string; name: string; location: string | null }>
  >([]);
  const [activeTab, setActiveTab] = useState("basics");
  const [tagsInput, setTagsInput] = useState(() => {
    if (!formData.tags) return "";
    if (Array.isArray(formData.tags)) return formData.tags.join(", ");
    if (typeof formData.tags === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.tags);
        return Array.isArray(parsed) ? parsed.join(", ") : "";
      } catch (e) {
        // If not a valid JSON, return as is (might be comma-separated already)
        return formData.tags;
      }
    }
    return "";
  });

  const [amenitiesInput, setAmenitiesInput] = useState(() => {
    if (!formData.amenities) return "";
    if (Array.isArray(formData.amenities)) return formData.amenities.join(", ");
    if (typeof formData.amenities === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.amenities);
        return Array.isArray(parsed) ? parsed.join(", ") : "";
      } catch (e) {
        // If not a valid JSON, return as is (might be comma-separated already)
        return formData.amenities;
      }
    }
    return "";
  });

  const [rulesInput, setRulesInput] = useState(() => {
    if (!formData.rules) return "";
    if (Array.isArray(formData.rules)) return formData.rules.join(", ");
    if (typeof formData.rules === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.rules);
        return Array.isArray(parsed) ? parsed.join(", ") : "";
      } catch (e) {
        // If not a valid JSON, return as is (might be comma-separated already)
        return formData.rules;
      }
    }
    return "";
  });

  const [safetyMeasuresInput, setSafetyMeasuresInput] = useState(() => {
    if (!formData.safety_measures) return "";
    if (Array.isArray(formData.safety_measures))
      return formData.safety_measures.join(", ");
    if (typeof formData.safety_measures === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.safety_measures);
        return Array.isArray(parsed) ? parsed.join(", ") : "";
      } catch (e) {
        // If not a valid JSON, return as is (might be comma-separated already)
        return formData.safety_measures;
      }
    }
    return "";
  });

  const form = useForm<HotelFormData>({
    defaultValues: formData,
  });

  const isEdit = !!formData.id;

  // Reset form when formData changes (e.g., when creating a new hotel after submitting one)
  useEffect(() => {
    // Reset the form with the new formData
    form.reset(formData);
  }, [formData, form]);

  // Fetch destinations
  useEffect(() => {
    fetch("/admin/hotel-management/destinations", { credentials: "include" })
      .then((res) => res.json())
      .then((data) => setDestinations(data.destinations))
      .catch((error) => console.error("Error fetching destinations:", error));
  }, []);

  // Handle form submission
  const handleSubmit = async () => {
    // Get the form values including media
    const formValues = form.getValues();

    // Ensure tags is properly formatted
    const tagsArray = tagsInput
      ? tagsInput
          .split(",")
          .map((tag) => tag.trim())
          .filter((tag) => tag)
      : [];
    const finalTags = tagsArray.length > 0 ? tagsArray : undefined;

    // Ensure amenities is properly formatted
    const amenitiesArray = amenitiesInput
      ? amenitiesInput
          .split(",")
          .map((amenity) => amenity.trim())
          .filter((amenity) => amenity)
      : [];
    const finalAmenities =
      amenitiesArray.length > 0 ? amenitiesArray : undefined;

    // Ensure rules is properly formatted
    const rulesArray = rulesInput
      ? rulesInput
          .split(",")
          .map((rule) => rule.trim())
          .filter((rule) => rule)
      : [];
    const finalRules = rulesArray.length > 0 ? rulesArray : undefined;

    // Ensure safety measures is properly formatted
    const safetyMeasuresArray = safetyMeasuresInput
      ? safetyMeasuresInput
          .split(",")
          .map((measure) => measure.trim())
          .filter((measure) => measure)
      : [];
    const finalSafetyMeasures =
      safetyMeasuresArray.length > 0 ? safetyMeasuresArray : undefined;

    // Ensure check-in and check-out times are properly formatted
    const checkInTime = formValues.check_in_time || "14:00";
    const checkOutTime = formValues.check_out_time || "11:00";

    // Prepare the data for submission
    const submissionData = {
      ...formValues,
      tags: finalTags,
      amenities: finalAmenities,
      rules: finalRules,
      safety_measures: finalSafetyMeasures,
      check_in_time: checkInTime,
      check_out_time: checkOutTime,
    };

    const success = await onSubmit(submissionData);
    if (success) {
      closeModal();
    }
  };

  return (
    <FocusModal.Content className="max-w-4xl mx-auto">
      <FocusModal.Header className="border-b border-gray-200 bg-white sticky top-0 z-10">
        <div className="flex justify-between items-center py-4 w-full">
          <Heading level="h2" className="text-xl font-semibold">
            {isEdit ? "Edit Hotel" : "Add New Hotel"}
          </Heading>
          <div className="flex gap-2">
            <Button variant="secondary" onClick={closeModal}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={
                !form.watch("name") ||
                !form.watch("handle") ||
                (!isEdit && !form.watch("destination_id"))
              }
            >
              {isEdit ? "Update Hotel" : "Create Hotel"}
            </Button>
          </div>
        </div>
      </FocusModal.Header>

      <FocusModal.Body className="flex overflow-y-auto bg-gray-50">
        <div className="flex w-full">
          {/* Left sidebar navigation */}
          <div className="w-64 bg-white border-r border-gray-200 p-4 shrink-0">
            <nav className="space-y-1">
              <button
                onClick={() => setActiveTab("basics")}
                className={`w-full text-left px-3 py-2 rounded-md flex items-center gap-2 ${
                  activeTab === "basics"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <Building size={18} />
                <span>Basic Information</span>
              </button>
              <button
                onClick={() => setActiveTab("contact")}
                className={`w-full text-left px-3 py-2 rounded-md flex items-center gap-2 ${
                  activeTab === "contact"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <Mail size={18} />
                <span>Contact Details</span>
              </button>
              <button
                onClick={() => setActiveTab("features")}
                className={`w-full text-left px-3 py-2 rounded-md flex items-center gap-2 ${
                  activeTab === "features"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <Tag size={18} />
                <span>Features & Amenities</span>
              </button>
              <button
                onClick={() => setActiveTab("media")}
                className={`w-full text-left px-3 py-2 rounded-md flex items-center gap-2 ${
                  activeTab === "media"
                    ? "bg-blue-50 text-blue-600"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
              >
                <Image size={18} />
                <span>Media</span>
              </button>
            </nav>
          </div>

          {/* Main content area */}
          <div className="flex-1 p-6">
            {/* Basic Information Tab */}
            {activeTab === "basics" && (
              <div className="space-y-6 max-w-2xl">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Hotel Information
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <AIInput
                        id="name"
                        label="Hotel Name"
                        required
                        value={form.watch("name") || ""}
                        onChange={(value) => {
                          form.setValue("name", value);
                          form.setValue(
                            "handle",
                            value.toLowerCase().replace(/\s+/g, "-")
                          );
                        }}
                        placeholder="e.g. Grand Hotel"
                        contentType="name"
                        context={{
                          type: "hotel",
                          destination: destinations.find(
                            (d) => d.id === form.watch("destination_id")
                          )?.name,
                        }}
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        The name of the hotel as it will appear to users
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="handle"
                        className="block mb-1 font-medium"
                      >
                        Handle <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="handle"
                        {...form.register("handle", { required: true })}
                        placeholder="e.g. grand-hotel"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        Used for the URL and internal references
                      </Text>
                    </div>

                    <div>
                      <TextareaField
                        id="description"
                        label="Description"
                        value={form.watch("description") || ""}
                        onChange={(value) =>
                          form.setValue("description", value)
                        }
                        placeholder="Enter a detailed description of the hotel"
                        rows={5}
                        contentType="description"
                        context={{
                          name: form.watch("name"),
                          type: "hotel",
                          destination: destinations.find(
                            (d) => d.id === form.watch("destination_id")
                          )?.name,
                        }}
                      />
                    </div>

                    {!isEdit && (
                      <div>
                        <Label
                          htmlFor="destination"
                          className="block mb-1 font-medium"
                        >
                          Destination <span className="text-red-500">*</span>
                        </Label>
                        <CustomSelect
                          id="destination"
                          options={destinations.map((destination) => ({
                            value: destination.id,
                            label: `${destination.name} ${
                              destination.location
                                ? `(${destination.location})`
                                : ""
                            }`,
                          }))}
                          value={form.watch("destination_id") || ""}
                          onChange={(value) => {
                            console.log("Destination changed to:", value);
                            form.setValue("destination_id", value);
                          }}
                          placeholder="Select a destination"
                          className="w-full"
                        />
                      </div>
                    )}

                    <div>
                      <Label
                        htmlFor="location"
                        className="block mb-1 font-medium"
                      >
                        Location
                      </Label>
                      <div className="relative">
                        <MapPin
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="location"
                          {...form.register("location")}
                          placeholder="e.g. 123 Main Street"
                          className="w-full pl-9"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Visibility Settings
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          id="is_active"
                          checked={form.watch("is_active")}
                          onCheckedChange={(checked) =>
                            form.setValue("is_active", checked)
                          }
                        />
                        <Label htmlFor="is_active" className="cursor-pointer">
                          Active
                        </Label>
                      </div>
                      <Text className="text-sm text-gray-500">
                        When active, the hotel will be visible to users
                      </Text>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          id="is_featured"
                          checked={form.watch("is_featured") || false}
                          onCheckedChange={(checked) =>
                            form.setValue("is_featured", checked)
                          }
                        />
                        <Label htmlFor="is_featured" className="cursor-pointer">
                          Featured
                        </Label>
                      </div>
                      <Text className="text-sm text-gray-500">
                        Featured hotels will be highlighted and shown
                        prominently to users
                      </Text>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          id="is_pets_allowed"
                          checked={form.watch("is_pets_allowed") || false}
                          onCheckedChange={(checked) =>
                            form.setValue("is_pets_allowed", checked)
                          }
                        />
                        <Label
                          htmlFor="is_pets_allowed"
                          className="cursor-pointer"
                        >
                          Pets Allowed
                        </Label>
                      </div>
                      <Text className="text-sm text-gray-500">
                        When enabled, this hotel allows pets
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Details Tab */}
            {activeTab === "contact" && (
              <div className="space-y-6 max-w-2xl">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Contact Information
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="website"
                        className="block mb-1 font-medium"
                      >
                        Website
                      </Label>
                      <div className="relative">
                        <Globe
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="website"
                          {...form.register("website")}
                          placeholder="e.g. https://www.grandhotel.com"
                          className="w-full pl-9"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email" className="block mb-1 font-medium">
                        Email Address
                      </Label>
                      <div className="relative">
                        <Mail
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="email"
                          type="email"
                          {...form.register("email")}
                          placeholder="e.g. <EMAIL>"
                          className="w-full pl-9"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="phone_number"
                        className="block mb-1 font-medium"
                      >
                        Phone Number
                      </Label>
                      <div className="relative">
                        <Phone
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="phone_number"
                          {...form.register("phone_number")}
                          placeholder="e.g. +****************"
                          className="w-full pl-9"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="address"
                        className="block mb-1 font-medium"
                      >
                        Full Address
                      </Label>
                      <Textarea
                        id="address"
                        {...form.register("address")}
                        placeholder="Enter the complete address"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Additional Information
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="check_in_time"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Check-in Time
                        <Tooltip content="The standard check-in time for the hotel">
                          <Info size={14} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Clock
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="check_in_time"
                          type="time"
                          {...form.register("check_in_time")}
                          placeholder="14:00"
                          className="w-full pl-9"
                          defaultValue={form.watch("check_in_time") || "14:00"}
                        />
                      </div>
                      <Text className="text-xs text-gray-500 mt-1">
                        Standard check-in time in 24-hour format (e.g., 14:00
                        for 2:00 PM)
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="check_out_time"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Check-out Time
                        <Tooltip content="The standard check-out time for the hotel">
                          <Info size={14} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Clock
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="check_out_time"
                          type="time"
                          {...form.register("check_out_time")}
                          placeholder="11:00"
                          className="w-full pl-9"
                          defaultValue={form.watch("check_out_time") || "11:00"}
                        />
                      </div>
                      <Text className="text-xs text-gray-500 mt-1">
                        Standard check-out time in 24-hour format (e.g., 11:00
                        for 11:00 AM)
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="currency"
                        className="block mb-1 font-medium"
                      >
                        Currency
                      </Label>
                      <div className="relative">
                        <DollarSign
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="currency"
                          {...form.register("currency")}
                          placeholder="e.g. USD"
                          className="w-full pl-9"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Features & Amenities Tab */}
            {activeTab === "features" && (
              <div className="space-y-6 max-w-2xl">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">Features & Tags</h3>
                  <div className="space-y-4">
                    <div>
                      <AIInput
                        id="tags"
                        label="Tags"
                        value={tagsInput}
                        onChange={(value) => setTagsInput(value)}
                        placeholder="e.g. family-skiing or beginner-skiing"
                        contentType="tags"
                        context={{
                          name: form.watch("name"),
                          description: form.watch("description"),
                          type: "hotel",
                        }}
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        Separate tags with commas
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="amenities"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Amenities
                        <Tooltip content="List of amenities available at the hotel">
                          <Info size={14} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <Textarea
                        id="amenities"
                        value={amenitiesInput}
                        onChange={(e) => setAmenitiesInput(e.target.value)}
                        placeholder="e.g. WiFi, Pool, Gym, Restaurant (comma separated)"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        Separate amenities with commas
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="rules"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Rules
                        <Tooltip content="List of rules and policies for the hotel">
                          <Info size={14} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <Textarea
                        id="rules"
                        value={rulesInput}
                        onChange={(e) => setRulesInput(e.target.value)}
                        placeholder="e.g. No smoking, Check-in after 3 PM, No pets allowed (comma separated)"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        Separate rules with commas
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="safety_measures"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Safety Measures
                        <Tooltip content="List of safety measures implemented at the hotel">
                          <Info size={14} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <Textarea
                        id="safety_measures"
                        value={safetyMeasuresInput}
                        onChange={(e) => setSafetyMeasuresInput(e.target.value)}
                        placeholder="e.g. 24/7 Security, CCTV, Fire Extinguishers, Smoke Detectors (comma separated)"
                        className="w-full"
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        Separate safety measures with commas
                      </Text>
                    </div>

                    <div>
                      <TextareaField
                        id="notes"
                        label="Additional Notes"
                        value={form.watch("notes") || ""}
                        onChange={(value) => form.setValue("notes", value)}
                        placeholder="Any additional information about the hotel"
                        rows={5}
                        contentType="description"
                        context={{
                          name: form.watch("name"),
                          type: "hotel notes",
                          description: form.watch("description"),
                        }}
                        helpText="Add any special instructions, policies, or other information about the hotel"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Media Tab */}
            {activeTab === "media" && (
              <div className="space-y-6 max-w-2xl">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">Hotel Images</h3>
                  <Text className="text-sm text-gray-500 mb-4">
                    Upload images that showcase this hotel. The first image or
                    the one marked as thumbnail will be used as the main image.
                  </Text>

                  <HotelMediaSection form={form} />
                </div>
              </div>
            )}
          </div>
        </div>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default HotelFormModern;
