import { Container, Heading } from "@camped-ai/ui";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, <PERSON>ltip, Legend, Responsive<PERSON>ontainer, CartesianGrid } from "recharts";



const BrowserBarChart = () => {
    const [browserData, setBrowserData] = useState([]);
    const [loading, setLoading] = useState(true);
  
    useEffect(() => {
      const fetchBrowserData = async () => {
        try {
          const response = await fetch("/admin/user-analytics/overview/browser",{
            credentials: "include"
          });
          const data = await response.json();
          setBrowserData(data); 
        } catch (error) {
          console.error("Failed to fetch browser data:", error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchBrowserData();
    }, []);
    const CustomTooltip = ({ active, payload }: any) => {
      if (active && payload && payload.length) {
        return (
          <Container className="p-4 rounded-lg shadow-lg border border-gray-200">
            <p className="font-semibold">{payload[0].payload.browser}</p>
            <p className="text-blue-600 ">{`Users: ${payload[0].value}`}</p>
          </Container>
        );
      }
      return null;
    };
  return (
     <>
       <Heading level="h2" className="chart-title">Active Users by Browser</Heading>
       <span className="text-sm">Last 30 days</span>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={browserData} >

          <CartesianGrid strokeDasharray="3 3" opacity={0.5} />

          <XAxis dataKey="browser" type="category"  tick={{ fontSize: 12 }} />
          <YAxis type="number" tick={{ fontSize: 14, fontWeight: 600 }} />


          <Tooltip content={CustomTooltip}/>
          <Legend verticalAlign="top" height={30} />

          <defs>
            <linearGradient id="colorUv" x1="0" y1="0" x2="1" y2="0">
              <stop offset="5%" stopColor="#2B7BE4" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#6D9EF7" stopOpacity={1} />
            </linearGradient>
          </defs>

          <Bar dataKey="users" fill="url(#colorUv)" barSize={25} radius={[8, 8, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </>
  );
};

export default BrowserBarChart;
