import React from "react";
import { But<PERSON>, Tooltip } from "@camped-ai/ui";
import { Sparkles } from "lucide-react";
import useAIGenerate, { ContentType } from "../hooks/useAIGenerate";

interface AIEnhanceProps {
  contentType?: ContentType;
  context?: Record<string, any>;
  onGenerate: (content: string) => void;
  buttonText?: string;
  buttonVariant?: "primary" | "secondary" | "transparent";
  buttonSize?: "small" | "base" | "large";
  tooltipText?: string;
  className?: string;
  disabled?: boolean;
}

/**
 * A component that adds AI generation capabilities to any input field
 *
 * @example
 * <div className="flex items-center gap-2">
 *   <Input
 *     value={name}
 *     onChange={(e) => setName(e.target.value)}
 *     placeholder="Enter name"
 *   />
 *   <AIEnhance
 *     contentType="name"
 *     context={{ type: "product" }}
 *     onGenerate={(content) => setName(content)}
 *   />
 * </div>
 */
const AIEnhance: React.FC<AIEnhanceProps> = ({
  contentType = "general",
  context = {},
  onGenerate,
  buttonText,
  buttonVariant = "transparent",
  buttonSize = "small",
  tooltipText = "Generate with AI",
  className = "",
  disabled = false,
}) => {
  const { generateContent, isGenerating, error } = useAIGenerate({
    contentType,
    context,
    onSuccess: onGenerate,
  });

  return (
    <Tooltip
      content={error || (isGenerating ? "Generating content..." : tooltipText)}
    >
      <Button
        variant={buttonVariant}
        size={buttonSize}
        onClick={() => generateContent()}
        disabled={disabled || isGenerating}
        className={`flex items-center ${className}`}
      >
        <Sparkles
          size={16}
          className={isGenerating ? "animate-pulse text-blue-500" : ""}
        />
        {buttonText && <span className="ml-1">{buttonText}</span>}
      </Button>
    </Tooltip>
  );
};

export default AIEnhance;
