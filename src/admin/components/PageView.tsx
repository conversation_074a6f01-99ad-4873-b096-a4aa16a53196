import { Container, Heading, Select,Text } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";

const timeRanges = [
  { label: "Yesterday", value: "yesterday" },
  { label: "Last Week", value: "last_week" },
  { label: "Last 14 Days", value: "last_14_days" },
  { label: "Last 30 Days", value: "last_30_days" },
];

const ProgressBar = ({ value, max }) => {
  const percentage = (value / max) * 100;
  return (
    <div className="w-full h-1.5 bg-gray-300 rounded-full mt-1">
      <div
        className="h-1.5 bg-[#3b82f6] rounded-full transition-all"
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};

const PageView = () => {
  const [selectedRange, setSelectedRange] = useState("last_week");
  const [data, setData] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      setIsFetching(true);
      setError(null);
      try {
        const response = await fetch(
          `/admin/user-analytics/PageView?range=${selectedRange}`,
          {
            credentials: "include",
          }
        );
        if (!response.ok) throw new Error("Failed to fetch data");
        const jsonData = await response.json();
        setData(jsonData);
      } catch (err) {
        setError(err.message);
      }
      finally{
        setIsFetching(false);
      }
    }
    fetchData();
  }, [selectedRange]);

 

  const sortedPages = data?.PageViews?.sort((a, b) => b.views - a.views).slice(0,8);
  const maxViews = sortedPages?.[0]?.views || 1;

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <div>
        <Heading level="h2">Views by</Heading>
          <p className="text-gray-500 text-sm mb-4">Page title and screen</p>
        </div>
        <Select
          value={selectedRange}
          onValueChange={(value) => setSelectedRange(value)}
        >
          <Select.Trigger className="w-full md:w-40 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none">
            <Select.Value placeholder="Select Range">
              {timeRanges.find((range) => range.value === selectedRange)
                ?.label || "Select Range"}
            </Select.Value>
          </Select.Trigger>
          <Select.Content>
            {timeRanges.map((range) => (
              <Select.Item key={range.value} value={range.value}>
                {range.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>

      {isFetching ? (
        <div className="flex justify-center items-center h-20">
          <CircularProgress size={20} />
        </div>
      ) : (
        sortedPages?.map((page, index) => (
          <div key={index} className="mb-3">
            <div className="flex justify-between items-center text-sm">
              <Text>{page.page}</Text>
              <div className="flex items-center gap-2">
                <Text className="font-medium">{page.views}</Text>
              </div>
          </div>
          <ProgressBar value={page.views} max={maxViews} />
          </div>
        ))
      )}
    </>
  );
};

export default PageView;
