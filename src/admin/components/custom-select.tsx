import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Check } from "lucide-react";

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
}

const CustomSelect: React.FC<CustomSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  className = "",
  id,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [internalValue, setInternalValue] = useState(value);
  const selectRef = useRef<HTMLDivElement>(null);
  // Find the selected option based on the current value
  const selectedOption = options.find((option) => option.value === value) ||
                       options.find((option) => option.value === internalValue);

  // Update internal value when external value changes
  useEffect(() => {
    if (value !== internalValue) {
      setInternalValue(value);
      console.log(`CustomSelect: External value updated to ${value}`);
    }
  }, [value, internalValue]);

  // Close dropdown when clicking outside or on escape key
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscapeKey);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isOpen) {
      if (event.key === 'Enter' || event.key === ' ' || event.key === 'ArrowDown') {
        event.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      event.preventDefault();
      const currentIndex = options.findIndex(option => option.value === value);
      let nextIndex;

      if (event.key === 'ArrowDown') {
        nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
      } else {
        nextIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
      }

      onChange(options[nextIndex].value);
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setIsOpen(false);
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (optionValue: string) => {
    // Update internal state
    setInternalValue(optionValue);

    // Call the onChange handler with the new value
    onChange(optionValue);

    // Close the dropdown
    setIsOpen(false);

    // Log the selection for debugging
    console.log(`CustomSelect: Selected option: ${optionValue}`);
  };

  return (
    <div
      ref={selectRef}
      className={`relative ${className}`}
      id={id}
    >
      <button
        type="button"
        className={`w-full rounded-md border border-ui-border-base px-3 py-1.5 text-left flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-ui-border-interactive transition-colors duration-200 ${disabled ? 'opacity-60 cursor-not-allowed' : 'hover:border-ui-border-hover cursor-pointer'}`}
        onClick={disabled ? undefined : toggleDropdown}
        onKeyDown={disabled ? undefined : handleKeyDown}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        tabIndex={disabled ? -1 : 0}
        disabled={disabled}
      >
        <span className={`text-sm ${selectedOption ? "text-ui-fg-base" : "text-ui-fg-subtle"}`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <ChevronDown
          size={14}
          className={`transition-transform duration-200 text-ui-fg-subtle ${isOpen ? "transform rotate-180" : ""}`}
        />
      </button>

      {isOpen && (
        <div
          className="absolute z-50 mt-1 w-full rounded-md border border-ui-border-base bg-ui-bg-base shadow-md max-h-48 overflow-auto"
          role="listbox"
          style={{ zIndex: 9999 }}
          onKeyDown={handleKeyDown}
        >
          {options.map((option) => (
            <div
              key={option.value}
              className={`px-3 py-1.5 text-sm cursor-pointer hover:bg-ui-bg-base-hover flex items-center justify-between ${
                option.value === value ? "bg-ui-bg-base-pressed text-ui-fg-interactive" : ""
              }`}
              onClick={() => handleOptionClick(option.value)}
              role="option"
              aria-selected={option.value === value}
            >
              <span>{option.label}</span>
              {option.value === value && <Check size={14} className="text-ui-fg-interactive" />}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomSelect;
