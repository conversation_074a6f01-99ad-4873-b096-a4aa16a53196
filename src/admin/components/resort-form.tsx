import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Textarea,
} from "@camped-ai/ui";

export type ResortFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_internal: boolean;
  website: string | null;
  country: string;
  currency: string;
  key_card_cost: number;
};

type ResortFormProps = {
  formData: ResortFormData;
  setFormData: (data: ResortFormData) => void;
  onSubmit: () => Promise<boolean>; // Changed to return Promise<boolean>
  isEdit?: boolean;
  closeModal: () => void;
};

const ResortForm = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
}: ResortFormProps) => {
  const handleSubmit = async () => {
    const success = await onSubmit();
    if (success) {
      closeModal();
    }
  };

  return (
    <FocusModal.Content>
      <FocusModal.Header>
        <div className="flex justify-between items-center">
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!formData.name || !formData.handle}
          >
            {isEdit ? "Update" : "Save"}
          </Button>
        </div>
      </FocusModal.Header>
      <FocusModal.Body className="flex flex-col items-center py-16 gap-4 overflow-y-auto">
        <Heading level="h1">{isEdit ? "Edit Resort" : "Create Resort"}</Heading>
        <div className="flex w-full max-w-lg flex-col gap-y-6">
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Resort Name <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.name}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  name: e.target.value,
                  handle: e.target.value.toLowerCase().replace(/\s+/g, "-"),
                })
              }
              placeholder="Enter resort name"
            />
          </div>
          <div>
            <Text className="mb-2">Description</Text>
            <Textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Enter description"
            />
          </div>
          <div className="flex gap-x-8">
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-active"
                checked={formData.is_active}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_active: checked })
                }
              />
              <Label htmlFor="is-active">Active</Label>
            </div>
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-internal"
                checked={formData.is_internal}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_internal: checked })
                }
              />
              <Label htmlFor="is-internal">Internal</Label>
            </div>
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Handle <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.handle}
              onChange={(e) =>
                setFormData({ ...formData, handle: e.target.value })
              }
              placeholder="Enter handle"
            />
          </div>
          <div>
            <Text className="mb-2">Country</Text>
            <Input
              value={formData.country}
              onChange={(e) =>
                setFormData({ ...formData, country: e.target.value })
              }
              placeholder="Enter country"
            />
          </div>
          <div>
            <Text className="mb-2">Currency</Text>
            <Input
              value={formData.currency}
              onChange={(e) =>
                setFormData({ ...formData, currency: e.target.value })
              }
              placeholder="Enter currency code (e.g., USD)"
            />
          </div>
          <div>
            <Text className="mb-2">Website</Text>
            <Input
              value={formData.website || ""}
              onChange={(e) =>
                setFormData({ ...formData, website: e.target.value })
              }
              placeholder="Enter website URL"
            />
          </div>
          <div>
            <Text className="mb-2">Key Card Cost</Text>
            <Input
              type="number"
              value={formData.key_card_cost}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  key_card_cost: Number(e.target.value),
                })
              }
              placeholder="Enter key card cost"
            />
          </div>
        </div>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default ResortForm;
