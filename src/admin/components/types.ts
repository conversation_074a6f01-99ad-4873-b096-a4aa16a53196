type DimensionValue = {
    value: string;
    oneValue: string;
  };
  
  type MetricValue = {
    value: string;
    oneValue: string;
  };
  
  export type DataEntry = {
    dimensionValues: DimensionValue[];
    metricValues: MetricValue[];
    hour: string;
  };
  
  export interface AnalyticsData {
    date: string;
    activeUsers: number;
    sessions: number;
    transactions: number;
    eventCount: number;
    screenPageViews: number;
    totalUsers: number;
    deviceCategory?: string;
    country?: string;
    city?: string;
    pageTitle?: string;
    eventName?: string;
    hour?: string;
  }
  
  export interface MetricCard {
    title: string;
    value: number;
    icon: React.ComponentType;
    change?: number;
  }
  
  // Helper function to transform GA4 data to our format
  export function transformGAData(data: DataEntry[]): AnalyticsData[] {
    // Group data by date to aggregate metrics
    const groupedData = data.reduce((acc, entry) => {
      const date = entry.dimensionValues[0].value;
      if (!acc[date]) {
        acc[date] = {
          activeUsers: 0,
          sessions: 0,
          transactions: 0,
          eventCount: 0,
          screenPageViews: 0,
          totalUsers: 0,
        };
      }
      
      // Add metrics for this entry
      acc[date].activeUsers = Math.max(acc[date].activeUsers, parseInt(entry.metricValues[0].value));
      acc[date].sessions = Math.max(acc[date].sessions, parseInt(entry.metricValues[1].value));
      acc[date].transactions = Math.max(acc[date].transactions, parseInt(entry.metricValues[2].value));
      acc[date].eventCount = Math.max(acc[date].eventCount, parseInt(entry.metricValues[3].value));
      acc[date].screenPageViews = Math.max(acc[date].screenPageViews, parseInt(entry.metricValues[4].value));
      acc[date].totalUsers = Math.max(acc[date].totalUsers, parseInt(entry.metricValues[5].value));
      
      return acc;
    }, {} as Record<string, Omit<AnalyticsData, 'date'>>);
  
    // Convert grouped data to array and format dates
    return Object.entries(groupedData).map(([date, metrics]) => ({
      date: `${date.slice(0, 4)}-${date.slice(4, 6)}-${date.slice(6, 8)}`,
      ...metrics
    }));
  }