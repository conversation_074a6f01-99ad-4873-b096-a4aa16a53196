import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  Badge,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { CheckCircle } from "@camped-ai/icons";
import { Bell } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";
import { useNavigate } from "react-router-dom";

interface NotificationData {
  title?: string;
  description?: string;
  message?: string;
  phone?: string;
  customer_id?: string;
  order_id?: string;
  message_id?: string;
  whatsapp_message_id?: string;
  message_type?: string;
  timestamp?: string;
  [key: string]: any;
}

interface Notification {
  id: string;
  event_name: string;
  content: string;
  read: boolean;
  created_at: string;
  data?: NotificationData;
  resource_id?: string;
  resource_type?: string;
}

const NotificationFeed: React.FC = () => {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const lastNotificationIdRef = useRef<string | null>(null);

  // Fetch notifications
  const { data: notifications, isLoading } = useQuery({
    queryKey: ["notifications"],
    queryFn: async () => {
      try {
        const response = await sdk.client.fetch("/admin/notifications?channel=feed");
        return (response as any).notifications as Notification[];
      } catch (error) {
        console.error("Error fetching notifications:", error);
        return [];
      }
    },
    refetchInterval: 30000, // Refetch every 10 seconds
  });

  // Check for new WhatsApp messages and show toast notifications
  useEffect(() => {
    if (!notifications || notifications.length === 0) return;

    // Get the most recent notification
    const sortedNotifications = [...notifications].sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    const latestNotification = sortedNotifications[0];

    // If we've already processed this notification, skip
    if (lastNotificationIdRef.current === latestNotification.id) return;

    // Update the last processed notification ID
    lastNotificationIdRef.current = latestNotification.id;

    // Check if this is a WhatsApp message notification
    if (latestNotification.event_name === "whatsapp.message.received" && !latestNotification.read) {
      // Extract data from the notification
      const data = latestNotification.data || {};
      const title = data.title || "New WhatsApp Message";
      const description = data.description || "You have received a new WhatsApp message";
      const orderId = data.order_id;

      // Show a toast notification
      toast.info(title, {
        description,
        action: orderId ? {
          label: "View",
          altText: "View WhatsApp conversation",
          onClick: () => {
            navigate(`/app/hotel-management/bookings/${orderId}/whatsapp`);
          },
        } : undefined,
        duration: 8000, // Show for 8 seconds
      });
    }
  }, [notifications, navigate]);

  // Mark as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (id: string) => {
      await sdk.client.fetch(`/admin/notifications/${id}`, {
        method: "POST",
        body: JSON.stringify({ read: true }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
    onError: (error) => {
      toast.error(`Error marking notification as read: ${error.message}`);
    },
  });

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      await sdk.client.fetch("/admin/notifications/batch", {
        method: "POST",
        body: JSON.stringify({
          filter: { channel: "feed", read: false },
          update: { read: true }
        }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      toast.success("All notifications marked as read");
    },
    onError: (error) => {
      toast.error(`Error marking all notifications as read: ${error.message}`);
    },
  });

  // Count unread notifications
  const unreadCount = notifications?.filter((n) => !n.read).length || 0;

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins} min${diffMins === 1 ? "" : "s"} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? "" : "s"} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? "" : "s"} ago`;

    return date.toLocaleDateString();
  };

  return (
    <>
      <Toaster />
      <Popover open={open} onOpenChange={setOpen}>
        <Popover.Trigger asChild>
          <Button variant="transparent" className="relative">
            <Bell />
            {unreadCount > 0 && (
              <Badge
                className="absolute -top-1 -right-1 min-w-[18px] h-[18px] flex items-center justify-center p-0 text-xs bg-ui-tag-red-bg text-ui-tag-red-text"
              >
                {unreadCount}
              </Badge>
            )}
          </Button>
        </Popover.Trigger>
        <Popover.Content align="end" className="w-[400px] p-0">
          <div className="flex items-center justify-between p-3 border-b border-ui-border-base">
            <Heading level="h3">Notifications</Heading>
            {unreadCount > 0 && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => markAllAsReadMutation.mutate()}
                disabled={markAllAsReadMutation.isPending}
              >
                Mark all as read
              </Button>
            )}
          </div>
          <div className="max-h-[400px] overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center">
                <Text>Loading notifications...</Text>
              </div>
            ) : notifications?.length === 0 ? (
              <div className="p-4 text-center">
                <Text className="text-ui-fg-subtle">No notifications</Text>
              </div>
            ) : (
              <ul className="divide-y divide-ui-border-base">
                {notifications?.map((notification) => (
                  <li
                    key={notification.id}
                    className={`p-3 hover:bg-ui-bg-base-hover ${
                      !notification.read ? "bg-ui-bg-highlight" : ""
                    }`}
                  >
                    <div className="flex justify-between">
                      <Text size="small" className="text-ui-fg-subtle">
                        {notification.data?.title || notification.event_name}
                      </Text>
                      <Text size="small" className="text-ui-fg-subtle">
                        {formatDate(notification.created_at)}
                      </Text>
                    </div>
                    <Text className="my-1">
                      {notification.data?.description || notification.content}
                    </Text>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {!notification.read && (
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => markAsReadMutation.mutate(notification.id)}
                          disabled={markAsReadMutation.isPending}
                        >
                          <CheckCircle className="w-4 h-4 mr-1" /> Mark as read
                        </Button>
                      )}
                      {notification.event_name === "whatsapp.message.received" && notification.data?.order_id && (
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => navigate(`/app/hotel-management/bookings/${notification.data.order_id}/whatsapp`)}
                        >
                          View Messages
                        </Button>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </Popover.Content>
      </Popover>
    </>
  );
};

export default NotificationFeed;
