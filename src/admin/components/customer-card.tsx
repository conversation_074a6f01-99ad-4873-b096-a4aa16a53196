import { Container, Text } from "@camped-ai/ui";
import { DateRange } from "../../ui-components/utils/types";
import { CustomersCountResponse, CustomersNumber } from "../../ui-components/customers/customers-number-overview";
import { Users, UserPlus, ShoppingCart, TrendingUp } from "lucide-react";
import { useEffect, useState } from "react";
import { amountToDisplay, deduceDateUrlParams } from "../../ui-components/utils/helpers";
import React from "react";
import { SalesHistoryResponse } from "../../ui-components/sales/types";
import { OrdersCountResponse } from "../../ui-components/orders/orders-number-overview";
import { format } from "date-fns"; 


interface CustomerData {
  analytics: {
    current: { customerCount: string; date: string }[];
    previous: { customerCount: string; date: string }[];
  };
}

const CustomerStatsCard = ({
  dateRange,
  dateRangeCompareTo,
  compareEnabled
}: {
  dateRange?: DateRange,
  dateRangeCompareTo?: DateRange,
  compareEnabled?: boolean
}) => {
  const [SalesData, setSalesData] = useState<SalesHistoryResponse| null>(null)
  const [orderData, setorderData] = useState<OrdersCountResponse | null>(null)
  const [data, setData] = useState<CustomersCountResponse | null>(null)
  const [customerData, setCustomerData] = useState<CustomerData | null>(null);
  const[loading,setloading] = useState(false)
  useEffect(() => {
    fetch("/admin/customers-analytics/history", { credentials: "include" })
      .then((res) => res.json())
      .then((result: CustomerData) => {
        setCustomerData(result);
      })
      .catch((error) => {
      
        console.error(error);
      });
      fetch(`/admin/customers-analytics/count?${deduceDateUrlParams(dateRange, dateRangeCompareTo).toString()}`, {
        credentials: "include",
      })
      .then((res) => res.json())
      .then((result) => {
        setData(result)
       setloading(true)
      })
      .catch((error) => {
       
        console.error(error);
      }) 

      const UrlParams = deduceDateUrlParams(dateRange)
      UrlParams.append('orderStatuses','completed')
      UrlParams.append('orderStatuses','pending')
      fetch(`/admin/orders-analytics/count?${UrlParams.toString()}`, {
        credentials: "include",
      })
      .then((res) => res.json())
      .then((result) => {
        setorderData(result)
        
      })
      .catch((error) => {
        
        console.error(error);
      }) 
    const searchParams = deduceDateUrlParams(dateRange);
    searchParams.append('orderStatuses','completed')
    searchParams.append('orderStatuses','pending')
    searchParams.append('currencyCode', 'inr')

    fetch(`/admin/sales-analytics/history?${searchParams.toString()}`, {
      credentials: "include",
    })
    .then((res) => res.json())
    .then((result) => {
      setSalesData(result)
    })
    .catch((error) => {
      console.error(error);
    }) 
  }, [dateRange, loading]);
  

  // Compute total customer count
  const totalCustomerCount = customerData?.analytics?.current?.reduce(
    (sum, item) => sum + Number(item.customerCount),
    0
  );
  const formattedDateRange = dateRange
  ? `${format(new Date(dateRange.from), "MMM d")} - ${format(new Date(dateRange.to), "MMM d")}`
  : "Last 30 days"; 

  const overallCurrentSum: number = SalesData?.analytics?.current.reduce((sum, order) => sum + parseInt(order.total), 0);
  return (
    <div className="grid grid-cols-2 gap-4">
    {/* Total Customers Card */}
    <Container className="p-6 rounded-xl border shadow-sm hover:shadow transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <Text className="text-sm font-medium ">Total Customers</Text>
        <Users className="w-5 h-5 text-blue-600" />
      </div>
      <div className="flex flex-col">
        <Text className="text-3xl font-bold">{totalCustomerCount}</Text>
        <Text className="text-sm text-gray-600 mt-1">{formattedDateRange}</Text>
      </div>
    </Container>
  
    {/* New Customers Card */}
    <Container className="p-6 rounded-xl border shadow-sm hover:shadow transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <Text className="text-sm font-medium">New Customers</Text>
        <UserPlus className="w-5 h-5 text-green-600" />
      </div>
      <div className="flex flex-col">
        <Text className="text-3xl font-bold">{data?.analytics?.current}</Text>
        <Text className="text-sm text-gray-600 mt-1">{formattedDateRange}</Text>
      </div>
    </Container>
  
    {/* Total Sales Card */}
    <Container className="p-6 rounded-xl border shadow-sm hover:shadow transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <Text className="text-sm font-medium">Total Sales</Text>
        <ShoppingCart className="w-5 h-5 text-purple-600" />
      </div>
      <div className="flex flex-col">
        <Text className="text-3xl font-bold">{amountToDisplay(overallCurrentSum, SalesData?.analytics?.currencyDecimalDigits)}</Text>
        <Text className="text-sm text-gray-600 mt-1">{formattedDateRange}</Text>
      </div>
    </Container>
  
    {/* Total Order Card */}
    <Container className="p-6 rounded-xl border shadow-sm hover:shadow transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <Text className="text-sm font-medium">Total Order</Text>
        <TrendingUp className="w-5 h-5 text-orange-600" />
      </div>
      <div className="flex flex-col">
        <Text className="text-3xl font-bold">{orderData?.analytics?.current}</Text>
        <Text className="text-sm text-gray-600 mt-1">{formattedDateRange}</Text>        
      </div>
    </Container>
  </div>
  );
};

export default CustomerStatsCard