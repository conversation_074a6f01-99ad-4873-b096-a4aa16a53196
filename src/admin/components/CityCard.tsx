import { Heading,Text } from "@camped-ai/ui";
import React, { useEffect, useState } from "react";


const CityProgressChart = () => {
    const [cityData, setCityData] = useState([]);
    const [loading, setLoading] = useState(true);
  
  
    useEffect(() => {
      const fetchCityData = async () => {
        try {
          const response = await fetch("/admin/user-analytics/overview/city",{
            credentials: "include"
          });
          const data = await response.json();
          const sortedData = data.sort((a, b) => b.users - a.users).slice(0, 7);
        setCityData(sortedData);
        } catch (error) {
          console.error("Failed to fetch browser data:", error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchCityData();
    }, []);
    const maxUsers = cityData.length > 0 ? Math.max(...cityData.map((data) => data.users)) : 1;
  return (
    <>
    <Heading level="h2" >
      Active Users by City
    </Heading>
    <span className="text-sm">Last 30 days</span>
    <div className="space-y-4 mt-4">
      {cityData.map((data) => (
        <div key={data.city} className="flex items-center gap-3">
          <Text className="w-24 text-sm font-medium">
            {data.city}
          </Text>
          <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-blue-500 to-blue-300 transition-all"
              style={{ width: `${(data.users / maxUsers) * 100}%` }}
            ></div>
          </div>
          <Text  className="w-10 text-sm font-medium text-right">
            {data.users}
            </Text >
        </div>
      ))}
    </div>
  </>

  );
};

export default CityProgressChart;
