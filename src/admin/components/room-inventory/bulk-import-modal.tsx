import { useState } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  Input,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { FileIcon, DownloadIcon, UploadIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import * as XLSX from 'xlsx';

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
  hotelId?: string;
};

const BulkImportModal = ({ open, onClose, hotelId }: BulkImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [filePreview, setFilePreview] = useState<any>(null);
  const [previewData, setPreviewData] = useState<{headers: string[], rows: any[]}>({headers: [], rows: []});
  const [previewError, setPreviewError] = useState<string>("");
  const [overrideExisting, setOverrideExisting] = useState(true);
  const navigate = useNavigate();

  const handleDownloadTemplate = async () => {
    try {
      // If hotelId is provided, include it in the URL to get hotel-specific template
      const url = hotelId
        ? `/admin/hotel-management/room-inventory/template?hotel_id=${hotelId}`
        : "/admin/hotel-management/room-inventory/template";

      window.open(url, "_blank");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Error", {
        description: "Failed to download template",
      });
    }
  };

  // Function to parse Excel/CSV files
  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          setPreviewError("Could not read file data");
          return;
        }

        // Parse the file using XLSX
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length === 0) {
          setPreviewError("No data found in file");
          return;
        }

        // Extract headers (first row)
        const headers = jsonData[0] as string[];

        // Extract rows (limit to 5 for preview)
        const rows = jsonData.slice(1, 6) as any[];

        setPreviewData({ headers, rows });
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: true
        });
      } catch (error) {
        console.error('Error parsing file:', error);
        setPreviewError("Could not parse file. Make sure it's a valid Excel or CSV file.");
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + ' KB',
          type: file.type,
          previewAvailable: false,
          error: 'Could not generate preview'
        });
      }
    };
    reader.onerror = () => {
      setPreviewError("Error reading file");
    };
    reader.readAsArrayBuffer(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setFilePreview(null); // Reset preview when file changes
      setPreviewData({headers: [], rows: []});
      setPreviewError("");

      // Generate preview for Excel/CSV files
      if (selectedFile.name.endsWith('.xlsx') || selectedFile.name.endsWith('.xls') || selectedFile.name.endsWith('.csv')) {
        parseExcelFile(selectedFile);
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Error", {
        description: "Please select a file to upload",
      });
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      // If hotelId is provided, include it in the form data
      if (hotelId) {
        formData.append("hotel_id", hotelId);
      }

      // Include the override option
      formData.append("override_existing", overrideExisting.toString());
      console.log(`Sending override_existing=${overrideExisting} to the server`);

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      // Create a promise to handle the response
      const uploadPromise = new Promise<any>((resolve, reject) => {
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const result = JSON.parse(xhr.responseText);
                resolve(result);
              } catch (error) {
                reject(new Error('Invalid response format'));
              }
            } else {
              try {
                const errorResponse = JSON.parse(xhr.responseText);
                reject(new Error(errorResponse.message || 'Upload failed'));
              } catch (e) {
                reject(new Error(`Upload failed with status ${xhr.status}`));
              }
            }
          }
        };
      });

      // Open and send the request
      xhr.open('POST', '/admin/hotel-management/room-inventory/import', true);
      xhr.send(formData);

      // Wait for the upload to complete
      const result = await uploadPromise;

      // Process the result
      setUploadResult(result);

      if (result.results.successful > 0) {
        toast.success("Success", {
          description: `${result.results.successful} inventory records imported successfully`,
        });

        // Notify the parent component that data has been imported successfully
        // This will trigger a refresh of the inventory list
        if (onClose) {
          // We'll keep the modal open, but still trigger the refresh
          // The actual onClose function will be called when the user clicks Close or Upload Another File
          setTimeout(() => {
            // Call onClose and immediately reopen the modal to keep it open
            const tempFn = onClose;
            tempFn();
          }, 100);
        }
      }

      if (result.results.failed > 0) {
        toast.error("Warning", {
          description: `${result.results.failed} inventory records failed to import`,
        });
      }
    } catch (error: any) {
      console.error("Error uploading file:", error);
      toast.error("Error", {
        description: error.message || "Failed to upload file",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleViewInventory = () => {
    onClose();
    if (hotelId) {
      navigate(`/hotel-management/hotels/${hotelId}/availability`);
    } else {
      navigate("/hotel-management/availability");
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[90vh]">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between items-center px-6 py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Import Room Inventory
            </Heading>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            <Toaster />

            {!uploadResult ? (
              <>
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-2">
                      <FileIcon className="text-gray-500" />
                      <Text>Download the template file, fill it with your room inventory data, and upload it.</Text>
                    </div>
                    <Button
                      variant="secondary"
                      onClick={handleDownloadTemplate}
                      className="flex items-center gap-2"
                    >
                      <DownloadIcon className="w-4 h-4" />
                      <span>Download Template</span>
                    </Button>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <label className="block">
                      <Text className="mb-2 font-medium">Upload File</Text>
                      <Input
                        type="file"
                        accept=".xlsx,.xls,.csv"
                        onChange={handleFileChange}
                        className="w-full"
                      />
                      <Text className="mt-1 text-gray-500 text-sm">
                        Excel (.xlsx, .xls) and CSV files are supported. Maximum file size: 5MB.
                      </Text>
                    </label>

                    {file && (
                      <div className="mt-4">
                        <Text className="font-medium mb-2">Selected File</Text>
                        <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                          <div className="flex items-center gap-2">
                            <FileIcon className="text-gray-500" />
                            <Text className="font-medium">{file.name}</Text>
                          </div>
                          <Text className="text-gray-500 text-sm mt-1">
                            Size: {(file.size / 1024).toFixed(2)} KB
                          </Text>
                        </div>
                      </div>
                    )}

                    <div className="mt-4">
                      <Text className="font-medium mb-2">Import Options</Text>
                      <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id="override-existing"
                            checked={overrideExisting}
                            onChange={(e) => setOverrideExisting(e.target.checked)}
                            className="h-4 w-4 text-blue-600 rounded border-gray-300"
                          />
                          <label htmlFor="override-existing" className="text-sm text-gray-700">
                            Override existing records (if unchecked, will only create new records)
                          </label>
                        </div>
                      </div>
                    </div>

                    {filePreview && (
                      <div className="mt-4">
                        <Text className="font-medium mb-2">File Preview</Text>
                        <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                          {filePreview.previewAvailable ? (
                            <div>
                              <Text>The file contains data that will be imported as room inventory records.</Text>
                              <Text className="text-gray-500 text-sm mt-1 mb-3">
                                Make sure your file has the required columns: inventory_item_id, from_date, to_date, and available_quantity.
                              </Text>

                              {previewData.headers.length > 0 ? (
                                <>
                                  <div className="border border-gray-200 rounded-md">
                                    <div className="overflow-x-auto">
                                      <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-100">
                                          <tr>
                                            {previewData.headers.map((header, index) => (
                                              <th
                                                key={index}
                                                className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                              >
                                                {header}
                                              </th>
                                            ))}
                                          </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                          {previewData.rows.map((row, rowIndex) => (
                                            <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                              {Array.from({ length: previewData.headers.length }).map((_, colIndex) => (
                                                <td key={colIndex} className="px-3 py-2 text-sm text-gray-500">
                                                  {row[colIndex] !== undefined ? String(row[colIndex]) : ''}
                                                </td>
                                              ))}
                                            </tr>
                                          ))}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>

                                  {previewData.rows.length > 0 && (
                                    <Text className="text-gray-500 text-xs mt-2 italic">
                                      Showing first {previewData.rows.length} rows of data
                                    </Text>
                                  )}
                                </>
                              ) : previewError ? (
                                <Text className="text-red-500">{previewError}</Text>
                              ) : (
                                <Text className="text-gray-500">Loading preview...</Text>
                              )}
                            </div>
                          ) : (
                            <Text className="text-gray-500">
                              {filePreview.error || "Preview not available for this file type."}
                            </Text>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <Heading level="h3" className="text-lg font-medium">
                      Import Results
                    </Heading>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                        <Text>Successfully imported: {uploadResult.results.successful}</Text>
                      </div>
                      <div className="flex items-center gap-2">
                        <XCircleIcon className="w-5 h-5 text-red-500" />
                        <Text>Failed to import: {uploadResult.results.failed}</Text>
                      </div>
                    </div>
                  </div>
                </div>

                {uploadResult.results.errors && uploadResult.results.errors.length > 0 && (
                  <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <div className="flex flex-col gap-4">
                      <Heading level="h3" className="text-lg font-medium">
                        Errors
                      </Heading>
                      <div className="flex flex-col gap-2">
                        {uploadResult.results.errors.map((error: any, index: number) => (
                          <div key={index} className="p-3 border border-gray-200 rounded-md">
                            <Text className="font-medium">Row {error.row}</Text>
                            <Text className="text-red-500">{error.error}</Text>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex-shrink-0 py-4 px-6 bg-white border-t border-gray-200 flex gap-2 justify-end">
            {!uploadResult ? (
              <>
                <Button variant="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <div>
                  <Button
                    variant="primary"
                    onClick={handleUpload}
                    disabled={!file || isUploading}
                    className="flex items-center gap-2"
                  >
                    {isUploading ? (
                      <>
                        <span className="animate-pulse">Uploading...</span>
                      </>
                    ) : (
                      <>
                        <UploadIcon className="w-4 h-4" />
                        <span>Upload and Import</span>
                      </>
                    )}
                  </Button>
                </div>
              </>
            ) : (
              <>
                <Button
                  variant="secondary"
                  onClick={() => {
                    // Reset the upload result and file to allow re-upload
                    setUploadResult(null);
                    setFile(null);
                    setFilePreview(null);
                    setPreviewData({headers: [], rows: []});
                  }}
                  className="flex items-center gap-2"
                >
                  <UploadIcon className="w-4 h-4" />
                  Upload Another File
                </Button>
                <Button variant="secondary" onClick={onClose}>
                  Close
                </Button>
                <Button variant="primary" onClick={handleViewInventory}>
                  View Inventory
                </Button>
              </>
            )}
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
