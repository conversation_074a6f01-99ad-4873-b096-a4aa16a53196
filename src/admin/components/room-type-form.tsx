import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Select,
  Textarea,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { TextareaField } from "./ai-enhanced-inputs";

export type RoomTypeFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_internal: boolean;
  website: string | null;
  star?: number;
  note?: string;
  accommodation_note?: string;
  message?: string;
  second_message?: string;
  parent_category_id?: string | null;
};

type RoomTypeFormProps = {
  formData: RoomTypeFormData;
  setFormData: (data: RoomTypeFormData) => void;
  onSubmit: () => Promise<boolean>;
  isEdit?: boolean;
  closeModal: () => void;
};

const RoomTypeForm = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
}: RoomTypeFormProps) => {
  const handleSubmit = async () => {
    const success = await onSubmit();
    if (success) {
      closeModal();
    }
  };

  return (
    <FocusModal.Content>
      <FocusModal.Header>
        <div className="flex justify-between items-center">
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!formData.name || !formData.handle}
          >
            {isEdit ? "Update" : "Save"}
          </Button>
        </div>
      </FocusModal.Header>
      <FocusModal.Body className="flex flex-col items-center py-16 gap-4 overflow-y-auto ">
        <Heading level="h1">
          {isEdit ? "Edit Room Type" : "Create Room Type"}
        </Heading>
        <div className="flex w-full max-w-lg flex-col gap-y-6">
          <div>
            <Text className="mb-2">
              Room Type Name{" "}
              <span style={{ color: "red", fontSize: "0.6em" }}>★</span>
            </Text>
            <Input
              value={formData.name}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  name: e.target.value,
                  handle: e.target.value.toLowerCase().replace(/\s+/g, "-"),
                })
              }
              placeholder="Enter room type name"
            />
          </div>
          <div>
            <TextareaField
              id="description"
              label="Description"
              value={formData.description}
              onChange={(value) =>
                setFormData({ ...formData, description: value })
              }
              placeholder="Enter description"
              rows={4}
              contentType="description"
              context={{
                name: formData.name,
                type: "room type",
              }}
              helpText="A detailed description helps guests understand what to expect"
            />
          </div>
          <div className="flex gap-x-8">
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-active"
                checked={formData.is_active}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_active: checked })
                }
              />
              <Label htmlFor="is-active">Active</Label>
            </div>
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-internal"
                checked={formData.is_internal}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_internal: checked })
                }
              />
              <Label htmlFor="is-internal">Internal</Label>
            </div>
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Handle <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.handle}
              onChange={(e) =>
                setFormData({ ...formData, handle: e.target.value })
              }
              placeholder="Enter handle"
            />
          </div>
        </div>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default RoomTypeForm;
