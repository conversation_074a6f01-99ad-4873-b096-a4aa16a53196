import { Con<PERSON><PERSON>, <PERSON>, <PERSON>ge, <PERSON><PERSON>, Head<PERSON> } from "@camped-ai/ui";
import { useNavigate } from "react-router-dom";
import { HotelData } from "../types";
import { ClockSolid } from "@camped-ai/icons";

interface HotelCardProps {
  hotel: HotelData;
  onUpdate: () => void;
}

const HotelCard = ({ hotel, onUpdate }: HotelCardProps) => {
  const navigate = useNavigate();
  const defaultImage = "https://placehold.co/600x400/e2e8f0/475569?text=Hotel+Image";

  // Find thumbnail image or use first image or default
  const thumbnailImage = hotel.images?.find(img => img.isThumbnail)?.url ||
                         hotel.images?.[0]?.url ||
                         defaultImage;

  // Generate star rating display
  const renderStars = (rating: number = 0) => {
    return Array.from({ length: 5 }).map((_, i) => (
      <ClockSolid
        key={i}
        className={`w-4 h-4 ${i < rating ? "text-yellow-400" : "text-gray-300"}`}
      />
    ));
  };

  return (
    <Container className="overflow-hidden flex flex-col h-full transition-all hover:shadow-md border border-gray-200 rounded-lg bg-white">
      <div className="relative h-48 overflow-hidden">
        <img
          src={thumbnailImage}
          alt={hotel.name}
          className="w-full h-full object-cover"
        />
        <Badge
          color={hotel.is_active ? "green" : "grey"}
          className="absolute top-2 right-2"
        >
          {hotel.is_active ? "Active" : "Inactive"}
        </Badge>
      </div>

      <div className="p-4 flex-grow flex flex-col">
        <div className="flex justify-between items-start mb-2">
          <Heading level="h3" className="text-lg font-medium">
            {hotel.name}
          </Heading>
          <div className="flex">
            {renderStars(hotel.star)}
          </div>
        </div>

        {hotel.location && (
          <Text className="text-gray-500 text-sm mb-2">
            {hotel.location}
          </Text>
        )}

        <Text className="text-gray-700 line-clamp-2 mb-4 flex-grow">
          {hotel.description || "No description available."}
        </Text>

        <div className="flex gap-2 mt-auto">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${hotel.id}`)}
            className="flex-1"
          >
            Manage
          </Button>
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${hotel.id}/room-configs`)}
            className="flex-1"
          >
            Room Configs
          </Button>
        </div>
      </div>
    </Container>
  );
};

export default HotelCard;
