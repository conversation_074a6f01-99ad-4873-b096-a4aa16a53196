import { Button, Prompt as NativePrompt } from "@camped-ai/ui";
import { ReactNode } from "react";

interface DeletePromptProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onDelete: () => void;
  trigger?: ReactNode;
}

const Prompt = ({
  open,
  onOpenChange,
  title,
  description,
  onDelete,
  trigger,
}: DeletePromptProps) => {
  return (
    <NativePrompt open={open} onOpenChange={onOpenChange} overlayProps={{ style: { zIndex: 9999 } }}>
      <NativePrompt.Trigger asChild>
        {trigger || <Button variant="danger">Delete</Button>}
      </NativePrompt.Trigger>
      <NativePrompt.Content style={{ zIndex: 10000 }}>
        <NativePrompt.Header>
          <NativePrompt.Title>{title}</NativePrompt.Title>
          <NativePrompt.Description>{description}</NativePrompt.Description>
        </NativePrompt.Header>
        <NativePrompt.Footer>
          <NativePrompt.Cancel onClick={() => onOpenChange(false)}>
            Cancel
          </NativePrompt.Cancel>
          <NativePrompt.Action onClick={onDelete}>Delete</NativePrompt.Action>
        </NativePrompt.Footer>
      </NativePrompt.Content>
    </NativePrompt>
  );
};

export default Prompt;
