import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  Container,
  Badge,
  Text,
  Heading,
  IconButton,
  Tooltip,
} from "@camped-ai/ui";
import { Plus, Minus, Trash2, PlusCircle } from "lucide-react";
import { use<PERSON><PERSON><PERSON><PERSON>x<PERSON>, Controller } from "react-hook-form";
import { toast } from "sonner";

// Define types for add-on services
interface AddOnService {
  id: string;
  name: string;
  description?: string;
  adult_price?: number;
  child_price?: number;
  currency_code: string;
  is_active: boolean;
  service_level: "hotel" | "destination";
  hotel_id?: string;
  hotel_name?: string;
}

// Define types for add-on items in the form
interface AddOnItem {
  id: string;
  name: string;
  adult_quantity: number;
  child_quantity: number;
  adult_price: number;
  child_price?: number;
  total_price: number;
  currency_code: string;
  service_id: string;
}

// Define types for custom line items
interface CustomLineItem {
  name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency_code: string;
}

interface AddOnServiceSelectorProps {
  hotelId: string;
  adults: number;
  children: number;
  currencyCode: string;
}

const AddOnServiceSelector = ({
  hotelId,
  adults,
  children,
  currencyCode,
}: AddOnServiceSelectorProps) => {
  const [addOnServices, setAddOnServices] = useState<AddOnService[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCustomLineItem, setShowCustomLineItem] = useState(false);
  const [customItem, setCustomItem] = useState<CustomLineItem>({
    name: "",
    quantity: 1,
    unit_price: 0,
    total_price: 0,
    currency_code: currencyCode,
  });

  const { control, watch, setValue, getValues } = useFormContext();

  // Watch for changes to add-ons and custom line items
  const watchAddOns = watch("add_ons") || [];
  const watchCustomLineItems = watch("custom_line_items") || [];

  // Format price for display
  const formatPrice = (price: number, currency: string = currencyCode) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price);
  };

  // Fetch add-on services for the selected hotel
  useEffect(() => {
    const fetchAddOnServices = async () => {
      if (!hotelId) return;

      try {
        setIsLoading(true);
        const response = await fetch(
          `/admin/add-on-services?hotel_id=${hotelId}&service_level=hotel`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch add-on services");
        }

        const data = await response.json();
        console.log("Add-on services:", data.add_on_services);

        // Filter out inactive services
        const activeServices = data.add_on_services.filter(
          (service: AddOnService) => service.is_active
        );

        setAddOnServices(activeServices);
      } catch (error) {
        console.error("Error fetching add-on services:", error);
        toast.error("Failed to fetch add-on services");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAddOnServices();
  }, [hotelId]);

  // Add an add-on service to the booking
  const addAddOnService = (service: AddOnService) => {
    // Check if the service is already added
    const existingAddOn = watchAddOns.find(
      (addOn: AddOnItem) => addOn.service_id === service.id
    );

    if (existingAddOn) {
      // If already added, increment adult quantity
      updateAddOnQuantity(existingAddOn.id, "adult", 1);
    } else {
      // Otherwise, add a new add-on item
      const newAddOn: AddOnItem = {
        id: `addon_${Date.now()}`,
        name: service.name,
        adult_quantity: 1,
        child_quantity: 0,
        adult_price: service.adult_price || 0,
        child_price: service.child_price,
        total_price: service.adult_price || 0,
        currency_code: service.currency_code || currencyCode,
        service_id: service.id,
      };

      const currentAddOns = getValues("add_ons") || [];
      setValue("add_ons", [...currentAddOns, newAddOn]);

      // Update the total amount
      updateTotalAmount();
    }
  };

  // Update the quantity of an add-on item
  const updateAddOnQuantity = (
    id: string,
    type: "adult" | "child",
    change: number
  ) => {
    const currentAddOns = getValues("add_ons") || [];
    const updatedAddOns = currentAddOns.map((addOn: AddOnItem) => {
      if (addOn.id === id) {
        const field = type === "adult" ? "adult_quantity" : "child_quantity";
        const newQuantity = Math.max(0, addOn[field] + change);

        // Calculate the new total price
        const adultTotal =
          addOn.adult_price *
          (type === "adult" ? newQuantity : addOn.adult_quantity);
        const childTotal =
          (addOn.child_price || 0) *
          (type === "child" ? newQuantity : addOn.child_quantity);

        return {
          ...addOn,
          [field]: newQuantity,
          total_price: adultTotal + childTotal,
        };
      }
      return addOn;
    });

    // Filter out add-ons with zero quantity
    const filteredAddOns = updatedAddOns.filter(
      (addOn: AddOnItem) => addOn.adult_quantity > 0 || addOn.child_quantity > 0
    );

    setValue("add_ons", filteredAddOns);

    // Update the total amount
    updateTotalAmount();
  };

  // Remove an add-on item
  const removeAddOn = (id: string) => {
    const currentAddOns = getValues("add_ons") || [];
    const updatedAddOns = currentAddOns.filter(
      (addOn: AddOnItem) => addOn.id !== id
    );
    setValue("add_ons", updatedAddOns);

    // Update the total amount
    updateTotalAmount();
  };

  // Add a custom line item
  const addCustomLineItem = () => {
    if (
      !customItem.name ||
      customItem.unit_price <= 0 ||
      customItem.quantity <= 0
    ) {
      toast.error("Please fill in all fields for the custom item");
      return;
    }

    const newCustomItem: CustomLineItem = {
      ...customItem,
      total_price: customItem.unit_price * customItem.quantity,
    };

    const currentCustomItems = getValues("custom_line_items") || [];
    setValue("custom_line_items", [...currentCustomItems, newCustomItem]);

    // Reset the custom item form
    setCustomItem({
      name: "",
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      currency_code: currencyCode,
    });

    // Hide the custom item form
    setShowCustomLineItem(false);

    // Update the total amount
    updateTotalAmount();
  };

  // Remove a custom line item
  const removeCustomLineItem = (index: number) => {
    const currentCustomItems = getValues("custom_line_items") || [];
    const updatedCustomItems = currentCustomItems.filter(
      (_: any, i: number) => i !== index
    );
    setValue("custom_line_items", updatedCustomItems);

    // Update the total amount
    updateTotalAmount();
  };

  // Update the total amount including add-ons and custom items
  const updateTotalAmount = () => {
    // Get the current room total
    const roomTotal = getValues("total_amount") || 0;

    // Calculate add-ons total
    const addOnsTotal = (getValues("add_ons") || []).reduce(
      (sum: number, addOn: AddOnItem) => sum + addOn.total_price,
      0
    );

    // Calculate custom items total
    const customItemsTotal = (getValues("custom_line_items") || []).reduce(
      (sum: number, item: CustomLineItem) => sum + item.total_price,
      0
    );

    // Update the total amount
    setValue("total_amount", roomTotal + addOnsTotal + customItemsTotal);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Heading level="h3" className="text-lg font-medium">
          Add-on Services
        </Heading>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            size="small"
            onClick={() => setShowCustomLineItem(!showCustomLineItem)}
          >
            <PlusCircle className="w-4 h-4 mr-2" />
            Custom Item
          </Button>
        </div>
      </div>

      {/* Available Add-on Services */}
      {isLoading ? (
        <div className="text-center py-4">
          <Text>Loading add-on services...</Text>
        </div>
      ) : addOnServices.length === 0 ? (
        <div className="text-center py-4 border rounded-lg">
          <Text className="text-gray-500">
            No add-on services available for this hotel
          </Text>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {addOnServices.map((service) => (
            <Container
              key={service.id}
              className="p-4 hover:shadow-md transition-shadow cursor-pointer border border-gray-200 rounded-lg bg-white"
              onClick={() => addAddOnService(service)}
            >
              <div className="flex justify-between items-start">
                <div>
                  <Heading level="h4" className="text-base font-medium">
                    {service.name}
                  </Heading>
                  {service.description && (
                    <Text className="text-sm text-gray-600 mt-1">
                      {service.description}
                    </Text>
                  )}
                </div>
                <div className="text-right">
                  {service.adult_price !== undefined && (
                    <Text className="font-medium">
                      {formatPrice(service.adult_price / 100)}
                    </Text>
                  )}
                  {service.child_price !== undefined &&
                    service.child_price > 0 && (
                      <Text className="text-sm text-gray-600">
                        Child: {formatPrice(service.child_price / 100)}
                      </Text>
                    )}
                </div>
              </div>
              <div className="mt-2">
                <Badge className="bg-blue-100 text-blue-800">
                  Click to add
                </Badge>
              </div>
            </Container>
          ))}
        </div>
      )}

      {/* Selected Add-on Services */}
      {watchAddOns.length > 0 && (
        <div className="mt-6">
          <Heading level="h4" className="text-base font-medium mb-3">
            Selected Add-ons
          </Heading>
          <div className="space-y-3">
            {watchAddOns.map((addOn: AddOnItem) => (
              <div
                key={addOn.id}
                className="flex justify-between items-center p-3 border rounded-lg bg-gray-50"
              >
                <div>
                  <Text className="font-medium">{addOn.name}</Text>
                  <div className="flex gap-4 mt-2">
                    {/* Adult quantity controls */}
                    <div className="flex items-center gap-2">
                      <Text className="text-sm">Adults:</Text>
                      <div className="flex items-center">
                        <IconButton
                          variant="secondary"
                          size="small"
                          onClick={() =>
                            updateAddOnQuantity(addOn.id, "adult", -1)
                          }
                        >
                          <Minus className="w-3 h-3" />
                        </IconButton>
                        <Text className="mx-2 min-w-[20px] text-center">
                          {addOn.adult_quantity}
                        </Text>
                        <IconButton
                          variant="secondary"
                          size="small"
                          onClick={() =>
                            updateAddOnQuantity(addOn.id, "adult", 1)
                          }
                        >
                          <Plus className="w-3 h-3" />
                        </IconButton>
                      </div>
                    </div>

                    {/* Child quantity controls (only if child price is available) */}
                    {addOn.child_price !== undefined && (
                      <div className="flex items-center gap-2">
                        <Text className="text-sm">Children:</Text>
                        <div className="flex items-center">
                          <IconButton
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              updateAddOnQuantity(addOn.id, "child", -1)
                            }
                          >
                            <Minus className="w-3 h-3" />
                          </IconButton>
                          <Text className="mx-2 min-w-[20px] text-center">
                            {addOn.child_quantity}
                          </Text>
                          <IconButton
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              updateAddOnQuantity(addOn.id, "child", 1)
                            }
                          >
                            <Plus className="w-3 h-3" />
                          </IconButton>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Text className="font-medium">
                    {formatPrice(addOn.total_price / 100)}
                  </Text>
                  <Tooltip content="Remove">
                    <IconButton
                      variant="destructive"
                      size="small"
                      onClick={() => removeAddOn(addOn.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Custom Line Item Form */}
      {showCustomLineItem && (
        <div className="mt-4 p-4 border rounded-lg bg-gray-50">
          <Heading level="h4" className="text-base font-medium mb-3">
            Add Custom Item
          </Heading>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <Text className="text-sm font-medium mb-1">Item Name</Text>
              <Input
                value={customItem.name}
                onChange={(e) =>
                  setCustomItem({ ...customItem, name: e.target.value })
                }
                placeholder="Enter item name"
              />
            </div>
            <div>
              <Text className="text-sm font-medium mb-1">Quantity</Text>
              <Input
                type="number"
                min={1}
                value={customItem.quantity}
                onChange={(e) =>
                  setCustomItem({
                    ...customItem,
                    quantity: parseInt(e.target.value) || 1,
                  })
                }
              />
            </div>
            <div>
              <Text className="text-sm font-medium mb-1">Unit Price</Text>
              <Input
                type="number"
                min={0}
                step={0.01}
                value={customItem.unit_price}
                onChange={(e) =>
                  setCustomItem({
                    ...customItem,
                    unit_price: parseFloat(e.target.value) || 0,
                  })
                }
              />
            </div>
          </div>
          <div className="flex justify-end mt-3 gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowCustomLineItem(false)}
            >
              Cancel
            </Button>
            <Button variant="primary" size="small" onClick={addCustomLineItem}>
              Add Item
            </Button>
          </div>
        </div>
      )}

      {/* Custom Line Items */}
      {watchCustomLineItems.length > 0 && (
        <div className="mt-6">
          <Heading level="h4" className="text-base font-medium mb-3">
            Custom Items
          </Heading>
          <div className="space-y-3">
            {watchCustomLineItems.map((item: CustomLineItem, index: number) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 border rounded-lg bg-gray-50"
              >
                <div>
                  <Text className="font-medium">{item.name}</Text>
                  <Text className="text-sm text-gray-600">
                    {item.quantity} x {formatPrice(item.unit_price)}
                  </Text>
                </div>
                <div className="flex items-center gap-4">
                  <Text className="font-medium">
                    {formatPrice(item.total_price)}
                  </Text>
                  <Tooltip content="Remove">
                    <IconButton
                      variant="destructive"
                      size="small"
                      onClick={() => removeCustomLineItem(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddOnServiceSelector;
