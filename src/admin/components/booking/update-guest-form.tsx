import { useState, useEffect } from "react";
import {
  But<PERSON>,
  Heading,
  Text,
  Input,
  Textarea,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useNavigate } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Spinner from "../shared/spinner";

// Form schema
const updateGuestSchema = z.object({
  special_requests: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

type UpdateGuestFormProps = {
  bookingId?: string;
};

const UpdateGuestForm = ({ bookingId }: UpdateGuestFormProps) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [booking, setBooking] = useState<any>(null);
  // No need for customer search state

  // Initialize form
  const form = useForm({
    resolver: zodResolver(updateGuestSchema),
    defaultValues: {
      special_requests: "",
      metadata: {
        travelers: {
          adults: [],
          children: [],
          infants: [],
        },
      },
    },
  });

  // Define types for travelers
  type Traveler = {
    name: string;
    age?: number;
  };

  type TravelersMetadata = {
    adults: Traveler[];
    children: Traveler[];
    infants: Traveler[];
  };

  // Helper function to update travelers in metadata
  const updateTravelerMetadata = (
    category: "adults" | "children" | "infants",
    index: number,
    data: Partial<Traveler>
  ) => {
    const metadataValue = form.getValues("metadata") || {};
    const travelers =
      metadataValue.travelers ||
      ({ adults: [], children: [], infants: [] } as TravelersMetadata);

    // Create a copy of the array
    const categoryArray = [...(travelers[category] || [])] as Traveler[];

    // Ensure the array has enough elements
    while (categoryArray.length <= index) {
      categoryArray.push(
        category === "adults" ? { name: "" } : { name: "", age: 0 }
      );
    }

    // Update the traveler data
    categoryArray[index] = {
      ...categoryArray[index],
      ...data,
    };

    // Update the metadata
    form.setValue("metadata", {
      ...metadataValue,
      travelers: {
        ...travelers,
        [category]: categoryArray,
      },
    } as any);
  };

  // Helper function to get traveler value safely
  const getTravelerValue = (
    category: "adults" | "children" | "infants",
    index: number,
    field: "name" | "age"
  ) => {
    const metadata = form.getValues("metadata") as any;
    const travelers = metadata?.travelers || {
      adults: [],
      children: [],
      infants: [],
    };
    const categoryArray = travelers[category] || [];
    const traveler = categoryArray[index];
    return traveler?.[field] || (field === "name" ? "" : 0);
  };

  // Fetch booking details
  useEffect(() => {
    const fetchBookingDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `/admin/hotel-management/bookings/${bookingId}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch booking details");
        }

        const data = await response.json();
        setBooking(data.booking);

        // Set special requests from booking data
        form.setValue("special_requests", data.booking.special_requests || "");

        // Handle metadata
        let metadata = data.booking.metadata || {};
        if (typeof metadata === "string") {
          try {
            metadata = JSON.parse(metadata);
          } catch (e) {
            console.error("Failed to parse metadata string:", e);
            metadata = {};
          }
        }

        // Get counts from metadata (they are stored there, not as direct booking properties)
        const adultsCount = metadata.adults || 2;
        const childrenCount = metadata.children || 0;
        const infantsCount = metadata.infants || 0;

        // Initialize travelers if not present
        if (!metadata.travelers) {
          metadata.travelers = {
            adults: Array(adultsCount)
              .fill({})
              .map(() => ({ name: "" })),
            children: Array(childrenCount)
              .fill({})
              .map(() => ({ name: "", age: 0 })),
            infants: Array(infantsCount)
              .fill({})
              .map(() => ({ name: "", age: 0 })),
          };
        }

        // Store the counts in the booking object for easy access in the form
        setBooking({
          ...data.booking,
          adults: adultsCount,
          children: childrenCount,
          infants: infantsCount,
        });

        // Only set the travelers metadata
        form.setValue("metadata", {
          travelers: metadata.travelers,
        });
      } catch (error) {
        console.error("Error fetching booking details:", error);
        toast.error("Error", {
          description: "Failed to fetch booking details",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (bookingId) {
      fetchBookingDetails();
    }
  }, [bookingId, form]);

  // No longer need customer search functions

  // Handle form submission
  const onSubmit = async (values: any) => {
    try {
      setIsSubmitting(true);

      // Get the current booking metadata
      const currentMetadata = booking.metadata || {};

      // Prepare the update data - only updating special requests and traveler information
      const updateData = {
        special_requests: values.special_requests,
        metadata: {
          ...currentMetadata,
          travelers: values.metadata.travelers,
        },
      };

      // Call API to update the booking
      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update guest information");
      }

      toast.success("Success", {
        description: "Traveler information updated successfully",
      });

      // Navigate back to booking details
      navigate(`/hotel-management/bookings/${bookingId}`);
    } catch (error) {
      console.error("Error updating traveler information:", error);
      toast.error("Error", {
        description: "Failed to update traveler information",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/hotel-management/bookings/${bookingId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Loading booking details...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Toaster />

      <div className="flex justify-between items-center mb-6">
        <div>
          <Heading level="h1" className="text-2xl">
            Update Traveler Information
          </Heading>
          <Text className="text-gray-500">Booking ID: {bookingId}</Text>
        </div>
        <Button variant="secondary" onClick={handleCancel}>
          Back to Booking
        </Button>
      </div>

      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <div className="mb-4">
          <Heading level="h2" className="text-xl">
            Update Traveler Information
          </Heading>
          <Text className="text-gray-500">
            Update traveler details and special requests
          </Text>
        </div>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Travelers Information */}
          <div>
            <h4 className="text-md font-medium mb-2">Travelers Information</h4>
            <p className="text-sm text-gray-500 mb-4">
              Enter details for all travelers
            </p>

            {/* Adults */}
            {Array.from({ length: booking?.adults || 2 }).map((_, index) => (
              <div
                key={`adult-${index}`}
                className="mb-4 p-3 border border-gray-200 rounded-md"
              >
                <h5 className="font-medium mb-2">Adult {index + 1}</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium">Full Name</label>
                    <Input
                      placeholder="Full Name"
                      value={
                        getTravelerValue("adults", index, "name") as string
                      }
                      onChange={(e) =>
                        updateTravelerMetadata("adults", index, {
                          name: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            ))}

            {/* Children */}
            {Array.from({ length: booking?.children || 0 }).map((_, index) => (
              <div
                key={`child-${index}`}
                className="mb-4 p-3 border border-gray-200 rounded-md"
              >
                <h5 className="font-medium mb-2">Child {index + 1}</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium">Full Name</label>
                    <Input
                      placeholder="Full Name"
                      value={
                        getTravelerValue("children", index, "name") as string
                      }
                      onChange={(e) =>
                        updateTravelerMetadata("children", index, {
                          name: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Age</label>
                    <Input
                      type="number"
                      min="2"
                      max="17"
                      placeholder="Age"
                      value={
                        getTravelerValue("children", index, "age") as number
                      }
                      onChange={(e) =>
                        updateTravelerMetadata("children", index, {
                          age: parseInt(e.target.value) || 0,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            ))}

            {/* Infants */}
            {Array.from({ length: booking?.infants || 0 }).map((_, index) => (
              <div
                key={`infant-${index}`}
                className="mb-4 p-3 border border-gray-200 rounded-md"
              >
                <h5 className="font-medium mb-2">Infant {index + 1}</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium">Full Name</label>
                    <Input
                      placeholder="Full Name"
                      value={
                        getTravelerValue("infants", index, "name") as string
                      }
                      onChange={(e) =>
                        updateTravelerMetadata("infants", index, {
                          name: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Age (months)</label>
                    <Input
                      type="number"
                      min="0"
                      max="23"
                      placeholder="Age in months"
                      value={
                        getTravelerValue("infants", index, "age") as number
                      }
                      onChange={(e) =>
                        updateTravelerMetadata("infants", index, {
                          age: parseInt(e.target.value) || 0,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Special Requests</label>
            <Controller
              control={form.control}
              name="special_requests"
              render={({ field }) => (
                <>
                  <Textarea
                    {...field}
                    placeholder="Any special requests or requirements"
                  />
                  {form.formState.errors.special_requests && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.special_requests.message}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="secondary" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Guest Information"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateGuestForm;
