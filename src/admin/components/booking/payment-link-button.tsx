import { useState } from "react";
import { Button, FocusModal, Input, Text, Toaster, toast } from "@camped-ai/ui";

interface PaymentLinkButtonProps {
  bookingId: string;
  onSuccess?: (paymentLink: any) => void;
}

const PaymentLinkButton = ({
  bookingId,
  onSuccess,
}: PaymentLinkButtonProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [expiryHours, setExpiryHours] = useState("24");
  const [email, setEmail] = useState("");
  const [paymentLink, setPaymentLink] = useState<any>(null);

  // Generate payment link
  const handleGeneratePaymentLink = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(
        `/admin/hotel-management/bookings/${bookingId}/payment-link`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            expiry_hours: parseInt(expiryHours),
            email: email || undefined,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to generate payment link: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      setPaymentLink(data.payment_link);

      toast.success("Success", {
        description: "Payment link generated successfully",
      });

      if (onSuccess) {
        onSuccess(data.payment_link);
      }
    } catch (error) {
      console.error("Error generating payment link:", error);
      toast.error("Error", {
        description: "Failed to generate payment link. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Copy payment link to clipboard
  const handleCopyLink = () => {
    if (paymentLink?.url) {
      navigator.clipboard.writeText(paymentLink.url);
      toast.success("Copied", {
        description: "Payment link copied to clipboard",
      });
    }
  };

  return (
    <>
      <Toaster />

      <Button variant="secondary" onClick={() => setIsDialogOpen(true)}>
        Generate Payment Link
      </Button>

      <FocusModal open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <FocusModal.Content className="max-w-2xl mx-auto">
          <FocusModal.Header>
            <FocusModal.Title>Generate Payment Link</FocusModal.Title>
          </FocusModal.Header>

          <FocusModal.Body className="flex-1 overflow-y-auto p-6">
            <Text className="text-ui-fg-subtle mb-4">
              Create a payment link for this booking that can be sent to the
              guest.
            </Text>

            <div className="space-y-6">
              <div>
                <Text className="font-medium mb-2">Expiry (hours)</Text>
                <Input
                  type="number"
                  value={expiryHours}
                  onChange={(e) => setExpiryHours(e.target.value)}
                  min="1"
                  max="720"
                />
                <Text className="text-sm text-gray-500 mt-1">
                  The payment link will expire after this many hours. Maximum
                  720 hours (30 days).
                </Text>
              </div>

              <div>
                <Text className="font-medium mb-2">Guest Email (optional)</Text>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
                <Text className="text-sm text-gray-500 mt-1">
                  If provided, this email will be pre-filled on the payment
                  page.
                </Text>
              </div>

              {paymentLink && (
                <div className="mt-4 p-4 bg-gray-50 rounded-md">
                  <Text className="font-medium">Payment Link Generated</Text>
                  <div className="flex items-center mt-2">
                    <Input
                      value={paymentLink.url}
                      readOnly
                      className="flex-1"
                    />
                    <Button
                      variant="secondary"
                      className="ml-2"
                      onClick={handleCopyLink}
                    >
                      Copy
                    </Button>
                  </div>
                  {paymentLink.expires_at && (
                    <Text className="text-sm text-gray-500 mt-2">
                      Expires:{" "}
                      {new Date(paymentLink.expires_at).toLocaleString()}
                    </Text>
                  )}
                </div>
              )}
            </div>
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex justify-end gap-x-2">
              <Button
                variant="secondary"
                onClick={() => setIsDialogOpen(false)}
              >
                Close
              </Button>
              {!paymentLink && (
                <Button
                  onClick={handleGeneratePaymentLink}
                  disabled={isLoading}
                >
                  {isLoading ? "Generating..." : "Generate Link"}
                </Button>
              )}
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};

export default PaymentLinkButton;
