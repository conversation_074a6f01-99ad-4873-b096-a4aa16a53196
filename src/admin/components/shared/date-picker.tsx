import React from "react"
import { Calendar, Popover } from "@camped-ai/ui"
import { format } from "date-fns"

type DatePickerProps = {
  date: Date
  onDateChange: (date: Date) => void
  placeholder?: string
}

const DatePicker: React.FC<DatePickerProps> = ({
  date,
  onDateChange,
  placeholder = "Select date",
}) => {
  return (
    <Popover>
      <Popover.Trigger asChild>
        <button
          className="w-full rounded-md border border-ui-border-base px-4 py-2 text-left"
        >
          {date ? format(date, "MMM dd, yyyy") : placeholder}
        </button>
      </Popover.Trigger>
      <Popover.Content side="bottom" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={onDateChange}
          initialFocus
        />
      </Popover.Content>
    </Popover>
  )
}

export default DatePicker
