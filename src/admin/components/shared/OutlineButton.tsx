import React from 'react';
import { Button } from '@camped-ai/ui';
import '../../styles/button-styles.css';

// This component wraps the Button component from @camped-ai/ui
// and adds the outline-button class to make secondary buttons look more like outline buttons
type OutlineButtonProps = React.ComponentProps<typeof Button>;

const OutlineButton: React.FC<OutlineButtonProps> = ({ 
  children, 
  className = '', 
  variant = 'secondary',
  ...props 
}) => {
  return (
    <Button
      variant={variant}
      className={`outline-button ${className}`}
      {...props}
    >
      {children}
    </Button>
  );
};

export default OutlineButton;
