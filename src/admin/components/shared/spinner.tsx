import React from "react"

const Spinner: React.FC<{ size?: "small" | "medium" | "large" }> = ({
  size = "medium",
}) => {
  const sizeClass = {
    small: "w-4 h-4",
    medium: "w-8 h-8",
    large: "w-12 h-12",
  }[size]

  return (
    <div className="flex items-center justify-center">
      <div
        className={`${sizeClass} animate-spin rounded-full border-2 border-ui-border-base border-t-ui-fg-base`}
      />
    </div>
  )
}

export default Spinner
