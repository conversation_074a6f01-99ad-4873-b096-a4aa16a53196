import React from "react"
import { <PERSON> } from "react-router-dom"
import { ChevronRight } from "lucide-react"

type BreadcrumbProps = {
  previousBreadcrumb?: string
  previousRoute?: string
  currentPage: string
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  previousBreadcrumb,
  previousRoute,
  currentPage,
}) => {
  return (
    <div className="flex items-center gap-x-2 text-ui-fg-subtle">
      {previousBreadcrumb && previousRoute && (
        <>
          <Link
            to={previousRoute}
            className="hover:text-ui-fg-base transition-colors"
          >
            {previousBreadcrumb}
          </Link>
          <ChevronRight className="w-4 h-4" />
        </>
      )}
      <span className="font-medium text-ui-fg-base">{currentPage}</span>
    </div>
  )
}

export default Breadcrumb
