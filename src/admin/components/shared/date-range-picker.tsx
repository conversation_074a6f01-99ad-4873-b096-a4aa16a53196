import React from "react"
import { Calendar, Popover } from "@camped-ai/ui"
import { format } from "date-fns"

type DateRangePickerProps = {
  initialDateFrom: Date
  initialDateTo: Date
  onRangeChange: (range: { from: Date; to: Date }) => void
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  initialDateFrom,
  initialDateTo,
  onRangeChange,
}) => {
  const [date, setDate] = React.useState<{
    from: Date
    to: Date
  }>({
    from: initialDateFrom,
    to: initialDateTo,
  })

  // Update parent component when date range changes
  React.useEffect(() => {
    if (date.from && date.to) {
      onRangeChange(date)
    }
  }, [date, onRangeChange])

  return (
    <Popover>
      <Popover.Trigger asChild>
        <button
          className="w-full rounded-md border border-ui-border-base px-4 py-2 text-left"
        >
          {date.from && date.to
            ? `${format(date.from, "MMM dd")} - ${format(date.to, "MMM dd")}`
            : "Select date range"}
        </button>
      </Popover.Trigger>
      <Popover.Content side="bottom" align="start" className="w-auto">
        <Calendar
          mode="range"
          selected={date}
          onSelect={setDate}
          numberOfMonths={2}
          initialFocus
        />
      </Popover.Content>
    </Popover>
  )
}

export default DateRangePicker
