import React from "react"
import { But<PERSON> } from "@camped-ai/ui"
import { useNavigate } from "react-router-dom"
import { CalendarDays } from "lucide-react"

type RoomAvailabilityButtonProps = {
  roomConfigId: string
}

const RoomAvailabilityButton: React.FC<RoomAvailabilityButtonProps> = ({
  roomConfigId,
}) => {
  const navigate = useNavigate()

  const handleClick = async () => {
    try {
      // Fetch the product to get the hotel ID
      const response = await fetch(`/admin/products/${roomConfigId}`);
      if (response.ok) {
        const data = await response.json();
        const hotelId = data.product?.metadata?.hotel_id;

        if (hotelId) {
          navigate(`/hotel-management/hotels/${hotelId}/availability`);
        } else {
          console.error('Hotel ID not found in product metadata');
          // Fallback to the hotel management page
          navigate('/hotel-management/hotels');
        }
      } else {
        console.error('Failed to fetch product details');
        // Fallback to the hotel management page
        navigate('/hotel-management/hotels');
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      // Fallback to the hotel management page
      navigate('/hotel-management/hotels');
    }
  }

  return (
    <Button
      variant="secondary"
      onClick={handleClick}
      className="flex items-center gap-x-2"
    >
      <CalendarDays className="w-4 h-4" />
      <span>Manage Availability</span>
    </Button>
  )
}

export default RoomAvailabilityButton
