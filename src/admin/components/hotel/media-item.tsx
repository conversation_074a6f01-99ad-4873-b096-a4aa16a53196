import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { IconButton, Text } from "@camped-ai/ui";
import {
  DotsSix,
  StackPerspective,
  Trash,
  XMark,
  ThumbnailBadge,
} from "@camped-ai/icons";
import { ActionMenu } from "../ActionMenu";
import ThumbnailPreview from "./thumbnail-preview";

export type MediaField = {
  isThumbnail: boolean;
  url: string;
  id?: string | undefined;
  file?: File;
  field_id: string;
};

const formatFileSize = (bytes: number, decimalPlaces: number = 2): string => {
  if (bytes === 0) {
    return "0 Bytes";
  }

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return (
    parseFloat((bytes / Math.pow(k, i)).toFixed(decimalPlaces)) + " " + sizes[i]
  );
};

type MediaItemProps = {
  field: MediaField;
  onDelete: () => void;
  onMakeThumbnail: () => void;
};

const MediaItem = ({ field, onDelete, onMakeThumbnail }: MediaItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: field.field_id,
    disabled: false,
  });

  const style = {
    opacity: isDragging ? 0.4 : 1,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: "grab",
  };

  if (!field.file && !field.url) {
    return null;
  }

  const fileName = field.file ? field.file.name : field.id || 'Existing Image';
  const fileSize = field.file ? formatFileSize(field.file.size) : '';

  return (
    <li
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="bg-ui-bg-component shadow-elevation-card-rest flex items-center justify-between rounded-lg px-3 py-2"
    >
      <div className="flex items-center gap-x-2">
        <IconButton
          variant="transparent"
          size="small"
          className="cursor-grab touch-none active:cursor-grabbing"
          ref={setActivatorNodeRef}
        >
          <DotsSix className="text-ui-fg-muted" />
        </IconButton>
        <div className="flex items-center gap-x-3">
          <div className="bg-ui-bg-base h-10 w-[30px] overflow-hidden rounded-md">
            <ThumbnailPreview url={field.file ? URL.createObjectURL(field.file) : field.url} />
          </div>
          <div className="flex flex-col">
            <Text size="small" leading="compact">
              {fileName}
            </Text>
            <div className="flex items-center gap-x-1">
              {field.isThumbnail && <ThumbnailBadge />}
              <Text
                size="xsmall"
                leading="compact"
                className="text-ui-fg-subtle"
              >
                {fileSize}
              </Text>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-x-1">
        <ActionMenu
          groups={[
            {
              actions: [
                {
                  label: "Make Thumbnail",
                  icon: <StackPerspective />,
                  onClick: onMakeThumbnail,
                },
              ],
            },
            {
              actions: [
                {
                  label: "Delete",
                  icon: <Trash />,
                  onClick: onDelete,
                },
              ],
            },
          ]}
        />
        <IconButton
          type="button"
          size="small"
          variant="transparent"
          onClick={onDelete}
        >
          <XMark />
        </IconButton>
      </div>
    </li>
  );
};

export default MediaItem;
