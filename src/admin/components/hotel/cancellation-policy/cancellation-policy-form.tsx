import React, { useState } from "react";
import {
  Button,
  Text,
  Heading,
  Input,
  Checkbox,
  Textarea,
  toast,
} from "@camped-ai/ui";
import { CancellationPolicyData } from "../../../types";

type CancellationPolicyFormProps = {
  initialData?: Partial<CancellationPolicyData>;
  hotelId: string;
  onSubmit: (data: Partial<CancellationPolicyData>) => Promise<void>;
  onCancel: () => void;
};

const CancellationPolicyForm: React.FC<CancellationPolicyFormProps> = ({
  initialData,
  hotelId,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState<Partial<CancellationPolicyData>>({
    name: "",
    description: "",
    hotel_id: hotelId,
    days_before_checkin: 0,
    refund_type: "percentage",
    refund_amount: 0,
    is_active: true,
    ...initialData,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const target = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: target.checked,
      });
    } else if (name === "days_before_checkin" || name === "refund_amount") {
      setFormData({
        ...formData,
        [name]: parseFloat(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Submit the form data to the parent component
      await onSubmit(formData);
    } catch (error) {
      console.error("Error saving cancellation policy:", error);
      toast.error("Error", {
        description: "Failed to save cancellation policy",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <Heading level="h3">Cancellation Policy Details</Heading>
            <Text size="small" className="text-gray-500 mt-1">
              Define when and how guests can cancel their bookings
            </Text>
          </div>

          <div className="space-y-4">
            <div>
              <Text className="font-medium mb-1">Policy Name</Text>
              <Input
                name="name"
                value={formData.name || ""}
                onChange={handleChange}
                placeholder="e.g., Standard Cancellation Policy"
                required
              />
            </div>

            <div>
              <Text className="font-medium mb-1">Description</Text>
              <Textarea
                name="description"
                value={formData.description || ""}
                onChange={handleChange}
                placeholder="Describe the cancellation policy details"
                rows={3}
              />
            </div>

            <div>
              <Text className="font-medium mb-1">Days Before Check-in</Text>
              <Input
                type="number"
                name="days_before_checkin"
                value={formData.days_before_checkin || 0}
                onChange={handleChange}
                min={0}
                required
              />
              <Text size="small" className="text-gray-500 mt-1">
                Number of days before check-in when this policy applies
              </Text>
            </div>

            <div>
              <Text className="font-medium mb-1">Refund Type</Text>
              <select
                name="refund_type"
                value={formData.refund_type || "percentage"}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
                <option value="no_refund">No Refund</option>
              </select>
            </div>

            {formData.refund_type !== "no_refund" && (
              <div>
                <Text className="font-medium mb-1">
                  {formData.refund_type === "percentage"
                    ? "Refund Percentage"
                    : "Refund Amount"}
                </Text>
                <Input
                  type="number"
                  name="refund_amount"
                  value={formData.refund_amount || 0}
                  onChange={handleChange}
                  min={0}
                  max={formData.refund_type === "percentage" ? 100 : undefined}
                  required
                />
                {formData.refund_type === "percentage" && (
                  <Text size="small" className="text-gray-500 mt-1">
                    Percentage of the booking amount to refund (0-100)
                  </Text>
                )}
              </div>
            )}

            <div className="flex items-center">
              <Checkbox
                name="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    is_active: checked === true,
                  })
                }
                id="is_active"
              />
              <label htmlFor="is_active" className="ml-2 cursor-pointer">
                <Text>Active</Text>
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" isLoading={isSubmitting}>
              {initialData?.id ? "Update Policy" : "Create Policy"}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CancellationPolicyForm;
