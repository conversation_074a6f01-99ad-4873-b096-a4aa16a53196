import React, { useState, useEffect } from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Prompt,
  Input,
  Label,
} from "@camped-ai/ui";
import {
  useAdminHotelOccupancyConfigs,
  useAdminCreateHotelOccupancyConfig,
  useAdminUpdateHotelOccupancyConfig,
  useAdminDeleteHotelOccupancyConfig
} from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";
import { PlusCircle, Edit, Trash } from "lucide-react";

type OccupancyConfig = {
  id: string;
  name: string;
  type: string;
  min_age: number | null;
  max_age: number | null;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
};

type OccupancyConfigsManagerProps = {
  hotelId: string;
};

const OccupancyConfigsManager: React.FC<OccupancyConfigsManagerProps> = ({
  hotelId,
}) => {
  const { occupancyConfigs, isLoading, refetch } = useAdminHotelOccupancyConfigs(hotelId);
  const { createOccupancyConfig } = useAdminCreateHotelOccupancyConfig();
  const { updateOccupancyConfig } = useAdminUpdateHotelOccupancyConfig();
  const { deleteOccupancyConfig } = useAdminDeleteHotelOccupancyConfig();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentOccupancyConfig, setCurrentOccupancyConfig] = useState<OccupancyConfig | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: "EXTRA_ADULT",
    min_age: 0,
    max_age: 99,
    min_occupancy: 1,
    max_occupancy: 1,
    is_default: false,
  });

  const handleOpenDialog = (occupancyConfig?: OccupancyConfig) => {
    if (occupancyConfig) {
      setCurrentOccupancyConfig(occupancyConfig);
      setFormData({
        name: occupancyConfig.name,
        type: occupancyConfig.type,
        min_age: occupancyConfig.min_age || 0,
        max_age: occupancyConfig.max_age || 99,
        min_occupancy: occupancyConfig.min_occupancy,
        max_occupancy: occupancyConfig.max_occupancy,
        is_default: occupancyConfig.is_default,
      });
    } else {
      setCurrentOccupancyConfig(null);
      setFormData({
        name: "",
        type: "EXTRA_ADULT",
        min_age: 0,
        max_age: 99,
        min_occupancy: 1,
        max_occupancy: 1,
        is_default: false,
      });
    }
    setIsDialogOpen(true);
  };

  const handleOpenDeleteDialog = (occupancyConfig: OccupancyConfig) => {
    setCurrentOccupancyConfig(occupancyConfig);
    setIsDeleteDialogOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : type === "number"
          ? Number(value)
          : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (currentOccupancyConfig) {
        // Update existing occupancy config
        await updateOccupancyConfig(
          hotelId,
          currentOccupancyConfig.id,
          formData
        );
        toast.success("Success", {
          description: "Occupancy configuration updated successfully",
        });
      } else {
        // Create new occupancy config
        await createOccupancyConfig(
          hotelId,
          formData
        );
        toast.success("Success", {
          description: "Occupancy configuration created successfully",
        });
      }

      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error saving occupancy configuration:", error);
      toast.error("Error", {
        description: "Failed to save occupancy configuration",
      });
    }
  };

  const handleDelete = async () => {
    if (!currentOccupancyConfig) return;

    try {
      console.log("Deleting occupancy config:", currentOccupancyConfig.id);
      const result = await deleteOccupancyConfig(
        hotelId,
        currentOccupancyConfig.id
      );
      console.log("Delete result:", result);

      toast.success("Success", {
        description: "Occupancy configuration deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Error deleting occupancy configuration:", error);
      toast.error("Error", {
        description: "Failed to delete occupancy configuration",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-100 rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Heading level="h2">Occupancy Configurations</Heading>
        <Text className="text-gray-600">
          Manage occupancy configurations for your hotel
        </Text>
      </div>

      <div className="flex justify-end mb-4">
        <Button
          variant="primary"
          onClick={() => handleOpenDialog()}
          className="flex items-center gap-2"
        >
          <PlusCircle className="w-4 h-4" />
          Add Occupancy Configuration
        </Button>
      </div>

      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Age Range
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Occupancy
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Default
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {occupancyConfigs.map((occupancyConfig) => (
              <tr key={occupancyConfig.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {occupancyConfig.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {occupancyConfig.type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {occupancyConfig.min_age !== null && occupancyConfig.max_age !== null
                    ? `${occupancyConfig.min_age} - ${occupancyConfig.max_age} years`
                    : "N/A"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {occupancyConfig.min_occupancy} - {occupancyConfig.max_occupancy} persons
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {occupancyConfig.is_default ? "Yes" : "No"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="secondary"
                      onClick={() => handleOpenDialog(occupancyConfig)}
                    >
                      <Edit className="w-4 h-4 mr-1" /> Edit
                    </Button>
                    <Button
                      variant="danger"
                      onClick={() => handleOpenDeleteDialog(occupancyConfig)}
                    >
                      <Trash className="w-4 h-4 mr-1" /> Delete
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Create/Edit Dialog */}
      <Prompt open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              {currentOccupancyConfig ? "Edit Occupancy Configuration" : "Add Occupancy Configuration"}
            </Prompt.Title>
          </Prompt.Header>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <select
                    id="type"
                    name="type"
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={formData.type}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="BASE_1">1 Person (Base)</option>
                    <option value="BASE_2">2 Persons (Base)</option>
                    <option value="EXTRA_ADULT">Extra Adult</option>
                    <option value="CHILD">Child</option>
                    <option value="INFANT">Infant</option>
                    <option value="CUSTOM">Custom</option>
                  </select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="min_age">Min Age</Label>
                    <Input
                      id="min_age"
                      name="min_age"
                      type="number"
                      min="0"
                      value={formData.min_age}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_age">Max Age</Label>
                    <Input
                      id="max_age"
                      name="max_age"
                      type="number"
                      min="0"
                      value={formData.max_age}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="min_occupancy">Min Occupancy</Label>
                    <Input
                      id="min_occupancy"
                      name="min_occupancy"
                      type="number"
                      min="1"
                      value={formData.min_occupancy}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_occupancy">Max Occupancy</Label>
                    <Input
                      id="max_occupancy"
                      name="max_occupancy"
                      type="number"
                      min="1"
                      value={formData.max_occupancy}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_default"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <Label htmlFor="is_default">Default</Label>
                </div>
              </div>
            </div>
            <Prompt.Footer>
              <Button variant="secondary" type="button" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                {currentOccupancyConfig ? "Update" : "Create"}
              </Button>
            </Prompt.Footer>
          </form>
        </Prompt.Content>
      </Prompt>

      {/* Delete Confirmation Dialog */}
      <Prompt open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Occupancy Configuration</Prompt.Title>
          </Prompt.Header>
          <div className="py-4">
            <p>
              Are you sure you want to delete the occupancy configuration "{currentOccupancyConfig?.name}"?
              This action cannot be undone.
            </p>
          </div>
          <Prompt.Footer>
            <Button variant="secondary" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              Delete
            </Button>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default OccupancyConfigsManager;
