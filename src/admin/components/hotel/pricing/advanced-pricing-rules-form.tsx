import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Toaster,
  toast,
  Select,
  Checkbox,
  DatePicker,
} from "@camped-ai/ui";
import { Calendar, DollarSign, Plus, Trash2, Edit } from "lucide-react";
import { format } from "date-fns";

interface DayOfWeekRule {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  adjustment_type: 'percentage' | 'fixed';
  adjustment_value: number;
}

interface SpecificDateRule {
  date: string;
  adjustment_type: 'percentage' | 'fixed';
  adjustment_value: number;
  reason?: string;
}

interface MinStayByDayRule {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  min_nights: number;
}

interface AdvancedRules {
  day_of_week?: DayOfWeekRule[];
  specific_dates?: SpecificDateRule[];
  min_stay_by_day?: MinStayByDayRule[];
  apply_to_room_configs?: string[];
}

interface RoomConfig {
  id: string;
  title: string;
  name?: string;
}

interface AdvancedPricingRulesFormProps {
  roomConfigs: RoomConfig[];
  currentRoomConfigId: string;
  initialRules?: AdvancedRules;
  onChange: (rules: AdvancedRules) => void;
}

const AdvancedPricingRulesForm: React.FC<AdvancedPricingRulesFormProps> = ({
  roomConfigs,
  currentRoomConfigId,
  initialRules,
  onChange,
}) => {
  const [dayOfWeekRules, setDayOfWeekRules] = useState<DayOfWeekRule[]>(
    initialRules?.day_of_week || []
  );
  const [specificDateRules, setSpecificDateRules] = useState<SpecificDateRule[]>(
    initialRules?.specific_dates || []
  );
  const [minStayByDayRules, setMinStayByDayRules] = useState<MinStayByDayRule[]>(
    initialRules?.min_stay_by_day || []
  );
  const [selectedRoomConfigs, setSelectedRoomConfigs] = useState<string[]>(
    initialRules?.apply_to_room_configs || []
  );

  // Current editing states
  const [currentDayRule, setCurrentDayRule] = useState<DayOfWeekRule | null>(null);
  const [currentDateRule, setCurrentDateRule] = useState<SpecificDateRule | null>(null);
  const [currentMinStayRule, setCurrentMinStayRule] = useState<MinStayByDayRule | null>(null);
  
  // Edit index states
  const [editingDayIndex, setEditingDayIndex] = useState<number | null>(null);
  const [editingDateIndex, setEditingDateIndex] = useState<number | null>(null);
  const [editingMinStayIndex, setEditingMinStayIndex] = useState<number | null>(null);

  // Update parent component when rules change
  useEffect(() => {
    const rules: AdvancedRules = {};
    
    if (dayOfWeekRules.length > 0) {
      rules.day_of_week = dayOfWeekRules;
    }
    
    if (specificDateRules.length > 0) {
      rules.specific_dates = specificDateRules;
    }
    
    if (minStayByDayRules.length > 0) {
      rules.min_stay_by_day = minStayByDayRules;
    }
    
    if (selectedRoomConfigs.length > 0) {
      rules.apply_to_room_configs = selectedRoomConfigs;
    }
    
    onChange(rules);
  }, [dayOfWeekRules, specificDateRules, minStayByDayRules, selectedRoomConfigs, onChange]);

  // Day of week rule handlers
  const handleAddDayRule = () => {
    if (!currentDayRule) return;
    
    if (editingDayIndex !== null) {
      // Update existing rule
      const updatedRules = [...dayOfWeekRules];
      updatedRules[editingDayIndex] = currentDayRule;
      setDayOfWeekRules(updatedRules);
    } else {
      // Add new rule
      setDayOfWeekRules([...dayOfWeekRules, currentDayRule]);
    }
    
    setCurrentDayRule(null);
    setEditingDayIndex(null);
  };
  
  const handleEditDayRule = (index: number) => {
    setCurrentDayRule(dayOfWeekRules[index]);
    setEditingDayIndex(index);
  };
  
  const handleRemoveDayRule = (index: number) => {
    const updatedRules = [...dayOfWeekRules];
    updatedRules.splice(index, 1);
    setDayOfWeekRules(updatedRules);
  };

  // Specific date rule handlers
  const handleAddDateRule = () => {
    if (!currentDateRule) return;
    
    if (editingDateIndex !== null) {
      // Update existing rule
      const updatedRules = [...specificDateRules];
      updatedRules[editingDateIndex] = currentDateRule;
      setSpecificDateRules(updatedRules);
    } else {
      // Add new rule
      setSpecificDateRules([...specificDateRules, currentDateRule]);
    }
    
    setCurrentDateRule(null);
    setEditingDateIndex(null);
  };
  
  const handleEditDateRule = (index: number) => {
    setCurrentDateRule(specificDateRules[index]);
    setEditingDateIndex(index);
  };
  
  const handleRemoveDateRule = (index: number) => {
    const updatedRules = [...specificDateRules];
    updatedRules.splice(index, 1);
    setSpecificDateRules(updatedRules);
  };

  // Min stay by day rule handlers
  const handleAddMinStayRule = () => {
    if (!currentMinStayRule) return;
    
    if (editingMinStayIndex !== null) {
      // Update existing rule
      const updatedRules = [...minStayByDayRules];
      updatedRules[editingMinStayIndex] = currentMinStayRule;
      setMinStayByDayRules(updatedRules);
    } else {
      // Add new rule
      setMinStayByDayRules([...minStayByDayRules, currentMinStayRule]);
    }
    
    setCurrentMinStayRule(null);
    setEditingMinStayIndex(null);
  };
  
  const handleEditMinStayRule = (index: number) => {
    setCurrentMinStayRule(minStayByDayRules[index]);
    setEditingMinStayIndex(index);
  };
  
  const handleRemoveMinStayRule = (index: number) => {
    const updatedRules = [...minStayByDayRules];
    updatedRules.splice(index, 1);
    setMinStayByDayRules(updatedRules);
  };

  // Room config selection handler
  const handleRoomConfigChange = (configId: string) => {
    if (configId === currentRoomConfigId) return; // Can't select the current room config
    
    if (selectedRoomConfigs.includes(configId)) {
      // Remove from selection
      setSelectedRoomConfigs(selectedRoomConfigs.filter(id => id !== configId));
    } else {
      // Add to selection
      setSelectedRoomConfigs([...selectedRoomConfigs, configId]);
    }
  };

  const formatDay = (day: string) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  const formatAdjustment = (type: 'percentage' | 'fixed', value: number) => {
    if (type === 'percentage') {
      return `${value}%`;
    } else {
      return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value / 100);
    }
  };

  return (
    <div className="space-y-6">
      <Toaster />
      
      {/* Day of Week Pricing */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-sm font-medium text-gray-700">Day of Week Pricing</h3>
          <Button
            variant="secondary"
            size="small"
            onClick={() => {
              setCurrentDayRule({
                day: 'monday',
                adjustment_type: 'percentage',
                adjustment_value: 0
              });
              setEditingDayIndex(null);
            }}
            className="text-xs"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Rule
          </Button>
        </div>
        
        {dayOfWeekRules.length === 0 ? (
          <div className="text-center py-3 text-gray-500 text-sm">
            No day of week pricing rules added.
          </div>
        ) : (
          <div className="space-y-2">
            {dayOfWeekRules.map((rule, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-white rounded-md border border-gray-200">
                <div>
                  <div className="font-medium">{formatDay(rule.day)}</div>
                  <div className="text-sm text-gray-500">
                    {rule.adjustment_type === 'percentage' ? 'Percentage' : 'Fixed'} adjustment: {formatAdjustment(rule.adjustment_type, rule.adjustment_value)}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleEditDayRule(index)}
                    className="text-xs"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleRemoveDayRule(index)}
                    className="text-xs text-red-600"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {currentDayRule && (
          <div className="mt-4 p-4 bg-white rounded-md border border-gray-200">
            <h4 className="text-xs font-medium mb-3">{editingDayIndex !== null ? 'Edit' : 'Add'} Day Rule</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Day</label>
                <select
                  value={currentDayRule.day}
                  onChange={(e) => setCurrentDayRule({
                    ...currentDayRule,
                    day: e.target.value as any
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                >
                  <option value="monday">Monday</option>
                  <option value="tuesday">Tuesday</option>
                  <option value="wednesday">Wednesday</option>
                  <option value="thursday">Thursday</option>
                  <option value="friday">Friday</option>
                  <option value="saturday">Saturday</option>
                  <option value="sunday">Sunday</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Adjustment Type</label>
                <select
                  value={currentDayRule.adjustment_type}
                  onChange={(e) => setCurrentDayRule({
                    ...currentDayRule,
                    adjustment_type: e.target.value as any
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  {currentDayRule.adjustment_type === 'percentage' ? 'Percentage (%)' : 'Amount'}
                </label>
                <Input
                  type="number"
                  value={currentDayRule.adjustment_value}
                  onChange={(e) => setCurrentDayRule({
                    ...currentDayRule,
                    adjustment_value: parseFloat(e.target.value) || 0
                  })}
                  placeholder={currentDayRule.adjustment_type === 'percentage' ? "e.g., 10" : "e.g., 1000"}
                  className="bg-white border-gray-300"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => {
                  setCurrentDayRule(null);
                  setEditingDayIndex(null);
                }}
                className="text-xs"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={handleAddDayRule}
                className="text-xs"
              >
                {editingDayIndex !== null ? 'Update' : 'Add'} Rule
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* Specific Date Pricing */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-sm font-medium text-gray-700">Specific Date Pricing</h3>
          <Button
            variant="secondary"
            size="small"
            onClick={() => {
              setCurrentDateRule({
                date: format(new Date(), 'yyyy-MM-dd'),
                adjustment_type: 'percentage',
                adjustment_value: 0
              });
              setEditingDateIndex(null);
            }}
            className="text-xs"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Date
          </Button>
        </div>
        
        {specificDateRules.length === 0 ? (
          <div className="text-center py-3 text-gray-500 text-sm">
            No specific date pricing rules added.
          </div>
        ) : (
          <div className="space-y-2">
            {specificDateRules.map((rule, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-white rounded-md border border-gray-200">
                <div>
                  <div className="font-medium">{new Date(rule.date).toLocaleDateString()}</div>
                  <div className="text-sm text-gray-500">
                    {rule.adjustment_type === 'percentage' ? 'Percentage' : 'Fixed'} adjustment: {formatAdjustment(rule.adjustment_type, rule.adjustment_value)}
                    {rule.reason && ` • ${rule.reason}`}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleEditDateRule(index)}
                    className="text-xs"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleRemoveDateRule(index)}
                    className="text-xs text-red-600"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {currentDateRule && (
          <div className="mt-4 p-4 bg-white rounded-md border border-gray-200">
            <h4 className="text-xs font-medium mb-3">{editingDateIndex !== null ? 'Edit' : 'Add'} Date Rule</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Date</label>
                <Input
                  type="date"
                  value={currentDateRule.date}
                  onChange={(e) => setCurrentDateRule({
                    ...currentDateRule,
                    date: e.target.value
                  })}
                  className="bg-white border-gray-300"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Reason (Optional)</label>
                <Input
                  type="text"
                  value={currentDateRule.reason || ''}
                  onChange={(e) => setCurrentDateRule({
                    ...currentDateRule,
                    reason: e.target.value
                  })}
                  placeholder="e.g., New Year's Eve"
                  className="bg-white border-gray-300"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Adjustment Type</label>
                <select
                  value={currentDateRule.adjustment_type}
                  onChange={(e) => setCurrentDateRule({
                    ...currentDateRule,
                    adjustment_type: e.target.value as any
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  {currentDateRule.adjustment_type === 'percentage' ? 'Percentage (%)' : 'Amount'}
                </label>
                <Input
                  type="number"
                  value={currentDateRule.adjustment_value}
                  onChange={(e) => setCurrentDateRule({
                    ...currentDateRule,
                    adjustment_value: parseFloat(e.target.value) || 0
                  })}
                  placeholder={currentDateRule.adjustment_type === 'percentage' ? "e.g., 10" : "e.g., 1000"}
                  className="bg-white border-gray-300"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => {
                  setCurrentDateRule(null);
                  setEditingDateIndex(null);
                }}
                className="text-xs"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={handleAddDateRule}
                className="text-xs"
              >
                {editingDateIndex !== null ? 'Update' : 'Add'} Rule
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* Minimum Stay by Day */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-sm font-medium text-gray-700">Minimum Stay by Day</h3>
          <Button
            variant="secondary"
            size="small"
            onClick={() => {
              setCurrentMinStayRule({
                day: 'friday',
                min_nights: 2
              });
              setEditingMinStayIndex(null);
            }}
            className="text-xs"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Rule
          </Button>
        </div>
        
        {minStayByDayRules.length === 0 ? (
          <div className="text-center py-3 text-gray-500 text-sm">
            No minimum stay rules added.
          </div>
        ) : (
          <div className="space-y-2">
            {minStayByDayRules.map((rule, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-white rounded-md border border-gray-200">
                <div>
                  <div className="font-medium">{formatDay(rule.day)}</div>
                  <div className="text-sm text-gray-500">
                    Minimum stay: {rule.min_nights} night{rule.min_nights !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleEditMinStayRule(index)}
                    className="text-xs"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => handleRemoveMinStayRule(index)}
                    className="text-xs text-red-600"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {currentMinStayRule && (
          <div className="mt-4 p-4 bg-white rounded-md border border-gray-200">
            <h4 className="text-xs font-medium mb-3">{editingMinStayIndex !== null ? 'Edit' : 'Add'} Minimum Stay Rule</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Day</label>
                <select
                  value={currentMinStayRule.day}
                  onChange={(e) => setCurrentMinStayRule({
                    ...currentMinStayRule,
                    day: e.target.value as any
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
                >
                  <option value="monday">Monday</option>
                  <option value="tuesday">Tuesday</option>
                  <option value="wednesday">Wednesday</option>
                  <option value="thursday">Thursday</option>
                  <option value="friday">Friday</option>
                  <option value="saturday">Saturday</option>
                  <option value="sunday">Sunday</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Minimum Nights</label>
                <Input
                  type="number"
                  value={currentMinStayRule.min_nights}
                  onChange={(e) => setCurrentMinStayRule({
                    ...currentMinStayRule,
                    min_nights: parseInt(e.target.value) || 1
                  })}
                  min="1"
                  className="bg-white border-gray-300"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={() => {
                  setCurrentMinStayRule(null);
                  setEditingMinStayIndex(null);
                }}
                className="text-xs"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={handleAddMinStayRule}
                className="text-xs"
              >
                {editingMinStayIndex !== null ? 'Update' : 'Add'} Rule
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* Apply to Multiple Room Types */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
        <h3 className="text-sm font-medium text-gray-700 mb-4">Apply to Multiple Room Types</h3>
        
        <div className="space-y-2">
          {roomConfigs
            .filter(config => config.id !== currentRoomConfigId)
            .map(config => (
              <div key={config.id} className="flex items-center p-3 bg-white rounded-md border border-gray-200">
                <Checkbox
                  checked={selectedRoomConfigs.includes(config.id)}
                  onCheckedChange={() => handleRoomConfigChange(config.id)}
                  id={`room-config-${config.id}`}
                />
                <label htmlFor={`room-config-${config.id}`} className="ml-2 text-sm">
                  {config.title || config.name}
                </label>
              </div>
            ))}
          
          {roomConfigs.filter(config => config.id !== currentRoomConfigId).length === 0 && (
            <div className="text-center py-3 text-gray-500 text-sm">
              No other room configurations available.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedPricingRulesForm;
