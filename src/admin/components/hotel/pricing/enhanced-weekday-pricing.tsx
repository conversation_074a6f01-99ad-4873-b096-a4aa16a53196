import React, { useState, useEffect } from "react";
import {
  But<PERSON>,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Input,
  Label,
} from "@camped-ai/ui";
import { PlusCircle, Trash, DollarSign, Euro, PoundSterling, Copy } from "lucide-react";
import { useAdminHotelOccupancyConfigs } from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";
import { useAdminHotelMealPlans } from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import { useAdminHotelPricing, WeekdayPrices } from "../../../hooks/hotel/use-admin-hotel-pricing";

// Helper function to generate a unique ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

type WeekdayPricingRow = {
  id: string;
  occupancyTypeId: string;
  mealPlanId: string;
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
  defaultPrice?: number;
};

type EnhancedWeekdayPricingProps = {
  hotelId: string;
  roomConfigId: string;
  roomConfigTitle?: string;
  initialData?: any;
  onSave: (data: any) => void;
  onCancel: () => void;
};

const EnhancedWeekdayPricing: React.FC<EnhancedWeekdayPricingProps> = ({
  hotelId,
  roomConfigId,
  roomConfigTitle,
  initialData,
  onSave,
  onCancel,
}) => {
  const { occupancyConfigs, isLoading: isLoadingOccupancy } = useAdminHotelOccupancyConfigs(hotelId);
  const { mealPlans, isLoading: isLoadingMealPlans } = useAdminHotelMealPlans(hotelId);
  const { savePricing, isLoading: isSaving } = useAdminHotelPricing();
  const [pricingRows, setPricingRows] = useState<WeekdayPricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState("USD");
  const [isLoading, setIsLoading] = useState(true);

  const weekdays = [
    { id: "mon", name: "MONDAY" },
    { id: "tue", name: "TUESDAY" },
    { id: "wed", name: "WEDNESDAY" },
    { id: "thu", name: "THURSDAY" },
    { id: "fri", name: "FRIDAY" },
    { id: "sat", name: "SATURDAY" },
  ];

  const sunday = { id: "sun", name: "SUNDAY" };

  const currencySymbols: Record<string, string> = {
    USD: "$",
    EUR: "€",
    GBP: "£",
  };

  // Initialize data when components load
  useEffect(() => {
    if (!isLoadingOccupancy && !isLoadingMealPlans) {
      initializeData();
    }
  }, [isLoadingOccupancy, isLoadingMealPlans, initialData]);

  const initializeData = () => {
    setIsLoading(true);

    // If we have initial data, use it
    if (initialData && initialData.weekday_rules && initialData.weekday_rules.length > 0) {
      const loadedRows = initialData.weekday_rules.map((rule: any) => {
        // Get the Monday price to use as default
        const mondayPrice = (rule.weekday_prices?.mon || 0) / 100;

        return {
          id: rule.id || generateId(),
          occupancyTypeId: rule.occupancy_type_id,
          mealPlanId: rule.meal_plan_id,
          mon: mondayPrice, // Convert from cents
          tue: (rule.weekday_prices?.tue || 0) / 100,
          wed: (rule.weekday_prices?.wed || 0) / 100,
          thu: (rule.weekday_prices?.thu || 0) / 100,
          fri: (rule.weekday_prices?.fri || 0) / 100,
          sat: (rule.weekday_prices?.sat || 0) / 100,
          sun: (rule.weekday_prices?.sun || 0) / 100,
          defaultPrice: mondayPrice, // Set default price to Monday's price
        };
      });

      setPricingRows(loadedRows);

      // Set currency code
      if (initialData.currency_code) {
        setCurrencyCode(initialData.currency_code);
      }
    } else {
      // If no initial data, add one empty row
      addNewRow();
    }

    setIsLoading(false);
  };

  const addNewRow = () => {
    // Find default occupancy type and meal plan
    const defaultOccupancy = occupancyConfigs.find(oc => oc.is_default) || occupancyConfigs[0];
    const defaultMealPlan = mealPlans.find(mp => mp.is_default) || mealPlans[0];

    // Check if we already have this combination
    if (defaultOccupancy && defaultMealPlan) {
      const existingCombination = pricingRows.some(
        row => row.occupancyTypeId === defaultOccupancy.id && row.mealPlanId === defaultMealPlan.id
      );

      if (existingCombination) {
        // Find an unused combination
        let foundUnusedCombination = false;
        let newOccupancyTypeId = "";
        let newMealPlanId = "";

        // Try to find an unused combination
        for (const oc of occupancyConfigs) {
          for (const mp of mealPlans) {
            const exists = pricingRows.some(
              row => row.occupancyTypeId === oc.id && row.mealPlanId === mp.id
            );

            if (!exists) {
              newOccupancyTypeId = oc.id;
              newMealPlanId = mp.id;
              foundUnusedCombination = true;
              break;
            }
          }
          if (foundUnusedCombination) break;
        }

        // If all combinations are used, show a toast
        if (!foundUnusedCombination) {
          toast.error("Error", {
            description: "All possible combinations of occupancy type and meal plan are already in use",
          });
          return;
        }

        const newRow: WeekdayPricingRow = {
          id: generateId(),
          occupancyTypeId: newOccupancyTypeId,
          mealPlanId: newMealPlanId,
          mon: 0,
          tue: 0,
          wed: 0,
          thu: 0,
          fri: 0,
          sat: 0,
          sun: 0,
        };

        setPricingRows(prev => [...prev, newRow]);
      } else {
        // Use the default combination
        const newRow: WeekdayPricingRow = {
          id: generateId(),
          occupancyTypeId: defaultOccupancy.id,
          mealPlanId: defaultMealPlan.id,
          mon: 0,
          tue: 0,
          wed: 0,
          thu: 0,
          fri: 0,
          sat: 0,
          sun: 0,
        };

        setPricingRows(prev => [...prev, newRow]);
      }
    } else {
      // Fallback if no defaults are found
      const newRow: WeekdayPricingRow = {
        id: generateId(),
        occupancyTypeId: "",
        mealPlanId: "",
        mon: 0,
        tue: 0,
        wed: 0,
        thu: 0,
        fri: 0,
        sat: 0,
        sun: 0,
      };

      setPricingRows(prev => [...prev, newRow]);
    }
  };

  const removeRow = (id: string) => {
    setPricingRows(prev => prev.filter(row => row.id !== id));
  };

  const handlePriceChange = (rowId: string, day: string, value: number) => {
    setPricingRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, [day]: value }
          : row
      )
    );
  };

  const handleOccupancyChange = (rowId: string, occupancyTypeId: string) => {
    // Get the current row to find its meal plan
    const currentRow = pricingRows.find(row => row.id === rowId);
    if (!currentRow) return;

    // Check if this combination already exists in another row
    const isDuplicate = pricingRows.some(
      row => row.id !== rowId &&
             row.occupancyTypeId === occupancyTypeId &&
             row.mealPlanId === currentRow.mealPlanId
    );

    if (isDuplicate) {
      toast.error("Error", {
        description: "This combination of occupancy type and meal plan already exists",
      });
      return;
    }

    setPricingRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, occupancyTypeId }
          : row
      )
    );
  };

  const handleMealPlanChange = (rowId: string, mealPlanId: string) => {
    // Get the current row to find its occupancy type
    const currentRow = pricingRows.find(row => row.id === rowId);
    if (!currentRow) return;

    // Check if this combination already exists in another row
    const isDuplicate = pricingRows.some(
      row => row.id !== rowId &&
             row.mealPlanId === mealPlanId &&
             row.occupancyTypeId === currentRow.occupancyTypeId
    );

    if (isDuplicate) {
      toast.error("Error", {
        description: "This combination of occupancy type and meal plan already exists",
      });
      return;
    }

    setPricingRows(prev =>
      prev.map(row =>
        row.id === rowId
          ? { ...row, mealPlanId }
          : row
      )
    );
  };

  const handleCurrencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCurrencyCode(e.target.value);
  };

  const handleSubmit = async () => {
    // Validate that we have at least one row
    if (pricingRows.length === 0) {
      toast.error("Error", {
        description: "Please add at least one pricing row",
      });
      return;
    }

    // Validate that all rows have occupancy type and meal plan selected
    const invalidRow = pricingRows.find(row => !row.occupancyTypeId || !row.mealPlanId);
    if (invalidRow) {
      toast.error("Error", {
        description: "Please select occupancy type and meal plan for all rows",
      });
      return;
    }

    // Check for duplicate combinations of occupancy type and meal plan
    const combinations = new Set();
    const duplicates = pricingRows.filter(row => {
      const combination = `${row.occupancyTypeId}-${row.mealPlanId}`;
      if (combinations.has(combination)) {
        return true;
      }
      combinations.add(combination);
      return false;
    });

    if (duplicates.length > 0) {
      toast.error("Error", {
        description: "Duplicate combinations of occupancy type and meal plan are not allowed",
      });
      return;
    }

    // Format data for API
    const weekdayRules = pricingRows.map(row => {
      const weekdayPrices: WeekdayPrices = {
        mon: Math.round(row.mon * 100), // Convert to cents
        tue: Math.round(row.tue * 100),
        wed: Math.round(row.wed * 100),
        thu: Math.round(row.thu * 100),
        fri: Math.round(row.fri * 100),
        sat: Math.round(row.sat * 100),
        sun: Math.round(row.sun * 100),
      };

      return {
        occupancy_type_id: row.occupancyTypeId,
        meal_plan_id: row.mealPlanId,
        weekday_prices: weekdayPrices,
      };
    });

    try {
      const data = {
        currency_code: currencyCode,
        weekday_rules: weekdayRules,
      };

      // Save using our consolidated hook
      const result = await savePricing(roomConfigId, data);

      // Call the onSave callback with the result
      onSave(result);

      toast.success("Success", {
        description: "Pricing saved successfully",
      });
    } catch (error) {
      console.error("Error saving pricing:", error);
      toast.error("Error", {
        description: "Failed to save pricing",
      });
    }
  };

  if (isLoading || isLoadingOccupancy || isLoadingMealPlans) {
    return (
      <Container>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="mb-6 h-4 bg-gray-200 rounded-md w-1/2"></div>

          <div className="flex justify-between mb-6">
            <div className="h-10 bg-gray-200 rounded-md w-32"></div>
            <div className="h-10 bg-gray-200 rounded-md w-28"></div>
          </div>

          <div className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="h-12 bg-gray-100 rounded-t-md w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-gray-50 border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
          </div>

          <div className="flex justify-end mt-8 gap-3">
            <div className="h-10 bg-gray-200 rounded-md w-24"></div>
            <div className="h-10 bg-gray-200 rounded-md w-32"></div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Heading level="h2">Base Pricing</Heading>
        <Text className="text-gray-600">
          Set base rates for each day of the week{roomConfigTitle ? ` for ${roomConfigTitle}` : ''}
        </Text>
      </div>

      <div className="flex justify-between mb-6">
        <div>
          <Label htmlFor="currency" className="mb-1 block">Currency</Label>
          <div className="relative inline-block">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              {currencyCode === "USD" && <DollarSign className="w-4 h-4 text-gray-500" />}
              {currencyCode === "EUR" && <Euro className="w-4 h-4 text-gray-500" />}
              {currencyCode === "GBP" && <PoundSterling className="w-4 h-4 text-gray-500" />}
            </div>
            <select
              id="currency"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              value={currencyCode}
              onChange={handleCurrencyChange}
            >
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
        <Button
          variant="primary"
          onClick={addNewRow}
          className="flex items-center gap-2 h-10 self-end"
        >
          <PlusCircle className="w-4 h-4" />
          Add Row
        </Button>
      </div>

      <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "140px" }}>
                Occupancy Type
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "140px" }}>
                Meal Plan
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "120px" }}>
                Default Price
              </th>
              {weekdays.map(day => (
                <th key={day.id} className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "80px" }}>
                  {day.name.slice(0, 3)}
                </th>
              ))}
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "80px" }}>
                {sunday.name.slice(0, 3)}
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "60px" }}>
                <span className="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {pricingRows.map((row, index) => (
              <tr key={row.id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                <td className="px-6 py-4 whitespace-nowrap" style={{ width: "140px", verticalAlign: "middle" }}>
                  <div className="flex justify-center">
                    <select
                      className="w-full p-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all"
                      value={row.occupancyTypeId}
                      onChange={(e) => handleOccupancyChange(row.id, e.target.value)}
                    >
                      <option value="">Select Occupancy Type</option>
                      {occupancyConfigs.map(oc => (
                        <option key={oc.id} value={oc.id}>
                          {oc.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap" style={{ width: "140px", verticalAlign: "middle" }}>
                  <div className="flex justify-center">
                    <select
                      className="w-full p-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all"
                      value={row.mealPlanId}
                      onChange={(e) => handleMealPlanChange(row.id, e.target.value)}
                    >
                      <option value="">Select Meal Plan</option>
                      {mealPlans.map(mp => (
                        <option key={mp.id} value={mp.id}>
                          {mp.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap" style={{ width: "120px", verticalAlign: "middle" }}>
                  <div className="flex items-center justify-center gap-2">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                        <span className="text-gray-500">{currencySymbols[currencyCode] || currencyCode}</span>
                      </div>
                      <Input
                        type="number"
                        className="w-20 pl-6 py-1 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all text-center"
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        title="Enter a price to use with Copy to All"
                        value={row.defaultPrice !== undefined ? row.defaultPrice : ""}
                        onChange={(e) => {
                          const value = Number(e.target.value);
                          // Just store the default price in the row object without applying it
                          setPricingRows(prev =>
                            prev.map(r =>
                              r.id === row.id
                                ? { ...r, defaultPrice: value }
                                : r
                            )
                          );
                        }}
                      />
                    </div>
                    <button
                      className="p-1.5 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors flex items-center justify-center"
                      onClick={() => {
                        // Use the default price if available, otherwise use Monday's price
                        const priceToApply = row.defaultPrice !== undefined ? row.defaultPrice : row.mon;

                        if (priceToApply !== undefined) {
                          const updates: Record<string, number> = {};
                          weekdays.forEach(day => {
                            updates[day.id] = priceToApply;
                          });
                          updates[sunday.id] = priceToApply;

                          setPricingRows(prev =>
                            prev.map(r =>
                              r.id === row.id
                                ? { ...r, ...updates }
                                : r
                            )
                          );
                        }
                      }}
                      title="Apply this price to all days"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </td>
                {weekdays.map(day => (
                  <td key={day.id} className="px-4 py-4 whitespace-nowrap" style={{ width: "80px", verticalAlign: "middle" }}>
                    <div className="flex justify-center">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                          <span className="text-gray-500">{currencySymbols[currencyCode] || currencyCode}</span>
                        </div>
                        <Input
                          type="number"
                          className="w-16 pl-6 py-1 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all text-center"
                          value={row[day.id as keyof WeekdayPricingRow] as number}
                          onChange={(e) => handlePriceChange(row.id, day.id, Number(e.target.value))}
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                  </td>
                ))}
                <td className="px-4 py-4 whitespace-nowrap" style={{ width: "80px", verticalAlign: "middle" }}>
                  <div className="flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                        <span className="text-gray-500">{currencySymbols[currencyCode] || currencyCode}</span>
                      </div>
                      <Input
                        type="number"
                        className="w-16 pl-6 py-1 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all text-center"
                        value={row[sunday.id as keyof WeekdayPricingRow] as number}
                        onChange={(e) => handlePriceChange(row.id, sunday.id, Number(e.target.value))}
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>
                </td>
                <td className="px-2 py-2 whitespace-nowrap" style={{ width: "60px", verticalAlign: "middle" }}>
                  <div className="flex justify-center">
                    <button
                      className="text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-red-50 transition-colors"
                      onClick={() => removeRow(row.id)}
                      disabled={pricingRows.length <= 1}
                      title="Remove row"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end mt-8 gap-3">
        <Button
          variant="secondary"
          onClick={onCancel}
          className="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
          disabled={isSaving}
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700 text-white transition-colors"
          disabled={isSaving}
        >
          {isSaving ? (
            <span className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
          ) : "Save Changes"}
        </Button>
      </div>
    </Container>
  );
};

export default EnhancedWeekdayPricing;
