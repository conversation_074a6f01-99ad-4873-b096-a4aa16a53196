import React, { useState, useEffect } from "react";
import {
  Button,
  Container,
  Text,
  toast,
  Toaster,
  Input,
  Label,
  Checkbox,
} from "@camped-ai/ui";
import {
  DollarSign,
  Save,
  Loader2,
  Co<PERSON>,
  CheckSquare,
  MoreHorizontal,
} from "lucide-react";
import {
  useAdminSaveRoomConfigChannelPricing,
  useAdminSalesChannels,
  ChannelPriceRule,
  SalesChannel
} from "../../../hooks/hotel/use-admin-hotel-channel-pricing";

// Helper function to generate a unique ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

type OverrideType = 'fixed' | 'percentage' | 'inherit';

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string;
  salesChannelId: string;
  seasonId: string;
  overrideType: OverrideType;
  basePrice: number;
  weekdayPrices: WeekdayPrices;
  percentageOverrides?: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  modified: boolean;
  selected: boolean;
  basePriceRuleId?: string;
};

type SalesChannelPriceOverridesProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  onSave?: (data: any) => void;
};

const SalesChannelPriceOverrides: React.FC<SalesChannelPriceOverridesProps> = ({
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  onSave,
}) => {
  // State for filters
  const [selectedSalesChannel, setSelectedSalesChannel] = useState<string>("");
  const [selectedSeason, setSelectedSeason] = useState<string>("base");

  // State for pricing data
  const [pricingRows, setPricingRows] = useState<PricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState("USD");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // State for bulk actions
  const [allSelected, setAllSelected] = useState(false);

  // Fetch all sales channels
  const {
    salesChannels: allSalesChannels,
    isLoading: isLoadingSalesChannels
  } = useAdminSalesChannels();

  // Save channel pricing mutation
  const saveChannelPricing = useAdminSaveRoomConfigChannelPricing();



  // Initialize data when components load
  useEffect(() => {
    if (isLoadingSalesChannels) {
      return;
    }

    // Set default sales channel if available
    if (allSalesChannels.length > 0 && !selectedSalesChannel) {
      setSelectedSalesChannel(allSalesChannels[0].id);
    }

    fetchPricingData();
  }, [roomConfigs, occupancyConfigs, mealPlans, seasonalPeriods, allSalesChannels, isLoadingSalesChannels, selectedSalesChannel, selectedSeason]);

  // Fetch pricing data for the selected sales channel and season
  const fetchPricingData = async () => {
    if (!selectedSalesChannel) return;

    setIsLoading(true);

    try {
      const rows: PricingRow[] = [];

      // Fetch pricing data for each room configuration
      for (const room of roomConfigs) {
        try {
          // 1. Fetch base pricing data (or seasonal pricing if a season is selected)
          let basePricingUrl = `/admin/hotel-management/room-configs/${room.id}/weekday-pricing`;
          if (selectedSeason !== 'base') {
            basePricingUrl = `/admin/hotel-management/room-configs/${room.id}/seasonal-pricing/${selectedSeason}`;
          }

          const basePricingResponse = await fetch(basePricingUrl);

          let basePricingData = { weekday_rules: [] };

          if (basePricingResponse.ok) {
            basePricingData = await basePricingResponse.json();
             console.log({basePricingData, room})
          }

          // 2. Fetch channel pricing data
          const channelPricingResponse = await fetch(`/admin/hotel-management/room-configs/${room.id}/channel-pricing`);
          let channelPricingData = { channel_prices: [] };

          if (channelPricingResponse.ok) {
            channelPricingData = await channelPricingResponse.json();
          }

          // Get currency code from the first rule if available
          if (basePricingData.weekday_rules?.length > 0) {
            const firstRule = basePricingData.weekday_rules[0];
            if (firstRule.currency_code) {
              setCurrencyCode(firstRule.currency_code);
            }
          }

          // Process base pricing data
          for (const rule of basePricingData.weekday_rules || []) {
            // Create default weekday prices (all days same as Monday)
            const baseWeekdayPrices = rule.weekday_prices || {
              mon: 0, tue: 0, wed: 0, thu: 0, fri: 0, sat: 0, sun: 0
            };

            // Find channel override if it exists
            const channelOverride = channelPricingData.channel_prices
              .flatMap((cp: any) => cp.rules)
              .find((r: any) =>
                r.occupancy_type_id === rule.occupancy_type_id &&
                r.meal_plan_id === rule.meal_plan_id &&
                r.sales_channel_id === selectedSalesChannel &&
                r.base_price_rule_id === rule.id
              );

            // Determine override type and prices
            let overrideType: OverrideType = 'inherit';
            let weekdayPrices = { ...baseWeekdayPrices };
            let percentageOverrides;

            if (channelOverride) {
              if (channelOverride.metadata?.percentage_override !== undefined) {
                overrideType = 'percentage';
                const percentage = Number(channelOverride.metadata.percentage_override);

                // Create percentage overrides for each day (all same percentage)
                percentageOverrides = {
                  mon: percentage,
                  tue: percentage,
                  wed: percentage,
                  thu: percentage,
                  fri: percentage,
                  sat: percentage,
                  sun: percentage
                };

                // Calculate actual prices based on percentage
                Object.keys(weekdayPrices).forEach(day => {
                  const basePrice = baseWeekdayPrices[day as keyof WeekdayPrices];
                  weekdayPrices[day as keyof WeekdayPrices] = Math.round(basePrice * (1 + percentage / 100));
                });
              } else {
                overrideType = 'fixed';
                // For now, set all days to the same fixed price
                // In a real implementation, you'd have day-specific overrides
                Object.keys(weekdayPrices).forEach(day => {
                  weekdayPrices[day as keyof WeekdayPrices] = channelOverride.amount;
                });
              }
            }

            // Create a pricing row
            rows.push({
              id: channelOverride?.id || generateId(),
              roomConfigId: room.id,
              occupancyTypeId: rule.occupancy_type_id,
              mealPlanId: rule.meal_plan_id,
              salesChannelId: selectedSalesChannel,
              seasonId: selectedSeason,
              overrideType,
              basePrice: baseWeekdayPrices.mon, // Using Monday as the reference
              weekdayPrices,
              percentageOverrides,
              modified: false,
              selected: false,
              basePriceRuleId: rule.id
            });
          }



          // Create rows for any missing combinations
          occupancyConfigs.forEach(occupancy => {
            mealPlans.forEach(mealPlan => {
              // Check if this combination already exists
              const existingRow = rows.find(row =>
                row.roomConfigId === room.id &&
                row.occupancyTypeId === occupancy.id &&
                row.mealPlanId === mealPlan.id
              );

              if (!existingRow) {
                // Create a new row with default values
                rows.push({
                  id: generateId(),
                  roomConfigId: room.id,
                  occupancyTypeId: occupancy.id,
                  mealPlanId: mealPlan.id,
                  salesChannelId: selectedSalesChannel,
                  seasonId: selectedSeason,
                  overrideType: 'inherit',
                  basePrice: 0,
                  weekdayPrices: {
                    mon: 0, tue: 0, wed: 0, thu: 0, fri: 0, sat: 0, sun: 0
                  },
                  modified: false,
                  selected: false
                });
              }
            });
          });
        } catch (error) {
          console.error(`Error fetching pricing data for room ${room.id}:`, error);
        }
      }
console.log({rows})
      setPricingRows(rows);
    } catch (error) {
      console.error("Error fetching pricing data:", error);
      toast.error("Error", {
        description: "Failed to load pricing data",
      });
    } finally {
      setIsLoading(false);
    }
  };





  // Toggle all rows selection
  const toggleAllSelection = () => {
    const newSelectedState = !allSelected;
    setPricingRows(prev =>
      prev.map(row => ({ ...row, selected: newSelectedState }))
    );
    setAllSelected(newSelectedState);
  };



  // Save all channel price overrides
  const handleSaveAll = async () => {
    if (!selectedSalesChannel) {
      toast.error("Error", {
        description: "Please select a sales channel",
      });
      return;
    }

    setIsSaving(true);

    try {
      // Group pricing rows by room config
      const roomConfigPrices: Record<string, any> = {};

      // Initialize room config prices
      roomConfigs.forEach(room => {
        roomConfigPrices[room.id] = {
          currency_code: currencyCode,
          channel_price_rules: [],
        };
      });

      // Process pricing rows
      pricingRows.forEach(row => {
        // Skip rows that inherit from base (no override)
        if (row.overrideType === 'inherit') return;

        // Only include rows that have been modified or have a non-zero price/percentage
        if (row.modified || row.overrideType !== 'inherit') {
          // Create the base rule object with all weekday prices
          const rule: any = {
            id: row.id.startsWith("temp_") ? undefined : row.id, // Don't send temp IDs to the server
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId,
            sales_channel_id: row.salesChannelId,
            override_type: row.overrideType,
            base_price_rule_id: row.basePriceRuleId,
            // We still need to set amount for API compatibility
            amount: row.weekdayPrices.mon, // Using Monday as the reference price
          };

          // Add weekday prices directly to the rule object
          rule.weekday_prices = {
            mon: row.weekdayPrices.mon,
            tue: row.weekdayPrices.tue,
            wed: row.weekdayPrices.wed,
            thu: row.weekdayPrices.thu,
            fri: row.weekdayPrices.fri,
            sat: row.weekdayPrices.sat,
            sun: row.weekdayPrices.sun
          };

          // Add percentage override if using percentage mode
          if (row.overrideType === 'percentage' && row.percentageOverrides) {
            rule.percentage_overrides = row.percentageOverrides;
          }

          roomConfigPrices[row.roomConfigId].channel_price_rules.push(rule);
        }
      });

      // Save each room config's prices
      const savePromises = Object.entries(roomConfigPrices).map(async ([roomConfigId, data]) => {
        // Only save if there are channel price rules
        if (data.channel_price_rules.length > 0) {
          return saveChannelPricing.mutateAsync({
            roomConfigId,
            currencyCode: data.currency_code,
            channelPriceRules: data.channel_price_rules
          });
        }
        return null;
      });

      const results = await Promise.all(savePromises);

      // Reset modified flags
      setPricingRows(prev =>
        prev.map(row => ({ ...row, modified: false }))
      );

      // Call onSave callback if provided
      if (onSave) {
        onSave(results);
      }

      toast.success("Success", {
        description: "Channel price overrides saved successfully",
      });
    } catch (error) {
      console.error("Error saving channel price overrides:", error);
      toast.error("Error", {
        description: "Failed to save channel price overrides",
      });
    } finally {
      setIsSaving(false);
    }
  };



  if (isLoading || isLoadingSalesChannels) {
    return (
      <Container>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="mb-6 h-4 bg-gray-200 rounded-md w-1/2"></div>

          <div className="flex justify-between mb-6">
            <div className="h-10 bg-gray-200 rounded-md w-32"></div>
            <div className="h-10 bg-gray-200 rounded-md w-28"></div>
          </div>

          <div className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="h-12 bg-gray-100 rounded-t-md w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-gray-50 border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
          </div>
        </div>
      </Container>
    );
  }





  return (
    <Container>
      <Toaster />
      <div className="mb-4">
        <Text className="text-lg font-medium">Sales Channel Price Overrides</Text>
        <Text className="text-sm text-gray-600">
          Manage pricing for specific sales channels across rooms, occupancy types, and meal plans
        </Text>
      </div>

      {/* Filter Section */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <Text className="text-sm font-medium mb-4">Filters</Text>
        <div className="grid grid-cols-3 gap-x-8 gap-y-4">
          {/* Column 1 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="salesChannel" className="mb-1 block text-sm font-normal text-gray-700">Sales Channel</Label>
              <select
                id="salesChannel"
                className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={selectedSalesChannel || ""}
                onChange={(e) => {
                  setSelectedSalesChannel(e.target.value);
                  // Add filter logic here
                  console.log("Selected sales channel:", e.target.value);
                  // Update the pricing title
                  const title = document.querySelector('.text-sm.font-medium');
                  if (title) {
                    const channelName = e.target.options[e.target.selectedIndex].text;
                    title.textContent = `Pricing for: ${channelName} | ${selectedSeason === 'base' ? 'Base' : 'June 2025'}`;
                  }
                }}
              >
                <option value="" disabled>Select a sales channel</option>
                {allSalesChannels && allSalesChannels.map((channel: SalesChannel) => (
                  <option key={channel.id} value={channel.id}>
                    {channel.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="mealPlan" className="mb-1 block text-sm font-normal text-gray-700">Meal Plan</Label>
              <select
                id="mealPlan"
                className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="all"
                onChange={(e) => {
                  setSelectedMealPlan(e.target.value);
                  // Add filter logic here
                  console.log("Selected meal plan:", e.target.value);

                  // Simple DOM manipulation to show/hide rows based on selection
                  const rows = document.querySelectorAll('tbody tr');
                  rows.forEach(row => {
                    const mealPlanCell = row.querySelector('td:nth-child(2) div.text-xs');
                    if (mealPlanCell) {
                      const mealPlanText = mealPlanCell.textContent.trim();

                      if (e.target.value === 'all') {
                        // Show all rows
                        row.style.display = '';
                      } else if (e.target.value === 'fullboard' && mealPlanText.includes('Full Board')) {
                        row.style.display = '';
                      } else {
                        row.style.display = 'none';
                      }
                    }
                  });
                }}
              >
                <option value="all">All Meal Plans</option>
                <option value="fullboard">Full Board</option>
              </select>

            </div>
          </div>

          {/* Column 2 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="roomConfig" className="mb-1 block text-sm font-normal text-gray-700">Room Type</Label>
              <select
                id="roomConfig"
                className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="all"
                onChange={(e) => {
                  setSelectedRoomConfig(e.target.value);
                  // Add filter logic here
                  console.log("Selected room type:", e.target.value);

                  // Simple DOM manipulation to show/hide rows based on selection
                  const rows = document.querySelectorAll('tbody tr');
                  rows.forEach(row => {
                    const roomTypeCell = row.querySelector('td:nth-child(2) div.font-medium');
                    if (roomTypeCell) {
                      const roomType = roomTypeCell.textContent.trim();

                      if (e.target.value === 'all') {
                        // Show all rows
                        row.style.display = '';
                      } else if (e.target.value === 'superior' && roomType === 'Superior Room') {
                        row.style.display = '';
                      } else if (e.target.value === 'ttttt' && roomType === 'TTTTT') {
                        row.style.display = '';
                      } else if (e.target.value === 'santhosh' && roomType === 'SANTHOSH') {
                        row.style.display = '';
                      } else if (e.target.value === 'halfboard' && roomType === 'Halfboard( Child2-5)') {
                        row.style.display = '';
                      } else {
                        row.style.display = 'none';
                      }
                    }
                  });
                }}
              >
                <option value="all">All Room Types</option>
                <option value="superior">Superior Room</option>
                <option value="ttttt">TTTTT</option>
                <option value="santhosh">SANTHOSH</option>
                <option value="halfboard">Halfboard( Child2-5)</option>
              </select>

            </div>

            <div>
              <Label htmlFor="season" className="mb-1 block text-sm font-normal text-gray-700">Season</Label>
              <select
                id="season"
                className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="base"
                onChange={(e) => {
                  setSelectedSeason(e.target.value);
                  // Add filter logic here
                  console.log("Selected season:", e.target.value);

                  // Update the pricing title
                  const title = document.querySelector('.text-sm.font-medium');
                  if (title) {
                    const channelName = document.getElementById('salesChannel').options[document.getElementById('salesChannel').selectedIndex].text;
                    const seasonName = e.target.options[e.target.selectedIndex].text;
                    title.textContent = `Pricing for: ${channelName} | ${seasonName}`;
                  }

                  // Simple DOM manipulation to show different pricing data based on season
                  if (e.target.value === 'june2025') {
                    // Show June 2025 pricing (for demo, we'll just modify the existing prices)
                    const basePriceCells = document.querySelectorAll('tbody tr td:nth-child(3) div.text-sm');
                    const finalPriceCells = document.querySelectorAll('tbody tr td:nth-child(6) div.text-sm');

                    basePriceCells.forEach((cell, index) => {
                      // Increase base prices by 20% for June 2025
                      const currentPrice = parseFloat(cell.textContent.replace('$', ''));
                      const newPrice = (currentPrice * 1.2).toFixed(2);
                      cell.textContent = `$${newPrice}`;

                      // Update final prices too
                      if (finalPriceCells[index]) {
                        finalPriceCells[index].textContent = `$${(newPrice * 1.1).toFixed(2)}`;
                      }
                    });
                  } else {
                    // Reset to base pricing
                    const basePriceCells = document.querySelectorAll('tbody tr td:nth-child(3) div.text-sm');
                    const finalPriceCells = document.querySelectorAll('tbody tr td:nth-child(6) div.text-sm');

                    // Reset first row
                    if (basePriceCells[0]) basePriceCells[0].textContent = '$12.00';
                    if (finalPriceCells[0]) finalPriceCells[0].textContent = '$13.20';

                    // Reset second row
                    if (basePriceCells[1]) basePriceCells[1].textContent = '$12.00';
                    if (finalPriceCells[1]) finalPriceCells[1].textContent = '$15.00';

                    // Reset third row
                    if (basePriceCells[2]) basePriceCells[2].textContent = '$10.00';
                    if (finalPriceCells[2]) finalPriceCells[2].textContent = '$10.00';
                  }
                }}
              >
                <option value="base">Base</option>
                <option value="june2025">June 2025</option>
              </select>
            </div>
          </div>

          {/* Column 3 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="occupancyConfig" className="mb-1 block text-sm font-normal text-gray-700">Occupancy Type</Label>
              <select
                id="occupancyConfig"
                className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                defaultValue="all"
                onChange={(e) => {
                  setSelectedOccupancyConfig(e.target.value);
                  // Add filter logic here
                  console.log("Selected occupancy type:", e.target.value);

                  // Simple DOM manipulation to show/hide rows based on selection
                  const rows = document.querySelectorAll('tbody tr');
                  rows.forEach(row => {
                    const occupancyCell = row.querySelector('td:nth-child(2) div.text-xs');
                    if (occupancyCell) {
                      const occupancyText = occupancyCell.textContent.trim();

                      if (e.target.value === 'all') {
                        // Show all rows
                        row.style.display = '';
                      } else if (e.target.value === 'adult' && occupancyText.includes('Adult')) {
                        row.style.display = '';
                      } else if (e.target.value === 'child' && occupancyText.includes('Child')) {
                        row.style.display = '';
                      } else {
                        row.style.display = 'none';
                      }
                    }
                  });
                }}
              >
                <option value="all">All Occupancy Types</option>
                <option value="adult">Adult</option>
                <option value="child">Child</option>
              </select>

            </div>

            <div>
              <Label htmlFor="currency" className="mb-1 block text-sm font-normal text-gray-700">Currency</Label>
              <div className="relative inline-block w-full">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                </div>
                <input
                  type="text"
                  id="currency"
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full"
                  value="USD ($)"
                  readOnly
                />
              </div>
            </div>
          </div>
        </div>


      </div>

      <div className="flex justify-between items-center mb-2">
        <Text className="text-sm font-medium">
          Pricing for: Default Sales Channel | Base
        </Text>

        <Button
          variant="secondary"
          onClick={handleSaveAll}
          className="flex items-center gap-2"
          disabled={isSaving}
          size="small"
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save Channel Overrides
            </>
          )}
        </Button>
      </div>

      {/* Pricing Grid/Table */}
      <div className="mb-6">
        <div className="overflow-x-auto rounded-lg border border-gray-200" style={{ maxWidth: "100%" }}>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-1 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "40px" }}>
                  <Checkbox
                    checked={allSelected}
                    onCheckedChange={toggleAllSelection}
                  />
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Room/Occupancy/Meal Plan
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Base Price
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "150px" }}>
                  Override Type
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ minWidth: "700px" }}>
                  Day-Specific Overrides
                </th>
                <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: "50px" }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {pricingRows.map((row, index) => {
                // Find room, occupancy, and meal plan details
                const room = roomConfigs.find(r => r.id === row.roomConfigId);
                const occupancy = occupancyConfigs.find(o => o.id === row.occupancyTypeId);
                const mealPlan = mealPlans.find(m => m.id === row.mealPlanId);

                // Format base price for display
                const basePrice = (row.basePrice / 100).toFixed(2);

                return (
                  <tr key={row.id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    <td className="px-1 py-3 whitespace-nowrap" style={{ width: "40px" }}>
                      <Checkbox
                        checked={row.selected}
                        onCheckedChange={(checked) => {
                          setPricingRows(prev =>
                            prev.map(r => r.id === row.id ? { ...r, selected: !!checked } : r)
                          );
                        }}
                      />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="font-medium text-sm">{room?.title || 'Unknown Room'}</div>
                      <div className="text-xs text-gray-500">
                        {occupancy?.name || 'Unknown Occupancy'} / {mealPlan?.name || 'Unknown Meal Plan'}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm font-medium">
                        ${basePrice}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                         style={{ width: "150px" }}
                        value={row.overrideType}
                        onChange={(e) => {
                          const newType = e.target.value as OverrideType;
                          // Update row with new override type
                          setPricingRows(prev =>
                            prev.map(r => {
                              if (r.id === row.id) {
                                let updatedRow = { ...r, overrideType: newType, modified: true };

                                // Reset weekday prices based on the new override type
                                if (newType === 'inherit') {
                                  // Inherit from base price
                                  updatedRow.weekdayPrices = {
                                    mon: r.basePrice,
                                    tue: r.basePrice,
                                    wed: r.basePrice,
                                    thu: r.basePrice,
                                    fri: r.basePrice,
                                    sat: r.basePrice,
                                    sun: r.basePrice
                                  };
                                  updatedRow.percentageOverrides = undefined;
                                } else if (newType === 'percentage') {
                                  // Set default percentage (0% if no previous percentage)
                                  const defaultPercentage = r.percentageOverrides?.mon || 0;
                                  updatedRow.percentageOverrides = {
                                    mon: defaultPercentage,
                                    tue: defaultPercentage,
                                    wed: defaultPercentage,
                                    thu: defaultPercentage,
                                    fri: defaultPercentage,
                                    sat: defaultPercentage,
                                    sun: defaultPercentage
                                  };
                                }
                                return updatedRow;
                              }
                              return r;
                            })
                          );
                        }}
                      >
                        <option value="fixed" className="text-sm">Fixed Price</option>
                        <option value="percentage" className="text-sm">% Difference</option>
                        <option value="inherit" className="text-sm">Inherit from Base</option>
                      </select>
                    </td>
                    <td className="px-4 py-3">
                      {row.overrideType === 'inherit' ? (
                        <div className="grid grid-cols-7 gap-2 text-xs" style={{ minWidth: "700px" }}>
                          {/* Days of the week with inherited base prices */}
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                            const dayKey = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'][index] as keyof WeekdayPrices;
                            const price = row.weekdayPrices[dayKey];

                            return (
                              <div key={day} className="text-center" style={{ minWidth: "50px" }}>
                                <div className="font-medium mb-1">{day}</div>
                                <div className="py-2 px-3 border border-gray-200 rounded-md bg-gray-50 text-gray-500">
                                  ${(price / 100).toFixed(2)}
                                </div>
                                <div className="text-xs text-gray-400 mt-1">
                                  Base Price
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="grid grid-cols-7 gap-2 text-xs" style={{ minWidth: "700px" }}>
                          {/* Days of the week with prices */}
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                            const dayKey = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'][index] as keyof WeekdayPrices;
                            const price = row.weekdayPrices[dayKey];
                            const percentage = row.percentageOverrides?.[dayKey];

                            return (
                              <div key={day} className="text-center" style={{ minWidth: "90px" }}>
                                <div className="font-medium mb-1">{day}</div>
                                {row.overrideType === 'percentage' ? (
                                  <div className="relative">
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                                      <span className="text-gray-500 text-xs">%</span>
                                    </div>
                                    <Input
                                      type="number"
                                      className="w-full pl-6 py-1 text-xs border rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                                      value={percentage?.toString() || "0"}
                                      onChange={(e) => {
                                        const newPercentage = parseFloat(e.target.value) || 0;
                                        const basePrice = row.basePrice;
                                        const newPrice = Math.round(basePrice * (1 + newPercentage / 100));

                                        setPricingRows(prev =>
                                          prev.map(r => {
                                            if (r.id === row.id) {
                                              // Create updated objects
                                              const updatedPercentageOverrides = { ...r.percentageOverrides };
                                              const updatedWeekdayPrices = { ...r.weekdayPrices };

                                              // Update the specific day
                                              updatedPercentageOverrides[dayKey] = newPercentage;
                                              updatedWeekdayPrices[dayKey] = newPrice;

                                              return {
                                                ...r,
                                                modified: true,
                                                percentageOverrides: updatedPercentageOverrides,
                                                weekdayPrices: updatedWeekdayPrices
                                              };
                                            }
                                            return r;
                                          })
                                        );
                                      }}
                                    />
                                    <div className="text-xs text-gray-500 mt-1">
                                      ${(price / 100).toFixed(2)}
                                    </div>
                                  </div>
                                ) : (
                                  <div className="relative">
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                                      <span className="text-gray-500 text-xs">$</span>
                                    </div>
                                    <Input
                                      type="number"
                                      className="w-full pl-6 py-1 text-xs border rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                                      value={(price / 100).toString()}
                                      onChange={(e) => {
                                        const value = parseFloat(e.target.value) || 0;
                                        const fixedPrice = Math.round(value * 100); // Convert to cents

                                        setPricingRows(prev =>
                                          prev.map(r => {
                                            if (r.id === row.id) {
                                              // Create updated weekday prices
                                              const updatedWeekdayPrices = { ...r.weekdayPrices };

                                              // Update the specific day
                                              updatedWeekdayPrices[dayKey] = fixedPrice;

                                              return {
                                                ...r,
                                                modified: true,
                                                weekdayPrices: updatedWeekdayPrices
                                              };
                                            }
                                            return r;
                                          })
                                        );
                                      }}
                                    />
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </td>

                    <td className="px-2 py-3 whitespace-nowrap text-center" >
                      <div className="flex items-center justify-center gap-1" style={{ width: "50px" }}>
                        <Button
                          variant="outline"
                          size="small"
                          className="h-7 w-7 p-0"
                          onClick={() => {
                            // Apply Monday's value to all days of the week
                            setPricingRows(prev =>
                              prev.map(r => {
                                if (r.id === row.id) {
                                  const mondayPrice = r.weekdayPrices.mon;
                                  const mondayPercentage = r.percentageOverrides?.mon;

                                  let updatedWeekdayPrices = {
                                    mon: mondayPrice,
                                    tue: mondayPrice,
                                    wed: mondayPrice,
                                    thu: mondayPrice,
                                    fri: mondayPrice,
                                    sat: mondayPrice,
                                    sun: mondayPrice
                                  };

                                  let updatedPercentageOverrides;
                                  if (r.overrideType === 'percentage' && mondayPercentage !== undefined) {
                                    updatedPercentageOverrides = {
                                      mon: mondayPercentage,
                                      tue: mondayPercentage,
                                      wed: mondayPercentage,
                                      thu: mondayPercentage,
                                      fri: mondayPercentage,
                                      sat: mondayPercentage,
                                      sun: mondayPercentage
                                    };
                                  }

                                  return {
                                    ...r,
                                    modified: true,
                                    weekdayPrices: updatedWeekdayPrices,
                                    percentageOverrides: updatedPercentageOverrides
                                  };
                                }
                                return r;
                              })
                            );

                            toast.success("Success", {
                              description: "Applied to all days of the week",
                            });
                          }}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => {
                            // Reset to inherit from base price
                            setPricingRows(prev =>
                              prev.map(r => {
                                if (r.id === row.id) {
                                  return {
                                    ...r,
                                    modified: true,
                                    overrideType: 'inherit',
                                    weekdayPrices: {
                                      mon: r.basePrice,
                                      tue: r.basePrice,
                                      wed: r.basePrice,
                                      thu: r.basePrice,
                                      fri: r.basePrice,
                                      sat: r.basePrice,
                                      sun: r.basePrice
                                    },
                                    percentageOverrides: undefined
                                  };
                                }
                                return r;
                              })
                            );

                            toast.success("Success", {
                              description: "Reset to base price",
                            });
                          }}
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })}

              {/* Show a message if no pricing rows are available */}
              {pricingRows.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-4 py-6 text-center text-gray-500">
                    No pricing data available. Please select a sales channel and season.
                  </td>
                </tr>
              )}
            </tbody>
            <tfoot className="bg-gray-50 border-t border-gray-200">
              <tr>
                <td className="px-1 py-3 whitespace-nowrap" style={{ width: "40px" }}>
                  <div className="flex items-center">
                    <Checkbox id="selectAll" className="mr-1" />
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <div className="text-xs font-medium">Fixed Price</div>
                    <div className="text-xs font-medium">% Difference</div>
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap"></td>
                <td className="px-4 py-3 whitespace-nowrap"></td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="relative">
                    <Input
                      type="number"
                      className="w-32 py-1 text-xs border rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all"
                      value="0"
                    />
                  </div>
                </td>
                <td className="px-4 py-3 whitespace-nowrap">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs flex items-center gap-1"
                  >
                    <CheckSquare className="w-3 h-3" />
                    Apply to Selected
                  </Button>
                </td>
                <td className="px-2 py-3 whitespace-nowrap text-center" style={{ width: "90px" }}>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="h-7 w-7"
                  >
                    <Save className="w-4 h-4" />
                  </Button>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>


    </Container>
  );
};

export default SalesChannelPriceOverrides;