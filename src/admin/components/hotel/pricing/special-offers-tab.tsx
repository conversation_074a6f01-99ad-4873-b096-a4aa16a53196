import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  But<PERSON>,
  Table,
  Badge,
  FocusModal,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { PlusMini } from "@camped-ai/icons";
import { Tag, Edit, Trash2, Percent, DollarSign, Bed, Calendar } from "lucide-react";
import SpecialOffersForm from "./special-offers-form";
import { format } from "date-fns";

interface RoomConfig {
  id: string;
  name?: string;
  title: string;
  handle?: string;
  description?: string;
}

interface SpecialOffer {
  id: string;
  code: string;
  name: string;
  description: string;
  type: "percentage" | "fixed" | "free_night";
  value: number;
  start_date: string;
  end_date: string;
  room_config_ids: string[];
  status: string;
  created_at: string;
  updated_at: string;
}

interface SpecialOffersTabProps {
  hotelId: string;
  roomConfigs?: RoomConfig[];
}

const SpecialOffersTab: React.FC<SpecialOffersTabProps> = ({ hotelId, roomConfigs: initialRoomConfigs }) => {
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>(initialRoomConfigs || []);
  const [specialOffers, setSpecialOffers] = useState<SpecialOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingSpecialOffer, setEditingSpecialOffer] = useState<SpecialOffer | null>(null);

  // Fetch room configurations and special offers
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Only fetch room configurations if not provided as props
        if (!initialRoomConfigs) {
          console.log(`Fetching room configurations for hotel ID: ${hotelId}`);
          const timestamp = new Date().getTime(); // Add cache-busting parameter
          const roomConfigsResponse = await fetch(`/admin/direct-room-configs?hotel_id=${hotelId}&_=${timestamp}`, {
            headers: {
              "Cache-Control": "no-cache, no-store, must-revalidate",
              "Pragma": "no-cache",
              "Expires": "0"
            }
          });

          if (!roomConfigsResponse.ok) {
            throw new Error("Failed to fetch room configurations");
          }

          const roomConfigsData = await roomConfigsResponse.json();
          console.log(`Received ${roomConfigsData.room_configs?.length || 0} room configurations`);

          if (roomConfigsData.room_configs && roomConfigsData.room_configs.length > 0) {
            console.log('Room configurations:', roomConfigsData.room_configs.map((c: RoomConfig) => ({ id: c.id, title: c.title || c.name })));
            setRoomConfigs(roomConfigsData.room_configs);
          } else {
            console.warn('No room configurations found for this hotel');
            setRoomConfigs([]);
          }
        }

        // Fetch special offers
        console.log(`Fetching special offers for hotel ID: ${hotelId}`);
        // Add timestamp to avoid caching issues
        const timestamp = new Date().getTime();
        const specialOffersResponse = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/offers?_=${timestamp}`, {
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0"
          }
        });

        if (!specialOffersResponse.ok) {
          throw new Error("Failed to fetch special offers");
        }

        const specialOffersData = await specialOffersResponse.json();
        console.log(`Received special offers response:`, specialOffersData);

        if (specialOffersData.special_offers && Array.isArray(specialOffersData.special_offers)) {
          console.log(`Found ${specialOffersData.special_offers.length} special offers`);
          setSpecialOffers(specialOffersData.special_offers);
        } else {
          console.warn('No special offers found or invalid response format');
          setSpecialOffers([]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load data: " + (error instanceof Error ? error.message : String(error)));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [hotelId, initialRoomConfigs]);

  const handleCreateSpecialOffer = async (data: {
    code: string;
    name: string;
    description: string;
    type: "percentage" | "fixed" | "free_night";
    value: number;
    start_date: string;
    end_date: string;
    room_config_ids: string[];
    status: string;
  }) => {
    setIsSubmitting(true);

    try {
      console.log(`Creating special offer for hotel ID: ${hotelId}`);
      console.log(`Room config IDs: ${JSON.stringify(data.room_config_ids)}`);

      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/offers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API error response:", errorData);
        throw new Error(errorData.message || "Failed to create special offer");
      }

      const responseData = await response.json();
      toast.success("Special offer created successfully");
      setSpecialOffers([...specialOffers, responseData.special_offer]);
      setOpen(false);
      setEditingSpecialOffer(null);
    } catch (error) {
      console.error("Error creating special offer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create special offer");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateSpecialOffer = async (data: {
    code: string;
    name: string;
    description: string;
    type: "percentage" | "fixed" | "free_night";
    value: number;
    start_date: string;
    end_date: string;
    room_config_ids: string[];
    status: string;
  }) => {
    if (!editingSpecialOffer) return;

    setIsSubmitting(true);

    try {
      console.log(`Updating special offer ${editingSpecialOffer.id} for hotel ID: ${hotelId}`);
      console.log(`Room config IDs: ${JSON.stringify(data.room_config_ids)}`);

      // Add a timestamp to avoid caching issues
      const timestamp = new Date().getTime();
      const url = `/admin/hotel-management/hotels/${hotelId}/pricing/offers/${editingSpecialOffer.id}?_=${timestamp}`;

      console.log(`Sending PUT request to: ${url}`);

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        },
        body: JSON.stringify({
          ...data,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API error response:", errorData);
        throw new Error(errorData.message || "Failed to update special offer");
      }

      const responseData = await response.json();
      toast.success("Special offer updated successfully");

      // Update the special offers list
      setSpecialOffers(specialOffers.map(offer =>
        offer.id === editingSpecialOffer.id ? responseData.special_offer : offer
      ));

      setOpen(false);
      setEditingSpecialOffer(null);
    } catch (error) {
      console.error("Error updating special offer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update special offer");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteSpecialOffer = async (id: string) => {
    if (!confirm("Are you sure you want to delete this special offer?")) {
      return;
    }

    try {
      // Add a timestamp to avoid caching issues
      const timestamp = new Date().getTime();
      const url = `/admin/hotel-management/hotels/${hotelId}/pricing/offers/${id}?_=${timestamp}`;

      console.log(`Sending DELETE request to: ${url}`);

      const response = await fetch(url, {
        method: "DELETE",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache",
          "Expires": "0"
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API error response:", errorData);
        throw new Error(errorData.message || "Failed to delete special offer");
      }

      toast.success("Special offer deleted successfully");
      setSpecialOffers(specialOffers.filter(offer => offer.id !== id));
    } catch (error) {
      console.error("Error deleting special offer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete special offer");
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (error) {
      return dateString;
    }
  };

  const formatValue = (offer: SpecialOffer) => {
    if (offer.type === "percentage") {
      return `${offer.value}% off`;
    } else if (offer.type === "fixed") {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD", // Assuming USD for fixed amounts
      }).format(offer.value / 100); // Convert from cents to dollars
    } else {
      return `${offer.value} free night${offer.value > 1 ? 's' : ''}`;
    }
  };

  const getRoomConfigNames = (ids: string[]) => {
    if (!ids || ids.length === 0) return "All rooms";

    const names = ids.map(id => {
      const config = roomConfigs.find(c => c.id === id);
      return config ? config.title : "Unknown";
    });

    if (names.length <= 2) {
      return names.join(", ");
    } else {
      return `${names.slice(0, 2).join(", ")} +${names.length - 2} more`;
    }
  };

  const handleSubmit = (data: {
    code: string;
    name: string;
    description: string;
    type: "percentage" | "fixed" | "free_night";
    value: number;
    start_date: string;
    end_date: string;
    room_config_ids: string[];
    status: string;
  }) => {
    if (editingSpecialOffer) {
      handleUpdateSpecialOffer(data);
    } else {
      handleCreateSpecialOffer(data);
    }
  };

  return (
    <>
      <Toaster />
      <Container className="p-0">
        <div className="flex justify-between items-center p-6 border-b border-gray-100">
          <div>
            <Heading level="h2" className="text-xl font-semibold text-gray-800 flex items-center">
              <Tag className="w-5 h-5 mr-2 text-blue-600" />
              Special Offers
            </Heading>
            <Text className="text-gray-500 mt-1">
              Create and manage promotional offers for your hotel
            </Text>
          </div>
          <Button
            variant="primary"
            onClick={() => {
              setEditingSpecialOffer(null);
              setOpen(true);
            }}
            className=" transition-colors"
          >
            <PlusMini className="w-4 h-4 mr-2" />
            Add Special Offer
          </Button>
        </div>

        {isLoading ? (
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-md m-6"></div>
            <div className="h-64 bg-gray-100 rounded-md mx-6 mb-6"></div>
          </div>
        ) : specialOffers.length === 0 ? (
          <div className="p-6 text-center">
            <Text className="text-gray-500">No special offers have been created yet.</Text>
            <Button
              variant="primary"
              onClick={() => {
                setEditingSpecialOffer(null);
                setOpen(true);
              }}
              className="mt-4  transition-colors"
            >
              <PlusMini className="w-4 h-4 mr-2" />
              Add Special Offer
            </Button>
          </div>
        ) : (
          <div className="p-6">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Offer</Table.HeaderCell>
                  <Table.HeaderCell>Discount</Table.HeaderCell>
                  <Table.HeaderCell>Validity</Table.HeaderCell>
                  <Table.HeaderCell>Applicable Rooms</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {specialOffers.map((offer) => (
                  <Table.Row key={offer.id}>
                    <Table.Cell>
                      <div className="font-medium">{offer.name}</div>
                      <Badge className="mt-1 bg-gray-50 text-gray-700 border border-gray-200">
                        {offer.code}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        className={`${
                          offer.type === "percentage"
                            ? "bg-green-50 text-green-700 border border-green-200"
                            : offer.type === "fixed"
                            ? "bg-blue-50 text-blue-700 border border-blue-200"
                            : "bg-amber-50 text-amber-700 border border-amber-200"
                        }`}
                      >
                        {offer.type === "percentage" ? (
                          <Percent className="w-3 h-3 mr-1 inline-block" />
                        ) : offer.type === "fixed" ? (
                          <DollarSign className="w-3 h-3 mr-1 inline-block" />
                        ) : (
                          <Bed className="w-3 h-3 mr-1 inline-block" />
                        )}
                        {formatValue(offer)}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                        {formatDate(offer.start_date)} - {formatDate(offer.end_date)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {offer.room_config_ids && offer.room_config_ids.length > 0 ? (
                        <Text className="text-sm">
                          {getRoomConfigNames(offer.room_config_ids)}
                        </Text>
                      ) : (
                        <Text className="text-sm text-gray-500">
                          All rooms
                        </Text>
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        className={`capitalize ${offer.status === "active" ? "bg-green-50 text-green-700 border border-green-200" : "bg-gray-50 text-gray-700 border border-gray-200"}`}
                      >
                        {offer.status}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => {
                            setEditingSpecialOffer(offer);
                            setOpen(true);
                          }}
                          className="bg-blue-50 hover:bg-blue-100 transition-colors"
                        >
                          <Edit className="w-4 h-4 text-blue-600" />
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => handleDeleteSpecialOffer(offer.id)}
                          className="bg-red-50 hover:bg-red-100 transition-colors"
                        >
                          <Trash2 className="w-4 h-4 text-red-600" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        )}
      </Container>

      {/* Add/Edit Special Offer Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="rounded-lg shadow-lg border border-gray-200 overflow-hidden max-w-2xl mx-auto my-8 !h-auto !max-h-[90vh]">
          <FocusModal.Header className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
            <FocusModal.Title className="text-xl font-semibold text-blue-700 flex items-center">
              <Tag className="w-5 h-5 mr-2 text-blue-500" />
              {editingSpecialOffer ? "Edit Special Offer" : "Add Special Offer"}
            </FocusModal.Title>
          </FocusModal.Header>
          <FocusModal.Body className="flex-1 overflow-y-auto p-0">
            <SpecialOffersForm
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              onSubmit={handleSubmit}
              onCancel={() => {
                setOpen(false);
                setEditingSpecialOffer(null);
              }}
              isSubmitting={isSubmitting}
              initialData={editingSpecialOffer ? {
                code: editingSpecialOffer.code,
                name: editingSpecialOffer.name,
                description: editingSpecialOffer.description,
                type: editingSpecialOffer.type,
                value: editingSpecialOffer.type === "fixed"
                  ? editingSpecialOffer.value / 100 // Convert from cents to dollars for fixed amounts
                  : editingSpecialOffer.value,
                start_date: editingSpecialOffer.start_date,
                end_date: editingSpecialOffer.end_date,
                room_config_ids: editingSpecialOffer.room_config_ids,
                status: editingSpecialOffer.status,
              } : undefined}
            />
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};

export default SpecialOffersTab;
