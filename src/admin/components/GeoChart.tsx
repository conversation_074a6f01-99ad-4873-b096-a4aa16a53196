import React, { useEffect, useState } from "react";
import { Heading, Select } from "@camped-ai/ui";
import { CircularProgress } from "@mui/material";

const timeRanges = [
  { label: "Yesterday", value: "yesterday" },
  { label: "Last Week", value: "last_week" },
  { label: "Last 14 Days", value: "last_14_days" },
  { label: "Last 30 Days", value: "last_30_days" },
];

const GeoChart = () => {
  const [selectedRange, setSelectedRange] = useState("last_week");
  const [geoData, setGeoData] = useState<[string, number][]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(
          `/admin/user-analytics/map?range=${selectedRange}`,
          { credentials: "include" }
        );
        if (!response.ok) throw new Error("Failed to fetch data");

        const result = await response.json();
        if (!result.data || !Array.isArray(result.data)) {
          throw new Error("Invalid data format");
        }

        setGeoData(result.data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedRange]);

  useEffect(() => {
    if (geoData.length === 0) return;

    const loadGoogleCharts = () => {
      window.google.charts.load("current", {
        packages: ["geochart", "table"],
      });
      window.google.charts.setOnLoadCallback(drawCharts);
    };

    const drawCharts = () => {
      if (!window.google || !window.google.visualization) return;

      // GeoChart Data
      const geoChartData = window.google.visualization.arrayToDataTable(geoData);
      const geoOptions = {
        colorAxis: { colors: ["#3b82f6"] },
        width: "100%",
        height: 350,
      };

      const geoChart = new window.google.visualization.GeoChart(
        document.getElementById("regions_div")
      );
      geoChart.draw(geoChartData, geoOptions);
    };

    if (!window.google || !window.google.charts) {
      const script = document.createElement("script");
      script.src = "https://www.gstatic.com/charts/loader.js";
      script.async = true;
      script.onload = loadGoogleCharts;
      document.body.appendChild(script);
    } else {
      loadGoogleCharts();
    }
  }, [geoData]);

  return (
    <div >
      {/* Header & Select */}
      <div className="flex flex-col md:flex-row justify-between items-center w-full mb-4 gap-4">
      <Heading level="h2">User Geography</Heading>
        <Select
          value={selectedRange}
          onValueChange={(value) => setSelectedRange(value)}
        >
          <Select.Trigger className="w-full md:w-40 px-3 py-2 rounded-md shadow-sm flex justify-between items-center cursor-pointer hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:outline-none">
            <span>
              {timeRanges.find((range) => range.value === selectedRange)?.label || "Select Range"}
            </span>
          </Select.Trigger>
          <Select.Content className="rounded-md shadow-lg w-full md:w-40">
            {timeRanges.map((range) => (
              <Select.Item key={range.value} value={range.value} className="px-3 py-2 hover:bg-blue-100 cursor-pointer">
                {range.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      </div>

      {/* Chart Section */}
      <div className="flex flex-col md:flex-row items-center justify-center gap-6">
       
          <div id="regions_div" className="w-full md:w-[500px] h-[350px]"></div>

      </div>
    </div>
  );
};

export default GeoChart;
