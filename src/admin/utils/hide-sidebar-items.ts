/**
 * Utility to hide specific sidebar navigation items
 * This uses DOM manipulation to target and hide specific items in the sidebar
 * that are rendered by the Medusa admin UI package
 */

// Enable debug mode to see console logs
const DEBUG = true;

/**
 * Hide specific sidebar navigation items
 * @param itemsToHide - Array of item labels to hide (case-insensitive)
 * @returns The MutationObserver instance (can be used to disconnect if needed)
 */
export const hideSidebarItems = (itemsToHide: string[]): MutationObserver => {
  if (DEBUG) {
    console.log('[HideSidebarItems] Initializing with items to hide:', itemsToHide);
  }

  // Function to check and hide sidebar items
  const checkAndHideSidebarItems = () => {
    // Specifically target search elements which might have a different structure
    removeSearchElements();
    // Convert all items to lowercase for case-insensitive comparison
    const normalizedItemsToHide = itemsToHide.map(item => item.toLowerCase());

    // Try different selectors to find sidebar items in Medusa admin UI
    // We'll try multiple selectors to ensure we catch the right elements

    // First, let's log all the nav elements to see what's available
    const allNavs = document.querySelectorAll('nav');
    if (DEBUG) {
      console.log('[HideSidebarItems] Found nav elements:', allNavs.length);
      allNavs.forEach((nav, index) => {
        console.log(`[HideSidebarItems] Nav ${index}:`, nav);
      });
    }

    // Try to find sidebar items using various selectors
    let sidebarItems: NodeListOf<Element> = document.querySelectorAll('nav a span.text-ui-fg-subtle');
    if (DEBUG) {
      console.log('[HideSidebarItems] Using primary selector, found items:', sidebarItems.length);
    }

    // If no items found with the first selector, try alternatives
    if (sidebarItems.length === 0) {
      // Try the generic sidebar nav items
      sidebarItems = document.querySelectorAll('nav a span');
      if (DEBUG) {
        console.log('[HideSidebarItems] Using first fallback selector, found items:', sidebarItems.length);
      }
    }

    // If still no items, try more specific Medusa admin selectors
    if (sidebarItems.length === 0) {
      // Try the sidebar items with specific class patterns
      sidebarItems = document.querySelectorAll('.bg-ui-bg-base nav a span');
      if (DEBUG) {
        console.log('[HideSidebarItems] Using second fallback selector, found items:', sidebarItems.length);
      }
    }

    // Try even more selectors
    if (sidebarItems.length === 0) {
      // Look for any links in the sidebar area
      sidebarItems = document.querySelectorAll('aside a span');
      if (DEBUG) {
        console.log('[HideSidebarItems] Using third fallback selector, found items:', sidebarItems.length);
      }
    }

    // One more attempt with a very generic selector
    if (sidebarItems.length === 0) {
      // Look for any links with text that might be in the sidebar
      const allLinks = document.querySelectorAll('a');
      const potentialSidebarItems: Element[] = [];

      allLinks.forEach(link => {
        const text = link.textContent?.trim().toLowerCase() || '';
        if (normalizedItemsToHide.some(item => text.includes(item))) {
          potentialSidebarItems.push(link);
        }
      });

      if (DEBUG) {
        console.log('[HideSidebarItems] Using generic text matching, found potential items:', potentialSidebarItems.length);
      }

      // Convert to a NodeList-like object
      sidebarItems = {
        length: potentialSidebarItems.length,
        item: (index: number) => potentialSidebarItems[index],
        forEach: (callback: (value: Element, key: number) => void) => {
          potentialSidebarItems.forEach(callback);
        },
        [Symbol.iterator]: function* () {
          yield* potentialSidebarItems;
        }
      } as any;
    }

    if (DEBUG) {
      console.log('[HideSidebarItems] Found sidebar items:', sidebarItems.length);
    }

    // Loop through all sidebar items and hide those that match
    let hiddenCount = 0;
    sidebarItems.forEach(item => {
      try {
        // Get the text content of the item
        const itemText = (item as HTMLElement).innerText?.toLowerCase() || '';
        if (DEBUG) {
          console.log(`[HideSidebarItems] Checking item with text: "${itemText}"`);
        }

        // Check if this item should be hidden - using both exact match and contains
        const shouldHide = normalizedItemsToHide.some(hideText => {
          // Check for exact match
          if (itemText === hideText) {
            if (DEBUG) console.log(`[HideSidebarItems] Found exact match for: ${hideText}`);
            return true;
          }

          // Check if the item text contains the text to hide
          if (itemText.includes(hideText)) {
            if (DEBUG) console.log(`[HideSidebarItems] Found partial match for: ${hideText} in ${itemText}`);
            return true;
          }

          return false;
        });

        if (shouldHide) {
          // Find the parent <a> element to hide
          const linkElement = item.closest('a');
          if (linkElement) {
            linkElement.style.display = 'none';
            hiddenCount++;
            if (DEBUG) {
              console.log(`[HideSidebarItems] Hidden item: ${itemText}`);
            }
          } else {
            // If we can't find a parent <a>, try to hide the item itself
            (item as HTMLElement).style.display = 'none';
            hiddenCount++;
            if (DEBUG) {
              console.log(`[HideSidebarItems] Hidden item directly: ${itemText}`);
            }
          }
        }
      } catch (error) {
        console.error('[HideSidebarItems] Error processing sidebar item:', error);
      }
    });

    if (DEBUG) {
      console.log(`[HideSidebarItems] Total items hidden: ${hiddenCount}`);
    }
  };

  // Function to specifically target and remove search elements
  const removeSearchElements = () => {
    if (DEBUG) {
      console.log('[HideSidebarItems] Looking for search elements to remove');
    }

    // Try various selectors that might match search elements
    const searchSelectors = [
      'nav input[type="search"]',
      'nav input[placeholder*="search" i]',
      'nav input[aria-label*="search" i]',
      'nav button:has(svg[data-icon="search"])',
      'nav svg[data-icon="search"]',
      'nav svg[data-testid="search-icon"]',
      'nav svg[aria-label*="search" i]',
      'nav i.search-icon',
      'nav form[role="search"]',
      'nav .search-container',
      'nav .search-box',
      'nav .search-wrapper',
      // Add more selectors as needed
    ];

    // Try each selector
    searchSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0 && DEBUG) {
          console.log(`[HideSidebarItems] Found ${elements.length} search elements with selector: ${selector}`);
        }

        elements.forEach(element => {
          // Try to find the most appropriate parent to remove
          // This helps eliminate gaps in the layout
          const parents = [
            element.closest('li'),
            element.closest('.search-container'),
            element.closest('.search-wrapper'),
            element.closest('form'),
            element.closest('div'),
            element.parentElement
          ].filter(Boolean) as Element[];

          if (parents.length > 0) {
            // Remove the first (closest) parent that exists
            const parent = parents[0];
            if (parent) {
              parent.remove();
              if (DEBUG) {
                console.log(`[HideSidebarItems] Removed search element parent: ${parent.tagName}`);
              }
            }
          } else {
            // If no suitable parent found, remove the element itself
            element.remove();
            if (DEBUG) {
              console.log(`[HideSidebarItems] Removed search element directly`);
            }
          }
        });
      } catch (error) {
        console.error(`[HideSidebarItems] Error processing search selector ${selector}:`, error);
      }
    });

    // Also try to find elements by text content
    try {
      const allElements = document.querySelectorAll('nav *');
      allElements.forEach(element => {
        const text = element.textContent?.toLowerCase() || '';
        if (text.includes('search')) {
          // Find the appropriate parent to remove
          const parent = element.closest('li') || element.closest('div') || element.parentElement;
          if (parent) {
            parent.remove();
            if (DEBUG) {
              console.log(`[HideSidebarItems] Removed element with 'search' text: ${text}`);
            }
          }
        }
      });
    } catch (error) {
      console.error('[HideSidebarItems] Error searching for text content:', error);
    }
  };

  // Run immediately
  checkAndHideSidebarItems();

  // Also set up a MutationObserver to handle dynamically loaded sidebar items
  const observer = new MutationObserver((mutations) => {
    // Check if any of the mutations involve the sidebar
    const shouldCheck = mutations.some(mutation => {
      return mutation.target.nodeName === 'NAV' ||
             mutation.target.nodeName === 'A' ||
             mutation.target.nodeName === 'SPAN';
    });

    if (shouldCheck) {
      checkAndHideSidebarItems();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });

  // Return the observer so it can be disconnected if needed
  return observer;
};
