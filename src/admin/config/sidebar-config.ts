/**
 * Configuration for sidebar navigation items
 *
 * This file contains the configuration for which sidebar items should be hidden.
 * Modify the HIDDEN_SIDEBAR_ITEMS array to control which items are hidden.
 */

/**
 * Array of sidebar item labels to hide (case-insensitive)
 *
 * Examples:
 * - "Products"
 * - "Customers"
 * - "Orders"
 * - "Settings"
 *
 * You can also use partial matches, which will hide any item containing the text.
 * For example, "Product" will hide both "Products" and "Product Categories".
 */
export const HIDDEN_SIDEBAR_ITEMS: string[] = [
  "Products",
  "Customers",
  "Price Lists",
  "Promotions",
  "Inventory",
  "Orders",
  "Search"
  // Add or remove items as needed
];

/**
 * Debug mode - set to true to see console logs
 */
export const DEBUG_MODE = true;

/**
 * Update this file to change which sidebar items are hidden
 *
 * The changes will take effect when you reload the page.
 *
 * Note: This configuration is used by both the React widget and the direct script injection.
 */
