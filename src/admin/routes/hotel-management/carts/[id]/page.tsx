import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import HideSidebarItemsWidget from "../../../../widgets/hide-sidebar-items-widget";
import CartDetail from "../../../../components/cart/cart-detail";

const CartDetailPage = () => {
  const { id } = useParams();

  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <CartDetail cartId={id} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Cart Details",
});

export default CartDetailPage;
