import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import CartList from "../../../components/cart/cart-list";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";

const CartsPage = () => {
  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <CartList />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Carts",
});

export default CartsPage;
