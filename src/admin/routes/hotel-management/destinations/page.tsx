import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ClockSolid, PlusMini, MagnifyingGlass } from "@camped-ai/icons";
import {
  Upload as UploadIcon,
  Download,
  Grid as GridIcon,
  List as ListIcon,
} from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Badge,
  FocusModal,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { DestinationData } from "../../../types";
import DestinationFormModern from "../../../components/destination-form-modern";
import { DestinationFormData } from "../../../components/destination-form";
import BulkImportModal from "../../../components/destination/bulk-import-modal";
import ExportModal from "../../../components/destination/export-modal";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";

interface DestinationWithImages extends DestinationData {
  images?: Array<{
    id?: string;
    url: string;
    isThumbnail?: boolean;
    metadata?: Record<string, any>;
  }>;
  thumbnailUrl?: string;
  hotelCount?: number;
}

const DestinationsPage = () => {
  const navigate = useNavigate();
  const [destinations, setDestinations] = useState<DestinationWithImages[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterFeatured, setFilterFeatured] = useState<boolean | null>(null);
  const [open, setOpen] = useState(false);
  const [bulkImportOpen, setBulkImportOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [hotelCounts, setHotelCounts] = useState<Record<string, number>>({});
  const [formData, setFormData] = useState<DestinationFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    currency: "",
    location: null,
    tags: null,
    website: null,
    category_id: "",
    media: [],
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(0);
  const pageLimit = 20;
  const [count, setCount] = useState(0);

  // Pagination calculations
  const pagesCount = useMemo(() => {
    return count / pageLimit;
  }, [count]);
  const canNextPage = useMemo(
    () => currentPage < pagesCount - 1,
    [currentPage, pagesCount]
  );
  const canPreviousPage = useMemo(() => currentPage > 0, [currentPage]);

  // Filtered destinations based on search query and featured filter
  const filteredDestinations = useMemo(() => {
    return destinations.filter((destination) => {
      // Apply search filter
      const matchesSearch =
        !searchQuery.trim() ||
        destination.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        destination.country
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        destination.location?.toLowerCase().includes(searchQuery.toLowerCase());

      // Apply featured filter
      const matchesFeatured =
        filterFeatured === null || destination.is_featured === filterFeatured;

      return matchesSearch && matchesFeatured;
    });
  }, [destinations, searchQuery, filterFeatured]);

  // Pagination functions
  const nextPage = () => {
    if (canNextPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const previousPage = () => {
    if (canPreviousPage) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  // Get a gradient color based on index
  const getGradient = (index: number) => {
    const gradients = [
      "from-blue-500 to-purple-500",
      "from-green-500 to-teal-500",
      "from-yellow-500 to-orange-500",
      "from-pink-500 to-rose-500",
      "from-indigo-500 to-blue-500",
      "from-red-500 to-pink-500",
    ];
    return gradients[index % gradients.length];
  };

  const fetchHotelCounts = async () => {
    try {
      const response = await fetch(
        `/admin/hotel-management/destinations/hotel-counts`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch hotel counts");
      }

      const { counts } = await response.json();
      setHotelCounts(counts);
      return counts;
    } catch (error) {
      console.error("Error fetching hotel counts:", error);
      return {};
    }
  };

  const fetchDestinations = async () => {
    setIsLoading(true);
    try {
      const query = new URLSearchParams({
        limit: `${pageLimit}`,
        offset: `${pageLimit * currentPage}`,
      });

      // Fetch destinations and hotel counts in parallel
      const [destinationsResponse, counts] = await Promise.all([
        fetch(`/admin/hotel-management/destinations?${query.toString()}`, {
          credentials: "include",
        }),
        fetchHotelCounts(),
      ]);

      if (!destinationsResponse.ok) {
        throw new Error("Failed to fetch destinations");
      }

      const { destinations: data, count } = await destinationsResponse.json();

      // Fetch images for each destination
      const destinationsWithImages = await Promise.all(
        data.map(async (destination: any) => {
          try {
            // Fetch images for this destination
            const imagesResponse = await fetch(
              `/admin/hotel-management/destinations/${destination.id}/images`,
              { credentials: "include" }
            );

            if (imagesResponse.ok) {
              const imagesData = await imagesResponse.json();

              if (imagesData.images && imagesData.images.length > 0) {
                // Find thumbnail image or use the first image
                const thumbnailImage =
                  imagesData.images.find(
                    (img: any) => img.metadata?.isThumbnail
                  ) || imagesData.images[0];

                return {
                  ...destination,
                  images: imagesData.images,
                  thumbnailUrl: thumbnailImage.url,
                  hotelCount: counts[destination.id] || 0,
                };
              }
            }

            // Return destination without images if fetch failed or no images
            return {
              ...destination,
              hotelCount: counts[destination.id] || 0,
            };
          } catch (error) {
            console.error(
              `Error fetching images for destination ${destination.id}:`,
              error
            );
            return {
              ...destination,
              hotelCount: counts[destination.id] || 0,
            };
          }
        })
      );

      setDestinations(destinationsWithImages);
      setCount(count);
    } catch (error) {
      console.error("Error fetching destinations:", error);
      toast.error("Error", {
        description: "Failed to load destinations",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDestinations();
  }, [currentPage]);

  const handleCreate = async (updatedData?: DestinationFormData) => {
    // Use the updated data if provided, otherwise use the current formData
    const dataToUse = updatedData || formData;
    try {
      // First create the destination
      // Ensure tags is properly formatted
      let formattedTags = dataToUse.tags;
      if (typeof dataToUse.tags === "string") {
        try {
          // Try to parse if it's a JSON string
          formattedTags = JSON.parse(dataToUse.tags as string);
        } catch (e) {
          // If not a valid JSON, split by comma
          formattedTags = (dataToUse.tags as string)
            .split(",")
            .map((tag) => tag.trim());
        }
      }

      const destinationData = {
        name: dataToUse.name,
        handle: dataToUse.handle,
        description: dataToUse.description,
        is_active: dataToUse.is_active,
        is_featured: dataToUse.is_featured,
        country: dataToUse.country,
        currency: dataToUse.currency,
        location: dataToUse.location,
        tags: formattedTags,
        website: dataToUse.website,
        category_id: dataToUse.category_id,
      };

      const response = await fetch("/admin/hotel-management/destinations", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const data = await response.json();

      if (response.ok && data.destination) {
        // If there are images to upload
        if (dataToUse.media && dataToUse.media.length > 0) {
          const destinationId = data.destination.id;

          // Upload each image
          for (const media of dataToUse.media) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              // Add metadata including thumbnail flag
              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  console.error(
                    "Failed to upload image",
                    await uploadResponse.text()
                  );
                }
              } catch (uploadError) {
                console.error("Error uploading image:", uploadError);
              }
            }
          }
        }

        toast.success("Success", {
          description: "Destination created successfully",
        });

        // Reset form data to initial state
        setFormData({
          name: "",
          handle: "",
          description: "",
          is_active: true,
          is_featured: false,
          country: "",
          currency: "",
          location: null,
          tags: null,
          website: null,
          category_id: "",
          media: [],
        });

        fetchDestinations();
        setOpen(false);
        return true;
      } else {
        toast.error("Error", {
          description: data.message || "Failed to create destination",
        });
        return false; // Return false on failure
      }
    } catch (error) {
      console.error("Error creating destination:", error);
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      return false;
    }
  };

  return (
    <>
      <HideSidebarItemsWidget />
      <Toaster />

      {/* Create Destination Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <DestinationFormModern
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreate}
          closeModal={() => setOpen(false)}
        />
      </FocusModal>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={bulkImportOpen}
        onClose={() => {
          setBulkImportOpen(false);
          // Refresh data when modal closes with a slight delay to ensure server has processed everything
          setTimeout(() => {
            console.log("Refreshing destinations after import");
            fetchDestinations();
          }, 500);
        }}
      />

      {/* Export Modal */}
      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />

      <Container className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1" className="text-2xl">
              Destinations
            </Heading>
            <Text className="text-gray-500">
              Manage your travel destinations
            </Text>
          </div>

          <div className="flex gap-2">
            <OutlineButton
              size="small"
              onClick={() => setExportModalOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </OutlineButton>
            <OutlineButton
              size="small"
              onClick={() => setBulkImportOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <UploadIcon className="w-4 h-4" />
              <span>Import</span>
            </OutlineButton>
            <Button
              variant="primary"
              size="small"
              onClick={() => setOpen(true)}
              className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <PlusMini className="w-4 h-4" />
              <span>Add Destination</span>
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search destinations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-9"
            />
          </div>

          <div className="flex gap-2">
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("grid")}
                className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                  viewMode === "grid"
                    ? " text-white"
                    : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
                }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </Button>

              <Button
                variant={viewMode === "list" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("list")}
                className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                  viewMode === "list"
                    ? " text-white"
                    : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
                }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Text className="font-medium">Filter:</Text>
          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() =>
                setFilterFeatured(filterFeatured === true ? null : true)
              }
              className={`px-3 py-1 rounded-md ${
                filterFeatured === true
                  ? "bg-purple-500 text-white"
                  : "bg-white border border-gray-300 text-gray-700"
              }`}
            >
              Featured
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={() =>
                setFilterFeatured(filterFeatured === false ? null : false)
              }
              className={`px-3 py-1 rounded-md ${
                filterFeatured === false
                  ? "bg-gray-500 text-white"
                  : "bg-white border border-gray-300 text-gray-700"
              }`}
            >
              Not Featured
            </Button>
            {filterFeatured !== null && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => setFilterFeatured(null)}
                className="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-700"
              >
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {isLoading ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="h-64 animate-pulse bg-gray-100 rounded-lg"
                ></div>
              ))}
            </div>
          ) : (
            <div className="animate-pulse space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-16 bg-gray-100 rounded-lg"></div>
              ))}
            </div>
          )
        ) : filteredDestinations.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDestinations.map((destination, index) => (
                <div
                  key={destination.id}
                  className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-all cursor-pointer flex flex-col"
                  onClick={() =>
                    navigate(
                      `/hotel-management/destinations/${destination.handle}`
                    )
                  }
                >
                  <div className="h-40 relative flex-shrink-0">
                    {destination.thumbnailUrl ? (
                      <>
                        <img
                          src={destination.thumbnailUrl}
                          alt={destination.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                          <Heading
                            level="h3"
                            className="text-white text-2xl font-bold"
                          >
                            {destination.name}
                          </Heading>
                        </div>
                      </>
                    ) : (
                      <div
                        className={`h-full w-full bg-gradient-to-r ${getGradient(
                          index
                        )} flex items-center justify-center`}
                      >
                        <Heading
                          level="h3"
                          className="text-white text-2xl font-bold"
                        >
                          {destination.name}
                        </Heading>
                      </div>
                    )}
                    {/* Hotel count badge */}
                    <div className="absolute top-2 right-2 bg-white bg-opacity-90 rounded-full px-2 py-1 text-xs font-medium flex items-center gap-1 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-3.5 h-3.5 text-blue-600"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                      </svg>
                      <span className="text-gray-800">
                        {destination.hotelCount || 0} Hotels
                      </span>
                    </div>
                  </div>
                  <div className="p-4 flex flex-col flex-grow">
                    <div className="flex-grow flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <Text className="font-medium">
                          {destination.country}
                        </Text>
                        <div className="flex gap-1">
                          <Badge
                            color={destination.is_active ? "green" : "grey"}
                          >
                            {destination.is_active ? "Active" : "Inactive"}
                          </Badge>
                          {destination.is_featured && (
                            <Badge color="purple">Featured</Badge>
                          )}
                        </div>
                      </div>
                      <Text className="text-sm text-gray-500 line-clamp-2 mb-3">
                        {destination.description || "No description available"}
                      </Text>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {(() => {
                          // Handle different formats of tags
                          let tagsArray: string[] = [];

                          if (destination.tags) {
                            if (Array.isArray(destination.tags)) {
                              tagsArray = destination.tags;
                            } else if (typeof destination.tags === "string") {
                              try {
                                // Try to parse JSON string
                                const parsed = JSON.parse(
                                  destination.tags as string
                                );
                                tagsArray = Array.isArray(parsed) ? parsed : [];
                              } catch (e) {
                                // If not valid JSON, try comma-separated
                                tagsArray = (destination.tags as string)
                                  .split(",")
                                  .map((t: string) => t.trim());
                              }
                            } else if (typeof destination.tags === "object") {
                              // Handle case where tags might be an object
                              tagsArray = Object.values(destination.tags);
                            }
                          }

                          return tagsArray.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                            >
                              {tag}
                            </span>
                          ));
                        })()}
                      </div>
                    </div>

                    {/* Action buttons */}
                    <div className="flex gap-2 mt-auto">
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(
                            `/hotel-management/destinations/${destination.handle}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path
                            fillRule="evenodd"
                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span>View</span>
                      </Button>
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Navigate to hotels filtered by this destination
                          navigate(
                            `/hotel-management/hotels?destination=${destination.id}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                        </svg>
                        <span>Hotels</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Destination
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Country
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Hotels
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDestinations.map((destination) => (
                    <tr
                      key={destination.id}
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() =>
                        navigate(
                          `/hotel-management/destinations/${destination.handle}`
                        )
                      }
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 relative">
                            {destination.thumbnailUrl ? (
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={destination.thumbnailUrl}
                                alt=""
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-md bg-gradient-to-r from-blue-500 to-purple-500"></div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {destination.name}
                            </div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {destination.description || "No description"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {destination.country || "--"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-medium">
                          {destination.hotelCount || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex gap-1">
                          <Badge
                            color={destination.is_active ? "green" : "grey"}
                          >
                            {destination.is_active ? "Active" : "Inactive"}
                          </Badge>
                          {destination.is_featured && (
                            <Badge color="purple">Featured</Badge>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(
                                `/hotel-management/destinations/${destination.handle}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path
                                fillRule="evenodd"
                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span>View</span>
                          </Button>
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Navigate to hotels filtered by this destination
                              navigate(
                                `/hotel-management/hotels?destination=${destination.id}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                            </svg>
                            <span>Hotels</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )
        ) : (
          <div className="text-center py-12">
            <Text className="text-gray-500">
              {searchQuery || filterFeatured !== null
                ? "No destinations match your search criteria"
                : "No destinations found"}
            </Text>
          </div>
        )}

        {/* Pagination controls */}
        {!searchQuery && filterFeatured === null && destinations.length > 0 && (
          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <Button
              variant="secondary"
              size="small"
              onClick={previousPage}
              disabled={!canPreviousPage}
            >
              Previous
            </Button>
            <Text>
              Page {currentPage + 1} of {Math.max(1, Math.ceil(pagesCount))}
            </Text>
            <Button
              variant="secondary"
              size="small"
              onClick={nextPage}
              disabled={!canNextPage}
            >
              Next
            </Button>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Destinations",
  icon: ClockSolid,
});

export default DestinationsPage;
