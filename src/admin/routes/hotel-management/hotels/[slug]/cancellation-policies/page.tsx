import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { ChevronLeft } from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Clock, Hotel, Shield } from "lucide-react";
import { HotelData } from "../../../../../types";
import CancellationPolicyList from "../../../../../components/hotel/cancellation-policy/cancellation-policy-list";

const HotelCancellationPoliciesPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchHotelDetails = async () => {
      try {
        const hotelResponse = await fetch(
          `/admin/hotel-management/hotels/${slug}`,
          {
            credentials: "include",
          }
        );
        const hotelData = await hotelResponse.json();
        const processedHotelData = Array.isArray(hotelData?.hotel)
          ? hotelData?.hotel[0]
          : hotelData?.hotel || hotelData;

        setHotel(processedHotelData);
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to fetch hotel details:", error);
        toast.error("Error", {
          description: "Failed to load hotel details",
        });
        setIsLoading(false);
      }
    };

    fetchHotelDetails();
  }, [slug]);

  if (isLoading) {
    return (
      <Container>
        <Text>Loading hotel details...</Text>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container>
        <Text>Hotel not found</Text>
        <Button
          variant="secondary"
          onClick={() => navigate("/hotel-management/hotels")}
          className="mt-4"
        >
          Back to Hotels
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Toaster />
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 mb-6">
        <Container className="py-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
            className="mb-4 bg-white hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back to Hotel
          </Button>
          <div className="flex items-center mb-2">
            <Shield className="w-8 h-8 text-blue-600 mr-3" />
            <Heading level="h1" className="text-2xl font-bold text-blue-800">
              {hotel.name} - Cancellation Policies
            </Heading>
          </div>
          <Text className="text-gray-600 ml-11 mb-4">
            Define when and how guests can cancel their bookings and what refunds they receive
          </Text>
        </Container>
      </div>

      <Container className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6">
            <CancellationPolicyList hotelId={hotel.id} />
          </div>
        </div>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Cancellation Policies",
  icon: Shield,
});

export default HotelCancellationPoliciesPage;
