import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Heading, Text, Container, Button, Badge, Select, Toaster, toast } from "@camped-ai/ui";
import HousekeepingDashboard from "../../../../../components/housekeeping/housekeeping-dashboard";
import { ChevronLeft } from "@camped-ai/icons";
import { RefreshCw } from "lucide-react";
import { format, addDays } from "date-fns";

// Simple Spinner component
const Spinner = ({ size = "medium" }) => {
  const sizeClass = {
    small: "w-4 h-4",
    medium: "w-8 h-8",
    large: "w-12 h-12",
  }[size];

  return (
    <div className="flex items-center justify-center">
      <div
        className={`${sizeClass} animate-spin rounded-full border-2 border-gray-200 border-t-gray-800`}
      />
    </div>
  );
};

export default function MaintenancePage() {
  const { slug } = useParams();
  const navigate = useNavigate();

  const [hotel, setHotel] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [availableRooms, setAvailableRooms] = useState<any[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<string>(format(new Date(), "yyyy-MM-dd"));
  const [endDate, setEndDate] = useState<string>(format(addDays(new Date(), 7), "yyyy-MM-dd"));

  // Fetch real hotel data
  useEffect(() => {
    const fetchHotel = async () => {
      try {
        setIsLoading(true);

        // Fetch real hotel data
        console.log(`Fetching hotel data for slug: ${slug}`);

        // Try different endpoints to fetch hotel data
        const endpoints = [
          `/admin/hotel-management/hotels/${slug}`,
          `/admin/hotels/${slug}`
        ];

        let hotelData = null;
        let apiSuccess = false;

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch hotel data from ${endpoint}`);
            const response = await fetch(endpoint, {
              headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
              }
            });
            console.log(`Hotel response status from ${endpoint}: ${response.status} ${response.statusText}`);

            if (response.ok) {
              const data = await response.json();
              console.log('Hotel API response:', data);

              if (data.hotel) {
                hotelData = data.hotel;
                apiSuccess = true;
                console.log('Loaded hotel data from API:', hotelData);
                break;
              }
            }
          } catch (endpointError) {
            console.warn(`Error fetching from ${endpoint}:`, endpointError);
          }
        }

        if (apiSuccess && hotelData) {
          setHotel(hotelData);
        } else {
          // Fallback to default data if API returns empty result
          setHotel({
            id: slug || '01JQV5KJZYJBK75VVZYYJDAVFW',
            name: 'Demo Hotel',
            description: 'Hotel maintenance management',
            address: '123 Main St, Anytown, USA',
            destination_id: 'dest_01',
            destination_name: 'Demo Destination',
            images: [],
            metadata: {},
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
          console.log('Using fallback hotel data');
        }
      } catch (error) {
        console.error("Error fetching hotel data:", error);
        // Fallback to default data if fetch fails
        setHotel({
          id: slug || '01JQV5KJZYJBK75VVZYYJDAVFW',
          name: 'Demo Hotel',
          description: 'Hotel maintenance management',
          address: '123 Main St, Anytown, USA',
          destination_id: 'dest_01',
          destination_name: 'Demo Destination',
          images: [],
          metadata: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchHotel();
  }, [slug]);

  // Function to fetch available rooms
  const fetchAvailableRooms = async () => {
    try {
      setIsLoadingRooms(true);

      if (!hotel || !hotel.id) {
        toast.error("Hotel information is missing");
        return;
      }

      // Validate dates
      if (new Date(startDate) >= new Date(endDate)) {
        toast.error("Start date must be before end date");
        return;
      }

      console.log(`Fetching available rooms for hotel: ${hotel.id}, from ${startDate} to ${endDate}`);

      // Try different endpoints to fetch available rooms
      const availableRoomsEndpoints = [
        `/admin/room-availability/available-rooms?hotel_id=${hotel.id}&start_date=${startDate}&end_date=${endDate}`,
        `/admin/hotel-management/available-rooms?hotel_id=${hotel.id}&start_date=${startDate}&end_date=${endDate}`
      ];

      let roomsData = null;
      let apiSuccess = false;

      for (const endpoint of availableRoomsEndpoints) {
        try {
          console.log(`Trying to fetch available rooms from ${endpoint}`);
          const response = await fetch(endpoint, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          console.log(`Available rooms response status from ${endpoint}: ${response.status} ${response.statusText}`);

          if (response.ok) {
            const data = await response.json();
            console.log('Available rooms API response:', data);

            if (data.rooms && Array.isArray(data.rooms)) {
              roomsData = data.rooms;
              apiSuccess = true;
              break;
            } else if (Array.isArray(data) && data.length >= 0) {
              roomsData = data;
              apiSuccess = true;
              break;
            }
          }
        } catch (endpointError) {
          console.warn(`Error fetching from ${endpoint}:`, endpointError);
        }
      }

      if (apiSuccess && roomsData) {
        setAvailableRooms(roomsData);
        console.log(`Found ${roomsData.length} available rooms`);

        if (roomsData.length === 0) {
          toast.info("No available rooms found for the selected date range");
        }
      } else {
        // If API fails, set empty array
        setAvailableRooms([]);
        toast.error("Failed to fetch available rooms");
      }
    } catch (error) {
      console.error("Error fetching available rooms:", error);
      toast.error("An error occurred while fetching available rooms");
      setAvailableRooms([]);
    } finally {
      setIsLoadingRooms(false);
    }
  };

  // Fetch available rooms when hotel data is loaded
  useEffect(() => {
    if (hotel && hotel.id) {
      fetchAvailableRooms();
    }
  }, [hotel]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spinner size="medium" />
      </div>
    );
  }

  if (!hotel) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <Heading level="h1">Hotel Not Found</Heading>
        <Text>The hotel you are looking for does not exist or you do not have permission to view it.</Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header with Back Button and Title */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate('/hotel-management/hotels')}
            className="flex items-center gap-1 shadow-sm"
          >
            <ChevronLeft className="w-5 h-5" />
            Back to Hotels
          </Button>
          <div>
            <Heading level="h1" className="text-2xl font-bold">
              {hotel?.name || "Demo Hotel"} - Maintenance
            </Heading>
            <Text className="text-gray-500 text-sm">
              Manage hotel maintenance tasks, room repairs, and maintenance status
            </Text>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4" role="alert">
        <p className="font-bold">Maintenance Module</p>
        <p>This module allows you to manage room maintenance status and repair tasks. The system will attempt to use real room data from the database, with fallback to sample data if needed.</p>
      </div>

      <Container>
        <div className="grid grid-cols-1 gap-6">
          {/* Available Rooms Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <Heading level="h2" className="text-xl font-semibold">Available Rooms</Heading>
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  size="small"
                  onClick={fetchAvailableRooms}
                  disabled={isLoadingRooms}
                  className="flex items-center gap-1"
                >
                  {isLoadingRooms ? (
                    <Spinner size="small" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>
            <p className="text-gray-600 mb-4">View all available rooms for the selected date range.</p>

            {/* Date Range Selector */}
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex flex-col gap-1">
                <Text className="text-sm font-medium">Start Date</Text>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div className="flex flex-col gap-1">
                <Text className="text-sm font-medium">End Date</Text>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div className="flex items-end">
                <Button
                  variant="primary"
                  size="small"
                  onClick={fetchAvailableRooms}
                  disabled={isLoadingRooms}
                >
                  Search Available Rooms
                </Button>
              </div>
            </div>

            {/* Available Rooms List */}
            {isLoadingRooms ? (
              <div className="flex justify-center items-center h-32">
                <Spinner size="medium" />
              </div>
            ) : availableRooms.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {availableRooms.map((room) => (
                  <div key={room.id} className="border border-gray-200 rounded-lg bg-white shadow-sm overflow-hidden">
                    <div className="bg-green-50 p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <Text className="font-bold text-lg">{room.title || `Room ${room.id.substring(0, 6)}`}</Text>
                          {room.product && (
                            <Text className="text-sm text-gray-600">{room.product.title || 'Unknown Type'}</Text>
                          )}
                        </div>
                        <Badge color="green">Available</Badge>
                      </div>
                    </div>
                    <div className="p-3">
                      <div className="flex flex-col gap-1">
                        {room.options && room.options.length > 0 && (
                          <div className="text-xs text-gray-500">
                            {room.options.map((option: any) => (
                              <span key={option.id} className="mr-2">
                                {option.value}
                              </span>
                            ))}
                          </div>
                        )}
                        {room.prices && room.prices.length > 0 && (
                          <Text className="text-sm font-medium">
                            Price: {(room.prices[0].amount / 100).toFixed(2)} {room.prices[0].currency_code}
                          </Text>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-50 p-4 rounded-md text-center">
                <Text>No available rooms found for the selected date range.</Text>
              </div>
            )}
          </div>

          {/* Room Maintenance Status Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <Heading level="h2" className="text-xl font-semibold mb-4">Room Maintenance Status</Heading>
            <p className="text-gray-600 mb-4">View and manage the maintenance status of all rooms in this hotel.</p>
            <HousekeepingDashboard hotelId={hotel.id} />
          </div>
        </div>
      </Container>
      <Toaster />
    </div>
  );
}
