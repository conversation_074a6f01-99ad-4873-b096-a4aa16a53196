import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { ArrowLeft, Tags } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import HotelPricingManager from "../../../../../components/hotel/pricing/hotel-pricing-manager";

interface Hotel {
  id: string;
  name: string;
}

interface RoomConfig {
  id: string;
  title: string;
  handle?: string;
  description?: string;
}

const HotelPricingPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  // No need for activeTab state anymore as we're showing DetailedPricing directly

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch hotel details
        const hotelResponse = await fetch(`/admin/hotel-management/hotels/${slug}`, {
          credentials: "include",
        });

        if (!hotelResponse.ok) {
          throw new Error("Failed to fetch hotel details");
        }

        const hotelData = await hotelResponse.json();
        const hotel = Array.isArray(hotelData?.hotel) ? hotelData?.hotel[0] : hotelData?.hotel;

        setHotel(hotel);

        // Fetch room configurations
        const roomConfigsResponse = await fetch(
          `/admin/direct-room-configs?hotel_id=${hotel.id}`,
          {
            credentials: "include",
          }
        );

        if (!roomConfigsResponse.ok) {
          throw new Error("Failed to fetch room configurations");
        }

        const roomConfigsData = await roomConfigsResponse.json();

        setRoomConfigs(roomConfigsData.roomConfigs || []);


        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error", {
          description: "Failed to load hotel data",
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Tags className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <Text className="text-gray-500 mb-4">Hotel not found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <Toaster />
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 mb-6">
        <Container className="py-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
            className="mb-4 bg-white hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Hotel
          </Button>
          <div className="flex items-center mb-2">
            <Tags className="w-8 h-8 text-blue-600 mr-3" />
            <Heading level="h1" className="text-2xl font-bold text-blue-800">
              {hotel.name} - Pricing Management
            </Heading>
          </div>
          <Text className="text-gray-600 ml-11 mb-4">
            Manage base rates, seasonal pricing, and special offers for your hotel rooms
          </Text>
        </Container>
      </div>

      <Container className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <HotelPricingManager
            hotelId={hotel.id}
            roomConfigs={roomConfigs}
            onBack={() => navigate(`/hotel-management/hotels/${slug}`)}
          />
        </div>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: Tags,
});

export default HotelPricingPage;
