import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { Edit, Upload as UploadIcon, Download } from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  FocusModal,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./[slug]/modal-fix.css"; // Import custom CSS to fix z-index issues with modals
import { HotelFormData } from "../../../components/hotel-form-modern";
import HotelFormModern from "../../../components/hotel-form-modern";
import { HotelData } from "../../../types";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";
import BulkImportModal from "../../../components/hotel/bulk-import-modal";
import ExportModal from "../../../components/hotel/export-modal";

interface Hotel extends HotelData {
  destination_name?: string;
  image_url?: string;
  star?: number;
  is_internal?: boolean;
  is_featured?: boolean;
}

const HotelsPage = () => {
  const navigate = useNavigate();
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [bulkImportOpen, setBulkImportOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [formData, setFormData] = useState<HotelFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    is_pets_allowed: false,
    website: null,
    email: null,
    destination_id: "",
    media: [], // Initialize with empty media array
    image_ids: [], // Initialize with empty image IDs array
  });
  const [showFilters, setShowFilters] = useState(false);
  const [filterDestination, setFilterDestination] = useState("");
  const [filterStars, setFilterStars] = useState<number[]>([]);
  const [filterFeatured, setFilterFeatured] = useState<boolean | null>(null);
  const [destinations, setDestinations] = useState<
    { id: string; name: string }[]
  >([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [roomTypes, setRoomTypes] = useState([]);

  // Get destination ID from URL query parameter
  const [searchParams] = useSearchParams();
  const destinationIdFromUrl = searchParams.get("destination");

  useEffect(() => {
    // First fetch destinations, then fetch hotels
    // This ensures we have destination data when processing hotel data
    const loadData = async () => {
      const destinations: any = await fetchDestinations();
      await fetchHotels(destinations);
      await fetchRoomTypes();

      // If destination ID is in URL, set the filter but don't show filters panel
      if (destinationIdFromUrl) {
        setFilterDestination(destinationIdFromUrl);
        // Don't automatically show filters panel
      }
    };

    loadData();
  }, [destinationIdFromUrl]);

  const fetchRoomTypes = async () => {
    try {
      const response = await fetch(`/admin/room-types`, {
        credentials: "include",
      });
      const data = await response.json();
      if (data.roomTypes) {
        setRoomTypes(data.roomTypes);
      }
    } catch (error) {
      console.error("Error fetching room types:", error);
    }
  };

  const fetchDestinations = async (): Promise<void> => {
    try {
      const response = await fetch("/admin/hotel-management/destinations", {
        credentials: "include",
      });
      const data = await response.json();

      if (data.destinations) {
        // Map the API response to the expected format
        const destinationsData = data.destinations.map((dest: any) => ({
          id: dest.id,
          name: dest.name,
        }));
        setDestinations(destinationsData);
        return destinationsData;
      } else {
        console.error("No destinations data found in API response");
        setDestinations([]);
      }
    } catch (error) {
      console.error("Error fetching destinations:", error);
      toast.error("Error", {
        description: "Failed to load destinations",
      });
      setDestinations([]);
    }
  };

  // Fetch destination details for a specific ID
  const fetchDestinationDetails = async (
    destinationId: string
  ): Promise<any> => {
    try {
      const response = await fetch(
        `/admin/hotel-management/destinations/${destinationId}`,
        {
          credentials: "include",
        }
      );
      const data = await response.json();

      if (
        data.destination &&
        Array.isArray(data.destination) &&
        data.destination.length > 0
      ) {
        return data.destination[0];
      } else if (data.destination && !Array.isArray(data.destination)) {
        return data.destination;
      }
      return null;
    } catch (error) {
      console.error(
        `Error fetching destination details for ID ${destinationId}:`,
        error
      );
      return null;
    }
  };

  const fetchHotels = async (destinations: any[]) => {
    setIsLoading(true);
    try {
      // Build URL with query parameters if destination filter is active
      let url = "/admin/hotel-management/hotels";

      // If we have a destination filter from the URL, add it to the API request
      if (destinationIdFromUrl) {
        // Create a searchParams object to hold our filter parameters
        const params = new URLSearchParams();
        // Add destination_id as a JSON string in the searchParams parameter
        params.append(
          "searchParams",
          JSON.stringify({ destination_id: destinationIdFromUrl })
        );
        url = `${url}?${params.toString()}`;
      }

      const response = await fetch(url, {
        credentials: "include",
      });
      const data = await response.json();

      if (data.hotels && Array.isArray(data.hotels)) {
        // Process the hotels data to match our interface
        const processedHotelsPromises = data.hotels.map(async (hotel: any) => {
          // Find the destination name based on destination_id
          let destinationName = "Unknown";

          // First try to find in the already loaded destinations
          const destinationObj = destinations.find(
            (d) => d.id === hotel.destination_id
          );

          if (destinationObj) {
            destinationName = destinationObj.name;
          } else if (hotel.destination_id) {
            // If not found in loaded destinations, fetch it directly
            const destinationDetails = await fetchDestinationDetails(
              hotel.destination_id
            );
            if (destinationDetails) {
              destinationName = destinationDetails.name;

              // Update the destinations list with this new destination
              if (!destinations.some((d) => d.id === hotel.destination_id)) {
                setDestinations((prev) => [
                  ...prev,
                  {
                    id: hotel.destination_id,
                    name: destinationDetails.name,
                  },
                ]);
              }
            }
          }

          // Get the first image URL if available
          let imageUrl = null;
          if (hotel.images && hotel.images.length > 0) {
            // Try to find the thumbnail image first
            const thumbnailImage = hotel.images.find(
              (img: any) => img.metadata?.isThumbnail
            );
            imageUrl = thumbnailImage
              ? thumbnailImage.url
              : hotel.images[0].url;
          }

          // Extract star rating from hotel data (could be in rating field)
          const starRating = hotel.rating || hotel.star || 0;

          return {
            ...hotel,
            destination_name: destinationName,
            image_url: imageUrl,
            star: starRating,
          };
        });

        // Wait for all hotel processing to complete
        const processedHotels = await Promise.all(processedHotelsPromises);
        setHotels(processedHotels);
      } else {
        console.error("No hotels data found in API response");
        setHotels([]);
      }
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Error", {
        description: "Failed to load hotels",
      });
      setHotels([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = async (data: HotelFormData) => {
    try {
      // Separate files to upload from existing URLs
      const mediaArray = data.media ?? [];
      const filesToUpload = mediaArray.filter(
        (media) => media.file instanceof File
      );
      // Prepare data for submission without media
      const dataWithoutMedia = { ...data };
      delete dataWithoutMedia.media;
      delete dataWithoutMedia.image_ids;

      const response = await fetch("/admin/hotel-management/hotels", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataWithoutMedia),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to create hotel: ${response.status} ${response.statusText}`
        );
      }

      const responseData = await response.json();

      // Upload new files if any
      if (filesToUpload.length > 0) {
        const uploadPromises = filesToUpload.map(async (mediaFile) => {
          if (!(mediaFile.file instanceof File)) {
            throw new Error("Invalid file");
          }

          const formData = new FormData();
          formData.append("files", mediaFile.file);

          try {
            const hotelId = responseData?.hotel?.id;
            if (!hotelId) {
              throw new Error("No hotel ID available for image upload");
            }

            const response = await fetch(
              `/admin/hotel-management/hotels/${hotelId}/upload`,
              {
                method: "POST",
                body: formData,
                credentials: "include",
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Upload error response:", errorText);
              throw new Error(`File upload failed: ${errorText}`);
            }

            const uploadedFiles = await response.json();
            const uploadedFile = uploadedFiles[0];

            return {
              ...uploadedFile,
              isThumbnail: mediaFile.isThumbnail,
            };
          } catch (error) {
            console.error("File upload error:", error);
            throw error;
          }
        });

        await Promise.all(uploadPromises);
      }

      if (responseData.hotel) {
        // Refresh the hotels list to include the new hotel
        fetchHotels(destinations);

        toast.success("Success", {
          description: "Hotel created successfully",
        });

        setOpen(false);
        // Reset form data completely, including media array
        setFormData({
          name: "",
          handle: "",
          description: "",
          is_active: true,
          is_featured: false,
          is_pets_allowed: false,
          website: null,
          email: null,
          destination_id: "",
          media: [], // Explicitly reset media array
          image_ids: [], // Reset image IDs
          thumbnail_image_id: undefined, // Reset thumbnail
        });

        return true; // Return true on success
      } else {
        throw new Error("No hotel data returned from API");
      }
    } catch (error) {
      console.error("Error creating hotel:", error);
      toast.error("Error", {
        description: "Failed to create hotel",
      });
      return false; // Return false on error
    }
  };

  const filteredHotels = hotels.filter((hotel) => {
    // Apply search filter
    const matchesSearch =
      hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      hotel.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      hotel.destination_name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      hotel.address?.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply destination filter
    const matchesDestination =
      !filterDestination || hotel.destination_id === filterDestination;

    // Apply star rating filter
    const matchesStars =
      filterStars.length === 0 ||
      (hotel.star && filterStars.includes(hotel.star));

    // Apply featured filter
    const matchesFeatured =
      filterFeatured === null || hotel.is_featured === filterFeatured;

    return (
      matchesSearch && matchesDestination && matchesStars && matchesFeatured
    );
  });

  const toggleStarFilter = (star: number) => {
    if (filterStars.includes(star)) {
      setFilterStars(filterStars.filter((s) => s !== star));
    } else {
      setFilterStars([...filterStars, star]);
    }
  };

  return (
    <>
      <HideSidebarItemsWidget />
      <Toaster />
      <Container className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1" className="text-2xl">
              Hotels
            </Heading>
            <Text className="text-gray-500">Manage your hotel properties</Text>
          </div>

          <div className="flex gap-2">
            <OutlineButton
              size="small"
              onClick={() => setExportModalOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </OutlineButton>
            <OutlineButton
              size="small"
              onClick={() => setBulkImportOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <UploadIcon className="w-4 h-4" />
              <span>Import</span>
            </OutlineButton>
            <Button
              variant="primary"
              size="small"
              onClick={() => setOpen(true)}
              className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <PlusMini className="w-4 h-4" />
              <span>Add Hotel</span>
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search hotels..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-9"
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(!showFilters)}
              className="whitespace-nowrap bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-2 px-3 py-2 rounded-md transition-all"
            >
              <Adjustments className="w-4 h-4 text-gray-600" />
              <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
            </Button>

            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("grid")}
                className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                  viewMode === "grid"
                    ? " text-white"
                    : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
                }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </Button>

              <Button
                variant={viewMode === "list" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("list")}
                className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                  viewMode === "list"
                    ? " text-white"
                    : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
                }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Active filters summary - only shown when filters are applied */}
        {(filterDestination ||
          filterStars.length > 0 ||
          filterFeatured !== null) &&
          !showFilters && (
            <div className="flex flex-wrap items-center gap-1.5 bg-blue-50 border border-blue-100 rounded-md p-2 text-xs">
              <span className="font-medium text-blue-700">Active filters:</span>

              {filterDestination && (
                <div className="bg-white border border-blue-200 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 shadow-sm">
                  <span>
                    Destination:{" "}
                    {destinations.find((d) => d.id === filterDestination)
                      ?.name || "Unknown"}
                  </span>
                  <button
                    onClick={() => {
                      setFilterDestination("");
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.delete("destination");
                      navigate({ search: newSearchParams.toString() });
                    }}
                    className="ml-1 text-blue-500 hover:text-blue-700"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterStars.length > 0 && (
                <div className="bg-white border border-blue-200 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 shadow-sm">
                  <span>Stars: {filterStars.sort().join(", ")}★</span>
                  <button
                    onClick={() => setFilterStars([])}
                    className="ml-1 text-blue-500 hover:text-blue-700"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {filterFeatured !== null && (
                <div className="bg-white border border-blue-200 rounded-full px-2 py-0.5 flex items-center gap-1 text-blue-700 shadow-sm">
                  <span>
                    Status: {filterFeatured ? "Featured" : "Not Featured"}
                  </span>
                  <button
                    onClick={() => setFilterFeatured(null)}
                    className="ml-1 text-blue-500 hover:text-blue-700"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              )}

              <button
                onClick={() => setShowFilters(true)}
                className="ml-auto bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-0.5 rounded-md transition-colors flex items-center gap-1 text-xs"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Edit Filters</span>
              </button>
            </div>
          )}

        {showFilters && (
          <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex items-center justify-between mb-3 border-b border-gray-100 pb-2">
              <Text className="font-medium text-gray-700">Filter Hotels</Text>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <Text className="font-medium text-sm">Destination</Text>
                </div>
                <div className="relative">
                  <select
                    value={filterDestination}
                    onChange={(e) => {
                      const newDestination = e.target.value;
                      setFilterDestination(newDestination);

                      // Update URL with the new destination or remove it if empty
                      const newSearchParams = new URLSearchParams(searchParams);
                      if (newDestination) {
                        newSearchParams.set("destination", newDestination);
                      } else {
                        newSearchParams.delete("destination");
                      }
                      navigate({ search: newSearchParams.toString() });
                    }}
                    className="w-full p-1.5 text-sm border border-gray-300 rounded-md bg-gray-50 appearance-none pr-8 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent transition-all"
                  >
                    <option value="">All Destinations</option>
                    {destinations.map((destination) => (
                      <option key={destination.id} value={destination.id}>
                        {destination.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                  <Text className="font-medium text-sm">Star Rating</Text>
                </div>
                <div className="bg-gray-50 p-2 rounded-md border border-gray-200">
                  <div className="flex flex-wrap gap-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => toggleStarFilter(star)}
                        className={`px-2 py-1 rounded-md transition-all flex items-center gap-1 text-xs ${
                          filterStars.includes(star)
                            ? "bg-yellow-400 text-yellow-900 shadow-sm border border-yellow-500"
                            : "bg-white hover:bg-gray-100 border border-gray-300 text-gray-700"
                        }`}
                      >
                        <span>{star}</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                    />
                  </svg>
                  <Text className="font-medium text-sm">Featured Status</Text>
                </div>
                <div className="bg-gray-50 p-2 rounded-md border border-gray-200">
                  <div className="flex flex-wrap gap-3">
                    <button
                      onClick={() =>
                        setFilterFeatured(filterFeatured === true ? null : true)
                      }
                      className={`px-3 py-1.5 rounded-md transition-all flex items-center gap-1 flex-1 justify-center text-xs ${
                        filterFeatured === true
                          ? "bg-purple-100 text-purple-800 border border-purple-300 shadow-sm"
                          : "bg-white hover:bg-gray-100 border border-gray-300 text-gray-700"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span>Featured</span>
                    </button>
                    <button
                      onClick={() =>
                        setFilterFeatured(
                          filterFeatured === false ? null : false
                        )
                      }
                      className={`px-3 py-1.5 rounded-md transition-all flex items-center gap-1 flex-1 justify-center text-xs ${
                        filterFeatured === false
                          ? "bg-gray-700 text-white border border-gray-800 shadow-sm"
                          : "bg-white hover:bg-gray-100 border border-gray-300 text-gray-700"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3.5 w-3.5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Not Featured</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between mt-3 pt-3 border-t border-gray-100">
              <div>
                {(filterDestination ||
                  filterStars.length > 0 ||
                  filterFeatured !== null) && (
                  <div className="text-sm text-gray-500">
                    <span className="font-medium">{filteredHotels.length}</span>{" "}
                    hotels match your filters
                  </div>
                )}
              </div>
              <div className="flex gap-3">
                {/* <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setShowFilters(false)}
                  className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 px-3 py-1.5 rounded-md transition-all text-gray-700 text-sm"
                >
                  <span>Close</span>
                </Button> */}

                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    setFilterDestination("");
                    setFilterStars([]);
                    setFilterFeatured(null);

                    // Remove destination from URL if it exists
                    if (searchParams.has("destination")) {
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.delete("destination");
                      navigate({ search: newSearchParams.toString() });
                    }
                  }}
                  className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-sm"
                  disabled={
                    !filterDestination &&
                    filterStars.length === 0 &&
                    filterFeatured === null
                  }
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-3.5 h-3.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Clear</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="h-64 animate-pulse bg-gray-100 rounded-lg"
              ></div>
            ))}
          </div>
        ) : filteredHotels.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredHotels.map((hotel) => (
                <div
                  key={hotel.id}
                  className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-all cursor-pointer flex flex-col"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                >
                  <div className="h-48 bg-gray-200 relative overflow-hidden">
                    {hotel.image_url ? (
                      <img
                        src={hotel.image_url}
                        alt={hotel.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                        <Text className="text-gray-400">No image</Text>
                      </div>
                    )}

                    <div className="absolute top-2 right-2 flex flex-col gap-1 items-end">
                      <div className="bg-white/80 rounded-full px-2 py-1 text-xs font-medium">
                        {Array.from({ length: hotel.star || 0 }).map((_, i) => (
                          <span key={i} className="text-yellow-500">
                            ★
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                      <Text className="text-white font-medium">
                        {hotel.destination_name}
                      </Text>
                    </div>
                  </div>

                  <div className="p-4 flex flex-col flex-grow">
                    <div className="flex justify-between items-start mb-2">
                      <Heading level="h3" className="text-lg font-medium">
                        {hotel.name}
                      </Heading>

                      <div className="flex gap-1">
                        <div
                          className={`px-2 py-1 rounded-full text-xs ${
                            hotel.is_active
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {hotel.is_active ? "Active" : "Inactive"}
                        </div>
                        {hotel.is_featured && (
                          <div className="px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                            Featured
                          </div>
                        )}
                      </div>
                    </div>

                    <Text className="text-gray-600 line-clamp-2 mb-3">
                      {hotel.description || "No description available"}
                    </Text>

                    <div className="flex items-center gap-1 text-gray-500 text-sm mb-3">
                      <Buildings className="w-3 h-3" />
                      <Text className="truncate">{hotel.address}</Text>
                    </div>

                    {/* Spacer to push action buttons to bottom */}
                    <div className="flex-grow"></div>

                    {/* Action buttons */}
                    <div className="flex gap-2 mt-auto">
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/hotel-management/hotels/${hotel.id}`);
                        }}
                      >
                        <Edit className="w-3.5 h-3.5" />
                        <span>Manage</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHotels.map((hotel) => (
                <div
                  key={hotel.id}
                  className="border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-all cursor-pointer p-5"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${hotel.id}`)
                  }
                >
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="w-full md:w-64 lg:w-80 h-40 md:h-52 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                      {hotel.image_url ? (
                        <img
                          src={hotel.image_url}
                          alt={hotel.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full bg-gray-100">
                          <Text className="text-gray-400">No image</Text>
                        </div>
                      )}
                    </div>

                    <div className="flex-grow flex flex-col h-full py-1">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <Heading level="h3" className="text-lg font-medium">
                            {hotel.name}
                          </Heading>

                          <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                            <Text>{hotel.destination_name}</Text>
                            <span>•</span>
                            <div className="flex">
                              {Array.from({ length: hotel.star || 0 }).map(
                                (_, i) => (
                                  <span key={i} className="text-yellow-500">
                                    ★
                                  </span>
                                )
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex gap-1">
                          <div
                            className={`px-2 py-1 rounded-full text-xs ${
                              hotel.is_active
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {hotel.is_active ? "Active" : "Inactive"}
                          </div>
                          {hotel.is_featured && (
                            <div className="px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                              Featured
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col flex-grow justify-between">
                        <div>
                          <Text className="text-gray-600 line-clamp-3 mb-3">
                            {hotel.description || "No description available"}
                          </Text>

                          <div className="flex items-center gap-1 text-gray-500 text-sm mb-4">
                            <Buildings className="w-3 h-3" />
                            <Text className="truncate">{hotel.address}</Text>
                          </div>
                        </div>

                        {/* Action buttons */}
                        <div className="flex gap-3 mt-auto">
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-white border border-gray-200 shadow-sm hover:bg-gray-50 flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-gray-700 text-xs flex-1 max-w-[120px]"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/hotel-management/hotels/${hotel.id}`);
                            }}
                          >
                            <Edit className="w-3.5 h-3.5" />
                            <span>Manage</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <Buildings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <Text className="text-gray-500 mb-4">
              {searchQuery ||
              filterDestination ||
              filterStars.length > 0 ||
              filterFeatured !== null
                ? "No hotels match your search criteria"
                : "No hotels found"}
            </Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => setOpen(true)}
              className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <PlusMini className="w-4 h-4" />
              <span>Add your first hotel</span>
            </Button>
          </div>
        )}
      </Container>

      <FocusModal open={open} onOpenChange={setOpen}>
        <HotelFormModern
          formData={formData}
          onSubmit={handleCreate}
          closeModal={() => setOpen(false)}
          roomTypes={roomTypes}
        />
      </FocusModal>

      <BulkImportModal
        open={bulkImportOpen}
        onClose={() => {
          setBulkImportOpen(false);
        }}
      />

      {useEffect(() => {
        // Listen for custom events
        const handleCloseModal = (event: any) => {
          setBulkImportOpen(false);
          // Only refresh if explicitly requested
          if (event.detail?.refresh) {
            setTimeout(() => {
              console.log("Refreshing hotels after import");
              fetchHotels(destinations);
            }, 500);
          }
        };

        const handleRefreshData = () => {
          // Refresh data without closing the modal
          setTimeout(() => {
            console.log("Refreshing hotels after successful import");
            fetchHotels(destinations);
          }, 500);
        };

        window.addEventListener("closeHotelModal", handleCloseModal);
        window.addEventListener("refreshHotelData", handleRefreshData);

        return () => {
          window.removeEventListener("closeHotelModal", handleCloseModal);
          window.removeEventListener("refreshHotelData", handleRefreshData);
        };
      }, [destinations])}

      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />
    </>
  );
};

// Make sure the config is properly exported
export const config = defineRouteConfig({
  label: "Hotels",
  icon: Buildings,
});

// Default export for the component
export default HotelsPage;
