import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import HideSidebarItemsWidget from "../../../../../widgets/hide-sidebar-items-widget";
import WhatsAppMessagesPanel from "../../../../../components/booking/whatsapp-messages-panel";

const BookingWhatsAppPage = () => {
  const { id } = useParams();

  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <WhatsAppMessagesPanel bookingId={id} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "WhatsApp Messages",
});

export default BookingWhatsAppPage;
