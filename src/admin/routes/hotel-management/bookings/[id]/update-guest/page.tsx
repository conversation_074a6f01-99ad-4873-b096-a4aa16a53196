import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import UpdateGuestForm from "../../../../../components/booking/update-guest-form";
import HideSidebarItemsWidget from "../../../../../widgets/hide-sidebar-items-widget";

const UpdateGuestPage = () => {
  const { id } = useParams();

  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <UpdateGuestForm bookingId={id} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Update Traveler Information",
});

export default UpdateGuestPage;
