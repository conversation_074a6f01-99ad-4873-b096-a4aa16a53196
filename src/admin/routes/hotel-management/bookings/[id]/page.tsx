import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import HideSidebarItemsWidget from "../../../../widgets/hide-sidebar-items-widget";
import SimpleBookingDetail from "../../../../components/booking/simple-booking-detail";

const BookingDetailPage = () => {
  const { id } = useParams();

  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <SimpleBookingDetail bookingId={id} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Booking Details",
});

export default BookingDetailPage;
