import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Button, Heading, Container, Text } from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";

const BookingDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  return (
    <Container>
      <div className="flex justify-between items-center mb-6">
        <div>
          <Heading level="h1" className="text-2xl">Booking Details</Heading>
          <Text className="text-gray-500">Booking ID: {id}</Text>
        </div>
        <Button
          variant="secondary"
          onClick={() => navigate(-1)}
        >
          Back
        </Button>
      </div>
      
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <Text>This feature is currently under development. Please check back later.</Text>
      </div>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Booking Details",
});

export default BookingDetailPage;
