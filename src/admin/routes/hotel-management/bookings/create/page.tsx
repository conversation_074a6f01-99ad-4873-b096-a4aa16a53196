import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import BookingForm from "../../../../components/booking/booking-form";
import HideSidebarItemsWidget from "../../../../widgets/hide-sidebar-items-widget";

const CreateBookingPage = () => {
  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <BookingForm />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Create Booking",
});

export default CreateBookingPage;
