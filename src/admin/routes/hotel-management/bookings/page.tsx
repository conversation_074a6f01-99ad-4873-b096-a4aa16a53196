import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container } from "@camped-ai/ui";
import BookingList from "../../../components/booking/booking-list";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";
import { useLocation } from "react-router-dom";

const BookingsPage = () => {
  // Get hotel_id from URL query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const hotelId = queryParams.get("hotel_id");
  return (
    <>
      <HideSidebarItemsWidget />
      <Container>
        <BookingList hotelId={hotelId} />
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Bookings",
});

export default BookingsPage;
