import React, { useState } from "react";
import {
  Contain<PERSON>,
  <PERSON>ing,
  Text,
  But<PERSON>,
  FocusModal,
  Tabs,
  Switch,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../../../lib/sdk";
import { Link, useParams, useSearchParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Envelope } from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import NotificationTemplateForm from "../../../../components/notification-template-form-simple";

const NotificationTemplateDetailPage = () => {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  // Open edit modal automatically if edit=true in URL
  const [isEditModalOpen, setIsEditModalOpen] = useState(searchParams.get("edit") === "true");

  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const formRef = React.useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update URL when closing edit modal
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    // Remove the edit=true query parameter
    navigate(`/settings/notification-templates/${id}`);
  };

  // Fetch notification template
  const { data: template, isLoading } = useQuery({
    queryKey: ["notification-template", id],
    queryFn: async () => {
      const response = await sdk.client.fetch(`/admin/notification-templates/${id}`) as any;
      return response.notification_template;
    },
  });

  // Fetch all templates for this event to get available channels
  const { data: allTemplates } = useQuery({
    queryKey: ["notification-templates-by-event", template?.event_name],
    queryFn: async () => {
      if (!template?.event_name) return [];
      const response = await sdk.client.fetch(`/admin/notification-templates?event_name=${template.event_name}`) as any;
      return response.notification_templates || [];
    },
    enabled: !!template?.event_name,
  });

  // State for selected channel
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);

  // Set selected channel on initial load
  React.useEffect(() => {
    if (template && !selectedChannel) {
      setSelectedChannel(template.channel);
    }
  }, [template, selectedChannel]);

  // Get available channels from all templates
  const availableChannels = React.useMemo(() => {
    if (!allTemplates) return [] as string[];
    return [...new Set(allTemplates.map((t: any) => t.channel))] as string[];
  }, [allTemplates]);

  // Get the current template based on selected channel
  const currentTemplate = React.useMemo(() => {
    if (!allTemplates || !selectedChannel) return template;
    const templateForChannel = allTemplates.find((t: any) => t.channel === selectedChannel);
    return templateForChannel || template;
  }, [template, allTemplates, selectedChannel]);

  // Toggle active status
  const toggleActiveMutation = useMutation({
    mutationFn: async () => {
      // Use the current template ID
      const templateId = currentTemplate?.id || id;
      return await sdk.client.fetch(`/admin/notification-templates/${templateId}`, {
        method: "PUT",
        body: {
          is_active: !currentTemplate?.is_active,
        },
      });
    },
    onSuccess: () => {
      toast.success(`Template ${currentTemplate?.is_active ? "deactivated" : "activated"} successfully`);
      // Invalidate both the specific template and the event templates
      queryClient.invalidateQueries({ queryKey: ["notification-template", id] });
      if (template?.event_name) {
        queryClient.invalidateQueries({ queryKey: ["notification-templates-by-event", template.event_name] });
      }
    },
    onError: (error) => {
      toast.error(`Error updating template: ${error.message}`);
    },
  });

  // Default status is no longer editable

  // We don't allow deleting templates, only activating/deactivating them

  // Reset template to default
  const resetMutation = useMutation({
    mutationFn: async () => {
      return await sdk.client.fetch(`/admin/notification-templates/${id}/reset`, {
        method: "POST",
      });
    },
    onSuccess: () => {
      toast.success("Template reset to default successfully");
      setIsResetModalOpen(false);
      queryClient.invalidateQueries({ queryKey: ["notification-template", id] });
    },
    onError: (error) => {
      toast.error(`Error resetting template: ${error.message}`);
    },
  });

  // Handle form submission
  const handleSubmit = () => {
    if (formRef.current) {
      setIsSubmitting(true);
      formRef.current.submitForm();
    }
  };

  if (isLoading) {
    return (
      <Container>
        <div className="text-center py-8">Loading notification template...</div>
      </Container>
    );
  }

  if (!template) {
    return (
      <Container>
        <div className="text-center py-8">
          <Text className="text-ui-fg-subtle mb-4">
            Notification template not found
          </Text>
          <Link to="/settings/notification-templates">
            <Button variant="secondary">Back to Templates</Button>
          </Link>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Link
          to="/settings/notification-templates"
          className="flex items-center text-ui-fg-subtle hover:text-ui-fg-base mb-2"
        >
          <ArrowLeft className="mr-1" />
          Back to Templates
        </Link>
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1">{template.event_name}</Heading>
            <div className="flex items-center gap-2 mt-1">
              <Text className="text-ui-fg-subtle">Channel:</Text>
              <div className="w-40">
                <select
                  className="w-full h-8 px-2 py-1 text-sm text-left bg-white border border-ui-border-base rounded-md appearance-none"
                  value={selectedChannel || template.channel}
                  onChange={(e) => setSelectedChannel(e.target.value)}
                >
                  {availableChannels.map((channel) => (
                    <option key={channel} value={channel}>
                      {channel.charAt(0).toUpperCase() + channel.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            {/* Only show Reset button for email channel */}
            {selectedChannel === 'email' && (
              <Button
                variant="secondary"
                onClick={() => setIsResetModalOpen(true)}
              >
                Reset to Default
              </Button>
            )}
            <Button
              variant="secondary"
              onClick={() => setIsEditModalOpen(true)}
            >
              Edit Template
            </Button>
          </div>
        </div>
      </div>

      {/* Channel selector is now in the header */}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="border rounded-lg p-4">
          <Text className="text-ui-fg-subtle mb-2 text-sm">Status</Text>
          <div className="mt-1 flex justify-between items-center">
            <div>
              {currentTemplate.is_active ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-green-bg text-ui-tag-green-text border border-ui-tag-green-border">
                  <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-green-text"></span>
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-ui-tag-red-bg text-ui-tag-red-text border border-ui-tag-red-border">
                  <span className="w-1.5 h-1.5 mr-1.5 rounded-full bg-ui-tag-red-text"></span>
                  Inactive
                </span>
              )}
            </div>
            <Button
              variant="secondary"
              size="small"
              onClick={() => toggleActiveMutation.mutate()}
            >
              {currentTemplate.is_active ? "Deactivate" : "Activate"}
            </Button>
          </div>
        </div>
      </div>

      {/* Only show content and preview for email channel */}
      {(selectedChannel === 'email' || selectedChannel === 'pdf') && (
        <div className="border rounded-lg overflow-hidden mb-6">
          <Tabs defaultValue="content">
            <Tabs.List className="px-4 pt-4">
              <Tabs.Trigger value="content">Content</Tabs.Trigger>
              <Tabs.Trigger value="preview">Preview</Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content value="content" className="p-4">
              {currentTemplate.subject && (
                <div className="mb-4">
                  <Text className="text-ui-fg-subtle mb-2 text-sm">Subject</Text>
                  <div className="border rounded-lg p-3 bg-ui-bg-subtle font-mono text-sm">
                    {currentTemplate.subject}
                  </div>
                </div>
              )}
              <Text className="text-ui-fg-subtle mb-2 text-sm">Content</Text>
              <div className="border rounded-lg p-3 bg-ui-bg-subtle font-mono text-sm whitespace-pre-wrap">
                {currentTemplate.content}
              </div>
            </Tabs.Content>
            <Tabs.Content value="preview" className="p-4">
              <div className="border rounded-lg p-4 bg-white">
                <div>
                  {currentTemplate.subject && (
                    <div className="mb-2 pb-2 border-b">
                      <span className="font-medium">Subject:</span> {currentTemplate.subject}
                    </div>
                  )}
                  <div className="text-sm" dangerouslySetInnerHTML={{ __html: currentTemplate.content }} />
                </div>
              </div>
              <Text className="text-ui-fg-subtle mt-2 text-xs">
                Note: Placeholders are not replaced in this preview.
              </Text>
            </Tabs.Content>
          </Tabs>
        </div>
      )}

      {/* Edit Modal */}
      <FocusModal open={isEditModalOpen} onOpenChange={handleCloseEditModal}>
        <FocusModal.Content className="max-h-[90vh] overflow-hidden max-w-4xl mx-auto">
          <FocusModal.Header className="sticky top-0 z-10 bg-white border-b border-ui-border-base">
            <div className="flex items-center">
              <Text size="large" weight="plus" className="text-left">
                Edit {template.event_name} Template
              </Text>
            </div>
            <div />
          </FocusModal.Header>
          <FocusModal.Body className="overflow-y-auto p-6">
            <div className="mb-4">
              <Text className="text-ui-fg-subtle mb-2">
                Edit the template for {template.event_name}. Select a channel below to edit its configuration.
                You can use placeholders in your content that will be replaced with actual values when the notification is sent.
              </Text>

              {/* Channel selector dropdown */}
              <div className="mt-4 mb-6">
                <label htmlFor="edit-channel" className="block text-sm font-medium mb-1">Channel</label>
                <div className="relative w-full max-w-xs">
                  <select
                    id="edit-channel"
                    className="w-full h-10 px-3 py-2 text-left bg-white border border-ui-border-base rounded-md appearance-none"
                    value={selectedChannel || template.channel}
                    onChange={(e) => {
                      // Find the template for this channel
                      const channel = e.target.value;
                      setSelectedChannel(channel);

                      // If we have a template for this channel, navigate to it
                      if (allTemplates) {
                        const templateForChannel = allTemplates.find((t: any) => t.channel === channel);
                        if (templateForChannel && templateForChannel.id !== id) {
                          // Navigate to the template for this channel
                          navigate(`/settings/notification-templates/${templateForChannel.id}?edit=true`);
                        }
                      }
                    }}
                  >
                    {availableChannels.map((channel: string) => (
                      <option key={channel} value={channel}>
                        {channel.charAt(0).toUpperCase() + channel.slice(1)}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <NotificationTemplateForm
              ref={formRef}
              initialData={currentTemplate}
              onSuccess={() => {
                handleCloseEditModal();
                setIsSubmitting(false);
                queryClient.invalidateQueries({ queryKey: ["notification-template", id] });
                if (template?.event_name) {
                  queryClient.invalidateQueries({ queryKey: ["notification-templates-by-event", template.event_name] });
                }
              }}
            />
          </FocusModal.Body>
          <FocusModal.Footer className="border-t border-ui-border-base">
            <div className="flex items-center justify-end gap-2 p-3">
              <Button
                variant="secondary"
                size="small"
                onClick={handleCloseEditModal}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={handleSubmit}
                isLoading={isSubmitting}
              >
                Update Template
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* We don't allow deleting templates, only activating/deactivating them */}

      {/* Ultra-Simple Reset Modal */}
      <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${isResetModalOpen ? '' : 'hidden'}`}>
        <div className="bg-white rounded-md shadow-lg p-4 max-w-xs w-full">
          <Text className="font-medium mb-2">Reset to Default?</Text>
          <div className="flex gap-2 justify-end mt-4">
            <Button
              variant="secondary"
              size="small"
              onClick={() => setIsResetModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={() => resetMutation.mutate()}
              isLoading={resetMutation.isPending}
            >
              Reset
            </Button>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default NotificationTemplateDetailPage;

export const config = defineRouteConfig({
  label: "Notification Template Details",
  icon: Envelope,
});
