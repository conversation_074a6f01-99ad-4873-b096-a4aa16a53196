import { useEffect, useState } from "react";
import {
  Container,
  <PERSON><PERSON>,
  Text,
  But<PERSON>,
  Drawer,
  Select
} from "@camped-ai/ui";
import{ PencilSquare} from "@camped-ai/icons"
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ActionMenu } from "../../../common/components/action-menu";
import { sdk } from "../../../lib/sdk";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";


const CheckoutSettings = () => {
  const [settings, setSettings] = useState({});
  const [fieldMappings, setFieldMappings] = useState({});
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const data = await sdk.client.fetch('/admin/field-validations/customer_address')
      const fieldData = data.fieldValidation.reduce((acc, field) => {
        acc[field.field_name] = {
          id: field.id,
          is_visible: field.is_visible,
          is_required: field.is_required,
          user_type: field.user_type
        };
        return acc;
      }, {});

      setFieldMappings(fieldData);
      setSettings(
        Object.keys(fieldData).reduce((acc, key) => {
          acc[key] = getSelectValue(fieldData[key]);
          return acc;
        }, {})
      );
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  const getSelectValue = ({ is_visible, is_required }) => {
    if (!is_visible) return "dont-include";
    return is_required ? "required" : "optional";
  };

  const formatFieldName = (name) => {
    return name
      .replace(/_/g, " ") // Replace underscores with spaces
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
  };

  const handleChange = (key, value) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    const payload = Object.keys(settings).map((key) => ({
      id: fieldMappings[key].id,
      is_visible: settings[key] !== "dont-include",
      is_required: settings[key] === "required",
      user_type: fieldMappings[key].user_type
    }));

    try {
      const response = await sdk.client.fetch("/admin/field-validations", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: payload
      });

      if (response.ok) {
        setIsDrawerOpen(false);
        fetchSettings(); // Refresh data after saving
      } else {
        console.error("Failed to save settings");
      }
    } catch (error) {
      console.error("Error saving settings:", error);
    }
    finally{
      setIsDrawerOpen(false)
    }
  };
  const handleEditClick = () => {
    setIsDrawerOpen(true)
  }

  return (
    <>
      <HideSidebarItemsWidget />
      <div className="flex flex-col gap-y-4">
      <Container className="mx-auto divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading>Checkout Settings</Heading>
            <Text className="text-ui-fg-subtle mt-1">
              Configure your checkout experience
            </Text>
          </div>

          <ActionMenu
          groups={[
            {
              actions: [
                {
                  icon: <PencilSquare />,
                  label: "Edit",
                  onClick: handleEditClick,
                },
              ],
            },
          ]}
        />
        </div>

        {Object.keys(settings).map((key) => (
          <div key={key} className="text-ui-fg-subtle grid grid-cols-2 px-6 py-4">
            <Text size="small" leading="compact" weight="plus">
            {formatFieldName(key)}
            </Text>
            <Text size="small" leading="compact">
             {formatFieldName(settings[key])}
            </Text>
          </div>
        ))}
      </Container>

      {/* Drawer */}
      <Drawer
        open={isDrawerOpen}
        onClose={() => setIsDrawerOpen(!isDrawerOpen)}
        onOpenChange={setIsDrawerOpen}
      >
        <Drawer.Content>
          <Drawer.Header className="flex justify-between items-center">
            <Heading level="h2">Edit Checkout Settings</Heading>
          </Drawer.Header>
          <Drawer.Body>
            <div className="flex flex-col gap-y-6">
              {Object.keys(settings).map((key) => (
                <div key={key} className="flex flex-col gap-y-2">
                  <Text className="font-semibold text-ui-fg-base capitalize">
                  {formatFieldName(key)}
                  </Text>
                  <Select
                    value={settings[key]}
                    onValueChange={(val) => handleChange(key, val)}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select an option" />
                    </Select.Trigger>
                    <Select.Content>
                    {["first_name", "last_name"].includes(key) ? (
                        <>
                          <Select.Item value="optional">Optional</Select.Item>
                          <Select.Item value="required">Required</Select.Item>
                        </>
                      ) : (
                        <>
                          <Select.Item value="dont-include">Don't Include</Select.Item>
                          <Select.Item value="optional">Optional</Select.Item>
                          <Select.Item value="required">Required</Select.Item>
                        </>
                      )}
                    </Select.Content>
                  </Select>
                </div>
              ))}
            </div>
          </Drawer.Body>
          <Drawer.Footer className="flex justify-end gap-x-4">
            <Button variant="primary" onClick={handleSave}>
              Save
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>
    </div>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Checkout",
});

export default CheckoutSettings;
