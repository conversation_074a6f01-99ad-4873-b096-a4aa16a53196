import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Button,
  Container,
  Select,
  Label,
  Input,
  Textarea,
  toast,
  Skeleton,
} from "@camped-ai/ui";
import { ExclamationCircle } from "@camped-ai/icons";
import { sdk } from "../../../../../lib/sdk";

const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 23 23"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 19.5l-7.5-7.5 7.5-7.5"
    />
  </svg>
);

export default function EditDestinationMetafield() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [name, setName] = useState("");
  const [namespaceKey, setNamespaceKey] = useState("");
  const [description, setDescription] = useState("");
  const [scopeValue, setScopeValue] = useState("store");
  const [type, setType] = useState("text");
  const [namespace, setNamespace] = useState("");
  const [key, setKey] = useState("");

  useEffect(() => {
    const fetchMetafieldDefinition = async () => {
      try {
        const response = await sdk.client.fetch(
          `/admin/metafield-definitions/${id}`
        );
        const data = response.metafieldDefinition[0];
        if (data) {
          setName(data.label || "");
          setDescription(data.description || "");
          setScopeValue(data.scope || "store");
          setType(data.type || "text");
          setNamespace(data.namespace || "");
          setKey(data.key || "");
          setNamespaceKey(`${data.namespace}.${data.key}`);
        }
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching metafield definition:", error);
        setIsLoading(false);
      }
    };

    fetchMetafieldDefinition();
  }, [id]);

  const handleScopeChange = (value: React.SetStateAction<string>) => {
    setScopeValue(value);
  };

  const handleTypeChange = (value: React.SetStateAction<string>) => {
    setType(value);
  };

  const handleUpdate = async () => {
    try {
      if (!name?.trim()) {
        toast.error("Error", {
          description: "Name is required",
        });
        return;
      }

      const response = await sdk.client.fetch("/admin/metafield-definitions", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: [
          {
            id,
            label: name,
            description,
            scope: scopeValue,
          },
        ],
      });

      toast.success("Success", {
        description: "Updated Successfully.",
        duration: 5000,
        position: "top-center",
      });

      navigate("/settings/custom/destination");
    } catch (error) {
      console.error("Error updating metafield definition:", error);
      toast.error("Error", {
        description: "Failed to update metafield definition.",
      });
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this metafield definition?")) {
      return;
    }

    try {
      await sdk.client.fetch("/admin/metafield-definitions", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          ids: id,
          delete_linked_values: true,
        },
      });

      toast.success("Success", {
        description: "Deleted Successfully.",
        duration: 5000,
        position: "top-center",
      });

      navigate("/settings/custom/destination");
    } catch (error) {
      console.error("Error deleting metafield definition:", error);
      toast.error("Error", {
        description: "Failed to delete metafield definition.",
      });
    }
  };

  if (isLoading) {
    return (
      <div>
        <Button
          variant="secondary"
          onClick={() => navigate("/settings/custom/destination")}
          className="mb-3 flex items-center"
        >
          <ArrowIcon />
          <span>Back</span>
        </Button>
        <Container className="p-5">
          <Skeleton className="h-8 w-full mb-4" />
          <Skeleton className="h-8 w-full mb-4" />
          <Skeleton className="h-8 w-full mb-4" />
        </Container>
      </div>
    );
  }

  return (
    <div>
      <Button
        variant="secondary"
        onClick={() => navigate("/settings/custom/destination")}
        className="mb-3 flex items-center"
      >
        <ArrowIcon />
        <span>Back</span>
      </Button>

      <Container className="p-0 m-0 shadow-sm space-y-4">
        <div className="flex flex-row justify-between items-center border-b px-5 py-5">
          <h1 className="font-medium h1-core">Edit destination metafield definition</h1>
          <div className="flex gap-2">
            <Button variant="danger" onClick={handleDelete}>
              Delete
            </Button>
            <Button onClick={handleUpdate}>Save</Button>
          </div>
        </div>
        <div className="flex flex-col px-5 pb-5 gap-5">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full"
              placeholder="Enter name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Namespace and Key
            </label>
            <Input
              type="text"
              value={namespaceKey}
              disabled
              className="w-full bg-gray-100"
            />
            <p className="text-xs text-gray-500 mt-1">
              Namespace and key cannot be changed after creation.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Type</label>
            <Input
              type="text"
              value={type}
              disabled
              className="w-full bg-gray-100"
            />
            <p className="text-xs text-gray-500 mt-1">
              Type cannot be changed after creation.
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={100}
              className="w-full"
            ></Textarea>
          </div>
        </div>
      </Container>

      <Container className="p-4 mt-4 rounded-2xl shadow-sm">
        <h2 className="flex font-sans font-medium h1-core pb-5 gap-1">
          Options{" "}
          <div className="mt-2">
            <ExclamationCircle />
          </div>
        </h2>
        <Container className="flex space-between rounded-lg p-4">
          <div className="w-full">
            <Label className="block text-sm font-medium mb-1">
              Storefront Access
            </Label>
            <Label className="text-xs">
              Definition is available in your Online Store
            </Label>
          </div>
          <div className="flex flex-row justify-end w-full pr-1">
            <Select value={scopeValue} onValueChange={handleScopeChange}>
              <Select.Trigger>
                <Select.Value placeholder="Option" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="store">Read</Select.Item>
                <Select.Item value="admin">No Access</Select.Item>
              </Select.Content>
            </Select>
          </div>
        </Container>
      </Container>
    </div>
  );
}
