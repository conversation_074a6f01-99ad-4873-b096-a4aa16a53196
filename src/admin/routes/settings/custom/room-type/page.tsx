import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import {
  Bad<PERSON>,
  Button,
  Container,
  Heading,
  Table,
  Skeleton,
} from "@camped-ai/ui";
import { PencilSquare } from "@camped-ai/icons";
import { ActionMenu } from "../../../../common/components/action-menu";
import {
  Key,
  ReactElement,
  JSXElementConstructor,
  ReactNode,
  ReactPortal,
} from "react";

const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 23 23"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 19.5l-7.5-7.5 7.5-7.5"
    />
  </svg>
);

export default function RoomTypeMetafieldDefinitions() {
  const navigate = useNavigate();

  // Fetch metafields using useQuery
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["metafields"],
    queryFn: async () => {
      const response = await fetch(
        "/admin/metafield-definitions?owner_type=room_type"
      );
      if (!response.ok) throw new Error("Failed to fetch metafields");
      return response.json();
    },
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true, // Fetch again when component mounts
    refetchOnWindowFocus: true, // Fetch when user switches back to tab
  });

  const metafields = data?.metafieldDefinition || [];

  return (
    <div>
      {/* Back Button */}
      <Button
        variant="secondary"
        onClick={() => navigate("/settings/custom")}
        className="mb-3 flex items-center"
      >
        <ArrowIcon />
        <span>Back</span>
      </Button>

      {/* Container */}
      <Container className="p-0 m-0 mx-auto w-full">
        <div className="flex flex-row items-center justify-between px-5 py-5">
          <Heading className="font-medium text-lg">
            Room Type Metafield Definitions
          </Heading>
          <Button
            onClick={() =>
              navigate("/settings/custom/roomtypemetafield/create")
            }
          >
            Add Definition
          </Button>
        </div>

        {/* Show Skeleton while loading */}
        {isLoading ? (
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-2" />
            <Skeleton className="h-8 w-full mb-2" />
            <Skeleton className="h-8 w-full mb-2" />
          </div>
        ) : error ? (
          <p className="text-red-500 text-center">Error fetching metafields</p>
        ) : metafields.length > 0 ? (
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Name</Table.HeaderCell>
                <Table.HeaderCell>Description</Table.HeaderCell>
                <Table.HeaderCell>Scope</Table.HeaderCell>
                <Table.HeaderCell className="text-right">
                  Actions
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {metafields.map(
                (field: {
                  id: Key | null | undefined;
                  label:
                    | string
                    | number
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | null
                    | undefined;
                  description: any;
                  scope: any;
                }) => (
                  <Table.Row
                    key={field.id}
                    onClick={() =>
                      navigate(`/settings/custom/roomtypemetafield/${field.id}`)
                    }
                    className="cursor-pointer"
                  >
                    <Table.Cell>{field.label}</Table.Cell>
                    <Table.Cell>{field.description || "—"}</Table.Cell>
                    <Table.Cell>{field.scope || "—"}</Table.Cell>

                    <Table.Cell className="text-right">
                      <ActionMenu
                        groups={[
                          {
                            actions: [
                              {
                                icon: <PencilSquare />,
                                label: "Edit",
                                onClick: () =>
                                  navigate(
                                    `/settings/custom/roomtypemetafield/${field.id}`
                                  ),
                              },
                            ],
                          },
                        ]}
                      />
                    </Table.Cell>
                  </Table.Row>
                )
              )}
            </Table.Body>
          </Table>
        ) : (
          <Container className="border rounded-lg p-10 text-center mt-4">
            <div className="bg-gray-100 p-4 rounded-lg mb-4 inline-block">
              <div className="w-12 h-12 text-gray-500">📋</div>
            </div>
            <h2 className="text-lg font-medium">
              Add a custom field to your Room Type
            </h2>
            <p className="text-sm text-gray-500 mb-4">
              Create a metafield definition to store and display unique content
              for each Room Type.
            </p>
            <Button
              onClick={() =>
                navigate("/settings/custom/roomtypemetafield/create")
              }
            >
              Add Definition
            </Button>
          </Container>
        )}
      </Container>
    </div>
  );
}
