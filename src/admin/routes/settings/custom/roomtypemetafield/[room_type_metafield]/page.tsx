import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Button,
  Container,
  Select,
  Label,
  Prompt,
  RadioGroup,
  Badge,
  Input,
  Textarea,
  toast,
  Kbd,
} from "@camped-ai/ui";
import { ExclamationCircle } from "@camped-ai/icons";
import { sdk } from "../../../../../lib/sdk";

const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 23 23"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 19.5l-7.5-7.5 7.5-7.5"
    />
  </svg>
);

export default function CustomDataInput() {
  const navigate = useNavigate();
  const location = useLocation();
  const id = location.pathname.split("/").pop() || "";

  const [name, setName] = useState("");
  const [namespaceKey, setNamespaceKey] = useState("custom.");
  const [description, setDescription] = useState("");
  const [scopeValue, setScopeValue] = useState("");
  const [deleteLinkedValues, setDeleteLinkedValues] = useState(false);

  const handleScopeChange = (value: string) => {
    setScopeValue(value);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await sdk.client.fetch(
          `/admin/metafield-definitions?id=${id}`
        );

        if (!result || !Array.isArray(result.metafieldDefinition)) {
          throw new Error("Invalid API response structure");
        }

        const matchedData = result.metafieldDefinition.find(
          (item: { id: string }) => item.id === id
        );

        if (!matchedData) throw new Error("Metafield data not found");

        setName(matchedData.label || "");
        setNamespaceKey(`${matchedData.namespace}.${matchedData.key}`);
        setDescription(matchedData.description || "");
        setScopeValue(matchedData.scope || "");
      } catch (error) {
        console.error("Error fetching metafield data:", error);
      }
    };

    if (id) fetchData();
  }, [id]);

  /** Handle Save (Create/Update) */
  const handleSave = async () => {
    try {
      if (!id)
        throw new Error(
          "Metafield ID is missing. Cannot update without an ID."
        );

      if (!name) {
        toast.error("Error", {
          description: "Name is required",
        });
        return;
      }
      if (!namespaceKey.includes(".")) {
        toast.error("Error", {
          description:
            "Namespace must include a period (.) separating namespace and key.",
        });
        return;
      }

      if (!scopeValue) {
        toast.error("Error", {
          description:
            "Namespace must include a period (.) separating namespace and key.",
        });
        return;
      }

      const [namespacePart, key] = namespaceKey.split(".");
      if (!namespacePart || !key) {
        {
          toast.error("Error", {
            description: "Invalid namespace format.",
          });
          return;
        }
      }

      const endpoint = `/admin/metafield-definitions`;

      const payload = [];
      // Create payload
      payload.push({
        id: id,
        label: name,
        description: description,
        scope: scopeValue,
      });

      const responseData = await sdk.client.fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: payload,
      });

      toast.success("Success", {
        description: "Updated Successfully.",
        duration: 5000,
        position: "top-center",
      });

      navigate("/settings/custom/room-type");
    } catch (error) {
      console.error("Error updating metafield:", error);
    }
  };

  /** Handle Delete */
  const handleDelete = async () => {
    try {
      if (!id)
        throw new Error(
          "Metafield ID is missing. Cannot delete without an ID."
        );

      const endpoint = "/admin/metafield-definitions";
      const payload = {
        ids: [id], // Send as an array
        delete_linked_values: deleteLinkedValues,
      };

      const responseData = await sdk.client.fetch(endpoint, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: payload,
      });
      toast.warning("Warning", {
        description: "Deleted Successfully",
        duration: 5000,
      });

      navigate("/settings/custom/room-type");
    } catch (error) {
      console.error("Error deleting metafield:", error);
    }
  };

  return (
    <div className="">
      <Button
        variant="secondary"
        onClick={() => navigate("/settings/custom/room-type")}
        className="mb-3 flex items-center"
      >
        <ArrowIcon />
        <span>Back</span>
      </Button>

      <Container className="p-0 m-0 shadow-sm space-y-4">
        <div className="flex flex-row justify-between border-b px-5 py-5">
          <div>
            <h1 className="font-sans font-medium h1-core">
              {id ? "Edit" : "Add"} Room Type Metafield Definition
            </h1>
          </div>
          <div className="flex gap-3 mb-3">
            <Prompt>
              <Prompt.Trigger asChild>
                <Button variant="danger" className="h-50%">
                  Delete
                </Button>
              </Prompt.Trigger>
              <Prompt.Content>
                <Prompt.Header>
                  <Prompt.Title className="my-2">Delete</Prompt.Title>
                  <Prompt.Description>
                    <RadioGroup
                      defaultValue="2"
                      onValueChange={(value) =>
                        setDeleteLinkedValues(value === "2")
                      } // ✅ Update state based on selection
                    >
                      <div className="flex items-center gap-x-3">
                        <RadioGroup.Item value="1" id="radio_1" />
                        <Label htmlFor="radio_1" weight="plus">
                          Delete Field Only
                        </Label>
                      </div>
                      <div className="flex items-center gap-x-3">
                        <RadioGroup.Item value="2" id="radio_2" />
                        <Label htmlFor="radio_2" weight="plus">
                          Delete Field and its saved values
                        </Label>
                        <Kbd className="text-xs p-1 pt-0 pb-0" rounded="full">
                          {" "}
                          <Label size="xsmall" weight="plus">
                            Recommend
                          </Label>{" "}
                        </Kbd>
                      </div>
                    </RadioGroup>
                  </Prompt.Description>
                </Prompt.Header>
                <Prompt.Footer>
                  <Prompt.Cancel>Cancel</Prompt.Cancel>
                  {id && (
                    <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
                  )}
                </Prompt.Footer>
              </Prompt.Content>
            </Prompt>
            <Button onClick={handleSave}>Save</Button>
          </div>
        </div>
        {/* px-5 py-5 */}
        <div className="flex flex-col px-5 pb-5 gap-5">
          {/* Name Field */}
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              placeholder="Enter name"
            />
          </div>

          {/* Namespace & Key Field (Read-Only) */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Namespace and Key
            </label>
            <Input
              type="text"
              disabled
              value={namespaceKey}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm cursor-not-allowed"
            />
          </div>

          {/* Description Field */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Description
            </label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={100}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            ></Textarea>
          </div>
        </div>
      </Container>

      {/* Options Section */}
      <Container className="p-4 mt-4  shadow-sm">
        <h2 className="flex font-sans font-medium h1-core pb-5 gap-1">
          Options{" "}
          <div className="mt-2">
            <ExclamationCircle />
          </div>
        </h2>
        <Container className="flex space-between rounded-lg p-4">
          <div className="w-full">
            <Label className="block text-sm font-medium mb-1">
              Storefront Access
            </Label>
            <Label className="text-xs">
              Definition is available in your Online Store
            </Label>
          </div>
          <div className="flex flex-row justify-end w-full pr-1">
            <Select value={scopeValue} onValueChange={handleScopeChange}>
              <Select.Trigger>
                <Select.Value placeholder="Select Access" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="store">Read</Select.Item>
                <Select.Item value="admin">No Access</Select.Item>
              </Select.Content>
            </Select>
          </div>
        </Container>
      </Container>
    </div>
  );
}
