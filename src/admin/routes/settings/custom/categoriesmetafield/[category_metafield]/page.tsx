import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Button,
  Container,
  Select,
  Label,
  Prompt,
  RadioGroup,
  Badge,
  Input,
  Textarea,
  Drawer,
  Heading,
  Text,
  toast,
  Kbd,
} from "@camped-ai/ui";
import { ExclamationCircle } from "@camped-ai/icons";
import { sdk } from "../../../../../lib/sdk";
import { useQuery } from "@tanstack/react-query";


const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 23 23"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 19.5l-7.5-7.5 7.5-7.5"
    />
  </svg>
);

export default function CustomDataInput() {
  const navigate = useNavigate();
  const location = useLocation();
  const id = location.pathname.split("/").pop() || "";

  const [name, setName] = useState("");
  const [namespaceKey, setNamespaceKey] = useState("custom.");
  const [description, setDescription] = useState("");
  const [scopeValue, setScopeValue] = useState("");
  const [deleteLinkedValues, setDeleteLinkedValues] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleChecklistChange = (id: string) => {
    setSelectedCategories(
      (prev) =>
        prev.includes(id)
          ? prev.filter((catId) => catId !== id) // Remove if already selected
          : [...prev, id] // Add if not selected
    );
  };


  const fetchProductCategories = async () => {
    const res = await sdk.client.fetch(`/admin/product-categories`, {
      credentials: "include",
    });

    return res?.product_categories || []; // Return only the product_categories array
  };

  const {
    data: productCategories,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["product-categories"],
    queryFn: fetchProductCategories,
  });

  const handleScopeChange = (value: string) => {
    setScopeValue(value);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await sdk.client.fetch(
          `/admin/metafield-definitions?id=${id}`
        );

        if (!result || !Array.isArray(result.metafieldDefinition)) {
          throw new Error("Invalid API response structure");
        }

        const matchedData = result.metafieldDefinition.find(
          (item: { id: string }) => item.id === id
        );

        if (!matchedData) throw new Error("Metafield data not found");

        setName(matchedData.label || "");
        setNamespaceKey(`${matchedData.namespace}.${matchedData.key}`);
        setDescription(matchedData.description || "");
        setScopeValue(matchedData.scope || "");
        setCategories(matchedData.categories);
      } catch (error) {
        console.error("Error fetching metafield data:", error);
      }
    };

    if (id) fetchData();
  }, [id]);

  const fetchProductCategoriesByIds = async (categoryIds: string[]) => {
    if (!categoryIds || categoryIds.length === 0) return [];

    // Fetch product category details for each ID
    const requests = categoryIds.map((categoryId) =>
      sdk.client.fetch(`/admin/product-categories/${categoryId}`, {
        credentials: "include",
      })
    );

    const responses = await Promise.all(requests);
    return responses; // Returns an array of product category details
  };

  // Example: Assume you receive this array dynamically
  const categoryIdsArray = categories;

  // React Query to fetch selected product categories
  const { data: productCategory } = useQuery({
    queryKey: ["product-categories", categoryIdsArray],
    queryFn: () => fetchProductCategoriesByIds(categoryIdsArray),
    enabled: Boolean(categoryIdsArray.length),
  });


  const extractedCategories =
    productCategory?.map((item: any) => ({
      id: item?.product_category?.id,
      name: item?.product_category?.name,
    })) || [];


  useEffect(() => {
    if (isInitialLoad && extractedCategories.length > 0) {
      setSelectedCategories(extractedCategories.map((category) => category.id));
      setIsInitialLoad(false); // Stop running on future updates
    }
  }, [extractedCategories, isInitialLoad]);

  /** Handle Save (Create/Update) */
  const handleSave = async () => {
    try {
      if (!id)
        throw new Error(
          "Metafield ID is missing. Cannot update without an ID."
        );

      if (!name){
        toast.error("Error", {
          description: "Name is required",
        })
        return;
      } 
      if (!namespaceKey.includes(".")){
        toast.error("Error", {
        description: "Namespace must include a period (.) separating namespace and key.",
      })
return;
    }

      if (!scopeValue){
        toast.error("Error", {
          description: "Namespace must include a period (.) separating namespace and key.",
        })
        return;
      }

      const [namespacePart, key] = namespaceKey.split(".");
      if (!namespacePart || !key){
        {
          toast.error("Error", {
            description: "Invalid namespace format.",
          })
          return;
        }
  
      }

      if (selectedCategories.length === 0) {
                  toast.error("Error", {
                            description: "Select atleast one category",
                          })
                  return;
              }

      

      const endpoint = `/admin/metafield-definitions`;

      const payload = [];
      // Create payload
      payload.push({
        id: id,
        label: name,
        description: description,
        scope: scopeValue,
        categories: selectedCategories,
      });

      const responseData = await sdk.client.fetch(endpoint, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: payload,
      });

      toast.success("Success", {
        description: "Updated Successfully.",
        duration: 5000,
        position:"top-center"
      })

      navigate(`/settings/custom/categories`);
    } catch (error) {
      console.error("Error updating metafield:", error);
    }
  };

  const handleDrawerSave = () => {
    console.log("Selected Category IDs:", selectedCategories);

    // 🔹 Ensure at least one category is selected
    if (selectedCategories.length === 0) {
      toast.error("Error", {
        description: "Select atleast one category",
      })
        return;
    }
    setIsDrawerOpen(false);

};


  /** Handle Delete */
  const handleDelete = async () => {
    try {
      if (!id)
        throw new Error(
          "Metafield ID is missing. Cannot delete without an ID."
        );

      const endpoint = "/admin/metafield-definitions";
      const payload = {
        ids: [id], // Send as an array
        delete_linked_values: deleteLinkedValues,
      };

      const responseData = await sdk.client.fetch(endpoint, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: payload,
      });

      navigate("/settings/custom/categories");
    } catch (error) {
      console.error("Error deleting metafield:", error);
    }
  };

  return (
    <div className="">
      <Button
        variant="secondary"
        onClick={() => navigate("/settings/custom/categories")}
        className="mb-3 flex items-center"
      >
        <ArrowIcon />
        <span>Back</span>
      </Button>

      <Container className="p-0 m-0 shadow-sm space-y-4">
        <div className="flex flex-row justify-between border-b px-5 py-5">
          <div>
            <h1 className="font-sans font-medium h1-core">
              {id ? "Edit" : "Add"} Category Metafield Definition
            </h1>
          </div>
          <div className="flex gap-3 mb-3">
            <Prompt>
              <Prompt.Trigger asChild>
                <Button variant="danger" className="h-50%">
                  Delete
                </Button>
              </Prompt.Trigger>
              <Prompt.Content>
                <Prompt.Header>
                  <Prompt.Title>Delete </Prompt.Title>
                  <Prompt.Description>
                    <RadioGroup
                      defaultValue="2"
                      onValueChange={(value) =>
                        setDeleteLinkedValues(value === "2")
                      } 
                    >
                      <div className="flex items-center gap-x-3">
                        <RadioGroup.Item value="1" id="radio_1" />
                        <Label htmlFor="radio_1" weight="plus">
                          Delete Field Only
                        </Label>
                      </div>
                      <div className="flex items-center gap-x-3">
                        <RadioGroup.Item value="2" id="radio_2" />
                        <Label htmlFor="radio_2" weight="plus">
                          Delete Field and its saved values
                        </Label>
                        <Kbd className="text-xs p-1 pt-0 pb-0" rounded="full">
                          {" "}
                          <Label size="xsmall" weight="plus">
                            Recommend
                          </Label>{" "}
                        </Kbd>
                      </div>
                    </RadioGroup>
                  </Prompt.Description>
                </Prompt.Header>
                <Prompt.Footer>
                  <Prompt.Cancel>Cancel</Prompt.Cancel>
                  {id && (
                    <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
                  )}
                </Prompt.Footer>
              </Prompt.Content>
            </Prompt>
            <Button onClick={handleSave}>Save</Button>
          </div>
        </div>
        {/* px-5 py-5 */}
        <div className="flex flex-col px-5 pb-5 gap-5">
          {/* Name Field */}
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              placeholder="Enter name"
            />
          </div>

          {/* Namespace & Key Field (Read-Only) */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Namespace and Key
            </label>
            <Input
              type="text"
              disabled
              value={namespaceKey}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm cursor-not-allowed"
            />
          </div>

          {/* Description Field */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Description
            </label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={100}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            ></Textarea>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Categories</label>

            <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
              <Drawer.Trigger asChild>
                <Button
                  variant="secondary"
                  className="w-full py-2 border rounded-md flex justify-center items-center gap-1"
                  onClick={() => setIsDrawerOpen(true)}
                >
                  {extractedCategories
                    .map((category) => category.name)
                    .join(", ")}
                </Button>
              </Drawer.Trigger>

              <Drawer.Content className="p-4 rounded-lg shadow-lg w-1/2 mx-auto border-b border-gray-300">
                <Drawer.Header>
                  <Heading>Select Categories</Heading>
                </Drawer.Header>
                <Drawer.Body className="flex flex-col items-center py-4">
                  <div className="flex w-full flex-col gap-y-4">
                    <Text className="text-ui-fg-subtle">
                      Select the categories from the checklist below:
                    </Text>
                    <div className="flex flex-col gap-y-2">
                      {productCategories?.map((item) => (
                        <label
                          key={item.id}
                          className="flex items-center gap-2 border-b border-gray-200 pb-2 text-sm"
                        >
                          <input
                            type="checkbox"
                            checked={selectedCategories.includes(item.id)}
                            onChange={() => handleChecklistChange(item.id)}
                            className="form-checkbox h-4 w-4 text-blue-600"
                          />
                          <span>{item.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </Drawer.Body>
                <Drawer.Footer className="flex justify-end gap-2 mt-4">
                  <Drawer.Close asChild>
                    <Button variant="secondary" onClick={() => setIsDrawerOpen(false)}>Cancel</Button>
                  </Drawer.Close>
                  <Button variant="primary" onClick={handleDrawerSave}>
                    Save
                  </Button>
                </Drawer.Footer>
              </Drawer.Content>
            </Drawer>
          </div>
        </div>
      </Container>

      {/* Options Section */}
      <Container className="p-4 mt-4  shadow-sm">
        <h2 className="flex font-sans font-medium h1-core pb-5 gap-1">
          Options{" "}
          <div className="mt-2">
            <ExclamationCircle />
          </div>
        </h2>

        <Container className="flex space-between rounded-lg p-4">
          <div className="w-full">
            <Label className="block text-sm font-medium mb-1">
              Storefront Access
            </Label>
            <Label className="text-xs">
              Definition is available in your Online Store
            </Label>
          </div>
          <div className="flex flex-row justify-end w-full pr-1">
            <Select value={scopeValue} onValueChange={handleScopeChange}>
              <Select.Trigger>
                <Select.Value placeholder="Select Access" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="store">Read</Select.Item>
                <Select.Item value="admin">No Access</Select.Item>
              </Select.Content>
            </Select>
          </div>
        </Container>
      </Container>
    </div>
  );
}
