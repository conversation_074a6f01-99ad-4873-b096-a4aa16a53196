import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Container, Select, Label, Input, Textarea, toast } from "@camped-ai/ui";
import { ExclamationCircle } from "@camped-ai/icons";
import {sdk} from "../../../../../lib/sdk"; // Ensure SDK is properly imported

const ArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 23 23"
    strokeWidth={1.5}
    stroke="currentColor"
    className="w-4 h-4"
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5l-7.5-7.5 7.5-7.5" />
  </svg>
);

export default function CustomDataInput() {
  const navigate = useNavigate();
  const [name, setName] = useState("");
  const [namespaceKey, setNamespaceKey] = useState("custom.");
  const [isNamespaceEdited, setIsNamespaceEdited] = useState(false);
  const [description, setDescription] = useState("");
  const [scopeValue, setScopeValue] = useState("store");

  const [value, setValue] = useState<string | undefined>();
  const triggerRef = useRef(null);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value; // Allow spaces
    setName(newName);

    if (!isNamespaceEdited) {
        let formattedKey = newName
            .toLowerCase()
            .replace(/\s+/g, "_") // Replace spaces with underscores
            .replace(/[^a-z0-9._]+/g, "_") // Replace non-alphanumeric (except . and _)
            .replace(/_+/g, "_") // Collapse multiple underscores
            .replace(/^_|_$/g, "") // Trim leading/trailing underscores
            .replace(/^\.|\.$/g, ""); // Prevent leading/trailing dots

        setNamespaceKey(`custom.${formattedKey}`); // Auto-update unless manually edited
    }
};

/** Handle Namespace Change (User Editable, Stops Auto-Update) */
const handleNamespaceKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.trim().toLowerCase();

    value = value
        .replace(/[^a-z0-9.]+/g, "_") // Replace non-alphanumeric (except .) with _
        .replace(/_+/g, "_") // Remove consecutive underscores
        .replace(/^_|_$/g, "") // Trim underscores from start/end
        .replace(/^\.|\.$/g, ""); // Prevent leading/trailing dots

    setNamespaceKey(value);
    setIsNamespaceEdited(true); // Mark namespace as edited manually
};

/** Set default namespace on page load */
useEffect(() => {
    setNamespaceKey("custom."); // Default namespace
}, []);

  // Handle store or admin
  const handleScopeChange = (value: React.SetStateAction<string>) => {
    setScopeValue(value);
  };

  /** Handle Save (Validates and Sends Correct Namespace & Key) */
  const handleSave = async () => {
    try {
      if (!name?.trim()) {
                      toast.error("Error", {
                          description: "Name is required",
                        })
                return;
                  }
                  if (!namespaceKey?.trim()) {
                      toast.error("Error", {
                          description: "Namespace and key required",
                        })
                return;
                  }
                  if (!namespaceKey.includes(".")) {
                      toast.error("Error", {
                          description: "Namespace must include a period (.) separating namespace and key.",
                        })
                return;
                      }

      const [namespacePart, key] = namespaceKey.split(".", 2); // Ensures only two parts

      const responseData = await sdk.client.fetch("/admin/metafield-definitions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          owner_type: "product",
          namespace: namespacePart,
          key: key,
          label: name,
          description: description,
          scope: scopeValue,
        },
      });


      toast.success("Success", {
                      description: "Created Successfully.",
                      duration: 5000,
                      position:"top-center"
                    })
          
      navigate("/settings/custom/product"); // Navigate after successful save
    } catch (error) {
      console.error("Error posting data:", error);
    }
  };

  return (
    <div className="">
      <Button variant="secondary" onClick={() => navigate("/settings/custom/product")} className="mb-3 flex items-center">
        <ArrowIcon />
        <span>Back</span>
      </Button>

      <Container className="p-0 m-0 shadow-sm space-y-4">
        <div className="flex flex-row justify-between items-center border-b px-5 py-5">
          <h1 className="font-medium h1-core">Add product metafield definition</h1>
          <Button onClick={handleSave}>Save</Button>
        </div>
        <div className="flex flex-col px-5 pb-5 gap-5">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <Input
              type="text"
              value={name}
              onChange={handleNameChange}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              placeholder="Enter name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Namespace and Key</label>
            <Input
              type="text"
              value={namespaceKey}
              onChange={handleNamespaceKeyChange}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={100}
              className="w-full border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            ></Textarea>
          </div>
          
        </div>
      </Container>

      <Container className="p-4 mt-4 rounded-2xl shadow-sm">
        <h2 className="flex font-sans font-medium h1-core pb-5 gap-1">
          Options <div className="mt-2"><ExclamationCircle /></div>
        </h2>
        <Container className="flex space-between rounded-lg p-4">
          <div className="w-full">
            <Label className="block text-sm font-medium mb-1">Storefront Access</Label>
            <Label className="text-xs">Definition is available in your Online Store</Label>
          </div>
          <div className="flex flex-row justify-end w-full pr-1">
            <Select value={scopeValue} onValueChange={handleScopeChange}>
              <Select.Trigger>
                <Select.Value placeholder="Option" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="store">Read</Select.Item>
                <Select.Item value="admin">No Access</Select.Item>
              </Select.Content>
            </Select>
          </div>
        </Container>
      </Container>
    </div>
  );
}
