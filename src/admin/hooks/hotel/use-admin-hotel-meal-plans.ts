import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

export type MealPlan = {
  id: string;
  name: string;
  type: string; // Allow any string for custom types
  is_default: boolean;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
};

type MealPlansResponse = {
  meal_plans: MealPlan[];
};

export const useAdminHotelMealPlans = (hotelId: string) => {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["hotel-meal-plans", hotelId],
    queryFn: async (): Promise<MealPlansResponse> => {
      const response = await sdk.client.fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/meal-plan`
      );
      return response as MealPlansResponse;
    },
    enabled: !!hotelId,
    retry: false,
  });

  return {
    mealPlans: data?.meal_plans || [],
    isLoading,
    isError,
    refetch,
  };
};

export const useAdminCreateHotelMealPlan = () => {
  const createMealPlan = async (
    hotelId: string,
    data: {
      name: string;
      type: string; // Allow any string for custom types
      is_default?: boolean;
      metadata?: Record<string, any>;
    }
  ) => {
    const response = await sdk.client.fetch(
      `/admin/hotel-management/hotels/${hotelId}/pricing/meal-plan`,
      {
        method: "POST",
        body: data,
      }
    );
    return (response as any).meal_plan;
  };

  return {
    createMealPlan,
  };
};

export const useAdminUpdateHotelMealPlan = () => {
  const updateMealPlan = async (
    hotelId: string,
    mealPlanId: string,
    data: {
      name?: string;
      type?: string; // Allow any string for custom types
      is_default?: boolean;
      metadata?: Record<string, any>;
    }
  ) => {
    const response = await sdk.client.fetch(
      `/admin/hotel-management/hotels/${hotelId}/pricing/meal-plan/${mealPlanId}`,
      {
        method: "PUT",
        body: data,
      }
    );
    return (response as any).meal_plan;
  };

  return {
    updateMealPlan,
  };
};

export const useAdminDeleteHotelMealPlan = () => {
  const deleteMealPlan = async (
    hotelId: string,
    mealPlanId: string
  ) => {
    try {
      // Use a direct fetch call to handle 204 responses properly
      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/meal-plan/${mealPlanId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // For 204 responses, just return a success object without trying to parse JSON
      if (response.status === 204 || response.status === 200) {
        return { success: true };
      }

      // Only try to parse JSON for non-204 responses
      if (response.ok) {
        try {
          return await response.json();
        } catch (e) {
          // If JSON parsing fails, still return success for successful requests
          return { success: true };
        }
      }

      throw new Error(`Failed to delete meal plan: ${response.statusText}`);
    } catch (error) {
      console.error("Error deleting meal plan:", error);
      throw error;
    }
  };

  return {
    deleteMealPlan,
  };
};
