import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

// Types for pricing
export type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

export type PricingRule = {
  id: string;
  occupancy_type_id: string;
  occupancy_type: {
    id: string;
    name: string;
    type: string;
    min_age: number | null;
    max_age: number | null;
  };
  meal_plan_id: string;
  meal_plan: {
    id: string;
    name: string;
    type: string;
  };
  room_config_id: string;
  weekday_prices: WeekdayPrices;
  currency_code: string;
  created_at: string;
  updated_at: string;
};

type PricingRulesResponse = {
  weekday_rules: PricingRule[];
};

// Hook to get room config pricing rules
export const useAdminRoomConfigPricing = (roomConfigId: string) => {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["room-config-pricing", roomConfigId],
    queryFn: async (): Promise<PricingRulesResponse> => {
      try {
        const response = await fetch(
          `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch pricing rules: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching pricing rules:", error);
        return { weekday_rules: [] };
      }
    },
    enabled: !!roomConfigId,
    retry: false,
  });

  return {
    pricingRules: data?.weekday_rules || [],
    isLoading,
    isError,
    refetch,
  };
};

// Type for seasonal pricing response
type SeasonalPricingResponse = {
  seasonal_prices: {
    id: string;
    name: string;
    start_date: string;
    end_date: string;
    currency_code: string;
    weekday_rules: {
      id: string;
      occupancy_type_id: string;
      meal_plan_id: string;
      weekday_prices: WeekdayPrices;
    }[];
  }[];
};

// Hook to get seasonal pricing rules
export const useAdminRoomConfigSeasonalPricing = (roomConfigId: string) => {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["room-config-seasonal-pricing", roomConfigId],
    queryFn: async (): Promise<SeasonalPricingResponse> => {
      try {
        const response = await fetch(
          `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch seasonal pricing: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error("Error fetching seasonal pricing:", error);
        return { seasonal_prices: [] };
      }
    },
    enabled: !!roomConfigId,
    retry: false,
  });

  return {
    seasonalPrices: data?.seasonal_prices || [],
    isLoading,
    isError,
    refetch,
  };
};

// Hook to save pricing rules
export const useAdminSavePricing = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      data,
    }: {
      roomConfigId: string;
      data: {
        currency_code: string;
        weekday_rules: {
          occupancy_type_id: string;
          meal_plan_id: string;
          weekday_prices: WeekdayPrices;
        }[];
        // Optional seasonal pricing fields
        start_date?: string;
        end_date?: string;
        name?: string;
      };
    }) => {
      // Determine if this is a seasonal pricing request
      const isSeasonal = data.start_date && data.end_date && data.name;

      // Choose the appropriate endpoint
      const endpoint = isSeasonal
        ? `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/bulk`
        : `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`;

      const response = await fetch(
        endpoint,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save pricing: ${errorText}`);
      }

      return await response.json();
    },
    onSuccess: (_, { roomConfigId }) => {
      queryClient.invalidateQueries({ queryKey: ["room-config-pricing", roomConfigId] });
      // Also invalidate seasonal pricing queries if they exist
      queryClient.invalidateQueries({ queryKey: ["room-config-seasonal-pricing", roomConfigId] });
    },
  });

  return mutation;
};



// Combined hook for all hotel pricing operations
export const useAdminHotelPricing = () => {
  const savePricingMutation = useAdminSavePricing();

  // Function to save pricing (works for both regular and seasonal pricing)
  const savePricing = async (
    roomConfigId: string,
    data: {
      currency_code: string;
      weekday_rules: {
        occupancy_type_id: string;
        meal_plan_id: string;
        weekday_prices: WeekdayPrices;
      }[];
      // Seasonal pricing array
      seasonal_prices?: {
        id: string;
        name: string;
        start_date: string;
        end_date: string;
        weekday_rules: {
          occupancy_type_id: string;
          meal_plan_id: string;
          weekday_prices: WeekdayPrices;
        }[];
      }[];
      // Optional seasonal pricing fields (for backward compatibility)
      start_date?: string;
      end_date?: string;
      name?: string;
    }
  ) => {
    // First save the base weekday pricing
    const baseResult = await savePricingMutation.mutateAsync({
      roomConfigId,
      data: {
        currency_code: data.currency_code,
        weekday_rules: data.weekday_rules
      }
    });

    // Then save each seasonal price if present
    if (data.seasonal_prices && data.seasonal_prices.length > 0) {
      const seasonalPromises = data.seasonal_prices.map(async (seasonalPrice) => {
        // For each seasonal price, we need to make a separate API call
        // to create or update the seasonal pricing
        const seasonalData = {
          currency_code: data.currency_code,
          name: seasonalPrice.name,
          start_date: seasonalPrice.start_date,
          end_date: seasonalPrice.end_date,
          weekday_rules: seasonalPrice.weekday_rules
        };

        try {
          // Use the seasonal pricing endpoint
          const response = await fetch(
            `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/bulk`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(seasonalData),
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`Failed to save seasonal pricing: ${errorText}`);
            return null;
          }

          return await response.json();
        } catch (error) {
          console.error("Error saving seasonal pricing:", error);
          return null;
        }
      });

      const seasonalResults = await Promise.all(seasonalPromises);
      return { baseResult, seasonalResults };
    }

    return baseResult;
  };

  return {
    savePricing,
    isLoading: savePricingMutation.isPending,
    isError: savePricingMutation.isError,
    error: savePricingMutation.error,
  };
};
