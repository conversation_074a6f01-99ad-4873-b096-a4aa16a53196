import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";

type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

type WeekdayRule = {
  id: string;
  occupancy_type_id: string;
  occupancy_type: {
    id: string;
    name: string;
    type: string;
    min_age: number | null;
    max_age: number | null;
  };
  meal_plan_id: string;
  meal_plan: {
    id: string;
    name: string;
    type: string;
  };
  room_config_id: string;
  weekday_prices: WeekdayPrices;
  currency_code: string;
  created_at: string;
  updated_at: string;
};

type WeekdayRulesResponse = {
  weekday_rules: WeekdayRule[];
};

export const useAdminRoomConfigWeekdayRules = (roomConfigId: string) => {
  const client = useAdminClient();

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["room-config-weekday-rules", roomConfigId],
    queryFn: async (): Promise<WeekdayRulesResponse> => {
      const response = await client.get(
        `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing`
      );
      return response.data;
    },
    enabled: !!roomConfigId,
    retry: false,
  });

  return {
    weekdayRules: data?.weekday_rules || [],
    isLoading,
    isError,
    refetch,
  };
};

export const useAdminCreateWeekdayRule = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      data,
    }: {
      roomConfigId: string;
      data: {
        occupancy_type_id: string;
        meal_plan_id: string;
        weekday_prices: WeekdayPrices;
        currency_code?: string;
      };
    }) => {
      const response = await client.post(
        `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing`,
        data
      );
      return response.data;
    },
    onSuccess: (_, { roomConfigId }) => {
      queryClient.invalidateQueries({ queryKey: ["room-config-weekday-rules", roomConfigId] });
    },
  });

  return mutation;
};

export const useAdminUpdateWeekdayRule = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      ruleId,
      data,
    }: {
      roomConfigId: string;
      ruleId: string;
      data: {
        occupancy_type_id?: string;
        meal_plan_id?: string;
        weekday_prices?: WeekdayPrices;
        currency_code?: string;
      };
    }) => {
      const response = await client.post(
        `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/${ruleId}`,
        data
      );
      return response.data;
    },
    onSuccess: (_, { roomConfigId }) => {
      queryClient.invalidateQueries({ queryKey: ["room-config-weekday-rules", roomConfigId] });
    },
  });

  return mutation;
};

export const useAdminDeleteWeekdayRule = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      ruleId,
    }: {
      roomConfigId: string;
      ruleId: string;
    }) => {
      const response = await client.delete(
        `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/${ruleId}`
      );
      return response.data;
    },
    onSuccess: (_, { roomConfigId }) => {
      queryClient.invalidateQueries({ queryKey: ["room-config-weekday-rules", roomConfigId] });
    },
  });

  return mutation;
};

export const useAdminBulkUpsertWeekdayRules = () => {
  const client = useAdminClient();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      data,
    }: {
      roomConfigId: string;
      data: {
        weekday_rules: {
          occupancy_type_id: string;
          meal_plan_id: string;
          weekday_prices: WeekdayPrices;
          currency_code?: string;
        }[];
      };
    }) => {
      const response = await client.post(
        `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`,
        data
      );
      return response.data;
    },
    onSuccess: (_, { roomConfigId }) => {
      queryClient.invalidateQueries({ queryKey: ["room-config-weekday-rules", roomConfigId] });
    },
  });

  return mutation;
};
