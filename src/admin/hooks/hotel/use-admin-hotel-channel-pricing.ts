import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

export type ChannelPriceRule = {
  id?: string;
  base_price_rule_id?: string;
  occupancy_type_id: string;
  meal_plan_id: string;
  sales_channel_id: string;
  amount: number;
  currency_code?: string;
  metadata?: Record<string, any>;
};

export type ChannelPriceGroup = {
  sales_channel_id: string;
  rules: ChannelPriceRule[];
};

export type SalesChannel = {
  id: string;
  name: string;
  description?: string;
  is_disabled?: boolean;
};

/**
 * Hook to fetch channel pricing for a room configuration
 */
export const useAdminRoomConfigChannelPricing = (roomConfigId: string) => {
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["room-config-channel-pricing", roomConfigId],
    queryFn: async () => {
      const response = await fetch(`/admin/hotel-management/room-configs/${roomConfigId}/channel-pricing`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch channel pricing: ${response.statusText}`);
      }
      
      return await response.json();
    },
    enabled: !!roomConfigId,
  });

  return {
    channelPrices: data?.channel_prices || [],
    salesChannels: data?.sales_channels || [],
    isLoading,
    isError,
    error,
    refetch,
  };
};

/**
 * Hook to save channel pricing for a room configuration
 */
export const useAdminSaveRoomConfigChannelPricing = () => {
  const queryClient = useQueryClient();
  
  const mutation = useMutation({
    mutationFn: async ({
      roomConfigId,
      currencyCode,
      channelPriceRules,
    }: {
      roomConfigId: string;
      currencyCode: string;
      channelPriceRules: ChannelPriceRule[];
    }) => {
      const response = await fetch(`/admin/hotel-management/room-configs/${roomConfigId}/channel-pricing`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currency_code: currencyCode,
          channel_price_rules: channelPriceRules,
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save channel pricing: ${errorText}`);
      }
      
      return await response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["room-config-channel-pricing", variables.roomConfigId],
      });
      
      toast.success("Success", {
        description: "Channel pricing saved successfully",
      });
      
      return data;
    },
    onError: (error) => {
      toast.error("Error", {
        description: error instanceof Error ? error.message : "Failed to save channel pricing",
      });
    },
  });
  
  return mutation;
};

/**
 * Hook to fetch all sales channels
 */
export const useAdminSalesChannels = () => {
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["sales-channels"],
    queryFn: async () => {
      const response = await fetch(`/admin/sales-channels`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch sales channels: ${response.statusText}`);
      }
      
      return await response.json();
    },
  });

  return {
    salesChannels: data?.sales_channels || [],
    isLoading,
    isError,
    error,
    refetch,
  };
};
