import { useQuery } from "@tanstack/react-query";
import { useAdminClient } from "../use-admin-client";

type OccupancyType = {
  id: string;
  name: string;
  type: string;
  min_age: number | null;
  max_age: number | null;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
};

type OccupancyTypesResponse = {
  occupancy_types: OccupancyType[];
};

export const useAdminHotelOccupancyTypes = (hotelId: string) => {
  const client = useAdminClient();

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["hotel-occupancy-types", hotelId],
    queryFn: async (): Promise<OccupancyTypesResponse> => {
      const response = await client.get(
        `/admin/hotel-management/hotels/${hotelId}/occupancy-types`
      );
      return response.data;
    },
    enabled: !!hotelId,
    retry: false,
  });

  return {
    occupancyTypes: data?.occupancy_types || [],
    isLoading,
    isError,
    refetch,
  };
};

export const useAdminCreateHotelOccupancyType = () => {
  const client = useAdminClient();

  const createOccupancyType = async (
    hotelId: string,
    data: {
      name: string;
      type: string;
      min_age?: number | null;
      max_age?: number | null;
      min_occupancy?: number;
      max_occupancy?: number;
      is_default?: boolean;
    }
  ) => {
    const response = await client.post(
      `/admin/hotel-management/hotels/${hotelId}/occupancy-types`,
      data
    );
    return response.data;
  };

  return {
    createOccupancyType,
  };
};

export const useAdminUpdateHotelOccupancyType = () => {
  const client = useAdminClient();

  const updateOccupancyType = async (
    hotelId: string,
    occupancyTypeId: string,
    data: {
      name?: string;
      type?: string;
      min_age?: number | null;
      max_age?: number | null;
      min_occupancy?: number;
      max_occupancy?: number;
      is_default?: boolean;
    }
  ) => {
    const response = await client.post(
      `/admin/hotel-management/hotels/${hotelId}/occupancy-types/${occupancyTypeId}`,
      data
    );
    return response.data;
  };

  return {
    updateOccupancyType,
  };
};

export const useAdminDeleteHotelOccupancyType = () => {
  const client = useAdminClient();

  const deleteOccupancyType = async (
    hotelId: string,
    occupancyTypeId: string
  ) => {
    const response = await client.delete(
      `/admin/hotel-management/hotels/${hotelId}/occupancy-types/${occupancyTypeId}`
    );
    return response.data;
  };

  return {
    deleteOccupancyType,
  };
};
