import { useState } from "react";

// Define content types
export type ContentType =
  | "description"
  | "name"
  | "tags"
  | "seo"
  | "email"
  | "general";

interface UseAIGenerateOptions {
  contentType?: ContentType;
  context?: Record<string, any>;
  onSuccess?: (content: string) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for AI-powered content generation
 *
 * @example
 * const { generateContent, isGenerating, error } = useAIGenerate({
 *   contentType: "description",
 *   context: { name: "Product Name", type: "product" },
 *   onSuccess: (content) => setDescription(content),
 * });
 *
 * // Then in your component:
 * <button onClick={generateContent} disabled={isGenerating}>
 *   {isGenerating ? "Generating..." : "Generate with AI"}
 * </button>
 */
export function useAIGenerate(options: UseAIGenerateOptions = {}) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { contentType = "general", context = {}, onSuccess, onError } = options;

  const generateContent = async (customPrompt?: string) => {
    try {
      setIsGenerating(true);
      setError(null);

      // Use fetch directly
      const response = await fetch("/admin/ai-generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: contentType,
          prompt: customPrompt,
          context,
        }),
      });

      const data = await response.json();

      if (data && data.content) {
        onSuccess?.(data.content);
        return data.content;
      } else {
        const errorMessage = "Failed to generate content. Please try again.";
        setError(errorMessage);
        onError?.(new Error(errorMessage));
        return null;
      }
    } catch (err) {
      const errorMessage = "An error occurred while generating content.";
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generateContent,
    isGenerating,
    error,
  };
}

export default useAIGenerate;
