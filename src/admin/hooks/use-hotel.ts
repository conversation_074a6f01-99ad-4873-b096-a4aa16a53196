import { useState, useEffect } from "react";

type HotelImage = {
  id: string;
  url: string;
  isThumbnail?: boolean;
  metadata?: Record<string, any>;
};

export type HotelData = {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  website?: string;
  email?: string;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_featured?: boolean;
  is_pets_allowed?: boolean;
  images?: HotelImage[];
  created_at?: string;
  updated_at?: string;
};

type UseHotelReturn = {
  hotel: HotelData | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
};

/**
 * Hook to fetch hotel data by slug or ID
 * @param slugOrId - The slug or ID of the hotel to fetch
 * @returns Object containing hotel data, loading state, error state, and refetch function
 */
export const useHotel = (slugOrId?: string): UseHotelReturn => {
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchHotel = async () => {
    if (!slugOrId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch hotel details
      const hotelResponse = await fetch(
        `/admin/hotel-management/hotels/${slugOrId}`,
        {
          credentials: "include",
        }
      );

      if (!hotelResponse.ok) {
        throw new Error(
          `Failed to fetch hotel with status ${hotelResponse.status}`
        );
      }

      const hotelData = await hotelResponse.json();

      // Process hotel data - handle both array and direct object responses
      const processedHotelData = Array.isArray(hotelData?.hotel)
        ? hotelData?.hotel[0]
        : hotelData?.hotel || hotelData;

      // If hotel data doesn't have images, fetch them separately
      if (
        !processedHotelData.images ||
        processedHotelData.images.length === 0
      ) {
        try {
          const imagesResponse = await fetch(
            `/admin/hotel-management/hotels/${processedHotelData.id}/images`,
            { credentials: "include" }
          );

          if (imagesResponse.ok) {
            const imagesData = await imagesResponse.json();

            // Combine hotel data with images
            const hotelWithImages = {
              ...processedHotelData,
              images: imagesData.images || [],
            };

            setHotel(hotelWithImages);
          } else {
            // If images fetch fails, still set the hotel data without images
            setHotel(processedHotelData);
          }
        } catch (imageError) {
          console.error("Failed to fetch hotel images:", imageError);
          // If images fetch fails, still set the hotel data without images
          setHotel(processedHotelData);
        }
      } else {
        // If hotel data already has images, use it directly
        setHotel(processedHotelData);
      }
    } catch (err) {
      console.error("Failed to fetch hotel details:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch hotel details")
      );
      setHotel(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHotel();
  }, [slugOrId]);

  return {
    hotel,
    isLoading,
    error,
    refetch: fetchHotel,
  };
};
