import { completeCartWorkflow } from "@camped-ai/medusa/core-flows";
import { Modules, ContainerRegistrationKeys } from "@camped-ai/framework/utils";

completeCartWorkflow.hooks.orderCreated(
  async ({ order_id, cart_id }, { container }) => {
    console.log("inside hook workflow");
    try {
       console.log("Cart completion subscriber triggered", JSON.stringify(order_id,cart_id));
   
       
   
       if (!order_id) {
         console.error("No order ID found in event data");
         return;
       }

       if (!cart_id) {
        console.log(
          `No cart found for cart ID ${cart_id}, skipping metadata transfer`
        );
        return;
      }
   
       // Resolve required services
       const orderModuleService = container.resolve(
         Modules.ORDER
       );
       const cartModuleService = container.resolve(
         Modules.CART
       );
      
   
       // Retrieve the order
       const order = await orderModuleService.retrieveOrder(order_id);
   
       if (!order) {
         console.error(`Order with ID ${order_id} not found`);
         return;
       }
   
       console.log(`Processing order: ${order_id}`);

       // Retrieve the cart with its metadata
       const cart = await cartModuleService.retrieveCart(cart_id);
   
       if (!cart || !cart.metadata) {
         console.log(`Cart ${cart_id} has no metadata, skipping metadata transfer`);
         return;
       }
   
       // Merge cart metadata with existing order metadata
       const updatedMetadata = {
         ...(order.metadata || {}),
         ...cart.metadata,
         // Ensure we don't overwrite any existing order-specific metadata
         cart_id: cart_id,
       };
   
       // Update the order with the merged metadata
       await orderModuleService.updateOrders(order_id, {
         metadata: updatedMetadata,
       });
   
       console.log(
         `Successfully transferred metadata from cart ${cart_id} to order ${order_id}`
       );
     } catch (error) {
       console.error("Error in cart completion subscriber:", error);
     }
   
  }

 
);
