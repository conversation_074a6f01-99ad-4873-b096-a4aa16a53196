import { deleteProductsWorkflow } from "@camped-ai/medusa/core-flows";
import { METAFIELD_MODULE } from "src/modules/metafield";
import MetafieldModuleService from "src/modules/metafield/service";

deleteProductsWorkflow.hooks.productsDeleted(async ({ ids }, { container }) => {
  const metafieldModuleService: MetafieldModuleService =
    container.resolve(METAFIELD_MODULE);

  // Fetch metafields related to the deleted products
  const metafields = await metafieldModuleService.listMetafields(
    { owner_id: ids }, // Fetch metafields related to the deleted product IDs
    { select: ["id"] }
  );

  // Extract IDs from metafield objects
  const metafieldIds = metafields.map((metafield) => metafield.id);

  if (metafieldIds.length > 0) {
    await metafieldModuleService.deleteMetafields(metafieldIds);
  }
});
