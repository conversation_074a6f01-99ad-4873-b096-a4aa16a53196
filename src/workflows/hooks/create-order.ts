import { createOrderWorkflow } from "@camped-ai/medusa/core-flows";
import { Modules } from "@camped-ai/framework/utils";

createOrderWorkflow.hooks.orderCreated(
  async ({ order, additional_data }, { container }) => {
    console.log("inside hook workflow");
    const eventModuleService = container.resolve(Modules.EVENT_BUS);
    await eventModuleService.emit({
      name: "order.placed",
      data: {
        id: order.id,
      },
    });
  }
);
