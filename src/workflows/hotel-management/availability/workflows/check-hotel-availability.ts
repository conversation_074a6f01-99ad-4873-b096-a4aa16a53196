import {
  WorkflowResponse,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import { validateAvailabilityParamsStep } from "../steps/validate-availability-params";
import { getHotelDetailsStep } from "../steps/get-hotel-details";
import { getRoomConfigurationsStep } from "../steps/get-room-configurations";
import { checkRoomAvailabilityStep } from "../steps/check-room-availability";
import { calculatePricesStep } from "../steps/calculate-prices";
import { calculateTaxesStep } from "../steps/calculate-taxes";
import { formatResponseStep } from "../steps/format-response";

export type CheckHotelAvailabilityInput = {
  hotel_id: string; // hotel_id is required for this workflow
  check_in: string;
  check_out: string;
  adults?: number;
  children?: number;
  infants?: number;
  currency_code?: string;
  country_code?: string; // Country code for tax calculation
  province_code?: string; // Province/state code for tax calculation
  include_price_tiers?: boolean;
  include_unavailable?: boolean;
  child_ages?: Array<{ age: number | string }>; // Array of child ages
  sales_channel_id?: string; // Sales channel ID for channel-specific pricing
};

export const checkHotelAvailabilityWorkflowId = "check-hotel-availability-workflow";
export const checkHotelAvailabilityWorkflow = createWorkflow(
  {
    name: checkHotelAvailabilityWorkflowId,
    retentionTime: 99999, // Time in seconds to keep the execution in the database
    store: true,
  },
  function (input: CheckHotelAvailabilityInput) {
    // Validate that hotel_id is provided
    if (!input.hotel_id) {
      throw new Error("hotel_id is required for the availability workflow");
    }

    // Step 1: Validate input parameters
    const validatedParams = validateAvailabilityParamsStep({
      ...input,
      hotel_id: input.hotel_id, // Explicitly pass hotel_id
    });

    // Step 2: Get hotel details
    const hotel = getHotelDetailsStep({
      ...validatedParams,
      // hotel_id is already included in validatedParams
    });

    // Step 3: Get room configurations for the hotel
    const {roomConfigsWithVariants} = getRoomConfigurationsStep({
      ...validatedParams,
      // hotel_id is already included in validatedParams
    });

    console.log(roomConfigsWithVariants)

    // Step 4: Check availability for each room
    const availabilityResults = checkRoomAvailabilityStep({


      room_configurations: roomConfigsWithVariants,
      check_in_date: validatedParams.check_in_date,
      check_out_date: validatedParams.check_out_date,
    });

    // Step 5: Calculate prices for available rooms
    const pricedRooms = calculatePricesStep({
      hotel,
      availability_results: availabilityResults,
      currency_code: validatedParams.currency_code,
      nights: validatedParams.nights,
      adults: validatedParams.adults,
      children: validatedParams.children,
      check_in_date: validatedParams.check_in_date,
      check_out_date: validatedParams.check_out_date,
      child_ages: validatedParams.child_ages,
      sales_channel_id: validatedParams.sales_channel_id,
    });

    // Step 6: Calculate taxes for the priced rooms
    const roomsWithTax = calculateTaxesStep({
      hotel,
      priced_rooms: pricedRooms,
      currency_code: validatedParams.currency_code,
      country_code: input.country_code,
      province_code: input.province_code,
    });

    // Step 7: Format the response
    const response = formatResponseStep({
      hotel,
      priced_rooms: roomsWithTax, // Use rooms with tax information
      include_unavailable: input.include_unavailable || false,
      check_in_date: validatedParams.check_in_date,
      check_out_date: validatedParams.check_out_date,
      adults: validatedParams.adults,
      children: validatedParams.children,
      infants: validatedParams.infants,
      currency_code: validatedParams.currency_code,
      nights: validatedParams.nights,
    });

    return new WorkflowResponse(response);
  }
);
