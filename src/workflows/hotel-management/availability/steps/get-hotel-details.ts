import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { MedusaError } from "@camped-ai/framework/utils";

type GetHotelDetailsInput = {
  hotel_id?: string;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  currency_code: string;
  nights: number;
};

export const getHotelDetailsStep = createStep(
  "get-hotel-details",
  async (input: GetHotelDetailsInput, { container }) => {
    const { hotel_id } = input;

    // Validate hotel_id is provided
    if (!hotel_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "hotel_id is required to get hotel details"
      );
    }

    const query = container.resolve("query");

    // Fetch hotel details
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: {
        id: hotel_id,
      },
      fields: [
        "id",
        "title",
        "handle",
        "description",
        "thumbnail",
        "category_id",
        "metadata",
        "is_active"
      ],
    });

    // Check if hotel exists
    if (!hotel || hotel.length === 0) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Hotel with ID ${hotel_id} not found`
      );
    }

    // Use any type to avoid TypeScript errors with dynamic properties
    const hotelData: any = hotel[0];
    console.log(`Found hotel: ${hotelData.title || hotelData.name} (${hotelData.id})`);
    console.log(`Hotel details:`, JSON.stringify(hotelData));

    // Make sure metadata exists
    if (!hotelData.metadata) {
      hotelData.metadata = {};
    }

    // Extract amenities from metadata if available
    if (!hotelData.metadata.amenities) {
      // Try to find amenities in other metadata fields
      const possibleAmenityFields = ['facilities', 'features', 'services'];
      for (const field of possibleAmenityFields) {
        if (hotelData.metadata[field] && Array.isArray(hotelData.metadata[field])) {
          console.log(`Found amenities in metadata.${field}`);
          hotelData.metadata.amenities = hotelData.metadata[field];
          break;
        }
      }

      // If still no amenities found, set a default empty array
      if (!hotelData.metadata.amenities) {
        console.log(`No amenities found in metadata, setting default empty array`);
        hotelData.metadata.amenities = [];
      }
    }

    return new StepResponse(hotelData);
  }
);
