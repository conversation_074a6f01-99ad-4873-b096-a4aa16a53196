import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { MedusaError, Modules } from "@camped-ai/framework/utils";

type GetRoomConfigurationsInput = {
  hotel_id?: string;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  currency_code: string;
  nights: number;
};

export const getRoomConfigurationsStep = createStep(
  "get-room-configurations",
  async (input: GetRoomConfigurationsInput, { container }) => {
    const { hotel_id } = input;

    // Validate hotel_id is provided
    if (!hotel_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "hotel_id is required to get room configurations"
      );
    }

    const query = container.resolve("query");
    const productModuleService = container.resolve(Modules.PRODUCT);

   


  
    const hotelRecordId = hotel_id; // Use the provided hotel_id

    console.log(`Getting room configurations for hotel:  (${hotelRecordId})`);

    // Try to get room configurations using different methods
    let roomConfigs = [];

    // Get all products once to avoid multiple queries
    const { data: allProducts } = await query.graph({
      entity: "product",
      fields: [
        "id",
        "title",
        "subtitle",
        "description",
        "handle",
        "thumbnail",
        "status",
        "metadata",
      ],
    });

    console.log(`Found ${allProducts?.length || 0} total products in the database`);

    // Method 2: Try using query.graph with hotel_id in metadata
    if (roomConfigs.length === 0) {
      try {
        console.log(`Trying hotel_id in metadata with query.graph: ${hotelRecordId}`);



        // Filter products where metadata.hotel_id matches and metadata.price_set_id exists
        // Using the allProducts we already fetched
        const graphRoomConfigs = allProducts.filter((product: any) => {
          return (
            product.metadata?.hotel_id === hotelRecordId &&
            product.metadata?.price_set_id !== undefined
          );
        });

        if (graphRoomConfigs && graphRoomConfigs.length > 0) {
          console.log(`Found ${graphRoomConfigs.length} room configs using query.graph`);
          roomConfigs = graphRoomConfigs;
        }
      } catch (error) {
        console.error(`Error getting room configs with query.graph:`, error);
      }
    }


 

    if (!roomConfigs || roomConfigs.length === 0) {
      console.log(`No room configurations found for hotel ${hotelRecordId}`);
      return new StepResponse([]);
    }

    console.log(`Found ${roomConfigs.length} room configurations for hotel ${hotelRecordId}`);

   
const roomConfigIds = roomConfigs.map(config => config.id);


const { data: variants } = await query.graph({
  entity: "product_variant",
  filters: {
    product_id: roomConfigIds,
  },
  fields: ["id", "title", "product_id", "metadata", "options"],
});


const variantsByProductId = variants.reduce((acc, variant) => {
  if (!acc[variant.product_id]) {
    acc[variant.product_id] = [];
  }
  acc[variant.product_id].push(variant);
  return acc;
}, {});


const roomConfigsWithVariants = roomConfigs.map(config => ({
  ...config,
  variants: variantsByProductId[config.id] || [],
}));
    

    console.log(`Found ${roomConfigsWithVariants.length} room configurations with variants`);

    return new StepResponse({roomConfigsWithVariants});
  }
  
);
