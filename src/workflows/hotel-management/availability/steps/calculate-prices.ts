import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { HOTEL_PRICING_MODULE } from "../../../../modules/hotel-management/hotel-pricing";

// Child age information
type ChildInfo = {
  age: number | string;
};

// Helper function to get the day of week from a date (0 = Sunday, 1 = Monday, etc.)
function getDayOfWeek(date: Date): number {
  return date.getDay();
}

// Helper function to get the weekday key for a given date
function getWeekdayKey(date: Date): string {
  const dayMap = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];
  return dayMap[getDayOfWeek(date)];
}

type CalculatePricesInput = {
  hotel: any;
  availability_results: any[];
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  child_ages?: ChildInfo[]; // Array of child ages
  currency_code: string;
  nights: number;
  sales_channel_id?: string; // Optional sales channel ID for channel-specific pricing
};

export const calculatePricesStep = createStep(
  "calculate-prices",
  async (input: CalculatePricesInput, { container }) => {
    console.log({ input });
    const {
      hotel,
      availability_results,
      currency_code,
      nights,
      adults = 1,
      children = 0,
      child_ages = [],
      sales_channel_id,
      check_in_date,
      check_out_date,
    } = input;

    // Format dates for the pricing module
    console.log(
      "Check-in date type:",
      typeof check_in_date,
      "Value:",
      check_in_date
    );
    console.log(
      "Check-out date type:",
      typeof check_out_date,
      "Value:",
      check_out_date
    );

    let formattedCheckInDate: string;
    let formattedCheckOutDate: string;

    try {
      formattedCheckInDate =
        check_in_date instanceof Date
          ? check_in_date.toISOString().split("T")[0]
          : typeof check_in_date === "string"
          ? check_in_date
          : new Date(check_in_date).toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting check-in date:", error);
      // Fallback to current date
      formattedCheckInDate = new Date().toISOString().split("T")[0];
    }

    try {
      formattedCheckOutDate =
        check_out_date instanceof Date
          ? check_out_date.toISOString().split("T")[0]
          : typeof check_out_date === "string"
          ? check_out_date
          : new Date(check_out_date).toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting check-out date:", error);
      // Fallback to tomorrow's date
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      formattedCheckOutDate = tomorrow.toISOString().split("T")[0];
    }

    console.log("Formatted check-in date:", formattedCheckInDate);
    console.log("Formatted check-out date:", formattedCheckOutDate);

    console.log(
      "Calculating prices for availability results:",
      availability_results?.length || 0
    );

    // Calculate prices for each available room
    const pricedResults = [];

    for (const config of availability_results) {
      const pricedVariants = [];

      // Process the room configuration
      console.log(`Processing room config ${config.id}`);

      // Calculate prices for each available variant
      for (const variant of config.available_variants) {
        try {
          console.log(`Calculating price for variant ${variant.id}`);

          // Base context for price calculation
          const baseContext = {
            currency_code: currency_code,
            check_in_date: formattedCheckInDate,
            check_out_date: formattedCheckOutDate,
            sales_channel_id: sales_channel_id,
          };

          // Get the hotel pricing service for day-wise pricing and child age calculations
          const hotelPricingService = container.resolve(HOTEL_PRICING_MODULE);

          // Log the parameters we're using for price calculation
          console.log(
            `Calculating prices for room config ${config.id}, variant ${variant.id}`
          );
          console.log(
            `Parameters: adults=${adults}, children=${children}, nights=${nights}`
          );
          console.log(
            `Check-in date: ${baseContext.check_in_date}, Check-out date: ${baseContext.check_out_date}`
          );
          console.log(`Hotel ID: ${hotel.id}`);

          // Initialize price variables with explicit zero values
          let totalPrice = 0;
          let pricePerNight = 0;
          let basePriceRule: any;
          let basePriceRules: any[];

          console.log(
            `Initial price values: totalPrice=${totalPrice}, pricePerNight=${pricePerNight}`
          );

          try {
            // Get base price rules from the hotel pricing service
            console.log(
              `Getting base price rules for room config ${config.id}`
            );

            // Get base price rules for this room configuration
            basePriceRules = await hotelPricingService.listBasePriceRules({
              room_config_id: config.id,
            });

            console.log(
              `Found ${basePriceRules.length} base price rules for room config ${config.id}`
            );

            if (basePriceRules.length === 0) {
              throw new Error(
                `No pricing rules found for room ${config.id}. Please set up proper pricing in the admin interface.`
              );
            }

            // Find the appropriate base price rule for the current occupancy
            // First, try to find a rule for the exact number of adults
            basePriceRule = basePriceRules.find(
              (rule) =>
                rule.min_occupancy <= adults &&
                (!rule.max_occupancy || rule.max_occupancy >= adults)
            );

            if (!basePriceRule) {
              // If no exact match, use the first rule as fallback
              basePriceRule = basePriceRules[0];
              console.log(
                `No exact occupancy match found, using fallback rule: ${basePriceRule.id}`
              );
            } else {
              console.log(
                `Found matching base price rule: ${basePriceRule.id}`
              );
            }

            // Check for seasonal price overrides that apply to the booking dates
            console.log(
              `Checking for seasonal price overrides for base price rule ${basePriceRule.id}`
            );

            const seasonalPriceRules =
              await hotelPricingService.listSeasonalPriceRules({
                base_price_rule_id: basePriceRule.id,
              });

            console.log(
              `Found ${seasonalPriceRules.length} seasonal price rules for base price rule ${basePriceRule.id}`
            );

            // Filter seasonal rules that apply to our booking dates and are active
            const applicableSeasonalRules = seasonalPriceRules.filter(
              (rule) => {
                // Check if rule is active
                const isActive = rule.metadata?.is_active !== false;
                if (!isActive) {
                  console.log(
                    `Seasonal rule ${rule.id} is not active, skipping`
                  );
                  return false;
                }

                // Check if booking dates overlap with seasonal rule dates
                const ruleStartDate = new Date(rule.start_date);
                const ruleEndDate = new Date(rule.end_date);
                const bookingStartDate = new Date(formattedCheckInDate);
                const bookingEndDate = new Date(formattedCheckOutDate);

                // Check for date range overlap
                const hasOverlap =
                  (bookingStartDate <= ruleEndDate &&
                    bookingStartDate >= ruleStartDate) || // Booking start is within rule
                  (bookingEndDate <= ruleEndDate &&
                    bookingEndDate >= ruleStartDate) || // Booking end is within rule
                  (bookingStartDate <= ruleStartDate &&
                    bookingEndDate >= ruleEndDate); // Booking completely contains rule

                if (hasOverlap) {
                  console.log(
                    `Seasonal rule ${rule.id} applies to booking dates ${formattedCheckInDate} to ${formattedCheckOutDate}`
                  );
                } else {
                  console.log(
                    `Seasonal rule ${rule.id} does not apply to booking dates ${formattedCheckInDate} to ${formattedCheckOutDate}`
                  );
                }

                return hasOverlap;
              }
            );

            // Sort by priority (higher priority first) to handle overlapping rules
            applicableSeasonalRules.sort(
              (a, b) => (b.priority || 0) - (a.priority || 0)
            );

            console.log(
              `Found ${applicableSeasonalRules.length} applicable seasonal rules`
            );

            // Use the highest priority seasonal rule if available, otherwise use base rule
            const effectivePriceRule =
              applicableSeasonalRules.length > 0
                ? applicableSeasonalRules[0]
                : null;

            if (effectivePriceRule) {
              console.log(
                `Using seasonal price rule ${effectivePriceRule.id} with priority ${effectivePriceRule.priority}`
              );
            } else {
              console.log(
                `No applicable seasonal rules found, using base price rule ${basePriceRule.id}`
              );
            }

            // Calculate day-wise pricing
            totalPrice = 0; // Reset the total price
            let dayPrices = [];

            // Create a date range for the stay
            const checkInDate = new Date(formattedCheckInDate);
            const checkOutDate = new Date(formattedCheckOutDate);

            // For each day of the stay, get the appropriate day price
            const currentDate = new Date(checkInDate);
            while (currentDate < checkOutDate) {
              const weekdayKey = getWeekdayKey(currentDate);

              let dayPrice: number;

              // Use seasonal pricing if available, otherwise use base pricing
              if (effectivePriceRule) {
                // Get weekday-specific price from seasonal rule metadata
                const weekdayPrices =
                  effectivePriceRule.metadata?.weekday_prices;
                if (weekdayPrices && weekdayPrices[weekdayKey] !== undefined) {
                  dayPrice = Number(weekdayPrices[weekdayKey] || 0);
                  console.log(
                    `Using seasonal weekday price for ${weekdayKey}: ${dayPrice}`
                  );
                } else {
                  // Fall back to seasonal rule base amount
                  dayPrice = Number(effectivePriceRule.amount || 0);
                  console.log(
                    `Using seasonal base price for ${weekdayKey}: ${dayPrice}`
                  );
                }
              } else {
                // Use base price rule
                dayPrice = Number(basePriceRule.amount || 0); // Default to base amount

                // Check if there's a specific price for this day of the week in base rule
                const dayPriceKey = `${weekdayKey}_price`;
                if (
                  basePriceRule[dayPriceKey] !== null &&
                  basePriceRule[dayPriceKey] !== undefined
                ) {
                  dayPrice = Number(basePriceRule[dayPriceKey] || 0);
                }
                console.log(`Using base price for ${weekdayKey}: ${dayPrice}`);
              }

              // Add to the total price
              totalPrice = Number(totalPrice) + dayPrice;

              console.log(
                `Day ${weekdayKey}: price=${dayPrice}, running total=${totalPrice}`
              );

              // Store the day price for reference
              dayPrices.push({
                date: new Date(currentDate),
                price: dayPrice,
                day_of_week: weekdayKey,
                source: effectivePriceRule ? "seasonal" : "base",
              });

              // Move to the next day
              currentDate.setDate(currentDate.getDate() + 1);
            }

            console.log(
              `Calculated day-wise pricing:`,
              dayPrices,
              basePriceRule
            );
            console.log(`Total price for ${nights} nights: ${totalPrice}`);

            // Calculate average price per night - ensure we're working with numbers
            pricePerNight = Number(totalPrice) / Number(nights);
            console.log(
              `Calculated average price per night: ${pricePerNight} (${totalPrice} / ${nights})`
            );

            // Apply extra adult pricing if needed
            if (adults > 2) {
              // Find extra adult price rule
              const extraAdultRule = basePriceRules.find(
                (rule) =>
                  rule.occupancy_type_id &&
                  rule.occupancy_type_id.includes("EXTRA_ADULT")
              );

              if (extraAdultRule) {
                const extraAdults = adults - 2;
                const extraAdultTotal =
                  Number(extraAdultRule.amount || 0) *
                  Number(nights) *
                  extraAdults;
                totalPrice = Number(totalPrice) + extraAdultTotal;

                console.log(
                  `Added ${extraAdultTotal} for ${extraAdults} extra adults, new total: ${totalPrice}`
                );
              }
            }

            // Apply child pricing based on ages if available
            if (children > 0 && child_ages && child_ages.length > 0) {
              console.log(
                `Processing pricing for ${child_ages.length} children with ages:`,
                child_ages.map((c) => c.age)
              );

              // Get all child occupancy configs
              const childOccupancyConfigs =
                await hotelPricingService.listOccupancyConfigs({
                  hotel_id: hotel.id,
                  type: "CHILD",
                });

              console.log(
                `Found ${childOccupancyConfigs.length} child occupancy configs:`,
                childOccupancyConfigs.map(
                  (c) =>
                    `${c.name || "Unnamed"} (${c.min_age || 0}-${
                      c.max_age || 99
                    })`
                )
              );

              // For each child, find the appropriate price based on age
              for (const child of child_ages) {
                console.log(`Finding price for child with age ${child.age}`);

                // Find the occupancy config that matches this child's age
                const matchingConfig = childOccupancyConfigs.find((cfg) => {
                  // Convert all values to numbers for comparison
                  const childAge =
                    typeof child.age === "string"
                      ? parseInt(child.age, 10)
                      : child.age;
                  const minAge =
                    typeof cfg.min_age === "string"
                      ? parseInt(cfg.min_age, 10)
                      : cfg.min_age;
                  const maxAge =
                    typeof cfg.max_age === "string"
                      ? parseInt(cfg.max_age, 10)
                      : cfg.max_age;
                  return childAge >= minAge && childAge <= maxAge;
                });

                if (matchingConfig) {
                  console.log(
                    `Found matching occupancy config: ${matchingConfig.name} (${matchingConfig.id})`
                  );

                  // Get specific price rules for this child occupancy type
                  const childPriceRules =
                    await hotelPricingService.listBasePriceRules({
                      room_config_id: config.id,
                      occupancy_type_id: matchingConfig.id,
                    });

                  if (childPriceRules && childPriceRules.length > 0) {
                    const childPriceRule = childPriceRules[0];
                    console.log(
                      `Found price rule for child: ${childPriceRule.id}`
                    );

                    // Calculate day-wise pricing for this child
                    let childTotalPrice = 0;
                    const childCheckInDate = new Date(formattedCheckInDate);
                    const childCheckOutDate = new Date(formattedCheckOutDate);

                    // For each day of the stay, get the appropriate day price
                    const childCurrentDate = new Date(childCheckInDate);
                    while (childCurrentDate < childCheckOutDate) {
                      const weekdayKey = getWeekdayKey(childCurrentDate);

                      // Get the price for this day of the week - ensure we're working with numbers
                      let dayPrice = Number(childPriceRule.amount || 0); // Default to base amount

                      // Check if there's a specific price for this day of the week
                      const dayPriceKey = `${weekdayKey}_price`;
                      if (
                        childPriceRule[dayPriceKey] !== null &&
                        childPriceRule[dayPriceKey] !== undefined
                      ) {
                        dayPrice = Number(childPriceRule[dayPriceKey] || 0);
                      }

                      // Add to the child's total price
                      childTotalPrice = Number(childTotalPrice) + dayPrice;

                      console.log(
                        `Child age ${child.age} day ${weekdayKey}: price=${dayPrice}, running total=${childTotalPrice}`
                      );

                      // Move to the next day
                      childCurrentDate.setDate(childCurrentDate.getDate() + 1);
                    }

                    // Add the child's price to the total
                    totalPrice = Number(totalPrice) + Number(childTotalPrice);
                    console.log(
                      `Added ${childTotalPrice} for child age ${child.age} (day-wise pricing), new total: ${totalPrice}`
                    );
                  } else {
                    console.log(
                      `No specific price rules found for child occupancy type ${matchingConfig.id}`
                    );

                    // Try to find a generic child price rule
                    const genericChildRule = basePriceRules.find(
                      (rule) =>
                        rule.occupancy_type_id &&
                        rule.occupancy_type_id.includes("CHILD")
                    );

                    if (genericChildRule) {
                      const childTotal =
                        Number(genericChildRule.amount || 0) * Number(nights);
                      totalPrice = Number(totalPrice) + childTotal;
                      console.log(
                        `Added ${childTotal} for child age ${child.age} (generic child rule), new total: ${totalPrice}`
                      );
                    } else {
                      console.log(
                        `No generic child price rule found for child age ${child.age}`
                      );
                    }
                  }
                } else {
                  console.log(
                    `No matching occupancy config found for child age ${child.age}`
                  );

                  // Try to find a generic child price rule
                  const genericChildRule = basePriceRules.find(
                    (rule) =>
                      rule.occupancy_type_id &&
                      rule.occupancy_type_id.includes("CHILD")
                  );

                  if (genericChildRule) {
                    const childTotal =
                      Number(genericChildRule.amount || 0) * Number(nights);
                    totalPrice = Number(totalPrice) + childTotal;
                    console.log(
                      `Added ${childTotal} for child age ${child.age} (generic child rule - no matching config), new total: ${totalPrice}`
                    );
                  } else {
                    console.log(
                      `No generic child price rule found for child age ${child.age}`
                    );
                  }
                }
              }
            } else if (children > 0) {
              console.log(
                `Processing pricing for ${children} children without age information`
              );

              // If no child ages provided, use a generic child price
              const childRule = basePriceRules.find(
                (rule) =>
                  rule.occupancy_type_id &&
                  rule.occupancy_type_id.includes("CHILD")
              );

              if (childRule) {
                const childTotal =
                  Number(childRule.amount || 0) *
                  Number(nights) *
                  Number(children);
                totalPrice = Number(totalPrice) + childTotal;
                console.log(
                  `Added ${childTotal} for ${children} children (generic pricing), new total: ${totalPrice}`
                );
              } else {
                console.log(
                  `No generic child price rule found for ${children} children`
                );
              }
            }
          } catch (priceError) {
            console.error(
              `Error calculating prices for room ${variant.id}:`,
              priceError
            );
            throw new Error(
              `Price for room ${variant.id} could not be calculated: ${priceError.message}`
            );
          }

          // Format the prices - ensure we're working with numbers
          console.log(
            `Before conversion: pricePerNight=${pricePerNight} (${typeof pricePerNight}), totalPrice=${totalPrice} (${typeof totalPrice})`
          );

          // Ensure we're working with numbers before division
          const pricePerNightNum = Number(pricePerNight);
          const totalPriceNum = Number(totalPrice);

          console.log(
            `After Number() conversion: pricePerNightNum=${pricePerNightNum}, totalPriceNum=${totalPriceNum}`
          );

          // Convert cents to decimal
          const pricePerNightDecimal = pricePerNightNum / 100;
          const totalPriceDecimal = totalPriceNum / 100;

          console.log(
            `Final price calculation: pricePerNight=${pricePerNight}, totalPrice=${totalPrice}`
          );
          console.log(
            `Converted to decimal: pricePerNightDecimal=${pricePerNightDecimal}, totalPriceDecimal=${totalPriceDecimal}`
          );

          let formattedPricePerNight = `${currency_code} ${pricePerNightDecimal.toFixed(
            2
          )}`;
          let formattedTotalPrice = `${currency_code} ${totalPriceDecimal.toFixed(
            2
          )}`;

          try {
            // Try to use Intl.NumberFormat for better formatting
            const formatter = new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency_code,
            });

            formattedPricePerNight = formatter.format(pricePerNightDecimal);
            formattedTotalPrice = formatter.format(totalPriceDecimal);
          } catch (formatError) {
            console.error(`Error formatting prices:`, formatError);
            // Continue with basic formatting
          }

          // Calculate prices for different meal plans
          const mealPlans = ["none", "bb", "hb", "fb"];
          const mealPlanPrices = {};

          // Calculate prices for all meal plans using the hotel pricing service
          for (const mealPlan of mealPlans) {
            try {
              console.log(`Calculating price for meal plan: ${mealPlan}`);

              let mealPlanRule: any;
              let mealPlanId: string | null = null;

              // Get meal plan ID for this meal plan type (including "none")
              const mealPlanEntities = await hotelPricingService.listMealPlans({
                hotel_id: hotel.id,
                type: mealPlan,
              });

              if (!mealPlanEntities || mealPlanEntities.length === 0) {
                console.log(`No meal plan found for type ${mealPlan}`);
                if (mealPlan === "none") {
                  // For "none" meal plan, if no specific meal plan is found, use the base price rule
                  mealPlanRule = basePriceRule;
                  console.log(
                    `No specific "none" meal plan found, using base price rule: ${mealPlanRule.id}`
                  );
                } else {
                  continue; // Skip to the next meal plan
                }
              } else {
                mealPlanId = mealPlanEntities[0].id;
                console.log(
                  `Found meal plan ID ${mealPlanId} for type ${mealPlan}`
                );

                // Get base price rules for this meal plan
                const mealPlanRules =
                  await hotelPricingService.listBasePriceRules({
                    room_config_id: config.id,
                    meal_plan_id: mealPlanId,
                  });

                if (!mealPlanRules || mealPlanRules.length === 0) {
                  console.log(`No price rules found for meal plan ${mealPlan}`);
                  if (mealPlan === "none") {
                    // For "none" meal plan, if no specific price rules found, use the base price rule
                    mealPlanRule = basePriceRule;
                    console.log(
                      `No specific price rules found for "none" meal plan, using base price rule: ${mealPlanRule.id}`
                    );
                  } else {
                    continue; // Skip to the next meal plan
                  }
                } else {
                  // Find the appropriate rule for the current occupancy
                  mealPlanRule = mealPlanRules.find(
                    (rule) =>
                      rule.min_occupancy <= adults &&
                      (!rule.max_occupancy || rule.max_occupancy >= adults)
                  );

                  if (!mealPlanRule) {
                    // If no exact match, use the first rule as fallback
                    mealPlanRule = mealPlanRules[0];
                    console.log(
                      `No exact occupancy match found for meal plan ${mealPlan}, using fallback rule: ${mealPlanRule.id}`
                    );
                  } else {
                    console.log(
                      `Found matching rule for meal plan ${mealPlan}: ${mealPlanRule.id}`
                    );
                  }
                }
              }

              // Check for seasonal price overrides for this meal plan rule
              console.log(
                `Checking for seasonal price overrides for meal plan ${mealPlan} base price rule ${mealPlanRule.id}`
              );

              const mealPlanSeasonalRules =
                await hotelPricingService.listSeasonalPriceRules({
                  base_price_rule_id: mealPlanRule.id,
                });

              console.log(
                `Found ${mealPlanSeasonalRules.length} seasonal price rules for meal plan ${mealPlan} base price rule ${mealPlanRule.id}`
              );

              // Filter seasonal rules that apply to our booking dates and are active
              const applicableMealPlanSeasonalRules =
                mealPlanSeasonalRules.filter((rule) => {
                  // Check if rule is active
                  const isActive = rule.metadata?.is_active !== false;
                  if (!isActive) {
                    console.log(
                      `Meal plan seasonal rule ${rule.id} is not active, skipping`
                    );
                    return false;
                  }

                  // Check if booking dates overlap with seasonal rule dates
                  const ruleStartDate = new Date(rule.start_date);
                  const ruleEndDate = new Date(rule.end_date);
                  const bookingStartDate = new Date(formattedCheckInDate);
                  const bookingEndDate = new Date(formattedCheckOutDate);

                  // Check for date range overlap
                  const hasOverlap =
                    (bookingStartDate <= ruleEndDate &&
                      bookingStartDate >= ruleStartDate) || // Booking start is within rule
                    (bookingEndDate <= ruleEndDate &&
                      bookingEndDate >= ruleStartDate) || // Booking end is within rule
                    (bookingStartDate <= ruleStartDate &&
                      bookingEndDate >= ruleEndDate); // Booking completely contains rule

                  if (hasOverlap) {
                    console.log(
                      `Meal plan seasonal rule ${rule.id} applies to booking dates ${formattedCheckInDate} to ${formattedCheckOutDate}`
                    );
                  }

                  return hasOverlap;
                });

              // Sort by priority (higher priority first) to handle overlapping rules
              applicableMealPlanSeasonalRules.sort(
                (a, b) => (b.priority || 0) - (a.priority || 0)
              );

              // Use the highest priority seasonal rule if available, otherwise use base rule
              const effectiveMealPlanRule =
                applicableMealPlanSeasonalRules.length > 0
                  ? applicableMealPlanSeasonalRules[0]
                  : null;

              if (effectiveMealPlanRule) {
                console.log(
                  `Using seasonal price rule ${effectiveMealPlanRule.id} for meal plan ${mealPlan} with priority ${effectiveMealPlanRule.priority}`
                );
              } else {
                console.log(
                  `No applicable seasonal rules found for meal plan ${mealPlan}, using base price rule ${mealPlanRule.id}`
                );
              }

              // Calculate day-wise pricing for this meal plan
              let mealPlanTotalPrice = 0;

              // Create a date range for the stay
              const checkInDate = new Date(formattedCheckInDate);
              const checkOutDate = new Date(formattedCheckOutDate);

              // For each day of the stay, get the appropriate day price
              const mealPlanCurrentDate = new Date(checkInDate);
              while (mealPlanCurrentDate < checkOutDate) {
                const weekdayKey = getWeekdayKey(mealPlanCurrentDate);

                let dayPrice: number;

                // Use seasonal pricing if available, otherwise use base pricing
                if (effectiveMealPlanRule) {
                  // Get weekday-specific price from seasonal rule metadata
                  const weekdayPrices =
                    effectiveMealPlanRule.metadata?.weekday_prices;
                  if (
                    weekdayPrices &&
                    weekdayPrices[weekdayKey] !== undefined
                  ) {
                    dayPrice = Number(weekdayPrices[weekdayKey] || 0);
                    console.log(
                      `Using seasonal weekday price for meal plan ${mealPlan} ${weekdayKey}: ${dayPrice}`
                    );
                  } else {
                    // Fall back to seasonal rule base amount
                    dayPrice = Number(effectiveMealPlanRule.amount || 0);
                    console.log(
                      `Using seasonal base price for meal plan ${mealPlan} ${weekdayKey}: ${dayPrice}`
                    );
                  }
                } else {
                  // Use base price rule
                  dayPrice = Number(mealPlanRule.amount || 0); // Default to base amount

                  // Check if there's a specific price for this day of the week in base rule
                  const dayPriceKey = `${weekdayKey}_price`;
                  if (
                    mealPlanRule[dayPriceKey] !== null &&
                    mealPlanRule[dayPriceKey] !== undefined
                  ) {
                    dayPrice = Number(mealPlanRule[dayPriceKey] || 0);
                  }
                  console.log(
                    `Using base price for meal plan ${mealPlan} ${weekdayKey}: ${dayPrice}`
                  );
                }

                // Add to the total price
                mealPlanTotalPrice = Number(mealPlanTotalPrice) + dayPrice;

                console.log(
                  `Meal plan ${mealPlan} day ${weekdayKey}: price=${dayPrice}, running total=${mealPlanTotalPrice}`
                );

                // Move to the next day
                mealPlanCurrentDate.setDate(mealPlanCurrentDate.getDate() + 1);
              }

              // Calculate average price per night - ensure we're working with numbers
              const mealPlanPricePerNight =
                Number(mealPlanTotalPrice) / Number(nights);

              // Convert cents to decimal
              const mealPlanPricePerNightDecimal =
                Number(mealPlanPricePerNight) / 100;
              const mealPlanTotalPriceDecimal =
                Number(mealPlanTotalPrice) / 100;

              console.log(
                `Meal plan ${mealPlan} price calculation: totalPrice=${mealPlanTotalPrice}, perNight=${mealPlanPricePerNight}`
              );
              console.log(
                `Converted to decimal: perNightDecimal=${mealPlanPricePerNightDecimal}, totalDecimal=${mealPlanTotalPriceDecimal}`
              );

              // Add the meal plan price
              const mealPlanTotalAmount = mealPlanTotalPriceDecimal;
              const mealPlanPerNightAmount =
                mealPlanTotalAmount / Number(nights);

              mealPlanPrices[mealPlan] = {
                amount: mealPlanPerNightAmount,
                per_night_amount: mealPlanPerNightAmount,
                currency_code,
                formatted: `${currency_code} ${mealPlanPerNightAmount.toFixed(
                  2
                )}`,
                total_amount: mealPlanTotalAmount,
                original_amount: mealPlanPerNightAmount,
                nights: Number(nights),
              };

              console.log(
                `Added price for meal plan ${mealPlan}: ${mealPlanPricePerNightDecimal} per night`
              );
            } catch (mealPlanError) {
              console.error(
                `Error processing meal plan ${mealPlan}:`,
                mealPlanError
              );
              // Skip this meal plan on error
              continue;
            }
          }

          console.log(
            `Meal plan prices for variant ${variant.id}:`,
            JSON.stringify(mealPlanPrices)
          );

          // Add the variant with price information including meal plans
          const finalTotalAmount = totalPriceDecimal;
          const finalPerNightAmount = finalTotalAmount / Number(nights);

          const priceObject = {
            amount: finalPerNightAmount,
            currency_code,
            formatted: formattedTotalPrice,
            total_amount: finalTotalAmount,
            original_amount: finalPerNightAmount,
            per_night_amount: finalPerNightAmount,
            nights: Number(nights),
            meal_plans: mealPlanPrices,
            selected_meal_plan: "none",
          };

          const pricePerNightObject = {
            amount: finalPerNightAmount,
            currency_code,
            formatted: new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency_code,
            }).format(finalPerNightAmount),
          };

          console.log("Final price object:", JSON.stringify(priceObject));
          console.log(
            "Final price per night object:",
            JSON.stringify(pricePerNightObject)
          );

          pricedVariants.push({
            ...variant,
            price: priceObject,
            price_per_night: pricePerNightObject,
          });
        } catch (error) {
          console.error(
            `Error calculating price for room ${variant.id}:`,
            error
          );
          // Add the variant without price information
          pricedVariants.push(variant);
        }
      }

      // Add the room configuration with its priced variants
      pricedResults.push({
        ...config,
        priced_variants: pricedVariants,
        unavailable_variants: config.unavailable_variants || [],
      });
    }

    return new StepResponse(pricedResults);
  }
);
