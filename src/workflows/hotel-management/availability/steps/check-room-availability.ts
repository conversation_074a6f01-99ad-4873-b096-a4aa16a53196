import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { MedusaError } from "@camped-ai/framework/utils";

type CheckRoomAvailabilityInput = {
  room_configurations: any[];
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  currency_code: string;
  nights: number;
};

export const checkRoomAvailabilityStep = createStep(
  "check-room-availability",
  async (input: CheckRoomAvailabilityInput, { container }) => {
    console.log("Input to checkRoomAvailabilityStep:", input);

    const { room_configurations, check_in_date, check_out_date } = input;

    // Ensure dates are Date objects
    const checkInDate = check_in_date instanceof Date ? check_in_date : new Date(check_in_date);
    const checkOutDate = check_out_date instanceof Date ? check_out_date : new Date(check_out_date);

    console.log(`Converted dates: check_in=${checkInDate.toISOString()}, check_out=${checkOutDate.toISOString()}`);

    const query = container.resolve("query");

    // Check availability for each room configuration and its variants
    const availabilityResults = [];

    for (const config of room_configurations) {
      const availableVariants = [];
      const unavailableVariants = [];

      // Check availability for each variant (room)
      for (const variant of config.variants) {
        try {
          console.log(`Checking availability for room ${variant.id} from ${checkInDate.toISOString()} to ${checkOutDate.toISOString()}`);

          // Query the room_inventory table for this variant
          const { data: roomInventory } = await query.graph({
            entity: "room_inventory",
            filters: {
              // Use inventory_item_id for the variant ID based on the database schema
              inventory_item_id: variant.id,
            },
            fields: [
              "id",
              "inventory_item_id",
              "from_date",
              "to_date",
              "status",
              "available_quantity",
           
            ],
          });

          console.log(`Found ${roomInventory ? roomInventory.length : 0} inventory entries for room ${variant.id}`);

          // Check if there are any entries that cover the requested date range
          let hasAvailableEntry = false;
          let hasConflict = false;

          // We need to check if there are entries covering each day of the stay
          const requestedDays = [];

          // Normalize dates to UTC for consistent comparison
          const checkInDateUTC = new Date(
            Date.UTC(
              checkInDate.getFullYear(),
              checkInDate.getMonth(),
              checkInDate.getDate()
            )
          );

          const checkOutDateUTC = new Date(
            Date.UTC(
              checkOutDate.getFullYear(),
              checkOutDate.getMonth(),
              checkOutDate.getDate()
            )
          );

          // Create a list of days to check
          let currentDate = new Date(checkInDateUTC);
          while (currentDate < checkOutDateUTC) {
            const dayToCheck = new Date(currentDate);
            requestedDays.push(dayToCheck);
            currentDate.setDate(currentDate.getDate() + 1);
          }

          // Collect unavailable dates and reasons
          const unavailableDates = [];
          const unavailableReasons = [];

          // Check if each day is covered by an available inventory entry
          for (const day of requestedDays) {
            let dayIsCovered = false;

            for (const entry of roomInventory) {
              // Parse dates and normalize to UTC for comparison
              const entryFromDate = new Date(entry.from_date);
              const entryToDate = new Date(entry.to_date);

              // For comparison, use only the date part (year, month, day) to avoid timezone issues
              const entryFromDateOnly = new Date(
                Date.UTC(
                  entryFromDate.getFullYear(),
                  entryFromDate.getMonth(),
                  entryFromDate.getDate()
                )
              );

              const entryToDateOnly = new Date(
                Date.UTC(
                  entryToDate.getFullYear(),
                  entryToDate.getMonth(),
                  entryToDate.getDate()
                )
              );

              const dayDateOnly = new Date(
                Date.UTC(day.getFullYear(), day.getMonth(), day.getDate())
              );

              // Check if this entry covers this day and is available
              if (entryFromDateOnly <= dayDateOnly && entryToDateOnly > dayDateOnly) {
                // Check if the entry status makes the room available
                const isAvailable =
                  entry.status === "available" &&
                  (entry.available_quantity === undefined || entry.available_quantity > 0);

                if (isAvailable) {
                  dayIsCovered = true;
                  break;
                } else {
                  // This day has a conflicting entry
                  hasConflict = true;

                  // Add to unavailable dates and reasons
                  const dateStr = day.toISOString().split('T')[0];
                  if (!unavailableDates.includes(dateStr)) {
                    unavailableDates.push(dateStr);
                    unavailableReasons.push({
                      date: dateStr,
                      reason: `Room is ${entry.status} on this date`
                    });
                  }

                  break;
                }
              }
            }

            if (!dayIsCovered) {
              // If day is not covered by any inventory entry, it's unavailable
              const dateStr = day.toISOString().split('T')[0];
              if (!unavailableDates.includes(dateStr)) {
                unavailableDates.push(dateStr);
                unavailableReasons.push({
                  date: dateStr,
                  reason: 'No inventory entry found for this date'
                });
              }

              hasAvailableEntry = false;
            } else if (!hasConflict) {
              hasAvailableEntry = true;
            }
          }

          // Room is available only if all days are covered by available entries and there are no conflicts
          const isAvailable = hasAvailableEntry && !hasConflict;

          // Create availability object
          const availability = {
            available: isAvailable,
            unavailableDates: unavailableDates,
            unavailableReasons: unavailableReasons,
            inventoryEntries: roomInventory
          };

          if (isAvailable) {
            availableVariants.push({
              ...variant,
              availability,
            });
          } else {
            unavailableVariants.push({
              ...variant,
              availability,
            });
          }
        } catch (error) {
          console.error(`Error checking availability for room ${variant.id}:`, error);
          unavailableVariants.push({
            ...variant,
            availability: {
              available: false,
              unavailableDates: [checkInDate.toISOString().split('T')[0]],
              unavailableReasons: [{
                date: checkInDate.toISOString().split('T')[0],
                reason: `Error checking availability: ${error.message || 'Unknown error'}`
              }]
            },
          });
        }
      }

      // Add the room configuration with its available and unavailable variants
      availabilityResults.push({
        ...config,
        available_variants: availableVariants,
        unavailable_variants: unavailableVariants,
        available: availableVariants.length > 0,
      });
    }

    return new StepResponse(availabilityResults);
  }
);
