import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { Modules } from "@camped-ai/framework/utils";
import { TaxTypes } from "@camped-ai/types";

type CalculateTaxesInput = {
  hotel: any;
  priced_rooms: any[];
  currency_code: string;
  country_code?: string; // Country code for tax calculation
  province_code?: string; // Province/state code for tax calculation
};

export const calculateTaxesStep = createStep(
  "calculate-taxes",
  async (input: CalculateTaxesInput, { container }) => {
    const {
      hotel,
      priced_rooms,
      currency_code,
      country_code = "US", // Default to US if not provided
      province_code
    } = input;

    console.log(`Calculating taxes for hotel ${hotel.id} with country code ${country_code}`);
    const startTime = Date.now();

    // Get the tax module service
    const taxService = container.resolve(Modules.TAX);

    if (!taxService) {
      console.warn(`Tax service not available, proceeding without tax calculation`);
      return new StepResponse(priced_rooms);
    }

    // Create a tax calculation context
    const taxContext: TaxTypes.TaxCalculationContext = {
      address: {
        country_code: country_code,
        province_code: province_code || undefined,
      },
    };

    // Prepare all taxable items in a single batch to reduce API calls
    const allTaxableItems: TaxTypes.TaxableItemDTO[] = [];

    // First pass: collect all items that need tax calculation
    for (const config of priced_rooms) {
      for (const variant of config.priced_variants || []) {
        if (variant.price) {
          // Create a taxable item for this variant
          allTaxableItems.push({
            id: variant.id,
            product_id: config.id,
            product_type_id: "hotel_room", // Use a consistent product type for hotel rooms
            unit_price: Math.round(variant.price.total_amount * 100), // Convert to cents
            quantity: 1,
          });
        }
      }
    }

    console.log(`Calculating taxes for ${allTaxableItems.length} items in a single batch`);

    // Get tax lines for all items in a single call
    let allTaxLines: any[] = [];

    // Make the API call to get tax lines
    try {
      if (allTaxableItems.length > 0) {
        const batchStartTime = Date.now();

        // Call the tax service to get tax lines
        allTaxLines = await taxService.getTaxLines(allTaxableItems, taxContext);
        console.log(`Batch tax calculation took ${Date.now() - batchStartTime}ms for ${allTaxableItems.length} items`);

        if (allTaxLines.length === 0) {
          console.log(`No tax lines returned from tax service, proceeding without tax calculation`);
          return new StepResponse(priced_rooms);
        }
      } else {
        console.log(`No taxable items to process, proceeding without tax calculation`);
        return new StepResponse(priced_rooms);
      }
    } catch (error) {
      console.error("Error calculating taxes in batch:", error);
      console.log(`Proceeding without tax calculation due to error`);
      // Return the original priced rooms without tax calculation
      return new StepResponse(priced_rooms);
    }

    // Create a map of variant ID to tax lines for quick lookup
    const taxLinesByVariantId = new Map();
    for (const taxLine of allTaxLines) {
      if ("line_item_id" in taxLine) {
        if (!taxLinesByVariantId.has(taxLine.line_item_id)) {
          taxLinesByVariantId.set(taxLine.line_item_id, []);
        }
        taxLinesByVariantId.get(taxLine.line_item_id).push(taxLine);
      }
    }

    // Process each room configuration and its variants
    const roomsWithTax = [];

    for (const config of priced_rooms) {
      const variantsWithTax = [];
      const unavailableVariants = config.unavailable_variants || [];

      // Process each variant with price information
      for (const variant of config.priced_variants || []) {
        try {
          // Skip if no price information
          if (!variant.price) {
            variantsWithTax.push(variant);
            continue;
          }

          // Get tax lines for this variant from our map
          const itemTaxLines = taxLinesByVariantId.get(variant.id) || [];

          // Calculate tax rate once
          const taxRate = itemTaxLines.reduce((sum: number, line: any) => sum + line.rate, 0);

          // Apply tax to each meal plan price
          const mealPlansWithTax = {};

          // Process each meal plan
          for (const [mealPlanKey, mealPlanPrice] of Object.entries(variant.price.meal_plans || {})) {
            // Calculate tax for this meal plan
            const mealPlanAmount = (mealPlanPrice as any).total_amount;
            const mealPlanTaxAmount = (mealPlanAmount * taxRate) / 100;
            const mealPlanTotalWithTax = mealPlanAmount + mealPlanTaxAmount;

            // Calculate per night amounts
            const perNightWithTax = mealPlanTotalWithTax / variant.price.nights;
            const perNightWithoutTax = mealPlanAmount / variant.price.nights;

            // Create the meal plan price with tax information
            mealPlansWithTax[mealPlanKey] = {
              ...(mealPlanPrice as any),
              amount_with_tax: Number(perNightWithTax.toFixed(2)),
              amount_without_tax: Number(perNightWithoutTax.toFixed(2)),
              total_amount_with_tax: Number(mealPlanTotalWithTax.toFixed(2)),
              total_amount_without_tax: Number(mealPlanAmount.toFixed(2)),
              tax_lines: itemTaxLines,
              tax_rate: taxRate,
              tax_amount: Number(mealPlanTaxAmount.toFixed(2)),
              includes_tax: false,
            };
          }

          // Calculate tax for the main price
          const mainAmount = variant.price.amount;
          const mainTaxAmount = (mainAmount * taxRate) / 100;
          const mainAmountWithTax = mainAmount + mainTaxAmount;

          // Calculate tax for the total amount
          const totalAmount = variant.price.total_amount;
          const totalTaxAmount = (totalAmount * taxRate) / 100;
          const totalAmountWithTax = totalAmount + totalTaxAmount;

          // Create a new price object with tax information
          const priceWithTax = {
            ...variant.price,
            meal_plans: mealPlansWithTax,
            amount_with_tax: Number(mainAmountWithTax.toFixed(2)),
            amount_without_tax: Number(mainAmount.toFixed(2)),
            total_amount_with_tax: Number(totalAmountWithTax.toFixed(2)),
            total_amount_without_tax: Number(totalAmount.toFixed(2)),
            tax_lines: itemTaxLines,
            tax_rate: taxRate,
            tax_amount: Number(totalTaxAmount.toFixed(2)),
            includes_tax: false,
          };

          // Create a new price per night object with tax information
          const pricePerNightWithTax = {
            ...variant.price_per_night,
            amount_with_tax: Number(mainAmountWithTax.toFixed(2)),
            amount_without_tax: Number(mainAmount.toFixed(2)),
            formatted_with_tax: `${currency_code} ${mainAmountWithTax.toFixed(2)}`,
            tax_rate: taxRate,
          };

          // Add the variant with tax information
          variantsWithTax.push({
            ...variant,
            price: priceWithTax,
            price_per_night: pricePerNightWithTax,
          });
        } catch (error) {
          console.error(`Error calculating taxes for variant ${variant.id}:`, error);
          // Add the variant without tax information
          variantsWithTax.push(variant);
        }
      }

      // Add the room configuration with its variants with tax information
      roomsWithTax.push({
        ...config,
        priced_variants: variantsWithTax,
        unavailable_variants: unavailableVariants,
      });
    }

    console.log(`Tax calculation completed in ${Date.now() - startTime}ms`);
    return new StepResponse(roomsWithTax);
  }
);
