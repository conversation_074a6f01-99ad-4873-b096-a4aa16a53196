import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";

type FormatResponseInput = {
  hotel: any;
  priced_rooms: any[];
  include_unavailable: boolean;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  currency_code: string;
  nights: number;
};

export const formatResponseStep = createStep(
  "format-response",
  async (input: FormatResponseInput) => {
    const {
      hotel,
      priced_rooms,
      include_unavailable,
      check_in_date,
      check_out_date,
      adults,
      children,
      infants,
      currency_code,
      nights,
    } = input;

    // Ensure dates are Date objects
    const checkInDate =
      check_in_date instanceof Date ? check_in_date : new Date(check_in_date);
    const checkOutDate =
      check_out_date instanceof Date
        ? check_out_date
        : new Date(check_out_date);

    // Group rooms by room configuration
    const roomsByConfig = {};

    // First, group all priced variants by their room configuration
    for (const config of priced_rooms) {
      if (!roomsByConfig[config.id]) {
        roomsByConfig[config.id] = {
          config,
          variants: [],
        };
      }

      // Add all priced variants to this config
      for (const variant of config.priced_variants || []) {
        roomsByConfig[config.id].variants.push({
          ...variant,
          available: true,
        });
      }

      // Add all unavailable variants if requested
      if (include_unavailable) {
        for (const variant of config.unavailable_variants || []) {
          roomsByConfig[config.id].variants.push({
            ...variant,
            available: false,
          });
        }
      }
    }

    // Format the available rooms by room configuration (like the existing API)
    const availableRooms = [];

    for (const configId in roomsByConfig) {
      const { config, variants } = roomsByConfig[configId];

      // Skip if no variants are available and we're not including unavailable rooms
      if (
        variants.length === 0 ||
        (!include_unavailable && !variants.some((v: any) => v.available))
      ) {
        continue;
      }

      // Get the first variant's price to use as the base price
      const availableVariants = variants.filter((v: any) => v.available);
      const firstAvailableVariant =
        availableVariants.length > 0 ? availableVariants[0] : null;

      // Get the meal plan prices from the variant's price object
      // These prices were calculated in the calculate-prices.ts step
      const mealPlans = firstAvailableVariant?.price?.meal_plans || {};

      // If no meal plans were calculated, create a default one
      if (Object.keys(mealPlans).length === 0) {
        const pricePerNight = firstAvailableVariant?.price_per_night?.amount
          ? Number(firstAvailableVariant.price_per_night.amount) / 100
          : 0;
        const totalPrice = firstAvailableVariant?.price?.amount
          ? Number(firstAvailableVariant.price.amount) / 100
          : 0;

        mealPlans.none = {
          amount: pricePerNight,
          original_amount: pricePerNight,
          currency_code,
          total_amount: totalPrice,
          per_night_amount: pricePerNight,
          nights,
        };
      }

      console.log(
        `Using meal plan prices from calculate-prices step:`,
        JSON.stringify(mealPlans)
      );

      // Check if all meal plans have 0 pricing - if so, skip this room configuration
      const hasValidPricing = Object.values(mealPlans).some(
        (mealPlan: any) =>
          mealPlan.amount > 0 ||
          mealPlan.total_amount > 0 ||
          mealPlan.per_night_amount > 0
      );

      if (!hasValidPricing) {
        console.log(
          `Skipping room config ${config.id} - all meal plans have 0 pricing`
        );
        continue;
      }

      // Format the room configuration
      availableRooms.push({
        id: config.id,
        title: config.title,
        description: config.description || "",
        thumbnail: config.thumbnail,
        handle: config.handle,
        room_size: config.metadata?.room_size,
        bed_type: config.metadata?.bed_type,
        max_adults: config.metadata?.max_adults,
        max_children: config.metadata?.max_children,
        max_infants: config.metadata?.max_infants,
        amenities: config.metadata?.amenities || [],
        available_rooms: availableVariants.length,
        price: {
          amount: mealPlans.none.amount,
          original_amount: mealPlans.none.original_amount,
          currency_code,
          total_amount: mealPlans.none.total_amount,
          per_night_amount: mealPlans.none.per_night_amount,
          nights,
          meal_plans: mealPlans,
          selected_meal_plan: "none",
        },
        available: availableVariants.length > 0,
      });
    }

    // For the admin API, we might still want to show individual rooms
    // This is different from the store API which groups by room configuration
    const individualRooms = [];

    if (process.env.NEXT_PUBLIC_SHOW_INDIVIDUAL_ROOMS === "true") {
      for (const config of priced_rooms) {
        // Format each priced variant
        for (const variant of config.priced_variants) {
          // Get the meal plan prices from the variant's price object
          // These prices were calculated in the calculate-prices.ts step
          const roomMealPlans = variant.price?.meal_plans || {};

          // If no meal plans were calculated, create a default one
          if (Object.keys(roomMealPlans).length === 0) {
            const basePricePerNight = variant.price_per_night?.amount
              ? Number(variant.price_per_night.amount) / 100
              : 0;
            const totalPrice = variant.price?.amount
              ? Number(variant.price.amount) / 100
              : 0;

            roomMealPlans.none = {
              amount: basePricePerNight,
              original_amount: basePricePerNight,
              currency_code,
              total_amount: totalPrice,
              per_night_amount: basePricePerNight,
              nights,
            };
          }

          console.log(
            `Using meal plan prices for individual room ${variant.id}:`,
            JSON.stringify(roomMealPlans)
          );

          // Check if all meal plans have 0 pricing - if so, skip this individual room
          const hasValidIndividualPricing = Object.values(roomMealPlans).some(
            (mealPlan: any) =>
              mealPlan.amount > 0 ||
              mealPlan.total_amount > 0 ||
              mealPlan.per_night_amount > 0
          );

          if (!hasValidIndividualPricing) {
            console.log(
              `Skipping individual room ${variant.id} - all meal plans have 0 pricing`
            );
            continue;
          }

          individualRooms.push({
            id: variant.id,
            title: variant.title,
            room_config_id: config.id,
            room_config_title: config.title,
            description: config.description,
            thumbnail: config.thumbnail,
            available: true,
            price: {
              amount: roomMealPlans.none.amount,
              original_amount: roomMealPlans.none.original_amount,
              currency_code,
              total_amount: roomMealPlans.none.total_amount,
              per_night_amount: roomMealPlans.none.per_night_amount,
              formatted: `${currency_code} ${roomMealPlans.none.amount.toFixed(
                2
              )}`,
            },
            price_per_night: {
              amount: roomMealPlans.none.per_night_amount,
              currency_code,
              formatted: `${currency_code} ${roomMealPlans.none.per_night_amount.toFixed(
                2
              )}`,
            },
            meal_plans: roomMealPlans,
            metadata: {
              ...variant.metadata,
              ...config.metadata,
            },
          });
        }

        // Format each unavailable variant if requested
        if (include_unavailable) {
          for (const variant of config.unavailable_variants || []) {
            individualRooms.push({
              id: variant.id,
              title: variant.title,
              room_config_id: config.id,
              room_config_title: config.title,
              description: config.description,
              thumbnail: config.thumbnail,
              available: false,
              unavailable_reasons:
                variant.availability?.unavailableReasons || [],
              metadata: {
                ...variant.metadata,
                ...config.metadata,
              },
            });
          }
        }
      }
    }

    // Use the appropriate rooms array based on the environment
    const allRooms =
      process.env.NEXT_PUBLIC_SHOW_INDIVIDUAL_ROOMS === "true"
        ? individualRooms
        : availableRooms;

    // Format the final response
    const response = {
      hotel: {
        id: hotel.id,
        name: hotel.title,
        handle: hotel.handle,
        category_id: hotel.category_id,
        description: hotel.description || "",
        rating: hotel.metadata?.rating || null,
        address: hotel.metadata?.address || "",
        amenities: hotel.metadata?.amenities || [],
      },
      check_in: checkInDate.toISOString().split("T")[0],
      check_out: checkOutDate.toISOString().split("T")[0],
      nights,
      adults,
      children,
      infants,
      currency_code,
      available_rooms: allRooms,
    };

    console.log(
      "Final response hotel details:",
      JSON.stringify(response.hotel)
    );

    return new StepResponse(response);
  }
);
