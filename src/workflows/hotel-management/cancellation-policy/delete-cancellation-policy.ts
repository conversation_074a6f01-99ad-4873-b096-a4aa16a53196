import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CANCELLATION_POLICY_SERVICE } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type DeleteCancellationPolicyStepInput = {
  id: string;
};

type DeleteCancellationPolicyWorkflowInput = DeleteCancellationPolicyStepInput;

export const deleteCancellationPolicyStep = createStep(
  "delete-cancellation-policy-step",
  async (input: DeleteCancellationPolicyStepInput, { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);

    try {
      // Get the policy - this will throw an error if not found
      const policy = await cancellationPolicyService.getCancellationPolicy(input.id);

      // Delete the policy
      await cancellationPolicyService.deleteCancellationPolicy(input.id);

      return new StepResponse({
        id: input.id,
        deleted: true,
        policy_data: {
          name: policy.name,
          hotel_id: policy.hotel_id,
          days_before_checkin: policy.days_before_checkin
        }
      });
    } catch (error) {
      console.error(`Error deleting cancellation policy: ${error.message}`);
      throw error;
    }
  }
);

export const DeleteCancellationPolicyWorkflow = createWorkflow(
  "delete-cancellation-policy",
  (input: DeleteCancellationPolicyWorkflowInput) => {
    // Delete the cancellation policy
    const result = deleteCancellationPolicyStep(input);

    // Emit an event for the deleted policy
    emitEventStep({
      eventName: "cancellation-policy.deleted",
      data: {
        id: input.id,
        name: result.policy_data.name,
        hotel_id: result.policy_data.hotel_id,
      },
    });

    return new WorkflowResponse({ id: input.id, deleted: true });
  }
);
