import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CANCELLATION_POLICY_SERVICE, RefundType } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateCancellationPolicyStepInput = {
  id?: string;
  name: string;
  description?: string | null;
  hotel_id: string;
  days_before_checkin: number;
  refund_type: RefundType;
  refund_amount: number;
  is_active?: boolean;
  metadata?: Record<string, any> | null;
};

type CreateCancellationPolicyWorkflowInput = CreateCancellationPolicyStepInput;

export const createCancellationPolicyStep = createStep(
  "create-cancellation-policy-step",
  async (input: CreateCancellationPolicyStepInput, { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);

    const policy = await cancellationPolicyService.createCancellationPolicy({
      id: input.id, // Pass the ID if provided
      name: input.name,
      description: input.description,
      hotel_id: input.hotel_id,
      days_before_checkin: input.days_before_checkin,
      refund_type: input.refund_type,
      refund_amount: input.refund_amount,
      is_active: input.is_active !== undefined ? input.is_active : true,
      metadata: input.metadata,
    });

    return new StepResponse(policy, policy.id);
  },
  async (ids: string[], { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);

    for (const id of ids) {
      await cancellationPolicyService.deleteCancellationPolicy(id);
    }
  }
);

export const CreateCancellationPolicyWorkflow = createWorkflow(
  "create-cancellation-policy",
  (input: CreateCancellationPolicyWorkflowInput) => {
    // Create the cancellation policy
    const policy = createCancellationPolicyStep(input);

    // Emit an event for the created policy
    emitEventStep({
      eventName: "cancellation-policy.created",
      data: {
        id: policy.id,
        name: input.name,
        hotel_id: input.hotel_id,
        days_before_checkin: input.days_before_checkin,
        refund_type: input.refund_type,
        refund_amount: input.refund_amount,
        is_active: input.is_active !== undefined ? input.is_active : true,
      },
    });

    return new WorkflowResponse(policy);
  }
);
