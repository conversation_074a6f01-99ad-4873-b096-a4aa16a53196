import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CANCELLATION_POLICY_SERVICE, RefundType } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type UpdateCancellationPolicyStepInput = {
  id: string;
  name?: string;
  description?: string | null;
  days_before_checkin?: number;
  refund_type?: RefundType;
  refund_amount?: number;
  is_active?: boolean;
  metadata?: Record<string, any> | null;
};

type UpdateCancellationPolicyWorkflowInput = UpdateCancellationPolicyStepInput;

export const updateCancellationPolicyStep = createStep(
  "update-cancellation-policy-step",
  async (input: UpdateCancellationPolicyStepInput, { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);

    try {
      // Get the policy - this will throw an error if not found
      const originalPolicy = await cancellationPolicyService.getCancellationPolicy(input.id);

      // Create a clean update object with only the fields we want to update
      const updateData: any = {};

      // Only include fields that are explicitly provided
      if (input.name !== undefined) updateData.name = input.name;
      if (input.description !== undefined) updateData.description = input.description;
      if (input.days_before_checkin !== undefined) updateData.days_before_checkin = input.days_before_checkin;
      if (input.refund_type !== undefined) updateData.refund_type = input.refund_type;
      if (input.refund_amount !== undefined) updateData.refund_amount = input.refund_amount;
      if (input.is_active !== undefined) updateData.is_active = input.is_active;
      if (input.metadata !== undefined) updateData.metadata = input.metadata;

      // Update the policy
      const policy = await cancellationPolicyService.updateCancellationPolicy(
        input.id,
        updateData
      );

      console.log('Policy updated successfully:', policy);

      // Return both the updated policy and the original for comparison
      return new StepResponse({
        policy,
        originalPolicy
      });
    } catch (error) {
      console.error(`Error updating cancellation policy: ${error.message}`);
      throw error;
    }
  }
);

export const UpdateCancellationPolicyWorkflow = createWorkflow(
  "update-cancellation-policy",
  (input: UpdateCancellationPolicyWorkflowInput) => {
    // Update the cancellation policy
    const result = updateCancellationPolicyStep(input);

    // Track changes for the event
    const changes = [];

    // Check for active status change
    if (input.is_active !== undefined &&
        result.originalPolicy.is_active !== input.is_active) {
      changes.push({
        type: "active_status",
        old_value: result.originalPolicy.is_active,
        new_value: input.is_active
      });
    }

    // Check for refund type change
    if (input.refund_type !== undefined &&
        result.originalPolicy.refund_type !== input.refund_type) {
      changes.push({
        type: "refund_type",
        old_value: result.originalPolicy.refund_type,
        new_value: input.refund_type
      });
    }

    // Check for refund amount change
    if (input.refund_amount !== undefined &&
        result.originalPolicy.refund_amount !== input.refund_amount) {
      changes.push({
        type: "refund_amount",
        old_value: result.originalPolicy.refund_amount,
        new_value: input.refund_amount
      });
    }

    // Check for days before checkin change
    if (input.days_before_checkin !== undefined &&
        result.originalPolicy.days_before_checkin !== input.days_before_checkin) {
      changes.push({
        type: "days_before_checkin",
        old_value: result.originalPolicy.days_before_checkin,
        new_value: input.days_before_checkin
      });
    }

    // Only emit event if there are changes
    if (changes.length > 0) {
      emitEventStep({
        eventName: "cancellation-policy.updated",
        data: {
          id: result.policy.id,
          name: result.policy.name,
          hotel_id: result.policy.hotel_id,
          changes: changes
        },
      });
    }

    return new WorkflowResponse(result.policy);
  }
);
