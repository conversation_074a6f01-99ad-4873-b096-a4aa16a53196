import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CANCELLATION_POLICY_SERVICE } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";

export type GetCancellationPolicyStepInput = {
  id: string;
};

type GetCancellationPolicyWorkflowInput = GetCancellationPolicyStepInput;

export const getCancellationPolicyStep = createStep(
  "get-cancellation-policy-step",
  async (input: GetCancellationPolicyStepInput, { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);
    
    // Get the policy
    const policy = await cancellationPolicyService.getCancellationPolicy(input.id);
    
    return new StepResponse(policy);
  }
);

export const GetCancellationPolicyWorkflow = createWorkflow(
  "get-cancellation-policy",
  (input: GetCancellationPolicyWorkflowInput) => {
    // Get the cancellation policy
    const policy = getCancellationPolicyStep(input);
    
    return new WorkflowResponse(policy);
  }
);
