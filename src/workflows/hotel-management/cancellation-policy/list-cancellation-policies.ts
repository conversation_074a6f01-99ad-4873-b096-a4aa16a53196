import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CANCELLATION_POLICY_SERVICE } from "src/modules/hotel-management/cancellation-policy";
import CancellationPolicyService from "src/modules/hotel-management/cancellation-policy/service";

export type ListCancellationPoliciesStepInput = {
  hotel_id?: string;
  is_active?: boolean;
};

type ListCancellationPoliciesWorkflowInput = ListCancellationPoliciesStepInput;

export const listCancellationPoliciesStep = createStep(
  "list-cancellation-policies-step",
  async (input: ListCancellationPoliciesStepInput, { container }) => {
    const cancellationPolicyService: CancellationPolicyService =
      container.resolve(CANCELLATION_POLICY_SERVICE);
    
    // Build the selector
    const selector: any = {};
    
    if (input.hotel_id) {
      selector.hotel_id = input.hotel_id;
    }
    
    if (input.is_active !== undefined) {
      selector.is_active = input.is_active;
    }
    
    // Get the policies
    const policies = await cancellationPolicyService.findCancellationPolicies(selector);
    
    return new StepResponse(policies);
  }
);

export const ListCancellationPoliciesWorkflow = createWorkflow(
  "list-cancellation-policies",
  (input: ListCancellationPoliciesWorkflowInput) => {
    // List the cancellation policies
    const policies = listCancellationPoliciesStep(input);
    
    return new WorkflowResponse(policies);
  }
);
