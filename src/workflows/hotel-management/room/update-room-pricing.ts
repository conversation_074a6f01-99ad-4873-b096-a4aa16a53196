import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import { IProductModuleService, IPricingModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export type UpdateRoomPricingStepInput = {
  // Room identifier (product variant ID)
  room_id: string;
  
  // Base pricing
  base_price: number;
  currency_code: string;
  
  // Optional date range for seasonal pricing
  start_date?: string | Date;
  end_date?: string | Date;
  
  // Optional conditions
  min_nights?: number;
  max_nights?: number;
  
  // Optional customer segment
  customer_group_id?: string;
};

type UpdateRoomPricingWorkflowInput = UpdateRoomPricingStepInput;

export const updateRoomPricingStep = createStep(
  "update-room-pricing-step",
  async (input: UpdateRoomPricingStepInput, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const pricingModuleService: IPricingModuleService = container.resolve(
      Modules.PRICING
    );
    
    // First, retrieve the room (product variant)
    const variant = await productModuleService.retrieveProductVariant(input.room_id);
    
    if (!variant) {
      throw new Error(`Room with ID ${input.room_id} not found`);
    }
    
    // Create or update the base price
    let moneyAmount;
    
    // Check if a base price already exists
    const existingPrices = await productModuleService.listMoneyAmounts({
      variant_id: input.room_id,
      currency_code: input.currency_code,
      price_list_id: null, // Base price has no price list
    });
    
    if (existingPrices.length > 0) {
      // Update existing base price
      moneyAmount = await productModuleService.updateMoneyAmounts({
        id: existingPrices[0].id,
        amount: input.base_price,
      });
    } else {
      // Create new base price
      moneyAmount = await productModuleService.createMoneyAmounts({
        variant_id: input.room_id,
        currency_code: input.currency_code,
        amount: input.base_price,
      });
    }
    
    // If date range is provided, create a seasonal price
    if (input.start_date && input.end_date) {
      // Create a price list for the seasonal pricing
      const priceListName = `Room ${variant.title} - ${new Date(input.start_date).toLocaleDateString()} to ${new Date(input.end_date).toLocaleDateString()}`;
      
      // Check if a price list already exists for this date range
      const query = container.resolve("query");
      const { data: existingPriceLists } = await query.graph({
        entity: "price_list",
        filters: {
          name: priceListName,
        },
        fields: ["id"],
      });
      
      let priceList;
      
      if (existingPriceLists && existingPriceLists.length > 0) {
        // Update existing price list
        priceList = await pricingModuleService.updatePriceLists({
          id: existingPriceLists[0].id,
          starts_at: new Date(input.start_date),
          ends_at: new Date(input.end_date),
          customer_groups: input.customer_group_id ? [{ id: input.customer_group_id }] : undefined,
        });
      } else {
        // Create a new price list
        priceList = await pricingModuleService.createPriceLists({
          name: priceListName,
          description: `Seasonal pricing for ${variant.title}`,
          type: "sale",
          status: "active",
          starts_at: new Date(input.start_date),
          ends_at: new Date(input.end_date),
          customer_groups: input.customer_group_id ? [{ id: input.customer_group_id }] : undefined,
        });
      }
      
      // Create price list items (the actual prices)
      const priceListItem = await pricingModuleService.addPriceListPrices({
        price_list_id: priceList.id,
        prices: [
          {
            variant_id: input.room_id,
            currency_code: input.currency_code,
            amount: input.base_price,
            min_quantity: input.min_nights,
            max_quantity: input.max_nights,
          },
        ],
      });
      
      return new StepResponse({
        room_id: input.room_id,
        base_price: {
          id: moneyAmount.id,
          amount: moneyAmount.amount,
          currency_code: moneyAmount.currency_code,
        },
        seasonal_price: {
          price_list_id: priceList.id,
          price_list_item_id: priceListItem[0].id,
          start_date: input.start_date,
          end_date: input.end_date,
          amount: input.base_price,
          currency_code: input.currency_code,
        },
      });
    }
    
    // If no date range, just return the base price
    return new StepResponse({
      room_id: input.room_id,
      base_price: {
        id: moneyAmount.id,
        amount: moneyAmount.amount,
        currency_code: moneyAmount.currency_code,
      },
    });
  }
);

export const UpdateRoomPricingWorkflow = createWorkflow(
  "update-room-pricing",
  (input: UpdateRoomPricingWorkflowInput) => {
    const result = updateRoomPricingStep(input);
    return new WorkflowResponse(result);
  }
);
