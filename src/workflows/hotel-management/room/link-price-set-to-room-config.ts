import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { IProductModuleService, IPricingModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

type LinkPriceSetToRoomConfigInput = {
  room_config_id: string; // Product ID representing the room configuration
  currency_code: string;
  base_price?: number; // Optional base price to create
};

export const linkPriceSetToRoomConfigStep = createStep(
  "link-price-set-to-room-config",
  async (input: LinkPriceSetToRoomConfigInput, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(Modules.PRODUCT);
    const pricingModuleService: IPricingModuleService = container.resolve(Modules.PRICING);

    const { room_config_id, currency_code, base_price } = input;

    console.log(`Linking price set to room configuration: ${room_config_id}`);

    // 1. Get the room configuration product
    const product = await productModuleService.retrieveProduct(room_config_id);
    
    if (!product) {
      throw new Error(`Room configuration with ID ${room_config_id} not found`);
    }

    // 2. Check if price_set_id already exists in metadata
    if (product.metadata?.price_set_id) {
      console.log(`Price set already linked to room config ${room_config_id}: ${product.metadata.price_set_id}`);
      return new StepResponse({
        room_config_id,
        price_set_id: product.metadata.price_set_id,
        message: "Price set already linked"
      });
    }

    // 3. Create a price set for this room configuration
    const prices = [];
    
    if (base_price) {
      prices.push({
        amount: base_price,
        currency_code: currency_code,
        rules: {}
      });
    } else {
      // Create a default price if none provided
      prices.push({
        amount: 10000, // $100.00 default
        currency_code: currency_code,
        rules: {}
      });
    }

    const priceSet = await pricingModuleService.createPriceSets({
      prices: prices,
    });

    console.log(`Created price set: ${priceSet.id} for room config: ${room_config_id}`);

    // 4. Update the room configuration metadata to include the price_set_id
    const updatedMetadata = {
      ...product.metadata,
      price_set_id: priceSet.id,
    };

    await productModuleService.updateProducts(room_config_id, {
      metadata: updatedMetadata,
    });

    console.log(`Updated room config ${room_config_id} metadata with price_set_id: ${priceSet.id}`);

    return new StepResponse({
      room_config_id,
      price_set_id: priceSet.id,
      message: "Price set created and linked successfully"
    });
  }
);

export const linkPriceSetToRoomConfigWorkflow = createWorkflow(
  "link-price-set-to-room-config",
  (input: LinkPriceSetToRoomConfigInput) => {
    const result = linkPriceSetToRoomConfigStep(input);
    return new WorkflowResponse(result);
  }
);

export default linkPriceSetToRoomConfigWorkflow;
