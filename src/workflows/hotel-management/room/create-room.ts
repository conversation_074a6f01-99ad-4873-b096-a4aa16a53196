import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../../../modules/hotel-management/room-inventory";

export type CreateRoomStepInput = {
  // Room details
  name: string;
  room_number: string;
  status: "available" | "occupied" | "maintenance" | "cleaning";
  floor: string;
  notes?: string;
  is_active: boolean;

  // Room relationships
  left_room?: string;
  opposite_room?: string;
  connected_room?: string;
  right_room?: string;

  // References
  room_config_id: string;
  hotel_id: string;

  // Optional pricing
  price?: number;
  currency_code?: string;
};

type CreateRoomWorkflowInput = CreateRoomStepInput;

export const createRoomStep = createStep(
  "create-room-step",
  async (input: CreateRoomStepInput, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const roomInventoryService = container.resolve(
      ROOM_INVENTORY_MODULE
    );

    // First, get the room configuration to use its details
    const query = container.resolve("query");
    const { data: roomConfigs } = await query.graph({
      entity: "room_config",
      filters: { id: [input.room_config_id] },
      fields: ["*"],
    });

    if (!roomConfigs || roomConfigs.length === 0) {
      throw new Error(`Room configuration with ID ${input.room_config_id} not found`);
    }

    const roomConfig = roomConfigs[0];

    // Check if a product already exists for this room configuration
    const { data: existingProducts } = await query.graph({
      entity: "product",
      filters: { metadata: { room_config_id: input.room_config_id } },
      fields: ["id", "title", "metadata"],
    });

    let productId;

    // If no product exists for this room configuration, create one
    if (!existingProducts || existingProducts.length === 0) {
      // Create a product for the room configuration
      const product = await productModuleService.createProducts({
        title: roomConfig.name,
        subtitle: roomConfig.type,
        description: roomConfig.description || "",
        is_giftcard: false,
        discountable: true,
        status: "published",
        categories: [input.hotel_id], // Use hotel_id as category
        metadata: {
          room_config_id: input.room_config_id,
          room_size: roomConfig.room_size,
          amenities: roomConfig.amenities,
          bed_type: roomConfig.bed_type,
          max_extra_beds: roomConfig.max_extra_beds,
          max_adults: roomConfig.max_adults,
          max_children: roomConfig.max_children,
          max_infants: roomConfig.max_infants,
          max_occupancy: roomConfig.max_occupancy,
        },
      });

      productId = product.id;
    } else {
      productId = existingProducts[0].id;
    }

    // Create a product variant for the specific room
    const variant = await productModuleService.createProductVariants({
      title: `Room ${input.room_number}`,
      product_id: productId,
      inventory_quantity: input.status === "available" && input.is_active ? 1 : 0,
      manage_inventory: true,
      allow_backorder: false,
      metadata: {
        room_number: input.room_number,
        floor: input.floor,
        notes: input.notes,
        status: input.status,
        is_active: input.is_active,
        left_room: input.left_room,
        opposite_room: input.opposite_room,
        connected_room: input.connected_room,
        right_room: input.right_room,
      },
    });

    // If price is provided, create a price for the variant
    if (input.price && input.currency_code) {
      await productModuleService.createMoneyAmounts({
        variant_id: variant.id,
        amount: input.price,
        currency_code: input.currency_code,
      });
    }

    // Create room inventory entries for the next year
    const today = new Date();
    const nextYear = new Date(today);
    nextYear.setFullYear(today.getFullYear() + 1);

    await roomInventoryService.createRoomInventories([
      {
        inventory_item_id: variant.id,
        from_date: today,
        to_date: nextYear,
        status: "available",
        available_quantity: input.status === "available" && input.is_active ? 1 : 0,
        check_in_time: "14:00",
        check_out_time: "12:00",
        is_noon_to_noon: true,
        metadata: {
          room_number: input.room_number,
          floor: input.floor,
          hotel_id: input.hotel_id,
        }
      }
    ]);

    // Return the created room (variant) with additional information
    return new StepResponse({
      id: variant.id,
      name: input.name,
      room_number: input.room_number,
      status: input.status,
      floor: input.floor,
      notes: input.notes,
      is_active: input.is_active,
      left_room: input.left_room,
      opposite_room: input.opposite_room,
      connected_room: input.connected_room,
      right_room: input.right_room,
      room_config_id: input.room_config_id,
      hotel_id: input.hotel_id,
      product_id: productId,
      variant_id: variant.id,
      inventory_item_id: variant.id, // Use variant ID as inventory item ID
    });
  }
);

export const CreateRoomWorkflow = createWorkflow(
  "create-room",
  (input: CreateRoomWorkflowInput) => {
    const room = createRoomStep(input);
    return new WorkflowResponse(room);
  }
);
