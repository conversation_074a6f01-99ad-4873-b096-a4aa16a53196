import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "src/modules/hotel-management/room-inventory";
import RoomInventoryModuleService from "src/modules/hotel-management/room-inventory/service";
import { updateSharedRoomInventory } from "src/modules/hotel-management/room-inventory/utils/shared-room-inventory";
import { parseISO, eachDayOfInterval, format } from "date-fns";

function getNextDate(dateStr: string): string {
  const [year, month, day] = dateStr.split("-").map(Number);
  const date = new Date(year, month - 1, day + 1); // Safe: avoids UTC shift
  const nextYear = date.getFullYear();
  const nextMonth = String(date.getMonth() + 1).padStart(2, "0");
  const nextDay = String(date.getDate()).padStart(2, "0");
  return `${nextYear}-${nextMonth}-${nextDay}`;
}

export type CreateOrUpdateRoomInventoryStepInput = {
  inventory_item_id: string;
  from_date: string | Date;
  to_date: string | Date;
  is_available?: boolean;
  available_quantity?: number;
  status?: string;
  notes?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_noon_to_noon?: boolean;
};

type CreateOrUpdateRoomInventoryWorkflowInput =
  CreateOrUpdateRoomInventoryStepInput;

export const createOrUpdateRoomInventoryStep = createStep(
  "create-or-update-room-inventory-step",
  async (input: CreateOrUpdateRoomInventoryStepInput, { container }) => {
    const roomInventoryModuleService: RoomInventoryModuleService =
      container.resolve(ROOM_INVENTORY_MODULE);

    // Convert string dates to Date objects and ensure they're properly formatted
    const fromDate =
      typeof input.from_date === "string"
        ? parseISO(input.from_date)
        : new Date(input.from_date);
    const toDate =
      typeof input.to_date === "string"
        ? parseISO(input.to_date)
        : new Date(input.to_date);

    // Fetch existing records using a date range filter
    let existingRecords = [];
    try {
      existingRecords = await roomInventoryModuleService.listRoomInventories({
        inventory_item_id: input.inventory_item_id,
        from_date: { $gte: fromDate, $lte: toDate },
      });
      console.log(
        `Found ${existingRecords.length} existing inventory records for room ${input.inventory_item_id}`
      );
    } catch (error) {
      console.error(
        `Error fetching existing inventory records: ${error.message}`
      );
      // Continue with empty records if there's an error
      existingRecords = [];
    }

    // Convert existing records into a Map for quick lookup using date string as key
    const existingRecordsMap = new Map(
      existingRecords.map((record) => [
        format(new Date(record.from_date), "yyyy-MM-dd"),
        record,
      ])
    );

    const inventoryRecordsToUpdate = [];
    const inventoryRecordsToCreate = [];

    // Generate an array of dates between from_date and to_date (inclusive)
    // For a stay from 7th to 9th, we need entries for 7th and 8th (the 9th is checkout day)

    // Create a formatted version of the dates for logging
    const fromDateStr = format(fromDate, "yyyy-MM-dd");
    const toDateStr = format(toDate, "yyyy-MM-dd");

    // Create the adjusted end date (one day before checkout)
    const adjustedEndDate = new Date(toDate);
    adjustedEndDate.setDate(adjustedEndDate.getDate() - 1);

    // Generate the date range
    const dateRange = eachDayOfInterval({
      start: fromDate,
      end: adjustedEndDate,
    }).map((date) => format(date, "yyyy-MM-dd"));

    const isAvailable = input.is_available ?? true;

    // Create base record data with all optional fields
    const baseRecordData = {
      available_quantity:
        input.available_quantity !== undefined
          ? input.available_quantity
          : isAvailable
          ? 1
          : 0,
      status: input.status || (isAvailable ? "available" : "booked"),
      ...(input.notes !== undefined && { notes: input.notes }),
      ...(input.check_in_time !== undefined && {
        check_in_time: input.check_in_time,
      }),
      ...(input.check_out_time !== undefined && {
        check_out_time: input.check_out_time,
      }),
      ...(input.is_noon_to_noon !== undefined && {
        is_noon_to_noon: input.is_noon_to_noon,
      }),
    };

    // Process each date in the range
    dateRange.forEach((dateStr) => {
      const nextDay = getNextDate(dateStr);

      // Use the dateStr directly for map lookup since we formatted the dates when creating the map
      if (existingRecordsMap.has(dateStr)) {
        // Update existing record
        inventoryRecordsToUpdate.push({
          id: existingRecordsMap.get(dateStr).id,
          from_date: dateStr,
          to_date: nextDay,
          ...baseRecordData,
        });
      } else {
        // Create new record
        inventoryRecordsToCreate.push({
          inventory_item_id: input.inventory_item_id,
          from_date: dateStr,
          to_date: nextDay,
          ...baseRecordData,
        });
      }
    });

    // Perform batch update and create operations
    let updatedRecords = [];
    let createdRecords = [];

    // Update existing records
    if (inventoryRecordsToUpdate.length > 0) {
      try {
        console.log(
          `Updating ${inventoryRecordsToUpdate.length} inventory records`
        );
        updatedRecords = await roomInventoryModuleService.updateRoomInventories(
          inventoryRecordsToUpdate
        );
        console.log(
          `Successfully updated ${updatedRecords.length} inventory records`
        );
      } catch (error) {
        console.error(`Error updating inventory records: ${error.message}`);
        // Continue with empty updated records
        updatedRecords = [];
      }
    }

    console.log({ inventoryRecordsToCreate });

    // Create new records
    if (inventoryRecordsToCreate.length > 0) {
      try {
        console.log(
          `Creating ${inventoryRecordsToCreate.length} inventory records`
        );
        createdRecords = await roomInventoryModuleService.createRoomInventories(
          inventoryRecordsToCreate
        );
        console.log(
          `Successfully created ${createdRecords.length} inventory records`
        );
      } catch (error) {
        console.error(`Error creating inventory records: ${error.message}`);
        // Continue with empty created records
        createdRecords = [];
      }
    }

    // Combine records for StepResponse
    const combinedRecords = [...updatedRecords, ...createdRecords];

    // Always update shared room inventory if we have an inventory_item_id, regardless of whether records were created
    if (input.inventory_item_id) {
      try {
        console.log(`Updating shared room inventory for room ${input.inventory_item_id}`);
        console.log(`Date range: ${fromDateStr} to ${toDateStr}`);
        console.log(`Status: ${baseRecordData.status}`);
        console.log(`Notes: ${baseRecordData.notes || 'none'}`);

        // Get the product service to check if this room has a room number
        try {
          const productModuleService = container.resolve(Modules.PRODUCT);
          const variant = await productModuleService.retrieveProductVariant(input.inventory_item_id);
          console.log(`Room variant details:`, variant);

          if (variant && variant.metadata && variant.metadata.room_number) {
            console.log(`Room number found: ${variant.metadata.room_number}`);

            // If we have a room number, we should update shared rooms
            // Use the utility function to update shared room inventory
            console.log(`Calling updateSharedRoomInventory with createIfNotExists=true`);
            await updateSharedRoomInventory(
              container,
              input.inventory_item_id,
              input.from_date,
              input.to_date,
              baseRecordData.status,
              baseRecordData.notes || null,
              null, // orderId
              null, // cartId
              true  // createIfNotExists - create new records if none exist
            );

            console.log(`Successfully updated shared room inventory for room ${input.inventory_item_id}`);
          } else {
            console.log(`No room number found in metadata for variant ${input.inventory_item_id}, skipping shared room update`);
          }
        } catch (variantError) {
          console.error(`Error retrieving variant details: ${variantError.message}`);
        }
      } catch (sharedRoomError) {
        console.error("Error updating shared room inventory:", sharedRoomError);
        console.error("Error details:", sharedRoomError.stack);
        // Continue with the main room update even if shared room update fails
      }
    } else {
      console.log(`Skipping shared room update - no inventory_item_id provided`);
    }

    return new StepResponse(
      combinedRecords,
      combinedRecords.map((r) => r.id)
    );
  },
  async (ids: string[], { container }) => {
    try {
      if (!ids || ids.length === 0) {
        console.log("No IDs to delete in rollback");
        return;
      }

      console.log(`Rolling back ${ids.length} inventory records`);
      const roomInventoryModuleService: RoomInventoryModuleService =
        container.resolve(ROOM_INVENTORY_MODULE);
      await roomInventoryModuleService.deleteRoomInventories(ids);
      console.log(`Successfully rolled back ${ids.length} inventory records`);
    } catch (error) {
      console.error(`Error in rollback: ${error.message}`);
      // We can't do much more in the rollback function if it fails
    }
  }
);

export const CreateOrUpdateRoomInventoryWorkflow = createWorkflow(
  "create-or-update-room-inventory",
  (input: CreateOrUpdateRoomInventoryWorkflowInput) => {
    const roomInventory = createOrUpdateRoomInventoryStep(input);
    return new WorkflowResponse(roomInventory);
  }
);
