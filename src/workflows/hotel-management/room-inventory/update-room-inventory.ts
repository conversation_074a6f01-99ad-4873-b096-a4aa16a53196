import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { ROOM_INVENTORY_MODULE } from "src/modules/hotel-management/room-inventory";
import RoomInventoryModuleService from "src/modules/hotel-management/room-inventory/service";

export type UpdateRoomInventoryStepInput = {
  id: string;
  available_quantity?: number;
  notes?: string;
};

type UpdateRoomInventoryWorkflowInput = UpdateRoomInventoryStepInput;

export const updateRoomInventoryStep = createStep(
  "update-room-inventory-step",
  async (input: UpdateRoomInventoryStepInput, { container }) => {
    const roomInventoryModuleService: RoomInventoryModuleService =
      container.resolve(ROOM_INVENTORY_MODULE);

    const prevUpdatedRoomInventory = await roomInventoryModuleService.retrieveRoomInventory(
      input.id
    );

    const updateRoomInventory = await roomInventoryModuleService.updateRoomInventories(input);

    return new StepResponse(updateRoomInventory, prevUpdatedRoomInventory);
  },
  async (prevUpdatedRoomInventory, { container }) => {
    if (!prevUpdatedRoomInventory) {
      return;
    }
    const roomInventoryModuleService: RoomInventoryModuleService =
      container.resolve(ROOM_INVENTORY_MODULE);

    await roomInventoryModuleService.updateRoomInventories(prevUpdatedRoomInventory);
  }
);

export const UpdateRoomInventoryWorkflow = createWorkflow(
  "update-room-inventory",
  (input: UpdateRoomInventoryWorkflowInput) => {
    const roomInventory = updateRoomInventoryStep(input);
    return new WorkflowResponse(roomInventory);
  }
);