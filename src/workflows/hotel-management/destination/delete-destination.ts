import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import DestinationModuleService from "src/modules/hotel-management/destination/service";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type DeleteDestinationStepInput = {
  ids: string[];
};

type DeleteDestinationWorkflowInput = DeleteDestinationStepInput;

export const deleteDestinationStep = createStep(
  "delete-destination-step",
  async (input: DeleteDestinationStepInput, { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );

    // Get destinations with more details
    const destinations = await destinationModuleService.listDestinations({
      id: input.ids,
    },{ select: ["id", "category_id"] });

    // Delete related images for each destination
    for (const destination of destinations) {
      try {
        // Get all images for this destination
        const images = await destinationModuleService.listDestinationImages({
          destination_id: destination.id
        });

        // Delete each image
        if (images && images.length > 0) {
          console.log(`Deleting ${images.length} images for destination ${destination.id}`);
          for (const image of images) {
            await destinationModuleService.deleteDestinationImages({
              id: image.id
            });
          }
        }
      } catch (error) {
        console.error(`Error deleting images for destination ${destination.id}:`, error);
        // Continue with deletion even if image deletion fails
      }
    }

    // Delete product categories
    const category_ids = destinations.map((destination) => destination.category_id);
    await productModuleService.softDeleteProductCategories(category_ids);

    // Delete destinations
    await destinationModuleService.deleteDestinations(input.ids);

    return new StepResponse(input.ids, destinations);
  },
);

// Workflow that combines delete step with event emission
export const DeleteDestinationWorkflow = createWorkflow(
  "delete-destination",
  (input: DeleteDestinationWorkflowInput) => {
    const destinations = deleteDestinationStep(input);

    // Now we can emit the event with the actual deleted destinations
    // The result contains the destinations that were deleted
    emitEventStep({
      eventName: "destination.deleted",
      data: {
        ids: input.ids,
        count: input.ids.length,
      },
    });

    return new WorkflowResponse(destinations);
  }
);
