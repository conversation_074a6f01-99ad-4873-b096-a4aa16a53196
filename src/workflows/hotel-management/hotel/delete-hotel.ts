import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import HotelModuleService from "src/modules/hotel-management/hotel/service";

export type DeleteHotelStepInput = {
  ids: string[];
};
type DeleteHotelWorkflowInput = DeleteHotelStepInput;

export const deleteHotelStep = createStep(
  "delete-hotel-step",
  async (input: any, { container }) => {
    const hotelModuleService: HotelModuleService =
      container.resolve(HOTEL_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const resorts = await hotelModuleService.listHotels(
      { id: input.ids },
      { select: ["id", "category_id"] }
    );
    const category_ids = resorts.map((resort) => resort.category_id);
    await productModuleService.softDeleteProductCategories(category_ids);
    await hotelModuleService.softDeleteHotels(input.ids);

    return new StepResponse(void 0);
  }
);

export const DeleteHotelWorkflow = createWorkflow(
  "delete-hotel",
  (input: DeleteHotelWorkflowInput) => {
    const hotel = deleteHotelStep(input);

    return new WorkflowResponse(hotel);
  }
);
