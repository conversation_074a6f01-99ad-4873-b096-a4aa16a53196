import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CAMPAIGN_EXTENSION_MODULE } from "src/modules/hotel-management/campaign-extension";
import CampaignExtensionModuleService from "src/modules/hotel-management/campaign-extension/service";

export type DeleteCampaignExtensionStepInput = {
  ids: string[];
};
type DeleteCampaignExtensionWorkflowInput = DeleteCampaignExtensionStepInput;

export const deleteCampaignExtensionStep = createStep(
  "delete-campaign-extension-step",
  async (input: any, { container }) => {
    const campaignExtensionModuleService: CampaignExtensionModuleService =
      container.resolve(CAMPAIGN_EXTENSION_MODULE);
    await campaignExtensionModuleService.softDeleteCampaignExtensions(
      input.ids
    );

    return new StepResponse(void 0);
  }
);

export const DeleteCampaignExtensionWorkflow = createWorkflow(
  "delete-campaign-extension",
  (input: DeleteCampaignExtensionWorkflowInput) => {
    const campaign = deleteCampaignExtensionStep(input);

    return new WorkflowResponse(campaign);
  }
);
