import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CAMPAIGN_EXTENSION_MODULE } from "src/modules/hotel-management/campaign-extension";
import CampaignExtension from "src/modules/hotel-management/campaign-extension/service";

export type CreateCampaignExtensionStepInput = {
  campaign_id: string;
  booking_to_date: Date;
  booking_from_date: Date;
};

type CreateCampaignExtensionWorkflowInput = CreateCampaignExtensionStepInput;

export const createCampaignExtensionStep = createStep(
  "create-campaign-extension-step",
  async (input: CreateCampaignExtensionStepInput, { container }) => {
    const campaignModuleService: CampaignExtension = container.resolve(
      CAMPAIGN_EXTENSION_MODULE
    );
    const campaign = await campaignModuleService.createCampaignExtensions(
      input
    );
    return new StepResponse(campaign, campaign.id);
  },
  async () => {}
);

export const CreateCampaignExtensionWorkflow = createWorkflow(
  "create-campaign-extension",
  (input: CreateCampaignExtensionWorkflowInput) => {
    const campaign = createCampaignExtensionStep(input);
    return new WorkflowResponse(campaign);
  }
);
