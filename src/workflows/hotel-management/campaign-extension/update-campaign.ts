import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { CAMPAIGN_EXTENSION_MODULE } from "src/modules/hotel-management/campaign-extension";
import CampaignExtensionModuleService from "src/modules/hotel-management/campaign-extension/service";

export type UpdateCampaignExtensionStepInput = {
  id: string;
  campaign_id: string;
  booking_to_date: Date;
  booking_from_date: Date;
};

type UpdateCampaignExtensionWorkflowInput = UpdateCampaignExtensionStepInput;

export const updateCampaignExtensionStep = createStep(
  "update-campaign-extension-step",
  async (input: UpdateCampaignExtensionStepInput, { container }) => {
    const campaignExtensionModuleService: CampaignExtensionModuleService =
      container.resolve(CAMPAIGN_EXTENSION_MODULE);
    const prevUpdatedCampaignExtensions =
      await campaignExtensionModuleService.retrieveCampaignExtension(input.id);
    const updateCampaignExtension =
      await campaignExtensionModuleService.updateCampaignExtensions(input);

    return new StepResponse(
      updateCampaignExtension,
      prevUpdatedCampaignExtensions
    );
  },
  async () => {}
);

export const UpdateCampaignExtensionWorkflow = createWorkflow(
  "update-campaign-extension",
  (input: UpdateCampaignExtensionWorkflowInput) => {
    const campaign = updateCampaignExtensionStep(input);

    return new WorkflowResponse(campaign);
  }
);
