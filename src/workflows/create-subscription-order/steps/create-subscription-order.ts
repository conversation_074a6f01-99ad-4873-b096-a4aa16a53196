import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk"
import { 
  CartWorkflowDTO,
  PaymentCollectionDTO,
  IOrderModuleService,
  LinkDefinition
} from "@camped-ai/framework/types"
import { 
  Modules
} from "@camped-ai/framework/utils"
import { createOrdersWorkflow } from "@camped-ai/medusa/core-flows"
import { SubscriptionData } from "../../../modules/subscription/types"
import { SUBSCRIPTION_MODULE } from "../../../modules/subscription"
import paymentLink from "razorpay/dist/types/paymentLink"

type StepInput = {
  subscription: SubscriptionData
  cart: CartWorkflowDTO
  payment_collection: PaymentCollectionDTO
  paymentLink: string
}

function getOrderData (cart: CartWorkflowDTO, paymentLink) {
  return {
    region_id: cart.region_id,
    customer_id: cart.customer_id,
    sales_channel_id: cart.sales_channel_id,
    email: cart.email,
    currency_code: cart.currency_code,
    is_draft_order: true,
    metadata: {
      order_type: "subscription",
      payment_Link: paymentLink,
    },
    shipping_address: {
      ...cart.shipping_address,
      id: null
    },
    billing_address: {
      ...cart.billing_address,
      id: null
    },
    items: cart.items,
    shipping_methods: cart.shipping_methods.map((method) => ({
      name: method.name,
      amount: method.amount,
      is_tax_inclusive: method.is_tax_inclusive,
      shipping_option_id: method.shipping_option_id,
      data: method.data,
      tax_lines: method.tax_lines.map((taxLine) => ({
        description: taxLine.description,
        tax_rate_id: taxLine.tax_rate_id,
        code: taxLine.code,
        rate: taxLine.rate,
        provider_id: taxLine.provider_id
      })),
      adjustments: method.adjustments.map((adjustment) => ({
        code: adjustment.code,
        amount: adjustment.amount,
        description: adjustment.description,
        promotion_id: adjustment.promotion_id,
        provider_id: adjustment.provider_id
      }))
    })),
  }
}

const createSubscriptionOrderStep = createStep(
  "create-subscription-order",
  async ({ 
    subscription, cart, payment_collection, paymentLink
  }: StepInput, { container, context }) => {
    const linkDefs: LinkDefinition[] = []

    const { result: order } = await createOrdersWorkflow(container)
      .run({
        input: getOrderData(cart, paymentLink),
        context
      })

    linkDefs.push({
      [Modules.ORDER]: {
        order_id: order.id
      },
      [Modules.PAYMENT]: {
        payment_collection_id: payment_collection.id
      }
    },
    {
      [SUBSCRIPTION_MODULE]: {
        subscription_id: subscription.id
      },
      [Modules.ORDER]: {
        order_id: order.id
      }
    })

    return new StepResponse({
      order,
      linkDefs
    }, {
      order
    })
  },
  async ({ order }, { container }) => {
    const orderModuleService: IOrderModuleService = container.resolve(
      Modules.ORDER
    )

    await orderModuleService.cancel(order.id)
  }
)

export default createSubscriptionOrderStep