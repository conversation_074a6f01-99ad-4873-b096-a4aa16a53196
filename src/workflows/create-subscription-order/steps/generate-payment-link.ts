import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import <PERSON><PERSON><PERSON><PERSON> from "razorpay";

type StepInput = {
  amount: number;
  currency_code: string;
  description: string;
  email: string;
  session_id: string;
};

const generatePaymentLinkStep = createStep(
  "generate-payment-link",
  async (input: StepInput, { container }) => {
    const razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_ID,
      key_secret: process.env.RAZORPAY_SECRET,
    });
    const paymentLink = await razorpay.paymentLink.create({
      amount: input.amount * 100, // Razorpay expects the amount in paise
      currency: "INR",
      // currency: currency_code.toUpperCase() || 'INR',
      accept_partial: false,
      description: "Payment for order",
      customer: {
        // name: customer_name,
        email: input.email,
        // contact: customer_contact,
      },
      notes:{
        session_id: input.session_id
      },
      notify: {
        // sms: true,
        email: true,
      },
      // reminder_enable: true,
      // callback_url: process.env.CALLBACK_URL,
      callback_method: "get",
    });
    return new StepResponse(paymentLink);
  }
);

export default generatePaymentLinkStep;
