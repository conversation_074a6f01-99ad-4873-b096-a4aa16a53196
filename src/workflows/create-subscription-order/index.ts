import { createWorkflow, WorkflowResponse } from "@camped-ai/framework/workflows-sdk"
import { 
  useQueryGraphStep,
  createPaymentSessionsWorkflow,
  createRemoteLinkStep,
} from "@camped-ai/medusa/core-flows"
import { 
  SubscriptionData
} from "../../modules/subscription/types"
import { 
  authorizePaymentSessionStep,
  createPaymentCollectionsStep
} from "@camped-ai/medusa/core-flows"
import createSubscriptionOrderStep from "./steps/create-subscription-order"
import updateSubscriptionStep from "./steps/update-subscription"

type WorkflowInput = {
  subscription: SubscriptionData
}

const createSubscriptionOrderWorkflow = createWorkflow(
  "create-subscription-order",
  (input: WorkflowInput) => {
    //@ts-ignore
    const { data: carts } = useQueryGraphStep({
      entity: "subscription",
      fields: [
        "*",
        "cart.*",
        "cart.items.*",
        "cart.items.tax_lines.*",
        "cart.items.adjustments.*",
        "cart.shipping_address.*",
        "cart.billing_address.*",
        "cart.shipping_methods.*",
        "cart.shipping_methods.tax_lines.*",
        "cart.shipping_methods.adjustments.*",
        "cart.payment_collection.*",
        "cart.payment_collection.payment_sessions.*"
      ],
      filters: {
        id: [input.subscription.id]
      },
      options: {
        throwIfKeyNotFound: true
      }
    })

    const payment_collection = createPaymentCollectionsStep([{
      currency_code: carts[0].cart.currency_code,
      amount: carts[0].cart.payment_collection.amount,
      metadata: carts[0].cart.payment_collection.metadata
    }])[0]

    const amount: number = carts[0].cart.payment_collection.amount
    const {email, customer_id, billing_address, metadata} = carts[0]?.cart?.payment_collection?.payment_sessions[0]?.context.extra
    
    const paymentSession = createPaymentSessionsWorkflow.runAsStep({
      input: {
        payment_collection_id: payment_collection.id,
        provider_id: "pp_razorpay_razorpay", 
        context: {
          extra: {
            email,
            customer_id,
            billing_address,
            metadata:{type: "subscription"}
          }
        }
      }
    })
   
    authorizePaymentSessionStep({
      id: paymentSession.id,
      context: paymentSession.context
    })

    const { order, linkDefs } = createSubscriptionOrderStep({
      subscription: input.subscription,
      cart: carts[0].cart,
      payment_collection,
      paymentLink: paymentSession?.data?.payment_link,
    })

    createRemoteLinkStep(linkDefs)

    updateSubscriptionStep({
      subscription_id: input.subscription.id
    })

    return new WorkflowResponse({
      order
    })
  }
)

export default createSubscriptionOrderWorkflow