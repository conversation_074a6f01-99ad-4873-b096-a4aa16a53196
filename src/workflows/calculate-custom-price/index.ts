import { 
  createWorkflow, 
  createStep,
  StepResponse,
  WorkflowResponse 
} from "@camped-ai/framework/workflows-sdk";
import { useQueryGraphStep } from "@camped-ai/medusa/core-flows";

type PriceCalculationInput = {
  variant_id: string;
  currency_code: string;
  addons?: string[];
};

const getVariantPriceStep = createStep(
  "get-variant-price",
  async (input: PriceCalculationInput) => {
    const { data: variants } = useQueryGraphStep({
      entity: "variant",
      fields: [
        "id",
        "calculated_price.*",
      ],
      filters: {
        id: input.variant_id,
      },
      context: {
        calculated_price: {
          currency_code: input.currency_code,
        },
      },
    });
    
    return new StepResponse({
      basePrice: variants[0].calculated_price.calculated_amount,
      variant: variants[0]
    });
  }
);

const getAddonPricesStep = createStep(
  "get-addon-prices",
  async (input: PriceCalculationInput) => {
    const { data: prices } = useQueryGraphStep({
      entity: "price",
      fields: [
        "id",
        "amount",
        "rules",
      ],
      filters: {
        variant_id: input.variant_id,
        currency_code: input.currency_code,
      },
    });
    
    return new StepResponse(prices);
  }
);

const calculateTotalPriceStep = createStep(
  "calculate-total-price",
  async (input: { 
    basePrice: number;
    addonPrices: any[];
    selectedAddons: string[];
  }) => {
    let totalPrice = input.basePrice;
    
    const addonPriceDetails = input.selectedAddons.map(addon => {
      const price = input.addonPrices.find(p => 
        p.rules && 
        p.rules.some(rule => 
          rule.id === "addon_type" && rule.value === addon
        )
      );
      
      if (price) {
        totalPrice += price.amount;
      }
      
      return {
        addon,
        price: price ? price.amount : 0
      };
    });
    
    return new StepResponse({
      totalPrice,
      addonPriceDetails
    });
  }
);

export const calculateCustomPriceWorkflow = createWorkflow(
  "calculate-custom-price",
  (input: PriceCalculationInput) => {
    const { basePrice } = getVariantPriceStep(input);
    const addonPrices = getAddonPricesStep(input);
    
    const { totalPrice, addonPriceDetails } = calculateTotalPriceStep({
      basePrice,
      addonPrices,
      selectedAddons: input.addons || []
    });
    
    return new WorkflowResponse({
      base_price: basePrice,
      addons: input.addons || [],
      addon_prices: addonPriceDetails,
      total_price: totalPrice
    });
  }
);