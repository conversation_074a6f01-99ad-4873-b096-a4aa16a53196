import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { STORE_DETAIL_MODULE } from "src/modules/store-detail";
import StoreDetailModuleService from "src/modules/store-detail/service";

export type UpdateStoreDetailStepInput = {
  storefront_url: string;
  store_id: string;
};

type UpdateStoreDetailWorkflowInput = {
  storefront_url: string;
  store_id: string;
};

export const updateStoreDetailStep = createStep(
  "update-store-step",
  async (input: UpdateStoreDetailStepInput, { container }) => {
    const storeModuleService: StoreDetailModuleService =
      container.resolve(STORE_DETAIL_MODULE);
    const prevUpdatedStoreDetails = await storeModuleService.listStoreDetails({
      store_id: input.store_id,
    });
    const updateStoreDetail =
      prevUpdatedStoreDetails.length == 0
        ? await storeModuleService.createStoreDetails(input)
        : await storeModuleService.updateStoreDetails(input);

    return new StepResponse(updateStoreDetail, prevUpdatedStoreDetails);
  },
  async (prevUpdatedStoreDetails, { container }) => {
    if (!prevUpdatedStoreDetails) {
      return;
    }
    const storeModuleService: StoreDetailModuleService =
      container.resolve(STORE_DETAIL_MODULE);

    await storeModuleService.updateStoreDetails(prevUpdatedStoreDetails);
  }
);

export const updateStoreDetailWorkflow = createWorkflow(
  "update-store-detail",
  (input: UpdateStoreDetailWorkflowInput) => {
    const storeDetail = updateStoreDetailStep(input);

    return new WorkflowResponse(storeDetail);
  }
);
