import { Modules } from "@camped-ai/framework/utils"
import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk"
import { CreateNotificationDTO } from "@camped-ai/framework/types"

export const sendNotificationStep = createStep(
  "send-notification",
  async (data: CreateNotificationDTO[], { container }) => {
    const notificationModuleService = container.resolve(
      Modules.NOTIFICATION
    )

    const notification = await notificationModuleService.createNotifications(data)
    return new StepResponse(notification)
  }
)