import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";

export type DeleteNotificationTemplateStepInput = {
  id: string;
};

type DeleteNotificationTemplateWorkflowInput = {
  id: string;
};

export const deleteNotificationTemplateStep = createStep(
  "delete-notification-template-step",
  async (input: DeleteNotificationTemplateStepInput, { container }) => {
    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Get the original template to store for rollback
    const originalTemplate = await notificationTemplateService.retrieveNotificationTemplate(input.id);

    // Delete the template (soft delete)
    await notificationTemplateService.softDeleteNotificationTemplates([input.id]);

    return new StepResponse(void 0, originalTemplate);
  },
  async (originalTemplate, { container }) => {
    if (!originalTemplate) {
      return;
    }

    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Restore the original template
    await notificationTemplateService.createNotificationTemplates(originalTemplate);
  }
);

export const DeleteNotificationTemplateWorkflow = createWorkflow(
  "delete-notification-template",
  (input: DeleteNotificationTemplateWorkflowInput) => {
    const result = deleteNotificationTemplateStep(input);

    return new WorkflowResponse(result);
  }
);
