import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";

export type UpdateNotificationTemplateStepInput = {
  id: string;
  subject?: string;
  content?: string;
  is_default?: boolean;
  is_active?: boolean;
  metadata?: Record<string, unknown>;
};

type UpdateNotificationTemplateWorkflowInput = {
  id: string;
  subject?: string;
  content?: string;
  is_default?: boolean;
  is_active?: boolean;
  metadata?: Record<string, unknown>;
};

export const updateNotificationTemplateStep = createStep(
  "update-notification-template-step",
  async (input: UpdateNotificationTemplateStepInput, { container }) => {
    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Get the original template to store for rollback
    const originalTemplate = await notificationTemplateService.retrieveNotificationTemplate(input.id);

    // Update the template
    const updateData = {
      id: input.id,
      ...(input.subject !== undefined && { subject: input.subject }),
      ...(input.content !== undefined && { content: input.content }),
      ...(input.is_active !== undefined && { is_active: input.is_active }),
      ...(input.metadata !== undefined && { metadata: input.metadata }),
    };

    const updatedTemplate = await notificationTemplateService.updateNotificationTemplates([updateData]);

    // If this template is set as default, make sure other templates for the same event and channel are not default
    if (input.is_default) {
      await notificationTemplateService.setAsDefault(input.id);
    }

    return new StepResponse(updatedTemplate, originalTemplate);
  },
  async (originalTemplate, { container }) => {
    if (!originalTemplate) {
      return;
    }

    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Restore the original template
    await notificationTemplateService.updateNotificationTemplates([originalTemplate]);
  }
);

export const UpdateNotificationTemplateWorkflow = createWorkflow(
  "update-notification-template",
  (input: UpdateNotificationTemplateWorkflowInput) => {
    const notificationTemplate = updateNotificationTemplateStep(input);

    return new WorkflowResponse(notificationTemplate);
  }
);