import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";

export type ResetNotificationTemplateStepInput = {
  id: string;
};

type ResetNotificationTemplateWorkflowInput = {
  id: string;
};

export const resetNotificationTemplateStep = createStep(
  "reset-notification-template-step",
  async (input: ResetNotificationTemplateStepInput, { container }) => {
    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Get the template to reset
    const template = await notificationTemplateService.retrieveNotificationTemplate(input.id);

    // Store the original template for rollback
    const originalTemplate = { ...template };

    // Get the original template content from the code files
    console.log(`Resetting template for event ${template.event_name} and channel ${template.channel}`);

    const originalContent = await notificationTemplateService.getOriginalTemplateContent(
      template.event_name,
      template.channel
    );

    console.log('Original content found:', originalContent ? 'Yes' : 'No');

    if (!originalContent) {
      console.error(`No original template content found for event ${template.event_name} and channel ${template.channel}`);
      throw new Error(`No original template content found for event ${template.event_name} and channel ${template.channel}`);
    }

    // Update the template with the original content
    const updatedTemplate = await notificationTemplateService.updateNotificationTemplates([{
      id: template.id,
      content: originalContent.content,
      subject: originalContent.subject,
    }]);

    return new StepResponse(updatedTemplate, originalTemplate);
  },
  async (originalTemplate, { container }) => {
    if (!originalTemplate) {
      return;
    }

    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // Restore the original template
    await notificationTemplateService.updateNotificationTemplates([originalTemplate]);
  }
);

export const ResetNotificationTemplateWorkflow = createWorkflow(
  "reset-notification-template",
  (input: ResetNotificationTemplateWorkflowInput) => {
    const result = resetNotificationTemplateStep(input);

    return new WorkflowResponse(result);
  }
);