import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";

export type CreateNotificationTemplateStepInput = {
  event_name: string;
  channel: string;
  subject?: string;
  content: string;
  is_default?: boolean;
  is_active?: boolean;
  metadata?: Record<string, unknown>;
};

type CreateNotificationTemplateWorkflowInput = {
  event_name: string;
  channel: string;
  subject?: string;
  content: string;
  is_default?: boolean;
  is_active?: boolean;
  metadata?: Record<string, unknown>;
};

export const createNotificationTemplateStep = createStep(
  "create-notification-template-step",
  async (input: CreateNotificationTemplateStepInput, { container }) => {
    const notificationTemplateService: NotificationTemplateService =
      container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    const notificationTemplate = await notificationTemplateService.createNotificationTemplates({
      event_name: input.event_name,
      channel: input.channel,
      subject: input.subject,
      content: input.content,
      is_default: input.is_default ?? false,
      is_active: input.is_active ?? true,
      metadata: input.metadata,
    });

    // If this template is set as default, make sure other templates for the same event and channel are not default
    if (input.is_default) {
      await notificationTemplateService.setAsDefault(notificationTemplate.id);
    }

    return new StepResponse(notificationTemplate);
  }
);

export const CreateNotificationTemplateWorkflow = createWorkflow(
  "create-notification-template",
  (input: CreateNotificationTemplateWorkflowInput) => {
    const notificationTemplate = createNotificationTemplateStep(input);

    return new WorkflowResponse(notificationTemplate);
  }
);