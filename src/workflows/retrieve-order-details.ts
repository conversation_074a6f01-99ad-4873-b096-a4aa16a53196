import {
  createWorkflow,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { useQueryGraphStep } from "@camped-ai/medusa/core-flows";

type WorkflowInput = {
  id: string;
};

export const retrieveOrderDetailsWorkflow = createWorkflow(
  "retrieve-order-details",
  ({ id }: WorkflowInput) => {
    // @ts-ignore
    const { data: fulfillment } = useQueryGraphStep({
      entity: "order_fulfillment",
      fields: ["*"],
      filters: {
        fulfillment_id: id,
      },
    });

    return new WorkflowResponse(fulfillment);
  }
);
