import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { LOOKUP_MODULE } from "src/modules/lookup";
import LookupModuleService from "src/modules/lookup/service";

export type CreateLookupStepInput = {
  entity_name: string; 
  value: string;
};

type CreateLookupWorkflowInput = CreateLookupStepInput;


const createLookupStep = createStep(
  "create-lookup-step",
  async (input: CreateLookupStepInput, { container }) => {
    const lookupService: LookupModuleService = container.resolve(LOOKUP_MODULE);
    const lookup = await lookupService.createLookups(input);
    return new StepResponse(lookup);
  },
  async (ids: string[], { container }) => {
    const lookupService: LookupModuleService = container.resolve(LOOKUP_MODULE);
    await lookupService.createLookups(ids);
  }
);

export const CreateLookupWorkflow = createWorkflow(
  "create-lookup",
  function (input: CreateLookupWorkflowInput) {
    const lookup = createLookupStep(input);
    return lookup;
  }
);