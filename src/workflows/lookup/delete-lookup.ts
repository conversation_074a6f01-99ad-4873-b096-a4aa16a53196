import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { LOOKUP_MODULE } from "src/modules/lookup";
import LookupModuleService from "src/modules/lookup/service";

export type DeleteResortStepInput = {
  ids: string[];
};
type DeleteResortWorkflowInput = DeleteResortStepInput;

const deleteLookupStep = createStep(
  "delete-lookup-step",
  async (input: DeleteResortStepInput, { container }) => {
    const lookupService: LookupModuleService = container.resolve(LOOKUP_MODULE);
        await lookupService.softDeleteLookups(input);
        return new StepResponse(void 0);
  }
);

export const DeleteLookupWorkflow = createWorkflow(
  "delete-lookup",
  function (input: DeleteResortWorkflowInput) {
    const lookup = deleteLookupStep(input);
    return lookup;
  }
);