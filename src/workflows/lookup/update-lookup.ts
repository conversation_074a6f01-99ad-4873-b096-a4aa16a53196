import {
  createStep,
  createWorkflow,
  StepResponse,
} from "@camped-ai/framework/workflows-sdk";
import { LOOKUP_MODULE } from "src/modules/lookup";
import LookupModuleService from "src/modules/lookup/service";

export type UpdateLookupStepInput = {
  id: string;
  value: string;
};

type UpdateLookupWorkflowInput = UpdateLookupStepInput;

const updateLookupStep = createStep(
  "update-lookup-step",
  async (input: UpdateLookupStepInput, { container }) => {
    const lookupService: LookupModuleService = container.resolve(LOOKUP_MODULE);
    const lookup = await lookupService.updateLookups(input);
    return new StepResponse(lookup);
  },
  async (prevUpdatedLookups, { container }) => {
    if (!prevUpdatedLookups) {
      return;
    }
    const lookupService: LookupModuleService = container.resolve(LOOKUP_MODULE);
    await lookupService.updateLookups(prevUpdatedLookups);
  }
);

export const UpdateLookupWorkflow = createWorkflow(
  "update-lookup",
  function (input: UpdateLookupWorkflowInput) {
    const lookup = updateLookupStep(input);
    return lookup;
  }
);
