import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { METAFIELD_MODULE } from "src/modules/metafield";
import MetafieldModuleService from "src/modules/metafield/service";

export type UpdateMetafieldStepInput = {
  id: string;
  value: string;
}[];

type UpdateMetafieldWorkflowInput = {
  id: string;
  value: string;
}[];

export const updateMetafieldStep = createStep(
  "update-metafield-step",
  async (input: UpdateMetafieldStepInput, { container }) => {
    const metafieldModuleService: MetafieldModuleService =
      container.resolve(METAFIELD_MODULE);
    const ids = input.map((item) => item.id);
    const prevUpdatedMetafield = await metafieldModuleService.listMetafields({
      id: ids,
    });

    const updateMetafield = await metafieldModuleService.updateMetafields(
      input
    );

    return new StepResponse(updateMetafield, prevUpdatedMetafield);
  },
  async (prevUpdatedMetafield, { container }) => {
    if (!prevUpdatedMetafield) {
      return;
    }

    const metafieldModuleService: MetafieldModuleService =
      container.resolve(METAFIELD_MODULE);

    await metafieldModuleService.updateMetafields(prevUpdatedMetafield);
  }
);

export const UpdateMetafieldWorkflow = createWorkflow(
  "update-metafield",
  (input: UpdateMetafieldWorkflowInput) => {
    const metafield = updateMetafieldStep(input);

    return new WorkflowResponse(metafield);
  }
);
