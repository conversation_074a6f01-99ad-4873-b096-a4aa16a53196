import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { METAFIELD_MODULE } from "src/modules/metafield";
import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";
import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";
import MetafieldModuleService from "src/modules/metafield/service";

export type CreateMetafieldStepInput = {
  owner_id: string;
  definition_id: string;
  value: string;
}[];

type CreateMetafieldWorkflowInput = CreateMetafieldStepInput;

export const createMetafieldStep = createStep(
  "create-metafield-step",
  async (input: CreateMetafieldStepInput, { container }) => {
    const metafieldModuleService: MetafieldModuleService =
      container.resolve(METAFIELD_MODULE);
    const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
      container.resolve(METAFIELD_DEFINITION_MODULE);

    const definitionIds = input.map(item => item.definition_id);
    const metafieldDefinitions = await metafieldDefinitionModuleService.listMetafieldDefinitions({
      id: definitionIds,
    });

    const metafieldsToCreate = input.map(item => {
      const definition = metafieldDefinitions.find(def => def.id === item.definition_id);
      return {
        ...item,
        owner_type: definition?.owner_type,
        namespace: definition?.namespace,
        key: definition?.key,
      };
    });

    const metafields = await metafieldModuleService.createMetafields(metafieldsToCreate);
    return new StepResponse(metafields, metafields.map(m => m.id));
  },
  async (ids: string[], { container }) => {
    const metafieldModuleService: MetafieldModuleService =
      container.resolve(METAFIELD_MODULE);

    await metafieldModuleService.deleteMetafields(ids);
  }
);

export const CreateMetafieldWorkflow = createWorkflow(
  "create-metafield",
  (input: CreateMetafieldWorkflowInput) => {
    const metafield = createMetafieldStep(input);
    return new WorkflowResponse(metafield);
  }
);
