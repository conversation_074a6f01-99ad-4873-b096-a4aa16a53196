import {
    createStep,
    createWorkflow,
    StepResponse,
    WorkflowResponse,
  } from "@camped-ai/framework/workflows-sdk";
  import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";
  import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";
  
  export type CreateMetafieldDefinitionStepInput = {
    owner_type: string;
    namespace: string;
    key: string;
    type: string;
    label: string;
    namespace_label: string;
    description: string;
    // default_value: string;
    required: boolean;
    scope: string;
    categories: string[];
  };
  
  type CreateMetafieldDefinitionWorkflowInput = {
    owner_type: string;
    namespace: string;
    key: string;
    type: string;
    label: string;
    namespace_label: string;
    description: string;
    // default_value: string;
    required: boolean;
    scope: string;
    categories: string[];
  };
  
  export const createMetafieldDefinitionStep = createStep(
    "create-metafield-definition-step",
    async (input: CreateMetafieldDefinitionStepInput, { container }) => {
      const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
        container.resolve(METAFIELD_DEFINITION_MODULE);
  
      const metafieldDefinition = await metafieldDefinitionModuleService.createMetafieldDefinitions(input);
  
      return new StepResponse(metafieldDefinition, metafieldDefinition.id);
    },
    async (id: string, { container }) => {
      const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
        container.resolve(METAFIELD_DEFINITION_MODULE);
  
      await metafieldDefinitionModuleService.deleteMetafieldDefinitions(id);
    }
  );
  
  export const CreateMetafieldDefinitionWorkflow = createWorkflow(
    "create-metafield-definition",
    (input: CreateMetafieldDefinitionWorkflowInput) => {
      const metafieldDefinition = createMetafieldDefinitionStep(input);
  
      return new WorkflowResponse(metafieldDefinition);
    }
  );
  