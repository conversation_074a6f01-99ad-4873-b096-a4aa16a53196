import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { METAFIELD_MODULE } from "src/modules/metafield";
import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";
import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";
import MetafieldModuleService from "src/modules/metafield/service";

export type DeleteMetafieldDefinitionStepInput = {
  ids: string[];
  delete_linked_values: boolean;
};
type DeleteMetafieldDefinitionWorkflowInput = {
  ids: string[];
  delete_linked_values: boolean;
};

export const deleteMetafieldDefinitionStep = createStep(
  "delete-metafield-definition-step",
  async (input: any, { container }) => {
    const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
      container.resolve(METAFIELD_DEFINITION_MODULE);
    const metafieldModuleService: MetafieldModuleService =
      container.resolve(METAFIELD_MODULE);

    await metafieldDefinitionModuleService.softDeleteMetafieldDefinitions(
      input.ids
    );
    const metafields = await metafieldModuleService.listMetafields(
      { definition_id: input.ids }, // Fetch metafields related to the deleted product IDs
      { select: ["id"] }
    );

    // Extract IDs from metafield objects
    const metafieldIds = metafields.map((metafield) => metafield.id);

    if (metafieldIds.length > 0 && input.delete_linked_values) {
      await metafieldModuleService.softDeleteMetafields(metafieldIds);
    }

    return new StepResponse(void 0);
  }
);

export const DeleteMetafieldDefinitionWorkflow = createWorkflow(
  "delete-metafield-definition",
  (input: DeleteMetafieldDefinitionWorkflowInput) => {
    const metafieldDefinition = deleteMetafieldDefinitionStep(input);

    return new WorkflowResponse(metafieldDefinition);
  }
);
