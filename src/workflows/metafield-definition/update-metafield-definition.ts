import {
    createStep,
    createWorkflow,
    StepResponse,
    WorkflowResponse,
  } from "@camped-ai/framework/workflows-sdk";
  import { METAFIELD_DEFINITION_MODULE } from "src/modules/metafield-definition";
  import MetafieldDefinitionModuleService from "src/modules/metafield-definition/service";
  
  export type UpdateMetafieldDefinitionStepInput = {
    id: string;
    namespace_label: string,
    description: string;
    // default_value: string;
    required: boolean;
    scope: string;
    categories: string[];
  }[];
  
  type UpdateMetafieldDefinitionWorkflowInput = {
    id: string;
    namespace_label: string,
    description: string;
    // default_value: string;
    required: boolean;
    scope: string;
    categories: string[];
  }[];
  
  export const updateMetafieldDefinitionStep = createStep(
    "update-metafield-definition-step",
    async (input: UpdateMetafieldDefinitionStepInput, { container }) => {
      const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
        container.resolve(METAFIELD_DEFINITION_MODULE);

        const ids = input.map((item) => item.id);
        const prevUpdatedMetafieldDefinition = await metafieldDefinitionModuleService.listMetafieldDefinitions({
            id: ids,
        })
  
      const updateMetafieldDefinition = await metafieldDefinitionModuleService.updateMetafieldDefinitions(input);
  
      return new StepResponse(updateMetafieldDefinition, prevUpdatedMetafieldDefinition);
    },
    async (prevUpdatedMetafieldDefinition, { container }) => {
      if (!prevUpdatedMetafieldDefinition) {
        return
      }
  
      const metafieldDefinitionModuleService: MetafieldDefinitionModuleService =
        container.resolve(METAFIELD_DEFINITION_MODULE);
  
      await metafieldDefinitionModuleService.updateMetafieldDefinitions(prevUpdatedMetafieldDefinition);
    }
  );
  
  export const UpdateMetafieldDefinitionWorkflow = createWorkflow(
    "update-metafield-definition",
    (input: UpdateMetafieldDefinitionWorkflowInput) => {
      const metafieldDefinition = updateMetafieldDefinitionStep(input);
  
      return new WorkflowResponse(metafieldDefinition);
    }
  );
  