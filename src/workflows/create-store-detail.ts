import {
    createStep,
    createWorkflow,
    StepResponse,
    WorkflowResponse,
  } from "@camped-ai/framework/workflows-sdk";
  import { STORE_DETAIL_MODULE } from "src/modules/store-detail";
  import StoreDetailModuleService from "src/modules/store-detail/service";
  
  export type CreateStoreDetailStepInput = {
    storefront_url: string;
    store_id: string;
  };
  
  type CreateStoreDetailWorkflowInput = {
    storefront_url: string;
    store_id: string;
  };
  
  export const createStoreDetailStep = createStep(
    "create-store-step",
    async (input: CreateStoreDetailStepInput, { container }) => {
      const storeModuleService: StoreDetailModuleService =
        container.resolve(STORE_DETAIL_MODULE);
  
      const storeDetail = await storeModuleService.createStoreDetails(input);
  
      return new StepResponse(storeDetail, storeDetail.id);
    },
    async (id: string, { container }) => {
      const storeModuleService: StoreDetailModuleService =
        container.resolve(STORE_DETAIL_MODULE);
  
      await storeModuleService.deleteStoreDetails(id);
    }
  );
  
  export const createStoreDetailWorkflow = createWorkflow(
    "create-store",
    (input: CreateStoreDetailWorkflowInput) => {
      const storeDetail = createStoreDetailStep(input);
  
      return new WorkflowResponse(storeDetail);
    }
  );
  