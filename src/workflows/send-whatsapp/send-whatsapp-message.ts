import { <PERSON><PERSON>aC<PERSON>r } from "@camped-ai/framework/types";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";

import { sendDirectWhatsAppMessage } from "../../modules/whatsapp-notification/direct-message";
import { WHATSAPP_MESSAGE_SERVICE } from "../../modules/whatsapp-message";

// Input type for the workflow
type SendWhatsAppMessageInput = {
  to: string;
  message: string;
  order_id?: string;
  customer_id?: string;
};

// Output type for the workflow
type SendWhatsAppMessageOutput = {
  success: boolean;
  message_id?: string;
  error?: string;
};

// Combined type for intermediate steps
type SendWhatsAppMessageData = SendWhatsAppMessageInput & Partial<SendWhatsAppMessageOutput>;

/**
 * Step 1: Validate the input parameters
 * - Checks required fields
 * - Formats phone number
 */
export const validateInputStep = createStep(
  "validate-whatsapp-message-input",
  async (
    input: SendWhatsAppMessageInput,
    { container }
  ) => {
    const logger = container.resolve("logger");

    // Validate required fields
    if (!input.to) {
      logger.error("WhatsApp message recipient (to) is required");
      throw new Error("WhatsApp message recipient (to) is required");
    }

    if (!input.message) {
      logger.error("WhatsApp message content is required");
      throw new Error("WhatsApp message content is required");
    }

    // Format phone number if needed
    let formattedPhone = input.to.replace(/[^\d+]/g, "");
    if (!formattedPhone.startsWith("+")) {
      formattedPhone = "+" + formattedPhone;
    }

    logger.info(`Validated WhatsApp message to ${formattedPhone}`);

    return new StepResponse<SendWhatsAppMessageData>({
      ...input,
      to: formattedPhone
    });
  }
);

/**
 * Step 2: Send the WhatsApp message
 * - Gets WhatsApp configuration
 * - Sends the message using direct message function
 * - Returns success/failure status
 */
export const sendMessageStep = createStep(
  "send-whatsapp-message",
  async (
    input: SendWhatsAppMessageData,
    { container }
  ) => {
    const logger = container.resolve("logger");

    // Get WhatsApp configuration from environment variables
    const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    const accessToken = process.env.WHATSAPP_ACCESS_TOKEN;

    if (!phoneNumberId) {
      logger.error("WhatsApp phone number ID not configured");
      throw new Error("WhatsApp phone number ID not configured");
    }

    if (!accessToken) {
      logger.error("WhatsApp access token not configured");
      throw new Error("WhatsApp access token not configured");
    }

    try {
      // Send the message using the direct message function
      const result = await sendDirectWhatsAppMessage(
        input.to,
        input.message,
        phoneNumberId,
        accessToken
      );

      logger.info(`WhatsApp message sent successfully: ${JSON.stringify(result)}`);

      return new StepResponse<SendWhatsAppMessageData>({
        ...input,
        success: true,
        message_id: result.messages?.[0]?.id
      });
    } catch (error) {
      logger.error(`Failed to send WhatsApp message: ${error.message}`);

      return new StepResponse<SendWhatsAppMessageData>({
        ...input,
        success: false,
        error: error.message
      });
    }
  }
);

/**
 * Step 3: Save the message to the database
 * - Skips if message sending failed
 * - Saves the message details to the database
 */
export const saveMessageToDatabaseStep = createStep(
  "save-whatsapp-message",
  async (
    input: SendWhatsAppMessageData,
    { container }
  ) => {
    const logger = container.resolve("logger");

    // Skip saving if the message wasn't sent successfully
    if (input.success === false) {
      logger.warn("Skipping database save for failed WhatsApp message");
      return new StepResponse<SendWhatsAppMessageData>(input);
    }

    try {
      const whatsAppMessageService = container.resolve(WHATSAPP_MESSAGE_SERVICE);

      if (!whatsAppMessageService) {
        logger.error("WhatsApp message service not found");
        return new StepResponse<SendWhatsAppMessageData>({
          ...input,
          error: "WhatsApp message service not found"
        });
      }

      const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;

      // Save the message to the database
      const savedMessage = await whatsAppMessageService.saveSentMessage({
        whatsapp_message_id: input.message_id,
        content: input.message,
        from_phone: phoneNumberId,
        to_phone: input.to,
        order_id: input.order_id,
        customer_id: input.customer_id
      });

      logger.info(`WhatsApp message saved to database with ID: ${savedMessage.id}`);

      return new StepResponse<SendWhatsAppMessageData>({
        ...input,
        success: true
      });
    } catch (error) {
      logger.error(`Failed to save WhatsApp message to database: ${error.message}`);

      return new StepResponse<SendWhatsAppMessageData>({
        ...input,
        error: `Message sent but failed to save to database: ${error.message}`
      });
    }
  }
);

/**
 * Workflow to send a WhatsApp message and save it to the database
 */
export const sendWhatsAppMessageWorkflowId = "send-whatsapp-message-workflow";
export const SendWhatsAppMessageWorkflow = createWorkflow(
  {
    name: sendWhatsAppMessageWorkflowId,
    retentionTime: 86400, // 24 hours in seconds
    store: true,
  },
  function (input: SendWhatsAppMessageInput) {
    // Execute steps in sequence
    const validatedInput = validateInputStep(input);
    const messageSent = sendMessageStep(validatedInput);
    const messageSaved = saveMessageToDatabaseStep(messageSent);

    // Return the final result
    return new WorkflowResponse({
      success: messageSaved.message_id ? true :false,
      message_id: messageSaved.message_id,
      error: messageSaved.error
    });
  }
);

