import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { FIELD_VALIDATION_MODULE } from "src/modules/field-validation";
import FieldValidationModuleService from "src/modules/field-validation/service";

export type UpdateFieldValidationStepInput = {
  id: string;
  user_type: string;
  is_required: boolean;
  is_visible: boolean;
}[];

type UpdateFieldValidationWorkflowInput = {
  id: string;
  user_type: string;
  is_required: boolean;
  is_visible: boolean;
}[];

export const updateFieldValidationStep = createStep(
  "update-field-validation-step",
  async (input: UpdateFieldValidationStepInput, { container }) => {
    const fieldValidationModuleService: FieldValidationModuleService =
      container.resolve(FIELD_VALIDATION_MODULE);

    const ids = input.map((item) => item.id);
    const prevUpdatedFieldValidation =
      await fieldValidationModuleService.listFieldValidations({
        id: ids,
      });

    const updateFieldValidation =
      await fieldValidationModuleService.updateFieldValidations(input);

    return new StepResponse(updateFieldValidation, prevUpdatedFieldValidation);
  },
  async (prevUpdatedFieldValidation, { container }) => {
    if (!prevUpdatedFieldValidation) {
      return;
    }

    const fieldValidationModuleService: FieldValidationModuleService =
      container.resolve(FIELD_VALIDATION_MODULE);

    await fieldValidationModuleService.updateFieldValidations(
      prevUpdatedFieldValidation
    );
  }
);

export const UpdateFieldValidationWorkflow = createWorkflow(
  "update-field-validation",
  (input: UpdateFieldValidationWorkflowInput) => {
    const fieldValidation = updateFieldValidationStep(input);

    return new WorkflowResponse(fieldValidation);
  }
);
