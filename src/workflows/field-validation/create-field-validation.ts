import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import  { FIELD_VALIDATION_MODULE } from "src/modules/field-validation";
import FieldValidationModuleService from "src/modules/field-validation/service";

export type CreateFieldValidationStepInput = {
    table_name: string;
    field_name: string;
    user_type: string;
    is_required: boolean;
    is_visible: boolean;
};

type CreateFieldValidationWorkflowInput = {
    table_name: string;
    field_name: string;
    user_type: string;
    is_required: boolean;
    is_visible: boolean;
};

export const createFieldValidationStep = createStep(
  "create-field-validation-step",
  async (input: CreateFieldValidationStepInput, { container }) => {
    const fieldValidationModuleService: FieldValidationModuleService =
      container.resolve(FIELD_VALIDATION_MODULE);

    const fieldValidation = await fieldValidationModuleService.createFieldValidations(input);

    return new StepResponse(fieldValidation, fieldValidation.id);
  },
  async (id: string, { container }) => {
    const fieldValidationModuleService: FieldValidationModuleService =
      container.resolve(FIELD_VALIDATION_MODULE);

    await fieldValidationModuleService.deleteFieldValidations(id);
  }
);

export const CreateFieldValidationWorkflow = createWorkflow(
  "create-field-validation",
  (input: CreateFieldValidationWorkflowInput) => {
    const fieldValidation = createFieldValidationStep(input);

    return new WorkflowResponse(fieldValidation);
  }
);
