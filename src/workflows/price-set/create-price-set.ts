import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { IPricingModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

type CreatePriceSetInput = {
  prices: {
    amount: number;
    currency_code: string;
    rules?: Record<string, any>;
  }[];
};

export const createPriceSetStep = createStep(
  "create-price-set-step",
  async (input: CreatePriceSetInput, { container }) => {
    const pricingModuleService: IPricingModuleService = 
      container.resolve(Modules.PRICING);

    const priceSet = await pricingModuleService.createPriceSets({
      prices: input.prices,
    });

    return new StepResponse(priceSet);
  }
);

const createPriceSetWorkflow = createWorkflow(
  "create-price-set",
  (input: CreatePriceSetInput) => {
    const priceSet = createPriceSetStep(input);
    return new WorkflowResponse(priceSet);
  }
);

export default createPriceSetWorkflow;