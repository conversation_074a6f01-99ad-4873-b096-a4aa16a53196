import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { IPricingModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

type CreatePriceListStepInput = {
  title: string;
  description?: string;
  type?: string;
  rules?: any;
  status?: string;
  prices: {
    price_set_id: string
  }[];
};

export const createPriceListStep = createStep(
  "create-price-list-step",
  async (input: CreatePriceListStepInput, { container }) => {
    const pricingModuleService: IPricingModuleService =
      container.resolve(Modules.PRICING);
      console.log({input})

    const priceList = await pricingModuleService.createPriceLists(input);
    console.log({priceList})

    return new StepResponse(priceList, priceList.id);
  },
  async (id: string, { container }) => {
    const pricingModuleService: IPricingModuleService =
      container.resolve(Modules.PRICING);

    await (pricingModuleService.removePriceLists as any)([id]);
  }
);

export const CreatePriceListWorkflow = createWorkflow(
  "create-price-list",
  (input: CreatePriceListStepInput) => {
    const priceList = createPriceListStep(input);
    return new WorkflowResponse(priceList);
  }
);