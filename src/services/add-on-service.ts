import { MedusaContainer } from "@camped-ai/framework/types";
import { AddOnServiceLevel } from "../modules/hotel-management/add-on-service";
import AddOnServiceModuleService from "../modules/hotel-management/add-on-service/service";

/**
 * Direct implementation of the add-on service
 * This is used as a fallback when the module-based service cannot be resolved
 */
class DirectAddOnServiceImplementation extends AddOnServiceModuleService {
  constructor(container: MedusaContainer) {
    super(container);
    console.log("Using direct add-on service implementation");
  }

  /**
   * Override createAddOnService to ensure unique handles and proper variant creation
   */
  async createAddOnService(data: any) {
    try {
      // Validate required fields
      if (!data.name) {
        throw new Error("Name is required for add-on services");
      }

      if (!data.service_level) {
        throw new Error("Service level is required for add-on services");
      }

      if (data.service_level === AddOnServiceLevel.HOTEL && !data.hotel_id) {
        throw new Error("Hotel ID is required for hotel-level add-on services");
      }

      if (
        data.service_level === AddOnServiceLevel.DESTINATION &&
        !data.destination_id
      ) {
        throw new Error(
          "Destination ID is required for destination-level add-on services"
        );
      }

      // Generate a unique handle for the product if not provided
      if (!data.handle) {
        const timestamp = new Date().getTime();
        const randomString = Math.random().toString(36).substring(2, 8);
        // Sanitize the name to create a URL-friendly base
        const sanitizedName = data.name
          ? data.name
              .toLowerCase()
              .replace(/[^a-z0-9]/g, "-")
              .replace(/-+/g, "-")
              .replace(/^-|-$/g, "")
          : "unnamed-service";
        data.handle = `${sanitizedName}-${data.service_level}-${timestamp}-${randomString}`;

        console.log(
          `Creating add-on service with generated handle: ${data.handle}`
        );
      } else {
        console.log(
          `Creating add-on service with provided handle: ${data.handle}`
        );
      }

      // Call the parent implementation with the enhanced data
      return super.createAddOnService(data);
    } catch (error) {
      console.error("Error in direct add-on service implementation:", error);
      throw error;
    }
  }
}

export default DirectAddOnServiceImplementation;
