/**
 * Medusa script to fix existing room configurations by adding price_set_id to their metadata
 * This script uses Medusa's container directly without going through API endpoints
 *
 * Usage:
 * npx medusa exec ./scripts/fix-room-config-price-sets-direct.ts [hotel_id]
 */

import { ExecArgs } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

export default async function fixRoomConfigPriceSets({
  container,
  args,
}: ExecArgs) {
  try {
    const hotelId = args?.[0] || null;

    console.log("🔧 Starting room configuration price set fix...");

    if (hotelId) {
      console.log(`📍 Processing room configs for hotel: ${hotelId}`);
    } else {
      console.log("📍 Processing all room configs without price sets");
    }

    // Get required services
    const query = container.resolve("query");
    const productModuleService = container.resolve(Modules.PRODUCT);
    const pricingModuleService = container.resolve(Modules.PRICING);

    console.log("✅ Services resolved successfully");

    console.log("🔍 Searching for room configurations without price sets...");

    let roomConfigs: any[] = [];

    if (hotelId) {
      // If hotel_id is specified, use the direct filter approach that works
      console.log(`   Using direct hotel_id filter for: ${hotelId}`);
      const { data: hotelProducts } = await query.graph({
        entity: "product",
        filters: {
          metadata: {
            hotel_id: hotelId,
          },
        },
        fields: ["id", "title", "metadata"],
      });

      // Filter out those that already have price_set_id
      roomConfigs = (hotelProducts || []).filter(
        (product) => !product.metadata?.price_set_id
      );
    } else {
      // If no hotel_id specified, use productModuleService approach
      console.log("   Using productModuleService to get all products...");
      const products = await productModuleService.listProducts(
        {},
        {
          select: ["id", "title", "metadata"],
        }
      );

      // Handle different return types
      let productList = [];
      if (Array.isArray(products)) {
        productList = products;
      } else if (products && typeof products === "object" && products.data) {
        productList = products.data;
      } else if (
        products &&
        typeof products === "object" &&
        products.products
      ) {
        productList = products.products;
      }

      // Filter for room configurations without price_set_id
      roomConfigs = productList.filter(
        (product) =>
          product.metadata?.hotel_id && !product.metadata?.price_set_id
      );
    }

    console.log(
      `Found ${roomConfigs?.length || 0} room configurations without price sets`
    );

    if (!roomConfigs || roomConfigs.length === 0) {
      console.log("✅ No room configurations found that need price sets");
      return;
    }

    const results: any[] = [];
    let successCount = 0;
    let errorCount = 0;

    // Process each room configuration
    for (const roomConfig of roomConfigs) {
      try {
        console.log(`\n🔄 Processing: ${roomConfig.title} (${roomConfig.id})`);

        // Check if price_set_id already exists (double-check)
        if (roomConfig.metadata?.price_set_id) {
          console.log(
            `⚠️  Price set already exists: ${roomConfig.metadata.price_set_id}`
          );
          results.push({
            room_config_id: roomConfig.id,
            room_config_name: roomConfig.title,
            success: true,
            price_set_id: roomConfig.metadata.price_set_id,
            message: "Price set already linked",
          });
          successCount++;
          continue;
        }

        // Create a price set for this room configuration
        console.log("💰 Creating price set...");
        const priceSet = await pricingModuleService.createPriceSets({
          prices: [
            {
              amount: 10000, // $100.00 default
              currency_code: "USD",
              rules: {},
            },
          ],
        });

        console.log(`✅ Created price set: ${priceSet.id}`);

        // Update the room configuration metadata to include the price_set_id
        const updatedMetadata = {
          ...roomConfig.metadata,
          price_set_id: priceSet.id,
        };

        await productModuleService.updateProducts(roomConfig.id, {
          metadata: updatedMetadata,
        });

        console.log(`✅ Updated metadata with price_set_id: ${priceSet.id}`);

        results.push({
          room_config_id: roomConfig.id,
          room_config_name: roomConfig.title,
          success: true,
          price_set_id: priceSet.id,
          message: "Price set created and linked successfully",
        });

        successCount++;
        console.log(`✅ Successfully processed: ${roomConfig.title}`);
      } catch (error: any) {
        console.error(
          `❌ Error processing ${roomConfig.title}:`,
          error.message
        );

        results.push({
          room_config_id: roomConfig.id,
          room_config_name: roomConfig.title,
          success: false,
          error: error.message,
        });

        errorCount++;
      }
    }

    console.log("\n🎉 Bulk operation completed!");
    console.log(`📊 Results:`);
    console.log(`   - Processed: ${roomConfigs.length} room configurations`);
    console.log(`   - Success: ${successCount}`);
    console.log(`   - Errors: ${errorCount}`);

    if (results.length > 0) {
      console.log("\n📋 Detailed results:");
      results.forEach((item, index) => {
        const status = item.success ? "✅" : "❌";
        const message = item.success
          ? `Price set ${item.price_set_id} linked`
          : `Error: ${item.error}`;

        console.log(`   ${index + 1}. ${status} ${item.room_config_name}`);
        console.log(`      ${message}`);
      });
    }

    console.log("\n🎉 Room configuration price set fix completed!");
  } catch (error: any) {
    console.error("❌ Error fixing room configuration price sets:", error);
    console.error("Stack trace:", error.stack);
    throw error; // Let Medusa handle the error
  }
}
