# Medusa E-commerce Backend

## Table of Contents
- [Project Structure](#project-structure)
- [Publishing Changes](#publishing-changes)
- [Updating Medusa Version](#updating-medusa-version)
- [Local Development Setup](#local-development-setup)
- [Creating New Instance](#creating-new-instance)
- [Custom Domain Setup](#custom-domain-setup)
- [Database Management](#database-management)

## Project Structure
```
medusa-backend/
├── src/
│   ├── admin/              # Admin dashboard customizations
│   │   ├── routes/        # Admin UI routes
│   │   ├── widgets/       # Admin UI widgets
│   │   └── lib/          # Admin utilities and shared code
│   ├── api/               # API endpoints
│   │   ├── admin/        # Admin-specific API endpoints
│   │   ├── store/        # Store-specific API endpoints
│   │   └── route.ts      # Shared API routes
│   ├── jobs/              # Scheduled jobs
│   ├── links/             # Module links
│   ├── modules/           # Custom modules
│   │   └── example-module/
│   │       ├── index.ts
│   │       ├── service.ts
│   │       └── migrations/
│   ├── subscribers/       # Event subscribers
│   └── scripts/          # Custom CLI scripts
├── medusa-config.ts      # Medusa configuration
├── tsconfig.json         # TypeScript configuration
├── package.json
└── README.md
```

## Publishing Changes

### Prerequisites
- Node.js 20
- Yarn package manager

### Steps to Publish Changes in shop-repo
1. **Setup Environment**
   ```bash
   yarn install
   ```

2. **Version Update**
   - Update version in `package.json` for all packages
   - Run `yarn install`

3. **NPM Login**
   ```bash
   npm login --scope=@camped-ai --registry=https://npm.pkg.github.com
   # Use GitHub ID as username
   # Use GitHub Token as password
   ```

4. **Publish Changes**
   ```bash
   yarn build
   yarn changeset publish
   ```

5. **Post-Publication**
   - Delete `.medusa`, `yarn.lock`, and `node_modules`
   - Use Node 20 and run `yarn`

## Updating Medusa Version in shop-repo

### Steps to Update
1. **Create Integration Branch**
   ```bash
   git checkout -b medusa-integration
   ```

2. **Add Medusa Remote**
   ```bash
   git remote add medusa https://github.com/medusajs/medusa.git
   ```

3. **Fetch Tags**
   ```bash
   git fetch medusa 'refs/tags/:refs/remotes/medusa/tags/'
   # or
   git fetch medusa --tags
   ```

4. **Checkout Latest Version**
   ```bash
   git checkout -b medusa-v2.4.0 medusa/tags/v2.4.0
   # or
   git checkout -b medusa-v2.6.1 v2.6.1
   ```

5. **Update Integration Branch**
   ```bash
   git checkout medusa-integration
   git reset --hard medusa-v2.4.0
   git push origin medusa-integration --force
   ```

6. **Package Updates**
   - Replace `@camped-ai` with `@camped-ai` package by package
   - Exclude `.md`, `.mdx`, `.changelog` files

## Local Development Setup

### Linking shop repo with medusa-shop-v2
1. Run medusa-store-v2
2. In the camped-ai/shop repo:
   ```bash
   cd dashboard
   yarn dev
   ```
3. Build at root:
   ```bash
   yarn build
   ```
4. Run dashboard:
   ```bash
   cd dashboard
   yarn dev
   ```

## Creating New Instance

### Database Setup (Azure PostgreSQL)
1. Navigate to Azure Database for PostgreSQL flexible server
2. Find "medusa-ecommerce" server
3. Go to Setting → databases → add
4. Create new database

### Container Registry Setup
1. Create new Azure Container Registry
2. Configure:
   - Resource group: camped-medusa
   - Location: central india
3. Generate admin credentials
4. Update application repo with registry credentials

### Kubernetes Setup
1. Create new AKS cluster
2. Update permissions:
   ```bash
   az aks get-credentials --resource-group camped-medusa --name shop-test
   kubectl get services
   az aks update -n <AKS_CLUSTER_NAME> -g <RESOURCE_GROUP> --attach-acr <ACR_NAME>
   ```

### CDN Setup
1. Create Azure Front Door profile
2. Configure:
   - Resource group: camped-medusa
   - Origin type: Container Instances/Kubernetes IP
   - HTTP port: 9000
3. Update protocols and forwarding settings

## Custom Domain Setup

1. Access Azure Front Door profile
2. Add custom domain:
   - Use "All other DNS services" option
   - Add CNAME and TXT records
3. Update Front Door Manager settings

## Database Management

### Migrations
```bash
# Generate migration for new module
npx medusa db:generate <module_name>

# Run migrations
npx medusa db:migrate

# Create admin user
npx medusa user -e [email] -p [password]
```

## Additional Resources
- [Medusa Documentation](https://docs.medusajs.com)
- [Plugin Development Guide](https://docs.medusajs.com/learn/fundamentals/plugins/create#main)

## ToDo
### Must Have
#### April 17
- Pricing  -- 10 April -- Santhosh
- Offers and Promotions --11 April --- Santhosh
- Add ons -- Santhosh
- Notifications/Emails -- Srini
- Bulk import (Hotels, room config, rooms, inventory, Add ons) -- Srini
- Rules (Hold till we get  info from SF/Ambra)
- Storefront modern UI -- Lokesh
- Support  --Lokesh
- Chat  --Lokesh  
- Payments/Cancellations/Refunds (Stripe) 

 #### April 24
 - Bookings -- Monday  --- Santhosh
 - Order Management  --Tuesday -- Santhosh
 - Analytics/Reports --Srini
 - Invoices (Hold)
 - RBAC



### Good To Have
- AI
- WhatsApp
- MultiLanguage

## Timeline
- April 22  April 17 - Inventory, Pricing, Add Ons, Offers and Promotions ready
- April 28
