# Multiple Guest Support in Hotel Booking Cart

The hotel booking cart API now supports multiple guest details, allowing you to specify detailed information for each traveler in your booking.

## Overview

Previously, the cart creation endpoint only supported basic guest information with counts for adults, children, and infants. Now you can provide detailed information for each individual traveler.

## API Changes

### New Fields

The `/store/hotel-management/cart` endpoint now accepts an optional `travelers` field:

```typescript
{
  // ... existing fields
  travelers?: {
    adults: Array<{ name: string }>,
    children: Array<{ name: string, age: number }>,
    infants: Array<{ name: string, age: number }>
  }
}
```

### Backward Compatibility

The API maintains full backward compatibility. The `guest_name`, `guest_email`, and `guest_phone` fields represent **one adult traveler**.

**When you don't provide the `travelers` field:**

1. Create default traveler entries based on the `adults`, `children`, and `infants` counts
2. Use the `guest_name` for the first adult
3. Set empty names for additional adults and all children/infants

**When you provide the `travelers` field:**

1. `guest_name`/`guest_email` represents the primary adult traveler
2. `travelers.adults` contains additional adult travelers (total adults - 1)
3. `travelers.children` and `travelers.infants` contain all children and infants

## Usage Examples

### Example 1: Basic Usage (Backward Compatible)

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
    "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04",
    "guest_name": "John Doe",
    "guest_email": "<EMAIL>",
    "guest_phone": "1234567890",
    "adults": 2,
    "children": 1,
    "infants": 0,
    "number_of_rooms": 1,
    "total_amount": 500,
    "currency_code": "USD",
    "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E"
  }'
```

### Example 2: With Multiple Guest Details

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
    "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04",
    "guest_email": "<EMAIL>",
    "guest_phone": "1234567890",
    "adults": 2,
    "children": 1,
    "infants": 1,
    "travelers": {
      "adults": [
        { "name": "John Doe" },
        { "name": "Jane Doe" }
      ],
      "children": [
        { "name": "Jimmy Doe", "age": 8 }
      ],
      "infants": [
        { "name": "Baby Doe", "age": 6 }
      ]
    },
    "number_of_rooms": 1,
    "total_amount": 500,
    "currency_code": "USD",
    "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E"
  }'
```

## Validation Rules

1. **Count Matching**: If you provide the `travelers` field:

   - `travelers.adults` should contain `(total adults - 1)` entries (since `guest_name` represents 1 adult)
   - `travelers.children` should contain exactly `children` count entries
   - `travelers.infants` should contain exactly `infants` count entries

2. **Required Fields**:

   - Adult travelers: Only `name` is required
   - Child travelers: Both `name` and `age` are required
   - Infant travelers: Both `name` and `age` are required (age in months)

3. **Age Constraints**:

   - Children: Age should be between 2-17 years
   - Infants: Age should be between 0-23 months

4. **Primary Guest**: When using `travelers` field, `guest_name` and `guest_email` are still required as they represent the primary adult traveler.

## Data Storage

The traveler information is stored in the cart metadata under the `travelers` field:

```json
{
  "metadata": {
    "travelers": {
      "adults": [{ "name": "John Doe" }, { "name": "Jane Doe" }],
      "children": [{ "name": "Jimmy Doe", "age": 8 }],
      "infants": [{ "name": "Baby Doe", "age": 6 }]
    }
    // ... other metadata
  }
}
```

## API Usage Examples

### Basic Usage (Backward Compatible)

```json
{
  "guest_name": "John Doe",
  "guest_email": "<EMAIL>",
  "adults": 2,
  "children": 1,
  "infants": 0
  // ... other fields
}
```

### With Multiple Guest Details

```json
{
  "guest_name": "John Doe",
  "guest_email": "<EMAIL>",
  "adults": 3,
  "children": 1,
  "infants": 1,
  "travelers": {
    "adults": [{ "name": "Jane Doe" }, { "name": "Bob Smith" }],
    "children": [{ "name": "Jimmy Doe", "age": 8 }],
    "infants": [{ "name": "Baby Doe", "age": 6 }]
  }
  // ... other fields
}
```

**Note**: In the above example:

- `guest_name` "John Doe" represents the 1st adult
- `travelers.adults` contains 2 additional adults (Jane Doe, Bob Smith)
- Total adults = 3 (1 from guest_name + 2 from travelers.adults)

## Frontend Integration

When implementing this in your frontend:

1. **Form Design**: Create forms that allow users to enter details for each traveler
2. **Dynamic Fields**: Add/remove traveler fields based on the selected counts
3. **Validation**: Ensure the traveler counts match the form data before submission
4. **Backward Compatibility**: Support both old and new API formats

## Benefits

1. **Detailed Guest Information**: Collect complete information for all travelers
2. **Better Customer Service**: Hotels can provide personalized service with guest names
3. **Compliance**: Meet regulatory requirements for guest registration
4. **Backward Compatibility**: Existing integrations continue to work without changes
5. **Flexible Implementation**: Choose when to implement detailed guest collection

## Migration Guide

If you're upgrading from the basic guest information:

1. **No Breaking Changes**: Your existing API calls will continue to work
2. **Gradual Migration**: You can implement detailed guest collection incrementally
3. **Optional Feature**: The `travelers` field is completely optional
4. **Data Consistency**: The system ensures data consistency between counts and detailed information
