# Tips for Working Effectively with Augment

This document provides guidance on how to effectively prompt and work with <PERSON><PERSON> to ensure it follows all the best practices and guidelines in the codebase.

## Prompting Augment to Consider All Guidelines

When assigning tasks to Augment, use the following template to ensure it considers all the guideline files:

```
I've added a task in .augie/tasks.md for you to work on. Before starting, please review ALL the following guideline files in the .augie folder:

1. tasks.md - Contains the specific task and general best practices
2. common-errors-solutions.md - Contains solutions for common errors
3. workflow-best-practices.md - Contains best practices for implementing workflows
4. service-resolution-guide.md - Contains guidelines for resolving services
5. augment-guidelines-mock-data.md - Contains guidelines for avoiding mock data
6. codebase-architecture-review.md - Contains architectural patterns to follow

Pay special attention to:
- NEVER use direct SQL queries
- NEVER use mock data, mock functions, or fallback mechanisms
- Always use proper service resolution with Modules constants
- Implement business logic using workflows
- Follow the architectural patterns described in the guidelines

After reviewing these guidelines, please create a detailed plan before making any changes, and ensure your implementation follows all the best practices outlined in these files.
```

## Effective Prompting Strategies

### 1. Reference Specific Guidelines

If there are particular guidelines that are especially relevant to the current task, mention them specifically:

```
For this task, pay special attention to the workflow implementation guidelines in workflow-best-practices.md and the service resolution patterns in service-resolution-guide.md.
```

### 2. Ask for Confirmation

Have Augment confirm that it has reviewed the guidelines before proceeding:

```
Before you start implementing, please confirm that you've reviewed all the guideline files and understand the key requirements regarding avoiding SQL queries and mock data.
```

### 3. Request a Plan First

Ask Augment to create a plan based on the guidelines before implementing:

```
Based on the guidelines in the .augie folder, please first create a detailed plan for implementing this task, showing how you'll follow the architectural patterns and avoid common pitfalls.
```

### 4. Periodic Reminders

For complex tasks, periodically remind Augment about key guidelines:

```
As you continue with the implementation, remember to follow the workflow patterns from workflow-best-practices.md and ensure proper service resolution as outlined in service-resolution-guide.md.
```

### 5. Review Request

Ask Augment to review its own implementation against the guidelines:

```
Once you've completed the implementation, please review your code against the guidelines in the .augie folder, particularly checking for any instances of direct SQL queries or mock data.
```

## Task-Specific Prompting

### For Workflow Implementation

```
Please implement this feature using the workflow pattern as described in workflow-best-practices.md. Ensure you create focused steps, use proper error handling, and follow the layered architecture approach.
```

### For Service Implementation

```
When implementing this service, please follow the service implementation guidelines in tasks.md. Make sure to extend MedusaService properly, use dependency injection for other services, and avoid direct SQL queries.
```

### For API Route Implementation

```
For this API route, please follow the API route implementation guidelines in tasks.md. Ensure proper input validation, error handling, and use workflows for complex operations.
```

### For Bug Fixes

```
When fixing this bug, please review common-errors-solutions.md for guidance on similar issues. Ensure your fix follows the architectural patterns and doesn't introduce mock data or direct SQL queries.
```

## Reviewing Augment's Work

After Augment completes a task, review the implementation with these questions in mind:

1. **Architecture Compliance**: Does the implementation follow the layered architecture (API → Workflow → Module → Data)?
2. **Workflow Pattern**: Is business logic implemented using workflows and steps?
3. **Service Resolution**: Are services resolved properly using Module constants?
4. **Mock Data**: Are there any instances of mock data or hardcoded values?
5. **SQL Queries**: Are there any direct SQL queries?
6. **Error Handling**: Is error handling implemented properly?
7. **Input Validation**: Is input validation implemented properly?
8. **Type Safety**: Are proper TypeScript types and interfaces used?

If issues are found, provide specific feedback referencing the relevant guidelines:

```
I noticed that the implementation includes direct SQL queries in [file]. According to our guidelines in tasks.md and common-errors-solutions.md, we should avoid direct SQL queries and use module services instead. Please refactor this part to follow our guidelines.
```

## Common Issues to Watch For

1. **Direct SQL Queries**: Augment might fall back to direct SQL queries when unsure how to implement a feature.
2. **Mock Data**: Augment might use mock data or hardcoded values to "make things work" quickly.
3. **Service Resolution**: Augment might use string literals instead of Module constants for service resolution.
4. **Circular Dependencies**: Augment might create circular dependencies between services or workflows.
5. **Large, Monolithic Steps**: Augment might create large, monolithic steps instead of breaking them down.
6. **Silent Fallbacks**: Augment might implement silent fallbacks to default values when services fail.

## Encouraging Good Practices

Positively reinforce when Augment follows the guidelines correctly:

```
Great job following the workflow pattern and avoiding direct SQL queries in your implementation. Your approach to service resolution using Module constants is exactly what we're looking for.
```

## Conclusion

By using these prompting strategies and review techniques, you'll help ensure that Augment consistently produces high-quality code that follows all the best practices and guidelines in the codebase. This will lead to more maintainable, scalable, and robust implementations.

Remember that Augment learns from interactions, so consistent guidance and feedback will improve its performance over time.
