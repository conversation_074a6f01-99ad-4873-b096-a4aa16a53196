# Service Resolution Guide for Medusa v2

This document provides a comprehensive guide for resolving services in Medusa v2 applications. Proper service resolution is critical for avoiding common errors and building maintainable applications.

## Understanding Service Resolution

In Medusa, services are registered in the dependency injection container and need to be properly resolved. The container is responsible for:

1. **Registering services**: Making services available for use
2. **Resolving dependencies**: Providing services with their dependencies
3. **Managing lifecycles**: Creating and disposing of service instances

## Best Practices for Service Resolution

### 1. Always Use Module Constants

Instead of using string literals, always use the predefined constants from the `Modules` enum:

```typescript
import { Modules } from "@camped-ai/framework/utils";

// Correct way
const orderService = container.resolve(Modules.ORDER);
const cartService = container.resolve(Modules.CART);
const productService = container.resolve(Modules.PRODUCT);

// Incorrect way - prone to typos and errors
const orderService = container.resolve("order");
const cartService = container.resolve("cart");
const productService = container.resolve("product");
```

### 2. Use Dependency Injection in Services

Instead of resolving services manually, use dependency injection in your service constructor:

```typescript
import { Modules } from "@camped-ai/framework/utils";
import { MedusaService } from "@camped-ai/framework/utils";
import { Inject } from "awilix";

class ExampleService extends MedusaService({
  ExampleModel,
}) {
  constructor(
    container,
    @Inject(Modules.ORDER) private readonly orderService,
    @Inject(Modules.CART) private readonly cartService,
    @Inject("roomInventoryService") private readonly roomInventoryService
  ) {
    super(container);
  }
  
  // Now you can use this.orderService, this.cartService, etc.
}
```

### 3. Create Module Constants for Custom Services

For your custom modules, create and export constants for service names:

```typescript
// src/modules/hotel-management/room-inventory/index.ts
import { Module } from "@camped-ai/framework/utils";
import RoomInventoryService from "./service";

export const ROOM_INVENTORY_MODULE = "roomInventoryService";

export const roomInventoryModule = Module(ROOM_INVENTORY_MODULE, {
  service: RoomInventoryService,
});

export default roomInventoryModule;
```

Then import and use these constants when resolving services:

```typescript
import { ROOM_INVENTORY_MODULE } from "../modules/hotel-management/room-inventory";

const roomInventoryService = container.resolve(ROOM_INVENTORY_MODULE);
```

### 4. Check Service Registration Before Use

Always check if a service is registered before using it:

```typescript
if (!container.hasRegistration(Modules.ORDER)) {
  throw new Error("Order service not registered");
}

const orderService = container.resolve(Modules.ORDER);
```

### 5. Use Try-Catch When Resolving Services

Use try-catch blocks when resolving services to handle errors gracefully:

```typescript
try {
  const orderService = container.resolve(Modules.ORDER);
  // Use orderService
} catch (error) {
  console.error("Failed to resolve order service:", error.message);
  // Handle the error or provide a fallback
}
```

## Complete List of Core Module Constants

Here's a comprehensive list of core module constants you can use:

```typescript
// Core Commerce Modules
Modules.PRODUCT        // Product module
Modules.ORDER          // Order module
Modules.CART           // Cart module
Modules.CUSTOMER       // Customer module
Modules.USER           // User module
Modules.PAYMENT        // Payment module
Modules.SHIPPING       // Shipping module
Modules.INVENTORY      // Inventory module
Modules.STORE          // Store module
Modules.REGION         // Region module
Modules.CURRENCY       // Currency module
Modules.TAX            // Tax module
Modules.PRICING        // Pricing module
Modules.PROMOTION      // Promotion module
Modules.FULFILLMENT    // Fulfillment module
Modules.SALES_CHANNEL  // Sales channel module
Modules.STOCK_LOCATION // Stock location module

// Architectural Modules
Modules.AUTH           // Authentication module
Modules.FILE           // File storage module
Modules.NOTIFICATION   // Notification module
Modules.EVENT_BUS      // Event bus module
Modules.CACHE          // Cache module
Modules.API_KEY        // API key module
```

## Resolving Services in Different Contexts

### 1. Resolving Services in API Routes

In API routes, use the request scope to resolve services:

```typescript
// src/api/admin/products/route.ts
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const productService = req.scope.resolve(Modules.PRODUCT);
    const products = await productService.list();
    res.json({ products });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
```

### 2. Resolving Services in Workflow Steps

In workflow steps, use the container parameter to resolve services:

```typescript
// src/workflows/product/steps/get-product.ts
import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { Modules } from "@camped-ai/framework/utils";

export const getProductStep = createStep(
  "get-product",
  async (productId: string, { container }) => {
    const productService = container.resolve(Modules.PRODUCT);
    const product = await productService.retrieve(productId);
    return new StepResponse(product);
  }
);
```

### 3. Resolving Services in Subscribers

In subscribers, use the container passed to the constructor:

```typescript
// src/subscribers/product-subscriber.ts
import { EventBusService } from "@camped-ai/framework/types";
import { MedusaContainer } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

type InjectedDependencies = {
  eventBusService: EventBusService;
};

class ProductSubscriber {
  protected readonly eventBusService_: EventBusService;
  protected readonly container_: MedusaContainer;

  constructor({ eventBusService }: InjectedDependencies, container: MedusaContainer) {
    this.eventBusService_ = eventBusService;
    this.container_ = container;
    this.registerSubscribers();
  }

  registerSubscribers(): void {
    this.eventBusService_.subscribe("product.created", this.handleProductCreated);
  }

  handleProductCreated = async (data: Record<string, any>): Promise<void> => {
    try {
      const productService = this.container_.resolve(Modules.PRODUCT);
      const inventoryService = this.container_.resolve(Modules.INVENTORY);
      
      // Handle the event
    } catch (error) {
      console.error("Error handling product.created event:", error);
    }
  };
}

export default ProductSubscriber;
```

## Debugging Service Resolution Issues

When you encounter service resolution errors, use these debugging techniques:

### 1. List All Registered Services

```typescript
console.log("Registered services:", Object.keys(container.registrations));
```

### 2. Check If a Specific Service Is Registered

```typescript
console.log(
  `Is order service registered: ${container.hasRegistration(Modules.ORDER)}`
);
```

### 3. Inspect Service Implementation

```typescript
const orderService = container.resolve(Modules.ORDER);
console.log("Order service methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(orderService)));
```

### 4. Check Service Dependencies

```typescript
// Create a helper function to check service dependencies
function checkServiceDependencies(container, serviceKey) {
  try {
    const service = container.resolve(serviceKey);
    console.log(`Successfully resolved ${serviceKey}`);
    return true;
  } catch (error) {
    console.error(`Failed to resolve ${serviceKey}:`, error.message);
    return false;
  }
}

// Check dependencies for a service
const dependencies = [Modules.PRODUCT, Modules.INVENTORY, "customService"];
dependencies.forEach(dep => checkServiceDependencies(container, dep));
```

## Common Service Resolution Errors and Solutions

### 1. "Service is not registered" Error

**Error Message:**
```
Error: Service with identifier "exampleService" could not be found. Make sure it's registered.
```

**Solutions:**

1. **Use the correct module constant:**
```typescript
// Incorrect
const orderService = container.resolve("orderService");

// Correct
import { Modules } from "@camped-ai/framework/utils";
const orderService = container.resolve(Modules.ORDER);
```

2. **Check module registration:**
```typescript
// Make sure your module is properly defined
export const exampleModule = Module(EXAMPLE_MODULE, {
  service: ExampleModuleService,
});

// And imported in src/modules/index.ts
import exampleModule from "./example-module";

export default [
  // other modules
  exampleModule,
];
```

### 2. "Could not resolve service" Error

**Error Message:**
```
Error: Could not resolve service with identifier "exampleService"
```

**Solutions:**

1. **Break circular dependencies:**
```typescript
// AVOID: Circular dependency
class ServiceA extends MedusaService({}) {
  constructor(container) {
    super(container);
    this.serviceB = container.resolve("serviceB"); // ServiceB depends on ServiceA
  }
}

// SOLUTION: Use lazy resolution
class ServiceA extends MedusaService({}) {
  constructor(container) {
    super(container);
    this.container = container;
  }

  get serviceB() {
    return this.container.resolve("serviceB");
  }
}
```

2. **Register services before resolving:**
```typescript
// Make sure services are registered before API routes are loaded
// In src/api/admin/example/route.ts
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  // Register the service if not already registered
  if (!req.scope.hasRegistration(EXAMPLE_SERVICE)) {
    req.scope.register({
      [EXAMPLE_SERVICE]: {
        resolve: () => new ExampleService(req.scope),
      },
    });
  }

  const service = req.scope.resolve(EXAMPLE_SERVICE);
  // Rest of the implementation
};
```

### 3. Service Methods Not Found or Undefined

**Error Message:**
```
TypeError: Cannot read property 'retrieve' of undefined
```

**Solutions:**

1. **Check service implementation:**
```typescript
// Make sure methods are properly defined
class ExampleService extends MedusaService({
  Example,
}) {
  async retrieve(id: string): Promise<any> {
    // Implementation
  }
}
```

2. **Use proper TypeScript interfaces:**
```typescript
interface IExampleService {
  retrieve(id: string): Promise<any>;
  // Other methods
}

// Then use this interface when resolving
const exampleService = req.scope.resolve(EXAMPLE_SERVICE) as IExampleService;
```

## Creating a Service Registry Helper

Create a helper function that provides type-safe access to services:

```typescript
// src/utils/service-registry.ts
import { MedusaContainer } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { ROOM_INVENTORY_MODULE } from "../modules/hotel-management/room-inventory";

export function getOrderService(container: MedusaContainer) {
  return container.resolve(Modules.ORDER);
}

export function getCartService(container: MedusaContainer) {
  return container.resolve(Modules.CART);
}

export function getRoomInventoryService(container: MedusaContainer) {
  return container.resolve(ROOM_INVENTORY_MODULE);
}

// Add more helper functions for other services
```

Then use these helpers in your code:

```typescript
import { getOrderService, getCartService } from "../utils/service-registry";

// In a workflow step
export const processOrderStep = createStep(
  "process-order",
  async (input, { container }) => {
    const orderService = getOrderService(container);
    const cartService = getCartService(container);
    
    // Use the services...
  }
);
```

## Conclusion

By following these best practices for service resolution, you'll avoid common errors and build more maintainable applications. Remember:

1. **Always use module constants** from the `Modules` enum for core services
2. **Create and export constants** for your custom module services
3. **Use dependency injection** in service constructors
4. **Check service registration** before using services
5. **Use try-catch blocks** when resolving services
6. **Create helper functions** for service resolution
7. **Debug service resolution issues** using the provided techniques

These practices will help you build robust applications with Medusa v2.
