# Architectural Review: Medusa v2 Codebase

## Overview

This document provides a comprehensive architectural review of the Medusa v2 codebase, focusing on standard practices, scalability, and code quality. The review identifies issues across the codebase and provides actionable recommendations for improvement.

## Current Architecture

The codebase follows a modular architecture based on Medusa v2, with the following key components:

1. **Modules**: Core business logic organized in domain-specific modules
2. **API Routes**: REST endpoints for admin and storefront interfaces
3. **Services**: Business logic implementations
4. **Models**: Data models using Medusa's model definition system
5. **Workflows**: Business process orchestration
6. **Links**: Connections between different modules

The project structure follows Medusa v2 conventions:

```
medusa-backend/
├── src/
│   ├── admin/              # Admin dashboard customizations
│   ├── api/                # API endpoints
│   │   ├── admin/          # Admin-specific API endpoints
│   │   ├── store/          # Store-specific API endpoints
│   ├── jobs/               # Scheduled jobs
│   ├── links/              # Module links
│   ├── modules/            # Custom modules
│   ├── subscribers/        # Event subscribers
│   └── workflows/          # Business process workflows
```

## Issues Identified

### 1. Direct SQL Queries Instead of Service/Workflow Patterns

**Issue**: Multiple parts of the codebase use direct SQL queries instead of proper service or workflow patterns:

```typescript
// Last resort: Use a direct SQL query
try {
  console.log("Attempting direct SQL query as last resort");
  const { Pool } = require("pg");

  // Create a connection pool using environment variables
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  });

  const query = `SELECT * FROM room_inventory ${whereClause}`;
  const { rows } = await pool.query(query, flatParams);
  // ...
}
```

This pattern appears in:
- `src/modules/hotel-management/room-inventory/service.ts`
- `src/api/admin/hotel-management/bookings/[id]/route.ts`
- `src/api/admin/hotel-management/bookings/route.ts`
- `src/api/store/hotel-management/bookings/create/route.ts`

Direct SQL queries bypass Medusa's data access layer, making the code harder to maintain, test, and migrate.

### 2. Inconsistent Service Registration

**Issue**: Services are registered in multiple ways across the codebase:

```typescript
// Method 1: Using Module function
export const roomConfigModule = Module(ROOM_CONFIG_SERVICE, {
  service: RoomConfigService,
});

// Method 2: Manual registration in loaders
container.register({
  [ADD_ON_SERVICE_MODULE]: {
    resolve: () => new AddOnServiceModuleService(container),
  },
});

// Method 3: Registration in API routes
export function registerAddOnServiceModule(container: MedusaContainer): void {
  try {
    if (!container.hasRegistration(ADD_ON_SERVICE)) {
      container.register({
        [ADD_ON_SERVICE]: {
          resolve: () => new AddOnServiceModuleService(container),
        },
      });
    }
  } catch (error) {
    console.error("Failed to register add-on service module:", error);
    throw error;
  }
}
```

This inconsistency makes it difficult to understand how services are registered and resolved.

### 3. Excessive Error Handling and Fallbacks

**Issue**: Many services contain excessive error handling and fallback mechanisms:

```typescript
try {
  // Try to use the service
  if (this.roomInventoryService_) {
    return await this.roomInventoryService_.list(selector, config);
  }

  // Try to get the manager if not already available
  if (!this.manager_) {
    try {
      this.manager_ = getManager();
    } catch (e) {
      console.error(`Failed to get EntityManager: ${e.message}`);
    }
  }

  // Fallback to direct repository access
  if (this.manager_) {
    const roomInventoryRepository = this.manager_.getRepository(RoomInventory);
    return await roomInventoryRepository.find({
      where: selector,
      ...config,
    });
  }

  // Last resort: Use a direct SQL query
  try {
    // Direct SQL query implementation
  } catch (dbError) {
    console.error(`Failed to create direct database connection: ${dbError.message}`);
    return [];
  }
} catch (error) {
  console.error(`Error listing room inventories: ${error.message}`);
  throw new Error(`Failed to list room inventories: ${error.message}`);
}
```

While error handling is important, the current implementation is overly complex and makes the code difficult to follow.

### 4. Type Safety Issues

**Issue**: The code uses `any` type assertions extensively:

```typescript
async listAddOnServices(
  selector: any = {},
  config: any = { skip: 0, take: 20 },
  options: any = {}
): Promise<[any[], number]> {
  // Implementation
}

const productServiceAny = this.productService as any;
```

This bypasses TypeScript's type checking, which can lead to runtime errors and makes the code harder to maintain.

### 5. Inconsistent Database Access Patterns

**Issue**: The codebase uses multiple database access patterns:

1. **Medusa's Model System**:
```typescript
export const Destination = model.define("destination", {
  id: model.id().primaryKey(),
  name: model.text(),
  // ...
});
```

2. **TypeORM Direct Repository Access**:
```typescript
const roomInventoryRepository = this.manager_.getRepository(RoomInventory);
return await roomInventoryRepository.find({
  where: selector,
  ...config,
});
```

3. **Direct SQL Queries**:
```typescript
const query = `
  SELECT * FROM room_inventory
  WHERE deleted_at IS NULL
  AND inventory_item_id = $1
  AND from_date <= $2
  AND to_date >= $3
  AND status = 'available'
`;
const { rows } = await pool.query(query, params);
```

4. **Medusa's Query Graph API**:
```typescript
const { data: hotels } = await query.graph({
  entity: "hotel",
  filters: {
    id: hotelId,
  },
  fields: ["id", "name", "handle", "description"],
});
```

This inconsistency makes the codebase harder to understand and maintain.

### 6. Metadata Overuse

**Issue**: Many services store excessive data in metadata fields:

```typescript
const metadata: Record<string, any> = {
  add_on_service: true,
  service_type: data.type || "general",
  service_level: data.service_level,
  max_capacity: data.max_capacity === null ? 999999 : data.max_capacity,
  is_active: data.is_active !== false,
  start_date: data.start_date,
  end_date: data.end_date,
  images: data.images || [],
  // Plus many more fields...
};
```

While metadata is useful for storing additional information, overusing it can lead to performance issues and makes querying more difficult.

### 7. Inconsistent API Route Implementation

**Issue**: API routes are implemented inconsistently:

1. **Using Workflows**:
```typescript
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { result } = await createPriceSetWorkflow(req.scope).run({
      input: req.body,
    });
    res.status(200).json({ price_set: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create price set",
    });
  }
}
```

2. **Direct Service Calls**:
```typescript
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const bookingAIService: BookingAI = req.scope.resolve(BOOKING_AI_SERVICE);
  
  try {
    const session = await bookingAIService.retrieveSession(req.params.id);
    res.json({ session });
  } catch (error) {
    res.status(404).json({ message: error.message });
  }
};
```

3. **Direct Database Queries**:
```typescript
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  try {
    const client = await pool.connect();
    // Direct database queries
  } finally {
    client.release();
  }
};
```

This inconsistency makes it difficult to understand and maintain the API routes.

### 8. Excessive Console Logging

**Issue**: The code contains numerous `console.log` statements:

```typescript
console.log(`Creating add-on service with generated handle: ${productHandle}`);
console.log(`Stored hotel name in metadata: ${hotel.name}`);
console.log(`Found adult price: ${adult_price} ${currency_code}`);
// Many more examples...
```

These statements are not appropriate for production code and should be replaced with proper logging.

### 9. Lack of Proper Interfaces

**Issue**: Many parts of the codebase lack proper TypeScript interfaces:

```typescript
async createAddOnService(data: any) { ... }
async listAddOnServices(selector: any = {}, config: any = { skip: 0, take: 20 }, options: any = {}) { ... }
```

This makes the code harder to understand and maintain.

### 10. Global Database Connection Pools

**Issue**: Some API routes create global database connection pools:

```typescript
// Database connection pool
let dbPool: Pool | null = null;

// Get database connection pool
function getDbPool() {
  if (!dbPool) {
    console.log("Creating new database connection pool");
    const connectionString = process.env.DATABASE_URL;
    // ...
    dbPool = new Pool({ connectionString });
  }
  return dbPool;
}
```

This approach can lead to resource leaks and makes it difficult to manage database connections properly.

## Action Items

### 1. Replace Direct SQL Queries with Proper Patterns

**Action**: Replace direct SQL queries with Medusa's query service or repository pattern:

```typescript
// Instead of direct SQL:
const query = `SELECT * FROM room_inventory ${whereClause}`;
const { rows } = await pool.query(query, flatParams);

// Use Medusa's query service:
const { data: inventoryEntries } = await queryService.graph({
  entity: "room_inventory",
  filters: {
    deleted_at: null,
    inventory_item_id: roomId,
    from_date: { $lte: end.toISOString() },
    to_date: { $gte: start.toISOString() },
    status: "available"
  },
  fields: ["*"]
});
```

### 2. Standardize Service Registration

**Action**: Use a consistent pattern for service registration:

```typescript
// In src/modules/example-module/index.ts
import { Module } from "@camped-ai/framework/utils";
import ExampleModuleService from "./service";

export const EXAMPLE_MODULE = "exampleModule";

export const exampleModule = Module(EXAMPLE_MODULE, {
  service: ExampleModuleService,
});

export default exampleModule;
```

### 3. Implement Proper Dependency Injection

**Action**: Use proper dependency injection patterns:

```typescript
class ExampleModuleService extends MedusaService({
  ExampleModel,
}) {
  constructor(
    container: MedusaContainer,
    @Inject(Modules.PRODUCT) private readonly productService: IProductModuleService,
    @Inject("otherService") private readonly otherService?: any
  ) {
    super(container);
  }
  
  // Methods can now use this.productService, this.otherService directly
}
```

### 4. Define Clear Interfaces

**Action**: Create proper TypeScript interfaces for all input and output data:

```typescript
export interface CreateExampleInput {
  name: string;
  description?: string;
  type: string;
  is_active?: boolean;
  // Other properties
}

export interface ExampleOutput {
  id: string;
  name: string;
  description: string;
  type: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  // Other properties
}

// Then use these interfaces in service methods:
async createExample(data: CreateExampleInput): Promise<ExampleOutput> {
  // Implementation
}
```

### 5. Standardize Database Access Patterns

**Action**: Use Medusa's model system and query service consistently:

```typescript
// Define models using Medusa's model system:
export const Example = model.define("example", {
  id: model.id().primaryKey(),
  name: model.text(),
  description: model.text().nullable(),
  is_active: model.boolean().default(true),
  // Other fields
});

// Use the model in services:
class ExampleModuleService extends MedusaService({
  Example,
}) {
  // Service methods
}

// Use Medusa's query service for complex queries:
const { data: examples } = await queryService.graph({
  entity: "example",
  filters: {
    is_active: true,
    name: { $like: `%${searchTerm}%` }
  },
  fields: ["id", "name", "description"],
  pagination: {
    skip: offset,
    take: limit
  }
});
```

### 6. Create Proper Data Models Instead of Relying on Metadata

**Action**: Create dedicated data models instead of storing everything in metadata:

```typescript
// Instead of:
const metadata = {
  add_on_service: true,
  service_type: data.type,
  service_level: data.service_level,
  // Many more fields...
};

// Create a proper model:
export const AddOnService = model.define("add_on_service", {
  id: model.id({ prefix: "addon" }).primaryKey(),
  product_id: model.text().index(),
  service_type: model.text(),
  service_level: model.text(),
  hotel_id: model.text().nullable(),
  destination_id: model.text().nullable(),
  max_capacity: model.integer().nullable(),
  is_active: model.boolean().default(true),
  start_date: model.date().nullable(),
  end_date: model.date().nullable(),
  // Other fields
});
```

### 7. Standardize API Route Implementation

**Action**: Use a consistent pattern for API routes:

```typescript
// For simple CRUD operations:
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const service = req.scope.resolve(SERVICE_KEY);
    const result = await service.retrieve(req.params.id);
    res.json({ data: result });
  } catch (error) {
    res.status(error.status || 500).json({
      message: error.message || "An unknown error occurred",
    });
  }
};

// For complex operations, use workflows:
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { result } = await exampleWorkflow(req.scope).run({
      input: req.body,
    });
    res.status(200).json({ data: result });
  } catch (error) {
    res.status(error.status || 500).json({
      message: error.message || "An unknown error occurred",
    });
  }
};
```

### 8. Implement Proper Logging

**Action**: Replace console logging with a proper logging service:

```typescript
class ExampleModuleService extends MedusaService({
  Example,
}) {
  constructor(
    container: MedusaContainer,
    @Inject("logger") private readonly logger: any
  ) {
    super(container);
  }
  
  async createExample(data: CreateExampleInput): Promise<ExampleOutput> {
    this.logger.debug(`Creating example: ${data.name}`);
    // Implementation
    this.logger.info(`Created example with ID: ${result.id}`);
    return result;
  }
}
```

### 9. Standardize Error Handling

**Action**: Implement a consistent error handling strategy:

```typescript
async createExample(data: CreateExampleInput): Promise<ExampleOutput> {
  try {
    // Validate input
    this.validateExampleInput(data);
    
    // Implementation
    
    return result;
  } catch (error) {
    this.logger.error(`Failed to create example: ${error.message}`, { error, data });
    
    if (error instanceof MedusaError) {
      throw error;
    }
    
    throw new MedusaError(
      MedusaError.Types.DB_ERROR,
      `Failed to create example: ${error.message}`
    );
  }
}

private validateExampleInput(data: CreateExampleInput): void {
  if (!data.name) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "Name is required"
    );
  }
  
  // Additional validation
}
```

### 10. Use Dependency Injection for Database Connections

**Action**: Use dependency injection for database connections instead of global pools:

```typescript
// Instead of global pools:
let dbPool: Pool | null = null;

function getDbPool() {
  if (!dbPool) {
    dbPool = new Pool({ connectionString: process.env.DATABASE_URL });
  }
  return dbPool;
}

// Use dependency injection:
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const dbConnection = req.scope.resolve("db");
  // Use dbConnection for database operations
};
```

## Implementation Priority

To improve the codebase systematically, focus on these issues in the following order:

1. **Replace Direct SQL Queries**: This is the most critical issue as it affects data integrity and security.
2. **Standardize Service Registration**: This will make the codebase more consistent and easier to understand.
3. **Define Clear Interfaces**: This will improve type safety and make the code more maintainable.
4. **Create Proper Data Models**: This will improve performance and make the code more maintainable.
5. **Standardize API Route Implementation**: This will make the API more consistent and easier to use.
6. **Implement Proper Logging**: This will make debugging and monitoring easier.
7. **Standardize Error Handling**: This will make the code more robust and easier to debug.
8. **Use Dependency Injection**: This will make the code more testable and maintainable.
9. **Standardize Database Access Patterns**: This will make the code more consistent and easier to maintain.
10. **Remove Excessive Error Handling**: This will make the code cleaner and easier to understand.

## Conclusion

The current codebase has several architectural issues that affect its maintainability, scalability, and reliability. By implementing the recommended action items, the codebase will become more robust, easier to maintain, and better aligned with standard practices for Medusa v2 development.

The most critical issues to address are:

1. Replacing direct SQL queries with proper service or workflow patterns
2. Standardizing service registration and dependency injection
3. Defining clear interfaces for type safety
4. Creating proper data models instead of relying on metadata
5. Standardizing API route implementation

These improvements will result in a more scalable and maintainable codebase that follows best practices for Medusa v2 development.
