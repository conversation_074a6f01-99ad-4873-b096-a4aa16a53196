# Common Errors and Solutions in Medusa v2

This document provides solutions for common errors encountered when working with the Medusa v2 codebase. Use this as a quick reference guide when troubleshooting issues.

## CRITICAL: Direct SQL Query Issues

**NEVER use direct SQL queries in the codebase**. This is a strict requirement. Direct SQL queries cause numerous issues:

1. **Bypassing Business Logic**: SQL queries bypass important business logic in services and workflows
2. **Maintenance Challenges**: SQL queries are harder to maintain and update
3. **Testing Difficulties**: SQL queries are difficult to test and mock
4. **Security Risks**: SQL queries can introduce security vulnerabilities like SQL injection
5. **Inconsistent Data Access**: SQL queries lead to inconsistent data access patterns

## CRITICAL: Mock Data and Implementation Issues

**NEVER use mock data, mock functions, or fallback mechanisms in production code**. This is a strict requirement. These practices cause numerous issues:

1. **False Sense of Functionality**: Mock implementations create an illusion that features work correctly
2. **Delayed Discovery of Issues**: Problems are often discovered only in production
3. **Technical Debt**: Mock implementations create technical debt that must be addressed later
4. **Data Inconsistencies**: Hardcoded values lead to data inconsistencies and incorrect business logic
5. **Maintenance Nightmares**: Mock implementations are often forgotten and become permanent

### Common Mock Data and Implementation Errors

**Error Message:**
```
Error: Cannot read property 'X' of undefined
```
or
```
Error: Price data is missing or invalid
```

**Causes:**
- Mock data doesn't match the expected structure in production
- Hardcoded values don't account for all scenarios
- Mock implementations return incomplete or incorrect data

**Solutions:**

1. **Replace Mock Data with Real Service Calls:**
```typescript
// AVOID: Mock data
const availableRooms = [
  { id: "room_1", name: "Deluxe Room", price: 200 },
  { id: "room_2", name: "Suite", price: 350 },
];

// SOLUTION: Use actual service
const roomService = container.resolve("roomService");
const availableRooms = await roomService.getAvailableRooms(
  hotelId,
  checkInDate,
  checkOutDate
);
```

2. **Replace Hardcoded Values with Configuration:**
```typescript
// AVOID: Hardcoded values
const basePrice = 100;
const taxRate = 0.1;

// SOLUTION: Use configuration service
const configService = container.resolve("configService");
const basePrice = await configService.getValue("base_price");
const taxRate = await configService.getValue("tax_rate");
```

3. **Replace Silent Fallbacks with Proper Error Handling:**
```typescript
// AVOID: Silent fallback
try {
  return await priceService.getPrice(roomId);
} catch (error) {
  console.log("Error getting price:", error);
  return { amount: 100 }; // Default price
}

// SOLUTION: Proper error handling
try {
  return await priceService.getPrice(roomId);
} catch (error) {
  throw new MedusaError(
    MedusaError.Types.INVALID_DATA,
    `Failed to get price for room ${roomId}: ${error.message}`
  );
}
```

### Common Direct SQL Query Errors

**Error Message:**
```
Error: Connection terminated unexpectedly
```
or
```
Error: Connection pool full
```

**Causes:**
- Direct SQL connections not properly released
- Too many connections opened
- Connection leaks in the code

**Solutions:**

1. **Replace with Workflow Pattern:**
```typescript
// AVOID: Direct SQL
const client = await pool.connect();
try {
  const result = await client.query("SELECT * FROM products WHERE id = $1", [id]);
  return result.rows[0];
} finally {
  client.release();
}

// SOLUTION: Use workflow pattern
export const getProductWorkflow = createWorkflow(
  "get-product",
  (input: { id: string }) => {
    return getProductStep(input.id);
  }
);

// In a step
export const getProductStep = createStep(
  "get-product",
  async (id: string, { container }) => {
    const productService = container.resolve(Modules.PRODUCT);
    const product = await productService.retrieve(id);
    return new StepResponse(product);
  }
);
```

2. **Use Module Services:**
```typescript
// AVOID: Direct SQL
const result = await client.query(
  "INSERT INTO products (title, description) VALUES ($1, $2) RETURNING *",
  [title, description]
);

// SOLUTION: Use module service
const productService = container.resolve(Modules.PRODUCT);
const product = await productService.create({
  title,
  description,
});
```

3. **Use Query Service for Complex Queries:**
```typescript
// AVOID: Direct SQL
const result = await client.query(
  "SELECT p.*, v.* FROM products p JOIN variants v ON p.id = v.product_id WHERE p.title LIKE $1",
  [`%${searchTerm}%`]
);

// SOLUTION: Use query service
const queryService = container.resolve("queryService");
const { data } = await queryService.graph({
  entity: "product",
  filters: {
    title: { $like: `%${searchTerm}%` },
  },
  fields: ["id", "title", "description", "variants.id", "variants.title"],
});
```

## Service Registration and Resolution Errors

### 1. "Service is not registered" Error

**Error Message:**

```
Error: Service with identifier "exampleService" could not be found. Make sure it's registered.
```

**Causes:**

- The service hasn't been registered in the container
- The service key used for resolution doesn't match the registration key
- The service module hasn't been imported correctly

**Solutions:**

1. **Check service registration in modules:**

```typescript
// Make sure your module is properly defined
export const exampleModule = Module(EXAMPLE_MODULE, {
  service: ExampleModuleService,
});

// And imported in src/modules/index.ts
import exampleModule from "./example-module";

export default [
  // other modules
  exampleModule,
];
```

2. **Register service manually in a loader:**

```typescript
// In src/loaders/example-module.ts
export default async (container: MedusaContainer): Promise<void> => {
  try {
    if (!container.hasRegistration(EXAMPLE_SERVICE)) {
      container.register({
        [EXAMPLE_SERVICE]: {
          resolve: () => new ExampleService(container),
        },
      });
    }
  } catch (error) {
    console.error("Failed to register example service:", error);
  }
};

// Make sure to add this loader to src/loaders/index.ts
```

3. **Use consistent service keys:**

```typescript
// Define constants for service keys
export const EXAMPLE_SERVICE = "exampleService";

// Use the same key when registering and resolving
container.register({
  [EXAMPLE_SERVICE]: { resolve: () => new ExampleService(container) },
});

const service = container.resolve(EXAMPLE_SERVICE);
```

### 2. "Could not resolve service" Error

**Error Message:**

```
Error: Could not resolve service with identifier "exampleService"
```

**Causes:**

- Circular dependencies between services
- Service registration happens after resolution attempt
- Error during service instantiation

**Solutions:**

1. **Break circular dependencies:**

```typescript
// AVOID: Circular dependency
class ServiceA extends MedusaService({}) {
  constructor(container) {
    super(container);
    this.serviceB = container.resolve("serviceB"); // ServiceB depends on ServiceA
  }
}

// SOLUTION: Use lazy resolution
class ServiceA extends MedusaService({}) {
  constructor(container) {
    super(container);
    this.container = container;
  }

  get serviceB() {
    return this.container.resolve("serviceB");
  }
}
```

2. **Register services before resolving:**

```typescript
// Make sure services are registered before API routes are loaded
// In src/api/admin/example/route.ts
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  // Register the service if not already registered
  if (!req.scope.hasRegistration(EXAMPLE_SERVICE)) {
    req.scope.register({
      [EXAMPLE_SERVICE]: {
        resolve: () => new ExampleService(req.scope),
      },
    });
  }

  const service = req.scope.resolve(EXAMPLE_SERVICE);
  // Rest of the implementation
};
```

3. **Check for errors during instantiation:**

```typescript
// Add try-catch when registering services
try {
  container.register({
    [EXAMPLE_SERVICE]: {
      resolve: () => new ExampleService(container),
    },
  });
} catch (error) {
  console.error("Error registering service:", error);
  // Provide a fallback or throw a more descriptive error
}
```

### 3. Service Methods Not Found or Undefined

**Error Message:**

```
TypeError: Cannot read property 'retrieve' of undefined
```

or

```
TypeError: service.retrieve is not a function
```

**Causes:**

- Service is resolved but methods are undefined
- Wrong service implementation is being used
- Inheritance chain issues

**Solutions:**

1. **Check service implementation:**

```typescript
// Make sure methods are properly defined
class ExampleService extends MedusaService({
  Example,
}) {
  async retrieve(id: string): Promise<any> {
    // Implementation
  }
}
```

2. **Check inheritance chain:**

- Ensure the service properly extends MedusaService
- Make sure all required models are passed to MedusaService

3. **Use proper TypeScript interfaces:**

```typescript
interface IExampleService {
  retrieve(id: string): Promise<any>;
  // Other methods
}

// Then use this interface when resolving
const exampleService = req.scope.resolve(EXAMPLE_SERVICE) as IExampleService;
```

## Database-Related Errors

### 1. "relation does not exist" Error

**Error Message:**

```
error: relation "example" does not exist
```

**Causes:**

- Missing migrations
- Model defined but migration not created or run
- Typo in table name

**Solutions:**

1. **Run migrations:**

```bash
# Make sure all migrations are applied
npx medusa migrations run
```

2. **Create missing migrations:**

```bash
# Generate migrations for new models
npx medusa migrations generate
```

3. **Check model definitions:**

```typescript
// Make sure models are properly defined with correct table names
export const Example = model.define("example", {
  id: model.id().primaryKey(),
  // Other fields
});
```

### 2. "column does not exist" Error

**Error Message:**

```
error: column "example_field" of relation "example" does not exist
```

**Causes:**

- Missing column in database
- Migration not applied
- Typo in column name

**Solutions:**

1. **Check model definition:**

```typescript
// Make sure the field is defined in the model
export const Example = model.define("example", {
  id: model.id().primaryKey(),
  example_field: model.text(), // Make sure this field exists
});
```

2. **Generate and run migrations:**

```bash
# Generate migrations for schema changes
npx medusa migrations generate

# Run migrations
npx medusa migrations run
```

### 3. Connection Pool Errors

**Error Message:**

```
Error: Connection terminated unexpectedly
```

or

```
Error: Connection pool full
```

**Causes:**

- Too many connections opened and not released
- Database server issues
- Connection leaks in the code

**Solutions:**

1. **Use connection pooling properly:**

```typescript
// AVOID: Creating new pools for each request
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  // ...
  // No release of connection
};

// SOLUTION: Use the container's database connection
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const manager = req.scope.resolve("manager");
  // Use manager for database operations
};
```

2. **Release connections properly:**

```typescript
// If you must use a direct connection, release it properly
const client = await pool.connect();
try {
  // Use client for queries
} finally {
  client.release();
}
```

3. **Use transactions properly:**

```typescript
await manager.transaction(async (transactionManager) => {
  // Use transactionManager for all database operations
  // Transaction is automatically committed or rolled back
});
```

## API Route Errors

### 1. "Cannot read property 'json' of undefined" Error

**Error Message:**

```
TypeError: Cannot read property 'json' of undefined
```

**Causes:**

- Response object not available
- Async function without proper error handling
- Response sent multiple times

**Solutions:**

1. **Use proper async/await pattern:**

```typescript
// AVOID: Missing await
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const result = service.retrieve(req.params.id); // Missing await
    res.json({ data: result });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// SOLUTION: Proper async/await
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const result = await service.retrieve(req.params.id);
    res.json({ data: result });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
```

2. **Prevent multiple responses:**

```typescript
// AVOID: Multiple responses
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const result = await service.retrieve(req.params.id);
    res.json({ data: result });

    // This will cause an error - headers already sent
    if (result.special) {
      res.status(200).json({ special: true });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// SOLUTION: Single response
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const result = await service.retrieve(req.params.id);

    const response = { data: result };
    if (result.special) {
      response.special = true;
    }

    res.json(response);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
```

### 2. "Headers already sent" Error

**Error Message:**

```
Error: Cannot set headers after they are sent to the client
```

**Causes:**

- Multiple response.send() or response.json() calls
- Async operations continuing after response is sent
- Missing return statements after sending response

**Solutions:**

1. **Add return statements after sending responses:**

```typescript
// AVOID: Missing return
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  if (!req.params.id) {
    res.status(400).json({ message: "ID is required" });
    // Missing return, execution continues
  }

  // This will cause an error if the previous condition was true
  const result = await service.retrieve(req.params.id);
  res.json({ data: result });
};

// SOLUTION: Add return statements
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  if (!req.params.id) {
    return res.status(400).json({ message: "ID is required" });
  }

  const result = await service.retrieve(req.params.id);
  return res.json({ data: result });
};
```

2. **Use early returns for error conditions:**

```typescript
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Validate input early
    if (!req.params.id) {
      return res.status(400).json({ message: "ID is required" });
    }

    // Check permissions early
    if (!hasPermission(req.user, "read_example")) {
      return res.status(403).json({ message: "Forbidden" });
    }

    // Main logic
    const result = await service.retrieve(req.params.id);
    return res.json({ data: result });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};
```

## Workflow Errors

### 1. "Workflow not found" Error

**Error Message:**

```
Error: Workflow with name "exampleWorkflow" not found
```

**Causes:**

- Workflow not registered
- Typo in workflow name
- Import issues
- Incorrect workflow creation pattern

**Solutions:**

1. **Use the correct workflow creation pattern:**

```typescript
// CORRECT: Use createWorkflow from workflows-sdk
import { createWorkflow, WorkflowResponse } from "@camped-ai/framework/workflows-sdk";

export const exampleWorkflow = createWorkflow(
  "example-workflow",
  (input) => {
    // Workflow implementation
    return new WorkflowResponse(result);
  }
);

export default exampleWorkflow;
```

2. **Check workflow imports and usage:**

```typescript
// In API route
import exampleWorkflow from "../../../workflows/example/workflows/example-workflow";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { result } = await exampleWorkflow(req.scope).run({
      input: req.body,
    });
    res.status(200).json({ data: result });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
```

3. **Check for circular dependencies in workflows:**

```typescript
// AVOID: Circular dependencies between workflows
// workflow-a.ts
import workflowB from "./workflow-b";

export const workflowA = createWorkflow(
  "workflow-a",
  (input) => {
    // Uses workflowB
    const result = workflowB.runAsStep({ input });
    return new WorkflowResponse(result);
  }
);

// workflow-b.ts
import workflowA from "./workflow-a"; // Circular dependency!

export const workflowB = createWorkflow(
  "workflow-b",
  (input) => {
    // Uses workflowA
    const result = workflowA.runAsStep({ input });
    return new WorkflowResponse(result);
  }
);

// SOLUTION: Break circular dependencies
// workflow-a.ts
export const workflowA = createWorkflow(
  "workflow-a",
  (input) => {
    // Implementation without direct import of workflowB
    return new WorkflowResponse(result);
  }
);

// workflow-b.ts
export const workflowB = createWorkflow(
  "workflow-b",
  (input) => {
    // Implementation without direct import of workflowA
    return new WorkflowResponse(result);
  }
);

// workflow-orchestrator.ts
import { workflowA } from "./workflow-a";
import { workflowB } from "./workflow-b";

// Orchestrate workflows here
```

### 2. "Step failed" Error

**Error Message:**

```
Error: Workflow step "validateInput" failed: Invalid input data
```

**Causes:**

- Error in workflow step
- Invalid input data
- Missing dependencies
- Service resolution issues in steps

**Solutions:**

1. **Use Zod for input validation:**

```typescript
import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { z } from "zod";

const InputSchema = z.object({
  name: z.string().min(1, "Name is required"),
  price: z.number().positive("Price must be positive"),
  description: z.string().optional(),
});

export const validateInputStep = createStep(
  "validate-input",
  async (input) => {
    try {
      const validatedInput = InputSchema.parse(input);
      return new StepResponse(validatedInput);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors = error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new Error(`Validation failed: ${formattedErrors}`);
      }
      throw error;
    }
  }
);
```

2. **Properly resolve services in steps:**

```typescript
import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { Modules } from "@camped-ai/framework/utils";

export const processDataStep = createStep(
  "process-data",
  async (input, { container }) => {
    try {
      // Check if service is registered
      if (!container.hasRegistration(Modules.PRODUCT)) {
        throw new Error("Product service not registered");
      }

      const productService = container.resolve(Modules.PRODUCT);
      const result = await productService.create(input);

      return new StepResponse(result);
    } catch (error) {
      throw new Error(`Failed to process data: ${error.message}`);
    }
  }
);
```

3. **Use proper error handling with MedusaError:**

```typescript
import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { MedusaError } from "@camped-ai/framework/utils";

export const processDataStep = createStep(
  "process-data",
  async (input, { container }) => {
    try {
      // Process data
      const result = await someProcessingFunction(input);
      return new StepResponse(result);
    } catch (error) {
      // Convert to MedusaError for better error handling
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `Failed to process data: ${error.message}`
      );
    }
  }
);
```

### 3. "Cannot access property of undefined" in Workflow

**Error Message:**

```
TypeError: Cannot read property 'id' of undefined
```

**Causes:**

- Missing step return values
- Incorrect step response format
- Not using StepResponse

**Solutions:**

1. **Always use StepResponse in steps:**

```typescript
// AVOID: Not using StepResponse
export const badStep = createStep(
  "bad-step",
  async (input) => {
    return { id: input.id }; // Missing StepResponse wrapper
  }
);

// SOLUTION: Use StepResponse
export const goodStep = createStep(
  "good-step",
  async (input) => {
    return new StepResponse({ id: input.id });
  }
);
```

2. **Check step return values:**

```typescript
// In your workflow
const result = someStep(input);

// Add validation before using result
if (!result || !result.id) {
  throw new Error("Step returned invalid data");
}

// Continue with workflow
const nextResult = nextStep(result.id);
```

3. **Use optional chaining and default values:**

```typescript
// In your workflow
const result = someStep(input);

// Use optional chaining and default values
const id = result?.id || "default-id";
const nextResult = nextStep(id);
```

### 4. Workflow Execution Timeout

**Error Message:**

```
Error: Workflow execution timed out after 30000ms
```

**Causes:**

- Long-running operations in steps
- Infinite loops
- Network or database issues

**Solutions:**

1. **Set appropriate timeout values:**

```typescript
// Set timeout for workflow execution
export const exampleWorkflow = createWorkflow(
  {
    name: "example-workflow",
    timeout: 60000, // 60 seconds
  },
  (input) => {
    // Workflow implementation
    return new WorkflowResponse(result);
  }
);
```

2. **Break down long-running steps:**

```typescript
// AVOID: One large step that does too much
export const bigStep = createStep(
  "big-step",
  async (input) => {
    // Lots of processing...
    return new StepResponse(result);
  }
);

// SOLUTION: Break into smaller steps
export const step1 = createStep(
  "step-1",
  async (input) => {
    // First part of processing
    return new StepResponse(intermediateResult);
  }
);

export const step2 = createStep(
  "step-2",
  async (input) => {
    // Second part of processing
    return new StepResponse(finalResult);
  }
);

// In workflow
const intermediate = step1(input);
const final = step2(intermediate);
```

3. **Use parallelize for independent operations:**

```typescript
import { parallelize } from "@camped-ai/framework/workflows-sdk";

// In workflow
const [result1, result2, result3] = parallelize(
  step1(input),
  step2(input),
  step3(input)
);
```

## Dependency Injection Errors

### 1. Circular Dependency Errors

**Error Message:**

```
Error: Circular dependency detected: serviceA -> serviceB -> serviceA
```

**Causes:**

- Services depending on each other
- Modules importing each other

**Solutions:**

1. **Use lazy loading:**

```typescript
// AVOID: Direct dependency
class ServiceA extends MedusaService({}) {
  constructor(container, @Inject("serviceB") private readonly serviceB: any) {
    super(container);
  }
}

// SOLUTION: Lazy loading
class ServiceA extends MedusaService({}) {
  constructor(container) {
    super(container);
    this.container = container;
  }

  get serviceB() {
    return this.container.resolve("serviceB");
  }
}
```

2. **Create an intermediary service:**

```typescript
// Create an intermediary service that both services can depend on
class IntermediaryService extends MedusaService({}) {
  // Common functionality
}

// Then have both services depend on the intermediary
class ServiceA extends MedusaService({}) {
  constructor(
    container,
    @Inject("intermediaryService") private readonly intermediary: any
  ) {
    super(container);
  }
}

class ServiceB extends MedusaService({}) {
  constructor(
    container,
    @Inject("intermediaryService") private readonly intermediary: any
  ) {
    super(container);
  }
}
```

### 2. "Cannot inject dependency" Error

**Error Message:**

```
Error: Cannot inject dependency "exampleService" into "otherService"
```

**Causes:**

- Dependency not registered
- Dependency registered after injection attempt
- Error during dependency instantiation

**Solutions:**

1. **Register dependencies in the correct order:**

```typescript
// Make sure dependencies are registered before services that use them
container.register({
  [DEPENDENCY_SERVICE]: {
    resolve: () => new DependencyService(container),
  },
});

container.register({
  [MAIN_SERVICE]: {
    resolve: () => new MainService(container),
  },
});
```

2. **Use optional dependencies:**

```typescript
class MainService extends MedusaService({}) {
  constructor(
    container,
    @Inject("optionalService", { optional: true })
    private readonly optionalService?: any
  ) {
    super(container);
  }

  async someMethod() {
    if (this.optionalService) {
      // Use the optional service
    } else {
      // Fallback behavior
    }
  }
}
```

## UI and Component Errors

### 1. "Icon Error Card Not Found" Error

**Error Message:**

```
Error: Icon card not found
```

or

```
Cannot find icon with name "..."
```

**Causes:**

- Missing icon import
- Incorrect icon name
- Icon library not properly loaded
- Path resolution issues with icon components

**Solutions:**

1. **Check icon imports:**

```tsx
// AVOID: Missing or incorrect imports
import { Icon } from "@camped-ai/components";

// Component usage
<Icon name="nonexistent-icon" />

// SOLUTION: Ensure proper imports and use available icons
import { Icon } from "@camped-ai/components";
// Import specific icons if needed
import { ShoppingBagIcon } from "@camped-ai/icons";

// Component usage with correct icon name
<Icon name="shopping-bag" />
// Or use the imported icon component directly
<ShoppingBagIcon />
```

2. **Verify icon availability:**

```tsx
// Create a helper function to check icon availability
const isIconAvailable = (iconName: string): boolean => {
  // List of available icons in your system
  const availableIcons = [
    "shopping-bag",
    "user",
    "settings",
    "hotel",
    "destination",
    "calendar",
    // Add other available icons
  ];

  return availableIcons.includes(iconName);
};

// Then use it in your component
const IconWithFallback = ({ name, ...props }) => {
  const iconExists = isIconAvailable(name);

  if (!iconExists) {
    console.warn(`Icon "${name}" not found, using fallback`);
    return <DefaultIcon {...props} />;
  }

  return <Icon name={name} {...props} />;
};
```

3. **Use icon mapping:**

```tsx
// Create a mapping for icons that might have different names
const ICON_MAPPING = {
  hotel: "building-office",
  booking: "calendar",
  "user-profile": "user",
  // Add other mappings as needed
};

// Use the mapping in your component
const MappedIcon = ({ name, ...props }) => {
  const mappedName = ICON_MAPPING[name] || name;
  return <Icon name={mappedName} {...props} />;
};
```

4. **Ensure proper path resolution:**

```tsx
// If using dynamic imports for icons, ensure paths are correct
const DynamicIcon = ({ name, ...props }) => {
  const [IconComponent, setIconComponent] = useState(null);

  useEffect(() => {
    const loadIcon = async () => {
      try {
        // Make sure the path is correct
        const module = await import(`@camped-ai/icons/${name}`);
        setIconComponent(() => module.default || module[`${name}Icon`]);
      } catch (error) {
        console.error(`Failed to load icon: ${name}`, error);
        // Load a fallback icon
        const fallback = await import(
          "@camped-ai/icons/QuestionMarkCircleIcon"
        );
        setIconComponent(() => fallback.default);
      }
    };

    loadIcon();
  }, [name]);

  if (!IconComponent) {
    return <div className="icon-placeholder" {...props} />;
  }

  return <IconComponent {...props} />;
};
```

## Conclusion

This document covers the most common errors encountered when working with the Medusa v2 codebase and provides practical solutions for each. If you encounter an error not listed here, consider adding it to this document along with the solution for future reference.

Remember that most errors in Medusa v2 are related to:

1. Service registration and resolution
2. Database connections and queries
3. API route implementation
4. Workflow configuration
5. Dependency injection
6. UI components and icon rendering

By following the best practices outlined in the tasks.md file and using the solutions provided here, you can avoid most of these errors and build a more robust application.
