# Aggregator Pricing Implementation Plan

This document outlines the plan for enhancing the hotel management system to support both aggregator/reseller business models and direct hotel operators within the same platform.

## Overview

The current system is designed primarily for direct hotel operators who manage their own properties and set their own prices. We need to enhance it to support aggregators/resellers who:

1. Purchase inventory from hotels at a contracted cost price
2. Apply their own margins to determine selling prices
3. Need to track profitability and margins across their portfolio

This enhancement will be implemented without disrupting the existing functionality for direct operators.

## Current System Analysis

### Existing Pricing Structure

The current pricing system includes:

- **Base Pricing**: Set through `BasePricingTab` and `BasePricingModal` components
- **Seasonal Pricing**: Managed through `SeasonalPricingTab` and related components
- **Pricing Rules**: Dynamic rules that adjust prices based on conditions
- **Special Offers**: Discounts and promotions

### Data Models

Pricing data is currently stored in:

- Medusa's native `money_amount` table for base prices
- Medusa's `price_list` and related tables for seasonal pricing
- Custom tables for pricing rules and special offers

### Workflows

- `update-room-pricing.ts` workflow handles price updates
- `calculate-custom-price` workflow calculates final prices

## Implementation Plan

### Phase 1: Core Data Model and Settings

#### Task 1.1: Create Supplier Contract Models

**Files to create:**
- `src/modules/hotel-management/supplier/models/supplier-contract.ts`
- `src/modules/hotel-management/supplier/models/contract-rate.ts`
- `src/modules/hotel-management/supplier/index.ts`
- `src/modules/hotel-management/supplier/service.ts`
- `src/modules/hotel-management/supplier/services/supplier-contract.ts`
- `src/modules/hotel-management/supplier/services/contract-rate.ts`

**Implementation details:**
- Create model definitions with appropriate fields and relationships
- Set up indexes for efficient querying
- Register models with the module system
- Create service classes with CRUD operations

**Sample model implementation:**
```typescript
// New model: supplier_contract
export const SupplierContract = model.define("supplier_contract", {
  id: model.id({ prefix: "supp_cont" }).primaryKey(),
  supplier_id: model.text().index(),
  hotel_id: model.text().index(),
  start_date: model.dateTime(),
  end_date: model.dateTime(),
  payment_terms: model.text().nullable(),
  currency_code: model.text(),
  is_active: model.boolean().default(true),
  contract_file_url: model.text().nullable(),
  notes: model.text().nullable(),
  metadata: model.json().nullable()
});

// New model: contract_rate
export const ContractRate = model.define("contract_rate", {
  id: model.id({ prefix: "cont_rate" }).primaryKey(),
  contract_id: model.text().index(),
  room_config_id: model.text().index(),
  start_date: model.dateTime(),
  end_date: model.dateTime(),
  cost_price: model.number(),
  currency_code: model.text(),
  conditions: model.text().nullable(),
  metadata: model.json().nullable()
});
```

#### Task 1.2: Create Organization Settings Model

**Files to create/modify:**
- `src/modules/organization-settings/models/organization-settings.ts`
- `src/modules/organization-settings/index.ts`
- `src/modules/organization-settings/service.ts`
- `medusa-config.ts` (add new module)

**Implementation details:**
- Create model with business model enum and margin settings
- Create service with methods to get/update settings
- Register module in medusa-config.ts
- Add migration script for the new table

**Sample model implementation:**
```typescript
// Add to store model or create a new organization_settings model
export const OrganizationSettings = model.define("organization_settings", {
  id: model.id().primaryKey(),
  store_id: model.text().unique(),
  business_model: model.enum(["direct_operator", "aggregator", "hybrid"]).default("direct_operator"),
  default_margin_type: model.enum(["percentage", "fixed"]).default("percentage"),
  default_margin_value: model.number().default(0),
  metadata: model.json().nullable()
});
```

#### Task 1.3: Create Organization Settings API

**Files to create:**
- `src/api/admin/organization-settings/route.ts`

**Implementation details:**
- Create GET endpoint to retrieve settings
- Create PUT endpoint to update settings
- Add validation for input data
- Implement proper error handling

#### Task 1.4: Create Organization Settings UI

**Files to create:**
- `src/admin/routes/settings/organization/page.tsx`
- `src/admin/components/settings/organization-settings-form.tsx`

**Implementation details:**
- Create settings page with form for business model selection
- Add radio buttons for business model type
- Add fields for default margin configuration
- Implement save functionality with API integration

#### Task 1.5: Create Database Migrations

**Files to create:**
- `src/modules/organization-settings/migrations/Migration20250501000000.ts`
- `src/modules/hotel-management/supplier/migrations/Migration20250501000001.ts`

**Implementation details:**
- Create migration scripts for new tables
- Add default values for existing stores
- Ensure backward compatibility

### Phase 2: Pricing Enhancements

#### Task 2.1: Enhance Pricing APIs

**Files to modify:**
- `src/api/admin/hotel-management/hotels/[id]/pricing/route.ts`
- `src/api/admin/hotel-management/rooms/[id]/pricing/route.ts`
- `src/workflows/hotel-management/room/update-room-pricing.ts`

**Implementation details:**
- Update API endpoints to accept cost price and margin parameters
- Modify workflows to store cost and margin in metadata
- Add logic to calculate selling price based on cost and margin
- Ensure backward compatibility for direct operators

#### Task 2.2: Update Base Pricing Components

**Files to modify:**
- `src/admin/components/hotel/pricing/base-pricing-tab.tsx`
- `src/admin/components/hotel/pricing/base-pricing-modal.tsx`
- `src/admin/components/hotel/pricing/base-pricing-form.tsx`

**Implementation details:**
- Add conditional rendering based on business model
- Add cost price input field
- Add margin type selector (percentage/fixed)
- Add margin value input
- Add calculated selling price display
- Implement real-time calculation as user inputs values

#### Task 2.3: Update Seasonal Pricing Components

**Files to modify:**
- `src/admin/components/hotel/pricing/seasonal-pricing-tab.tsx`
- `src/admin/components/hotel/pricing/seasonal-pricing-form.tsx`
- `src/admin/components/hotel/pricing/seasonal-pricing-calendar.tsx`

**Implementation details:**
- Add cost price fields to seasonal pricing form
- Update calendar view to optionally show margins
- Modify data handling to include cost and margin
- Update UI to show profitability indicators

#### Task 2.4: Create Margin Calculation Utilities

**Files to create:**
- `src/utils/margin-calculator.ts`

**Implementation details:**
- Create utility functions for margin calculations
- Implement functions to convert between cost, margin, and selling price
- Add validation and error handling
- Create helper functions for formatting margin displays

**Sample implementation:**
```typescript
export class MarginCalculator {
  /**
   * Calculate selling price based on cost price and margin
   */
  static calculateSellingPrice(
    costPrice: number,
    marginValue: number,
    marginType: MarginType
  ): number {
    if (marginType === MarginType.PERCENTAGE) {
      // For percentage margin, add the percentage to the cost price
      return costPrice + (costPrice * (marginValue / 100));
    } else {
      // For fixed margin, simply add the fixed amount
      return costPrice + marginValue;
    }
  }
  
  /**
   * Calculate margin percentage based on cost and selling price
   */
  static calculateMarginPercentage(
    costPrice: number,
    sellingPrice: number
  ): number {
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }
  
  /**
   * Calculate cost price based on selling price and margin
   */
  static calculateCostPrice(
    sellingPrice: number,
    marginValue: number,
    marginType: MarginType
  ): number {
    if (marginType === MarginType.PERCENTAGE) {
      return sellingPrice / (1 + (marginValue / 100));
    } else {
      return sellingPrice - marginValue;
    }
  }
}
```

### Phase 3: Supplier Management

#### Task 3.1: Create Supplier Contract APIs

**Files to create:**
- `src/api/admin/hotel-management/supplier-contracts/route.ts`
- `src/api/admin/hotel-management/supplier-contracts/[id]/route.ts`
- `src/api/admin/hotel-management/supplier-contracts/[id]/rates/route.ts`
- `src/api/admin/hotel-management/supplier-contracts/[id]/rates/[rateId]/route.ts`

**Implementation details:**
- Implement CRUD operations for contracts
- Implement CRUD operations for contract rates
- Add validation for input data
- Implement proper error handling and responses

#### Task 3.2: Create Supplier Contract UI

**Files to create:**
- `src/admin/routes/hotel-management/supplier-contracts/page.tsx`
- `src/admin/routes/hotel-management/supplier-contracts/[id]/page.tsx`
- `src/admin/components/supplier/contract-list.tsx`
- `src/admin/components/supplier/contract-form.tsx`
- `src/admin/components/supplier/contract-rate-form.tsx`
- `src/admin/components/supplier/contract-rate-list.tsx`

**Implementation details:**
- Create list view for all supplier contracts
- Create detail view for individual contracts
- Implement forms for creating/editing contracts
- Implement forms for managing contract rates
- Add date range pickers for contract validity periods

#### Task 3.3: Create Contract Rate Import/Export

**Files to create:**
- `src/admin/components/supplier/contract-rate-import.tsx`
- `src/admin/components/supplier/contract-rate-export.tsx`
- `src/api/admin/hotel-management/supplier-contracts/[id]/rates/import/route.ts`
- `src/api/admin/hotel-management/supplier-contracts/[id]/rates/export/route.ts`

**Implementation details:**
- Create UI for importing rates from CSV/Excel
- Create UI for exporting rates to CSV/Excel
- Implement server-side import/export handlers
- Add validation and error handling for imports

### Phase 4: Analytics and Advanced Features

#### Task 4.1: Create Margin Analytics Components

**Files to create:**
- `src/admin/components/analytics/margin-overview-card.tsx`
- `src/admin/components/analytics/margin-by-hotel-chart.tsx`
- `src/admin/components/analytics/margin-trend-chart.tsx`
- `src/admin/components/analytics/cost-vs-revenue-chart.tsx`

**Implementation details:**
- Create overview card with key margin metrics
- Implement bar chart for margin by hotel
- Create line chart for margin trends over time
- Implement stacked area chart for cost vs. revenue

#### Task 4.2: Enhance Hotel Analytics Dashboard

**Files to modify:**
- `src/admin/routes/hotel-management/analytics/page.tsx` (create if not exists)
- `src/api/admin/hotel-management/analytics/margins/route.ts` (create)

**Implementation details:**
- Add margin analytics section to dashboard
- Create API endpoints for margin data
- Implement conditional rendering based on business model
- Integrate with existing analytics components

#### Task 4.3: Implement Margin Rules Engine

**Files to create:**
- `src/modules/hotel-management/supplier/models/margin-rule.ts`
- `src/modules/hotel-management/supplier/services/margin-rule.ts`
- `src/api/admin/hotel-management/margin-rules/route.ts`
- `src/admin/components/supplier/margin-rules-tab.tsx`
- `src/admin/components/supplier/margin-rules-form.tsx`

**Implementation details:**
- Create data model for margin rules
- Implement rule evaluation logic
- Create UI for managing margin rules
- Integrate with pricing calculation workflow

## Technical Considerations

### Backward Compatibility

- All changes must maintain compatibility with existing data
- Direct operator mode should work exactly as before
- Use feature flags to control visibility of aggregator features

### Performance

- Optimize queries to handle additional pricing calculations
- Consider caching for frequently accessed margin data

### Security

- Ensure cost prices are only visible to authorized users
- Add permission checks for aggregator-specific features

## Conclusion

This implementation plan provides a comprehensive approach to adding aggregator functionality to the hotel management system while maintaining compatibility with the existing direct operator model. By following this phased approach, we can ensure a smooth transition and minimize disruption to existing users.

The plan includes detailed tasks for each phase, with specific files to create or modify and implementation details. The use of metadata fields for storing cost and margin data ensures compatibility with the existing database schema, while the conditional rendering based on business model type ensures that direct operators continue to see the familiar interface.
