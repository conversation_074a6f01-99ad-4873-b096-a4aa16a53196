# Workflow Best Practices in Medusa v2

This document provides best practices for implementing workflows in Medusa v2. Workflows are the recommended way to implement business logic in Medusa applications.

## Workflow Structure

### 1. Organize Workflows by Domain

Organize workflows by domain in a clear directory structure:

```
src/
  workflows/
    hotel-management/
      availability/
        workflows/
          check-hotel-availability.ts
        steps/
          validate-availability-params.ts
          get-hotel-details.ts
          check-room-availability.ts
          calculate-prices.ts
          format-response.ts
    order-management/
      workflows/
        create-order.ts
      steps/
        validate-order-input.ts
        create-order.ts
        process-payment.ts
```

### 2. Create Focused Workflows

Each workflow should have a single responsibility:

```typescript
// GOOD: Focused workflow
export const checkHotelAvailabilityWorkflow = createWorkflow(
  "check-hotel-availability",
  (input: AvailabilityInput) => {
    // Validate input
    const validatedParams = validateAvailabilityParamsStep(input);
    
    // Get hotel details
    const hotel = getHotelDetailsStep(validatedParams.hotel_id);
    
    // Get room configurations
    const roomConfigs = getRoomConfigurationsStep({
      hotel_id: validatedParams.hotel_id,
    });
    
    // Check availability for each room
    const availabilityResults = checkRoomAvailabilityStep({
      room_configurations: roomConfigs,
      check_in_date: validatedParams.check_in_date,
      check_out_date: validatedParams.check_out_date,
    });
    
    // Calculate prices
    const pricedRooms = calculatePricesStep({
      availability_results: availabilityResults,
      currency_code: validatedParams.currency_code,
      nights: validatedParams.nights,
    });
    
    // Format response
    const response = formatResponseStep({
      hotel,
      priced_rooms: pricedRooms,
    });
    
    return new WorkflowResponse(response);
  }
);
```

### 3. Use Typed Inputs and Outputs

Always define types for workflow inputs and outputs:

```typescript
type CheckAvailabilityInput = {
  hotel_id: string;
  check_in_date: string;
  check_out_date: string;
  adults: number;
  children?: number;
  infants?: number;
  currency_code?: string;
};

type CheckAvailabilityOutput = {
  hotel: {
    id: string;
    name: string;
    // Other hotel properties
  };
  available_rooms: Array<{
    id: string;
    name: string;
    price: {
      amount: number;
      currency_code: string;
      formatted: string;
    };
    // Other room properties
  }>;
};

export const checkAvailabilityWorkflow = createWorkflow<
  CheckAvailabilityInput,
  CheckAvailabilityOutput
>("check-availability", (input) => {
  // Workflow implementation
  return new WorkflowResponse(output);
});
```

## Step Implementation

### 1. Create Focused Steps

Each step should have a single responsibility:

```typescript
// GOOD: Focused step
export const validateAvailabilityParamsStep = createStep(
  "validate-availability-params",
  async (input: CheckAvailabilityInput) => {
    // Validate input
    const schema = z.object({
      hotel_id: z.string().min(1, "Hotel ID is required"),
      check_in_date: z.string().refine(
        (date) => isValidDate(date),
        "Invalid check-in date"
      ),
      check_out_date: z.string().refine(
        (date) => isValidDate(date),
        "Invalid check-out date"
      ),
      adults: z.number().min(1, "At least one adult is required"),
      children: z.number().optional(),
      infants: z.number().optional(),
      currency_code: z.string().optional().default("USD"),
    });
    
    try {
      const validatedInput = schema.parse(input);
      
      // Additional validation logic
      const checkInDate = new Date(validatedInput.check_in_date);
      const checkOutDate = new Date(validatedInput.check_out_date);
      
      if (checkInDate >= checkOutDate) {
        throw new Error("Check-out date must be after check-in date");
      }
      
      // Calculate nights
      const nights = differenceInDays(checkOutDate, checkInDate);
      
      return new StepResponse({
        ...validatedInput,
        nights,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new Error(`Validation failed: ${formattedErrors}`);
      }
      throw error;
    }
  }
);
```

### 2. Properly Resolve Services

Always resolve services from the container in steps:

```typescript
export const getHotelDetailsStep = createStep(
  "get-hotel-details",
  async (hotelId: string, { container }) => {
    try {
      // Resolve the hotel service from the container
      const hotelService = container.resolve("hotelService");
      
      // Get hotel details
      const hotel = await hotelService.retrieve(hotelId, {
        relations: ["address", "images"],
      });
      
      if (!hotel) {
        throw new Error(`Hotel with ID ${hotelId} not found`);
      }
      
      return new StepResponse(hotel);
    } catch (error) {
      throw new Error(`Failed to get hotel details: ${error.message}`);
    }
  }
);
```

### 3. Use Proper Error Handling

Use proper error handling in steps:

```typescript
export const processPaymentStep = createStep(
  "process-payment",
  async (input, { container }) => {
    try {
      const paymentService = container.resolve(Modules.PAYMENT);
      
      const result = await paymentService.capturePayment(input.payment_id);
      
      return new StepResponse({
        payment_status: result.status,
        payment_data: result.data,
      });
    } catch (error) {
      // Log the error
      console.error("Payment processing failed:", error);
      
      // Convert to MedusaError for better error handling
      throw new MedusaError(
        MedusaError.Types.PAYMENT_ERROR,
        `Failed to process payment: ${error.message}`
      );
    }
  }
);
```

## Advanced Workflow Patterns

### 1. Parallel Processing

Use parallelize for operations that can run concurrently:

```typescript
export const createProductWithVariantsWorkflow = createWorkflow(
  "create-product-with-variants",
  (input) => {
    // Create the product
    const product = createProductStep(input.product);
    
    // Create variants and options in parallel
    const [variants, options] = parallelize(
      createVariantsStep({
        product_id: product.id,
        variants: input.variants,
      }),
      createOptionsStep({
        product_id: product.id,
        options: input.options,
      })
    );
    
    // Create prices for variants
    const variantsWithPrices = createPricesStep({
      variants,
      prices: input.prices,
    });
    
    // Get the complete product
    const completeProduct = getProductStep(product.id);
    
    return new WorkflowResponse(completeProduct);
  }
);
```

### 2. Conditional Execution

Use when-then for conditional execution:

```typescript
import { when } from "@camped-ai/framework/workflows-sdk";

export const processOrderWorkflow = createWorkflow(
  "process-order",
  (input) => {
    // Create the order
    const order = createOrderStep(input);
    
    // Conditionally process payment
    when(order, (o) => o.requires_payment)
      .then(() => {
        return processPaymentStep({
          order_id: order.id,
          amount: order.total,
        });
      });
    
    // Conditionally create fulfillment
    when(order, (o) => o.requires_shipping)
      .then(() => {
        return createFulfillmentStep({
          order_id: order.id,
          items: order.items,
        });
      });
    
    return new WorkflowResponse(order);
  }
);
```

### 3. Workflow Hooks

Add hooks to trigger events after workflow completion:

```typescript
export const createBookingWorkflow = createWorkflow(
  "create-booking",
  (input) => {
    // Create the booking
    const booking = createBookingStep(input);
    
    // Update inventory
    const updatedInventory = updateInventoryStep({
      room_id: booking.room_id,
      check_in_date: booking.check_in_date,
      check_out_date: booking.check_out_date,
      status: "reserved",
    });
    
    // Create a hook for the booking created event
    const bookingCreatedHook = createHook(
      "booking-created",
      {
        booking_id: booking.id,
        room_id: booking.room_id,
        check_in_date: booking.check_in_date,
        check_out_date: booking.check_out_date,
      }
    );
    
    return new WorkflowResponse(booking, {
      hooks: [bookingCreatedHook],
    });
  }
);
```

### 4. Execute Other Workflows

Execute other workflows from within a workflow:

```typescript
export const createHotelBookingWorkflow = createWorkflow(
  "create-hotel-booking",
  (input) => {
    // Check availability
    const availability = checkAvailabilityWorkflow.runAsStep({
      input: {
        hotel_id: input.hotel_id,
        check_in_date: input.check_in_date,
        check_out_date: input.check_out_date,
        adults: input.adults,
        children: input.children,
        infants: input.infants,
      },
    });
    
    // Create booking if room is available
    if (!availability.available_rooms.length) {
      throw new Error("No available rooms for the selected dates");
    }
    
    // Create the booking
    const booking = createBookingStep({
      hotel_id: input.hotel_id,
      room_id: availability.available_rooms[0].id,
      check_in_date: input.check_in_date,
      check_out_date: input.check_out_date,
      guest_name: input.guest_name,
      guest_email: input.guest_email,
      total_amount: availability.available_rooms[0].price.amount,
      currency_code: availability.available_rooms[0].price.currency_code,
    });
    
    // Create order for the booking
    const order = createOrderWorkflow.runAsStep({
      input: {
        email: input.guest_email,
        items: [
          {
            variant_id: booking.room_id,
            quantity: 1,
            metadata: {
              booking_id: booking.id,
              check_in_date: booking.check_in_date,
              check_out_date: booking.check_out_date,
            },
          },
        ],
        metadata: {
          booking_id: booking.id,
        },
      },
    });
    
    // Link the order to the booking
    const linkedBooking = linkOrderToBookingStep({
      booking_id: booking.id,
      order_id: order.id,
    });
    
    return new WorkflowResponse({
      booking: linkedBooking,
      order,
    });
  }
);
```

## Testing Workflows

### 1. Unit Testing Steps

Unit test individual steps:

```typescript
describe("validateAvailabilityParamsStep", () => {
  it("should validate and transform input", async () => {
    const input = {
      hotel_id: "hotel_123",
      check_in_date: "2023-01-01",
      check_out_date: "2023-01-05",
      adults: 2,
      children: 1,
      infants: 0,
      currency_code: "USD",
    };
    
    const result = await validateAvailabilityParamsStep(input);
    
    expect(result).toEqual({
      ...input,
      nights: 4,
    });
  });
  
  it("should throw error for invalid dates", async () => {
    const input = {
      hotel_id: "hotel_123",
      check_in_date: "invalid-date",
      check_out_date: "2023-01-05",
      adults: 2,
    };
    
    await expect(validateAvailabilityParamsStep(input)).rejects.toThrow(
      "Validation failed: check_in_date: Invalid check-in date"
    );
  });
});
```

### 2. Integration Testing Workflows

Integration test complete workflows:

```typescript
describe("checkAvailabilityWorkflow", () => {
  let container;
  
  beforeEach(() => {
    container = createMedusaContainer();
    
    // Mock services
    const mockHotelService = {
      retrieve: jest.fn().mockResolvedValue({
        id: "hotel_123",
        name: "Test Hotel",
      }),
    };
    
    const mockRoomService = {
      list: jest.fn().mockResolvedValue([
        {
          id: "room_123",
          name: "Deluxe Room",
        },
      ]),
    };
    
    container.register({
      hotelService: { resolve: () => mockHotelService },
      roomService: { resolve: () => mockRoomService },
    });
  });
  
  it("should check availability and return available rooms", async () => {
    const input = {
      hotel_id: "hotel_123",
      check_in_date: "2023-01-01",
      check_out_date: "2023-01-05",
      adults: 2,
      currency_code: "USD",
    };
    
    const { result } = await checkAvailabilityWorkflow(container).run({
      input,
    });
    
    expect(result.hotel.id).toBe("hotel_123");
    expect(result.available_rooms).toHaveLength(1);
    expect(result.available_rooms[0].id).toBe("room_123");
  });
});
```

## Common Pitfalls to Avoid

### 1. Avoid Direct Database Access

Don't use direct database queries in workflows or steps:

```typescript
// AVOID: Direct database access
export const badStep = createStep(
  "bad-step",
  async (input, { container }) => {
    const manager = container.resolve("manager");
    const result = await manager.query(
      "SELECT * FROM products WHERE id = $1",
      [input.id]
    );
    return new StepResponse(result[0]);
  }
);

// GOOD: Use services
export const goodStep = createStep(
  "good-step",
  async (input, { container }) => {
    const productService = container.resolve(Modules.PRODUCT);
    const product = await productService.retrieve(input.id);
    return new StepResponse(product);
  }
);
```

### 2. Avoid Large, Monolithic Steps

Break down large steps into smaller, focused steps:

```typescript
// AVOID: Large, monolithic step
export const badStep = createStep(
  "bad-step",
  async (input, { container }) => {
    // Validate input
    if (!input.name) {
      throw new Error("Name is required");
    }
    
    // Create product
    const productService = container.resolve(Modules.PRODUCT);
    const product = await productService.create({
      title: input.name,
      description: input.description,
    });
    
    // Create variants
    const variantService = container.resolve(Modules.PRODUCT_VARIANT);
    const variants = [];
    for (const variantData of input.variants) {
      const variant = await variantService.create({
        product_id: product.id,
        title: variantData.title,
        sku: variantData.sku,
      });
      variants.push(variant);
    }
    
    // Create prices
    const priceService = container.resolve(Modules.PRICING);
    for (const variant of variants) {
      await priceService.createPriceSet({
        variant_id: variant.id,
        prices: input.prices,
      });
    }
    
    return new StepResponse({
      product,
      variants,
    });
  }
);

// GOOD: Break down into smaller steps
export const validateInputStep = createStep(
  "validate-input",
  async (input) => {
    // Validation logic
    return new StepResponse(validatedInput);
  }
);

export const createProductStep = createStep(
  "create-product",
  async (input, { container }) => {
    // Create product logic
    return new StepResponse(product);
  }
);

export const createVariantsStep = createStep(
  "create-variants",
  async (input, { container }) => {
    // Create variants logic
    return new StepResponse(variants);
  }
);

export const createPricesStep = createStep(
  "create-prices",
  async (input, { container }) => {
    // Create prices logic
    return new StepResponse(variantsWithPrices);
  }
);
```

### 3. Avoid Circular Dependencies

Avoid circular dependencies between workflows:

```typescript
// AVOID: Circular dependencies
// workflow-a.ts
import workflowB from "./workflow-b";

export const workflowA = createWorkflow(
  "workflow-a",
  (input) => {
    // Uses workflowB
    const result = workflowB.runAsStep({ input });
    return new WorkflowResponse(result);
  }
);

// workflow-b.ts
import workflowA from "./workflow-a"; // Circular dependency!

export const workflowB = createWorkflow(
  "workflow-b",
  (input) => {
    // Uses workflowA
    const result = workflowA.runAsStep({ input });
    return new WorkflowResponse(result);
  }
);

// GOOD: Break circular dependencies
// workflow-a.ts
export const workflowA = createWorkflow(
  "workflow-a",
  (input) => {
    // Implementation without direct import of workflowB
    return new WorkflowResponse(result);
  }
);

// workflow-b.ts
export const workflowB = createWorkflow(
  "workflow-b",
  (input) => {
    // Implementation without direct import of workflowA
    return new WorkflowResponse(result);
  }
);

// workflow-orchestrator.ts
import { workflowA } from "./workflow-a";
import { workflowB } from "./workflow-b";

// Orchestrate workflows here
```

## Conclusion

By following these best practices, you'll create workflows that are:

1. **Maintainable**: Easy to understand, modify, and extend
2. **Testable**: Easy to test in isolation and as part of integration tests
3. **Scalable**: Can handle complex business logic without becoming unwieldy
4. **Reliable**: Properly handle errors and edge cases

Remember that workflows are the recommended way to implement business logic in Medusa applications. They provide a structured approach to handling complex operations and ensure proper separation of concerns.
