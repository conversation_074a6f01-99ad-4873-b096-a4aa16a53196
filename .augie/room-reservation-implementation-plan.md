# Room Reservation Implementation Plan for Unassigned Bookings

## Overview

This document outlines a technical solution to prevent double bookings when bookings are created at the room configuration level without immediately assigning specific rooms. The solution leverages the existing room inventory system and handles both single-room bookings and scenarios where a stay requires multiple rooms on different days.

## Implementation Status

This document has been updated to reflect the current implementation status of each task:

- ✅ Completed
- 🔄 In Progress
- ❌ Not Started

The implementation is currently in progress, with some key components already implemented and others still pending.

## Current System Analysis

### Existing Components

1. **Room Inventory System**
   - Uses `room_inventory` table with `from_date` and `to_date` fields
   - Tracks availability status for individual rooms
   - Supports statuses like "available", "booked", "reserved", "maintenance"

2. **Booking System**
   - Bookings can be created at room configuration level
   - Room assignment can happen later by staff
   - Uses `booking_order` table to store booking information

3. **Cart System**
   - Standard Medusa cart functionality
   - Cart completion converts to orders
   - No current mechanism to temporarily reserve rooms during checkout

### Current Gaps

1. **Unassigned Bookings**: When a booking is created at the room configuration level without assigning a specific room, the system doesn't reserve any room in the inventory. This can lead to overbooking if multiple users book the same room configuration when there's limited availability.

2. **Fragmented Availability**: The system cannot handle scenarios where no single room is available for the entire stay, but the stay could be accommodated using different rooms on different days (e.g., Room 1 for day 1, Room 2 for days 2-4).

## Implementation Plan

### Phase 1: Extend Room Inventory Status

**Task 1.1: Add New Inventory Status Values** ❌ Not Started
- Add `reserved_unassigned` status for rooms reserved by bookings without specific assignment
- Add `cart_reserved` status for rooms temporarily reserved during checkout
- Update any UI components that display room status

**Current Status**: The room inventory model has a status field with default value 'available', but the new status values (`reserved_unassigned` and `cart_reserved`) have not been added yet. Currently, the system only supports 'available', 'reserved', 'booked', 'maintenance', and 'cleaning' statuses.

**Task 1.2: Update Room Inventory Service** ❌ Not Started
- Modify `updateInventoryStatus` method to support new status values
- Add validation for status transitions
- Ensure backward compatibility with existing code

**Current Status**: The `updateInventoryStatus` method in the RoomInventoryService currently validates against a fixed list of statuses: 'available', 'reserved', 'booked', 'maintenance', 'cleaning'. This needs to be updated to include the new status values.

### Phase 2: Implement Unassigned Booking Reservation

**Task 2.1: Modify Booking Creation Process** 🔄 In Progress
- Create a helper function to find an available room for a configuration
- Update booking creation to automatically reserve an available room
- Store the reserved room ID in booking metadata
- Add error handling for no availability scenarios

**Current Status**: The booking creation process has been partially implemented. The system currently updates room inventory status to "reserved" when a booking is created, but only if a specific room_id is provided. The helper function to find an available room for a configuration has been defined in the plan but not fully implemented in the codebase.

**Task 2.2: Implement Fragmented Availability Handling** ❌ Not Started
- Create algorithm to find combinations of rooms that cover the entire stay
- Implement function to reserve multiple rooms for a single booking when needed
- Store multiple room reservations in booking metadata
- Add flags to indicate bookings that require manual room assignment

**Current Status**: The algorithm for finding room combinations has been defined in the plan but not implemented in the codebase. The current implementation does not handle fragmented availability.

**Task 2.3: Update Room Assignment Process** ❌ Not Started
- Modify room assignment to handle previously reserved rooms
- Release previously reserved room if a different room is assigned
- Update inventory status from `reserved_unassigned` to `booked` when assigned

**Current Status**: The room assignment process has not been updated to handle previously reserved rooms. This task depends on the implementation of Task 1.1 (adding new inventory status values).

**Task 2.4: Update Booking Cancellation** 🔄 In Progress
- Ensure cancellation releases reserved rooms
- Update inventory status back to `available`
- Handle partial cancellations if implemented

**Current Status**: The RoomInventoryService has a method to release rooms, which updates the inventory status to 'available'. However, it's not clear if this is fully integrated with the booking cancellation process.

### Phase 3: Implement Cart Reservation System

**Task 3.1: Modify Cart Addition Process** 🔄 In Progress
- Create a helper function to find and reserve an available room when added to cart
- Add expiration timestamp to reservation metadata
- Store reservation information in cart metadata

**Current Status**: The cart addition process has been partially implemented. The system adds room information to the cart metadata, but it doesn't yet reserve the room in the inventory with a temporary status. The cart API endpoint exists but doesn't implement the room reservation functionality.

**Task 3.2: Create Expiration Job** ❌ Not Started
- Implement scheduled job to find and release expired cart reservations
- Set appropriate run frequency (e.g., every 5 minutes)
- Add logging for monitoring and debugging

**Current Status**: The expiration job has been defined in the plan but not implemented in the codebase. This task depends on the implementation of Task 1.1 (adding new inventory status values).

**Task 3.3: Handle Cart Completion** ❌ Not Started
- Update cart completion process to convert temporary reservations to booking reservations
- Ensure proper status transition in room inventory

**Current Status**: The cart completion process has not been updated to handle the conversion of temporary reservations to booking reservations. This task depends on the implementation of Tasks 1.1 and 3.1.

### Phase 4: Implementation Review

**Task 4.1: Code Review** ❌ Not Started
- Review implementation for edge cases
- Ensure proper error handling
- Verify transaction handling for race conditions

**Current Status**: This task will be performed after the implementation of Phases 1-3.

**Task 4.2: Performance Optimization** ❌ Not Started
- Optimize room finding algorithms
- Add necessary database indexes
- Implement caching where appropriate

**Current Status**: This task will be performed after the implementation of Phases 1-3.

## Next Steps

Based on the current implementation status, the following tasks should be prioritized:

1. **Implement Task 1.1: Add New Inventory Status Values**
   - Update the room inventory model to include the new status values
   - Update any UI components that display room status
   - This is a prerequisite for many other tasks

2. **Implement Task 1.2: Update Room Inventory Service**
   - Modify the `updateInventoryStatus` method to support the new status values
   - Add validation for status transitions
   - This is a prerequisite for many other tasks

3. **Complete Task 2.1: Modify Booking Creation Process**
   - Implement the helper function to find an available room for a configuration
   - Update booking creation to automatically reserve an available room
   - Store the reserved room ID in booking metadata

4. **Implement Task 2.2: Fragmented Availability Handling**
   - Implement the algorithm for finding room combinations
   - Implement the function to reserve multiple rooms for a single booking

5. **Complete Task 3.1: Modify Cart Addition Process**
   - Implement the room reservation functionality in the cart API endpoint
   - Add expiration timestamp to reservation metadata

These tasks should be implemented following the architectural patterns described in the guidelines, particularly using workflows for business logic and avoiding direct SQL queries.

## Technical Implementation Details

### 1. Finding Available Rooms Function

```typescript
async function findAvailableRoomForConfig(roomConfigId, startDate, endDate) {
  // Get all rooms for this configuration
  const variants = await productVariantService.list(
    { product_id: roomConfigId },
    { select: ["id", "title"] }
  );

  if (!variants || variants.length === 0) {
    return null;
  }

  // Find an available room
  for (const variant of variants) {
    const availability = await roomInventoryService.checkAvailability(
      variant.id,
      startDate,
      endDate
    );

    if (availability.available) {
      return variant;
    }
  }

  return null;
}

// Function to find room combinations for fragmented availability
async function findRoomCombinationForStay(roomConfigId, startDate, endDate) {
  // Get all rooms for this configuration
  const variants = await productVariantService.list(
    { product_id: roomConfigId },
    { select: ["id", "title"] }
  );

  if (!variants || variants.length === 0) {
    return null;
  }

  // Create an array of dates for the stay
  const dates = [];
  let currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate < end) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // For each date, find available rooms
  const availabilityMap = {};

  for (const date of dates) {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);

    availabilityMap[date.toISOString().split('T')[0]] = [];

    for (const variant of variants) {
      const availability = await roomInventoryService.checkAvailability(
        variant.id,
        date,
        nextDay
      );

      if (availability.available) {
        availabilityMap[date.toISOString().split('T')[0]].push(variant.id);
      }
    }

    // If no rooms available for a date, return null
    if (availabilityMap[date.toISOString().split('T')[0]].length === 0) {
      return null;
    }
  }

  // Find optimal room assignments with minimum switches
  return findOptimalRoomAssignments(availabilityMap, dates);
}

function findOptimalRoomAssignments(availabilityMap, dates) {
  // This is a simplified version - a real implementation would use a more sophisticated algorithm
  const assignments = [];
  let currentRoom = null;
  let segmentStart = null;

  for (let i = 0; i < dates.length; i++) {
    const dateStr = dates[i].toISOString().split('T')[0];
    const availableRooms = availabilityMap[dateStr];

    // Try to keep the same room if possible
    if (currentRoom && availableRooms.includes(currentRoom)) {
      // Continue with the same room
      continue;
    } else {
      // Need to switch rooms
      if (currentRoom) {
        // End the previous segment
        const segmentEnd = new Date(dates[i]);
        assignments.push({
          room_id: currentRoom,
          from_date: segmentStart,
          to_date: segmentEnd
        });
      }

      // Start a new segment
      currentRoom = availableRooms[0]; // Pick the first available room
      segmentStart = new Date(dates[i]);
    }
  }

  // Add the last segment
  if (currentRoom && segmentStart) {
    const lastDate = new Date(dates[dates.length - 1]);
    lastDate.setDate(lastDate.getDate() + 1);

    assignments.push({
      room_id: currentRoom,
      from_date: segmentStart,
      to_date: lastDate
    });
  }

  return assignments;
}
```

### 2. Reserving Room During Booking Creation

```typescript
// In booking creation endpoint
async function reserveRoomsForBooking(roomConfigId, checkInDate, checkOutDate, guestName) {
  // First try to find a single room for the entire stay
  const availableRoom = await findAvailableRoomForConfig(
    roomConfigId,
    checkInDate,
    checkOutDate
  );

  // If a single room is available, reserve it
  if (availableRoom) {
    // Reserve this room in the inventory
    await roomInventoryService.updateInventoryStatus(
      availableRoom.id,
      checkInDate,
      checkOutDate,
      "reserved_unassigned",
      `Booking created (unassigned): ${guestName}`,
      { booking_type: "unassigned" }
    );

    // Return single room reservation
    return {
      type: "single_room",
      reservations: [{
        room_id: availableRoom.id,
        from_date: checkInDate,
        to_date: checkOutDate
      }]
    };
  }

  // If no single room is available, try to find a combination of rooms
  const roomCombination = await findRoomCombinationForStay(
    roomConfigId,
    checkInDate,
    checkOutDate
  );

  // If no combination is available, throw error
  if (!roomCombination) {
    throw new Error("No rooms available for the selected dates");
  }

  // Reserve each room segment
  for (const segment of roomCombination) {
    await roomInventoryService.updateInventoryStatus(
      segment.room_id,
      segment.from_date,
      segment.to_date,
      "reserved_unassigned",
      `Booking created (fragmented stay): ${guestName}`,
      {
        booking_type: "unassigned",
        is_fragmented: true
      }
    );
  }

  // Return fragmented reservation
  return {
    type: "fragmented",
    reservations: roomCombination,
    requires_assignment: true
  };
}

// Use in booking creation
const roomReservation = await reserveRoomsForBooking(
  roomConfigId,
  checkInDate,
  checkOutDate,
  guestName
);

// Add the reservation info to booking metadata
bookingData.metadata = {
  ...bookingData.metadata,
  room_reservation: roomReservation,
  requires_assignment: roomReservation.type === "fragmented"
};
```

### 3. Cart Reservation Expiration Job

```typescript
// src/jobs/expire-cart-reservations.ts
export default async function expireCartReservationsJob(container) {
  const roomInventoryService = container.resolve(ROOM_INVENTORY_SERVICE);
  const logger = container.resolve("logger");

  try {
    const now = new Date();
    const expiredReservations = await roomInventoryService.list({
      status: "cart_reserved",
      "metadata.expires_at": { $lt: now.toISOString() }
    });

    for (const reservation of expiredReservations) {
      await roomInventoryService.updateInventoryStatus(
        reservation.inventory_item_id,
        reservation.from_date,
        reservation.to_date,
        "available",
        `Cart reservation expired`,
        { expired_at: now.toISOString() }
      );
    }

    logger.info(`Released ${expiredReservations.length} expired cart reservations`);
  } catch (error) {
    logger.error(`Error expiring cart reservations: ${error.message}`);
  }
}

export const config = {
  name: "expire-cart-reservations",
  schedule: "*/5 * * * *"
};
```

## Potential Risks and Mitigations

### Risk: Race Conditions
**Mitigation**: Use database transactions when updating inventory status to ensure atomicity.

### Risk: Performance Impact
**Mitigation**:
- Optimize queries for finding available rooms
- Consider adding indexes to improve performance
- Monitor query execution time

### Risk: Disruption to Existing Bookings
**Mitigation**:
- Only apply new logic to new bookings
- Ensure backward compatibility with existing code
- Thorough testing before deployment

### Risk: Expired Reservations Not Released
**Mitigation**:
- Implement monitoring for reservation counts
- Add redundant cleanup job that runs less frequently but is more thorough
- Set up alerts for reservations that exceed expected duration

### Risk: Fragmented Stay Complexity
**Mitigation**:
- Implement clear UI indicators for bookings requiring manual assignment
- Create admin tools to easily manage fragmented stays
- Add validation to ensure all days of a booking are covered by room assignments

## Integration with Medusa Workflows

The implementation will leverage Medusa's existing workflows:

1. **Cart Workflow**: Extend the cart workflow to handle room reservations
2. **Order Creation Workflow**: Modify to handle the transition from cart reservations to booking reservations
3. **Inventory Management**: Use Medusa's inventory system as the foundation

## Implementation Summary

### Current Progress

The implementation of the room reservation system for unassigned bookings is currently in the following state:

- **Phase 1: Extend Room Inventory Status** - ❌ Not Started
  - The room inventory model has a status field, but the new status values have not been added yet
  - The room inventory service needs to be updated to support the new status values

- **Phase 2: Implement Unassigned Booking Reservation** - 🔄 Partially Implemented
  - The booking creation process has been partially implemented
  - The fragmented availability handling has not been implemented
  - The room assignment process has not been updated
  - The booking cancellation process has been partially implemented

- **Phase 3: Implement Cart Reservation System** - 🔄 Partially Implemented
  - The cart addition process has been partially implemented
  - The expiration job has not been implemented
  - The cart completion process has not been updated

- **Phase 4: Implementation Review** - ❌ Not Started
  - Code review and performance optimization will be performed after Phases 1-3

### Implementation Approach

The implementation should follow these architectural principles:

1. **Use Workflows**: Implement business logic using Medusa workflows
2. **Avoid Direct SQL Queries**: Use module services for data access
3. **Proper Error Handling**: Implement proper error handling for all operations
4. **No Mock Data**: Avoid using mock data or fallback mechanisms
5. **Service Resolution**: Use proper service resolution with Module constants

## Conclusion

This implementation provides a solution to the double booking issue by automatically reserving rooms in the inventory system when bookings are created, even if specific rooms aren't assigned yet. The solution handles both simple cases (single room for entire stay) and complex cases (different rooms on different days).

Key benefits:

1. **Prevents Overbooking**: By reserving rooms immediately, even if they're not officially assigned, the system prevents accepting more bookings than available capacity.

2. **Handles Fragmented Availability**: The system can maximize occupancy by using different rooms on different days when no single room is available for the entire stay.

3. **Maintains Staff Flexibility**: Hotel staff retain control over final room assignments while the system ensures sufficient capacity is reserved.

4. **Leverages Existing Infrastructure**: The solution builds on the current room inventory system without requiring significant schema changes.

The next steps focus on implementing the new inventory status values and updating the room inventory service, which are prerequisites for many other tasks.
