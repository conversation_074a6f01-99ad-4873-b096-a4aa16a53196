# Guidelines for Augment: Avoiding Mock Data and Implementations

## Overview

Augment, this document provides specific guidelines to help you avoid creating mock data, mock functions, and fallback mechanisms in production code. These practices create a false sense that functionality is working when it actually isn't in production, leading to difficult-to-diagnose issues and data inconsistencies.

## Critical Issues with Mock Data and Implementations

### 1. False Sense of Functionality

Mock data and functions create an illusion that features are working correctly, but they will fail in production environments where real data and services are required.

### 2. Delayed Discovery of Issues

Problems are often discovered only in production, leading to critical issues that could have been caught earlier if proper implementations were used.

### 3. Technical Debt

Mock implementations create technical debt that must be addressed later, often under time pressure when production issues arise.

### 4. Data Inconsistencies

Hardcoded values and mock data can lead to data inconsistencies and incorrect business logic.

## Specific Guidelines for Augment

### 1. Never Create Mock Data in Production Code

**AVOID:**
```typescript
// This creates mock room data instead of fetching from the database
const rooms = [
  { id: "room_1", name: "Deluxe Room", price: 200 },
  { id: "room_2", name: "Suite", price: 350 },
];

return res.json({ data: rooms });
```

**INSTEAD:**
```typescript
// Fetch real data from the appropriate service
const roomService = container.resolve("roomService");
const rooms = await roomService.list();
return res.json({ data: rooms });
```

### 2. Never Create Mock Service Implementations

**AVOID:**
```typescript
// Creating a mock service implementation
if (!roomInventoryService) {
  console.log("Room inventory service not available, using mock implementation");
  return {
    checkAvailability: async () => ({ available: true }),
    updateInventory: async () => ({ success: true }),
  };
}
```

**INSTEAD:**
```typescript
// Throw a clear error if a required service is not available
if (!roomInventoryService) {
  throw new MedusaError(
    MedusaError.Types.UNEXPECTED_STATE,
    "Room inventory service is not available"
  );
}
```

### 3. Never Use Silent Fallbacks to Default Values

**AVOID:**
```typescript
// Silently falling back to default values
try {
  const price = await priceService.getPrice(roomId, dates);
  return price;
} catch (error) {
  console.log("Error getting price, using default:", error);
  return {
    amount: 100,
    currency_code: "USD",
    formatted: "$100.00",
  };
}
```

**INSTEAD:**
```typescript
// Throw a clear error when services fail
try {
  const price = await priceService.getPrice(roomId, dates);
  return price;
} catch (error) {
  throw new MedusaError(
    MedusaError.Types.INVALID_DATA,
    `Failed to get price for room ${roomId}: ${error.message}`
  );
}
```

### 4. Never Hardcode Business Logic

**AVOID:**
```typescript
// Hardcoding business logic for price calculation
function calculateTotalPrice(nights, adults, children) {
  const basePrice = 100; // Hardcoded base price
  const adultRate = 1.0; // Hardcoded adult rate
  const childRate = 0.5; // Hardcoded child rate
  
  return nights * (
    basePrice * (adults * adultRate + children * childRate)
  );
}
```

**INSTEAD:**
```typescript
// Use services to apply business logic
async function calculateTotalPrice(roomId, nights, adults, children, dates) {
  const pricingService = container.resolve(Modules.PRICING);
  
  // Get the appropriate price based on room, dates, and occupancy
  const price = await pricingService.calculatePrice({
    room_id: roomId,
    start_date: dates.check_in,
    end_date: dates.check_out,
    adults,
    children,
  });
  
  return price.total_amount;
}
```

### 5. Never Create Conditional Mock Implementations

**AVOID:**
```typescript
// Conditional mock implementation based on environment
if (process.env.NODE_ENV === "development") {
  // Return mock data for development
  return res.json({
    data: [
      { id: "room_1", name: "Deluxe Room", price: 200 },
      { id: "room_2", name: "Suite", price: 350 },
    ],
  });
} else {
  // Use real implementation for production
  const roomService = req.scope.resolve("roomService");
  const rooms = await roomService.list();
  return res.json({ data: rooms });
}
```

**INSTEAD:**
```typescript
// Use the same implementation for all environments
const roomService = req.scope.resolve("roomService");
const rooms = await roomService.list();
return res.json({ data: rooms });
```

### 6. Never Use Mock Data for Testing in Production Code

**AVOID:**
```typescript
// Using mock data for "testing" in production code
if (roomId === "test_room") {
  return {
    available: true,
    price: {
      amount: 100,
      currency_code: "USD",
      formatted: "$100.00",
    },
  };
}

// Real implementation
const availability = await roomInventoryService.checkAvailability(roomId, dates);
return availability;
```

**INSTEAD:**
```typescript
// Always use the real implementation
const availability = await roomInventoryService.checkAvailability(roomId, dates);
return availability;

// If testing is needed, use proper test environments and test files
```

### 7. Never Hardcode IDs or References

**AVOID:**
```typescript
// Hardcoding IDs or references
const defaultHotelId = "hotel_123";
const defaultRoomTypeId = "room_type_456";

// Using hardcoded IDs if not provided
const hotelId = req.params.hotelId || defaultHotelId;
const roomTypeId = req.params.roomTypeId || defaultRoomTypeId;
```

**INSTEAD:**
```typescript
// Require proper IDs or throw clear errors
const hotelId = req.params.hotelId;
if (!hotelId) {
  throw new MedusaError(
    MedusaError.Types.INVALID_DATA,
    "Hotel ID is required"
  );
}

const roomTypeId = req.params.roomTypeId;
if (!roomTypeId) {
  throw new MedusaError(
    MedusaError.Types.INVALID_DATA,
    "Room type ID is required"
  );
}
```

## What to Do Instead of Using Mock Data

### 1. Implement the Actual Functionality

Always implement the actual functionality using proper services, workflows, and data access patterns.

### 2. Throw Clear Errors for Unimplemented Features

If a feature is not yet implemented, throw a clear error indicating that the feature is not available:

```typescript
throw new MedusaError(
  MedusaError.Types.NOT_IMPLEMENTED,
  "This feature is not yet implemented"
);
```

### 3. Use Feature Flags for Controlled Rollout

If you need to control the availability of features, use feature flags:

```typescript
const featureFlagService = container.resolve("featureFlagService");

if (await featureFlagService.isEnabled("new_pricing_engine")) {
  // Use new pricing engine
  return await newPricingEngine.calculatePrice(input);
} else {
  // Use standard pricing engine
  return await standardPricingEngine.calculatePrice(input);
}
```

### 4. Write Proper Tests

Instead of using mock data in production code, write proper tests:

```typescript
// In a test file, not production code
describe("RoomInventoryService", () => {
  let container;
  let roomInventoryService;
  
  beforeEach(() => {
    container = createMedusaContainer();
    
    // Mock dependencies for testing
    const mockDatabase = {
      query: jest.fn().mockResolvedValue({ rows: [] }),
    };
    
    container.register({
      database: { resolve: () => mockDatabase },
    });
    
    roomInventoryService = new RoomInventoryService(container);
  });
  
  it("should check availability correctly", async () => {
    // Test implementation
  });
});
```

## Conclusion

Augment, by following these guidelines, you'll help create a more robust and reliable codebase that works correctly in all environments. Remember:

1. **Never use mock data** in production code
2. **Never create mock service implementations**
3. **Never use silent fallbacks** to default values
4. **Never hardcode business logic**
5. **Never create conditional mock implementations**
6. **Never use mock data for testing** in production code
7. **Never hardcode IDs or references**

Instead, implement the actual functionality, throw clear errors for unimplemented features, use feature flags for controlled rollout, and write proper tests.

If you're unsure about how to implement a feature properly, ask for guidance rather than creating a mock implementation that will cause problems later.
