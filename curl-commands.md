# Hotel Booking API Curl Commands

## Store API Endpoints

### 1. Create a Hotel Booking Cart

#### Basic Usage (Backward Compatible)

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
    "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "room_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04",
    "check_in_time": "14:00",
    "check_out_time": "11:00",
    "guest_name": "<PERSON>",
    "guest_email": "<EMAIL>",
    "guest_phone": "1234567890",
    "adults": 2,
    "children": 0,
    "infants": 0,
    "number_of_rooms": 2,
    "total_amount": 500,
    "currency_code": "usd",
    "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E",
    "special_requests": "Late check-in",
    "notes": "First floor room preferred",
    "metadata": {
      "source": "website",
      "promo_code": "SUMMER2025"
    },
    "shipping_address": {
      "first_name": "John",
      "last_name": "Doe",
      "address_1": "123 Main St",
      "city": "New York",
      "country_code": "us",
      "postal_code": "10001",
      "phone": "1234567890"
    }
  }'
```

#### With Multiple Guest Details

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
    "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04",
    "check_in_time": "14:00",
    "check_out_time": "11:00",
    "guest_name": "John Doe",
    "guest_email": "<EMAIL>",
    "guest_phone": "1234567890",
    "adults": 3,
    "children": 1,
    "infants": 1,
    "travelers": {
      "adults": [
        { "name": "Jane Doe" },
        { "name": "Bob Smith" }
      ],
      "children": [
        { "name": "Jimmy Doe", "age": 8 }
      ],
      "infants": [
        { "name": "Baby Doe", "age": 6 }
      ]
    },
    "number_of_rooms": 1,
    "total_amount": 500,
    "currency_code": "usd",
    "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E",
    "special_requests": "Late check-in",
    "notes": "Family vacation",
    "metadata": {
      "source": "website",
      "promo_code": "FAMILY2025"
    },
    "shipping_address": {
      "first_name": "John",
      "last_name": "Doe",
      "address_1": "123 Main St",
      "city": "New York",
      "country_code": "us",
      "postal_code": "10001",
      "phone": "1234567890"
    }
  }'
```

### 2. Create a Stripe Checkout Session for a Cart

```bash
curl -X POST "http://localhost:9000/store/checkout/sessions" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "cart_id": "cart_01JSP7808B5F0EX1GQA4BYER6P",
    "success_url": "https://yourfrontend.com/checkout/success?session_id={CHECKOUT_SESSION_ID}",
    "cancel_url": "https://yourfrontend.com/cart"
  }'
```

This endpoint creates a Stripe Checkout Session and returns a checkout URL that you can redirect users to for payment. It also automatically creates a payment collection for the cart. The response will include:

```json
{
  "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_...",
  "session_id": "cs_test_...",
  "cart_id": "cart_01JSP7808B5F0EX1GQA4BYER6P"
}
```

### 3. Complete Cart After Stripe Payment (Manual Method)

```bash
curl -X POST "http://localhost:9000/store/checkout/complete" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "cart_id": "cart_01JSP7808B5F0EX1GQA4BYER6P",
    "session_id": "cs_test_..."
  }'
```

This endpoint should be called after the user successfully completes payment on Stripe. It verifies the payment and converts the cart to an order.

### 4. Create a Payment Session for a Cart (Alternative Method)

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart/payment" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "cart_id": "cart_01JSP7808B5F0EX1GQA4BYER6P",
    "payment_provider_id": "pp_stripe_stripe"
  }'
```

The `payment_provider_id` parameter specifies which payment provider to use:

- `manual` - For manual payments (default if not provided)
- `stripe` - For Stripe payments (requires Stripe plugin)
- `pp_stripe_stripe` - For Stripe payments with payment processor plugin
- `paypal` - For PayPal payments (requires PayPal plugin)

For non-manual payment providers, you'll need to handle the payment authorization in your frontend application using the payment session data returned by this endpoint.

### 5. Update a Payment Session (for non-manual providers)

Updates a payment session with additional data, such as payment intent confirmation.

```bash
curl -X PUT "http://localhost:9000/store/hotel-management/cart/payment" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "cart_id": "cart_01JSNVC3RWG25MR8N1BSQ9N70V",
    "payment_session_id": "ps_01JSNVC3RWG25MR8N1BSQ9N70V",
    "data": {
      "status": "authorized"
    }
  }'
```

### 6. Complete a Hotel Booking (Convert Cart to Order)

Completes the cart and converts it to an order. A payment session must be created and authorized before calling this endpoint.

```bash
curl -X PUT "http://localhost:9000/store/hotel-management/cart" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "cart_id": "cart_01JSNVC3RWG25MR8N1BSQ9N70V",
    "room_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04"
  }'
```

Note: You must create and authorize a payment session using the `/store/hotel-management/cart/payment` endpoint before completing the cart.

### 7. Get Customer Bookings

```bash
curl -X GET "http://localhost:9000/store/hotel-management/bookings" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -H "Cookie: connect.sid=YOUR_SESSION_COOKIE"
```

## Admin API Endpoints

### 1. Create a Hotel Booking (Admin)

```bash
curl -X POST "http://localhost:9000/admin/hotel-management/bookings" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
    "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "room_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
    "check_in_date": "2025-05-01",
    "check_out_date": "2025-05-04",
    "check_in_time": "14:00",
    "check_out_time": "11:00",
    "guest_name": "John Doe",
    "guest_email": "<EMAIL>",
    "guest_phone": "1234567890",
    "adults": 2,
    "children": 0,
    "infants": 0,
    "number_of_rooms": 1,
    "total_amount": 500,
    "currency_code": "usd",
    "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E",
    "special_requests": "Late check-in",
    "notes": "First floor room preferred",
    "metadata": {
      "source": "admin",
      "promo_code": "SUMMER2025"
    }
  }'
```

### 2. Get All Bookings (Admin)

```bash
curl -X GET "http://localhost:9000/admin/hotel-management/bookings" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b"
```

### 3. Get Booking by ID (Admin)

```bash
curl -X GET "http://localhost:9000/admin/hotel-management/bookings/order_01HPVXS5JYQWQ5KZ3GCWH06W0E" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b"
```

### 4. Update Booking (Admin)

```bash
curl -X PUT "http://localhost:9000/admin/hotel-management/bookings/order_01HPVXS5JYQWQ5KZ3GCWH06W0E" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b" \
  -d '{
    "status": "completed",
    "payment_status": "paid",
    "notes": "Customer paid in full at check-in"
  }'
```

### 5. Cancel Booking (Admin)

```bash
curl -X DELETE "http://localhost:9000/admin/hotel-management/bookings/order_01HPVXS5JYQWQ5KZ3GCWH06W0E" \
  -H "Content-Type: application/json" \
  -H "x-publishable-key: pk_d00b2f6be8293bf310a4f2dab6bba2f4e7e1800647febdbe9b994e9af6191a9b"
```

## Complete Booking Flow Example

Here's a complete example of the booking flow:

1. Create a cart:

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart" -H "Content-Type: application/json" -d '{
  "hotel_id": "01JQV5KJZYJBK75VVZYYJDAVFW",
  "room_config_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
  "check_in_date": "2025-05-01",
  "check_out_date": "2025-05-04",
  "guest_name": "John Doe",
  "guest_email": "<EMAIL>",
  "number_of_rooms": 2,
  "region_id": "reg_01HPVXS5JYQWQ5KZ3GCWH06W0E",
  "currency_code": "usd",
  "total_amount": 500
}'
```

2. Create a payment session:

```bash
curl -X POST "http://localhost:9000/store/hotel-management/cart/payment" -H "Content-Type: application/json" -d '{
  "cart_id": "cart_01JSNVC3RWG25MR8N1BSQ9N70V",
  "payment_provider_id": "manual"
}'
```

3. Complete the cart:

```bash
curl -X PUT "http://localhost:9000/store/hotel-management/cart" -H "Content-Type: application/json" -d '{
  "cart_id": "cart_01JSNVC3RWG25MR8N1BSQ9N70V",
  "room_id": "prod_01JS8ACDRTNCCZ7R0F86RX1VPR",
  "check_in_date": "2025-05-01",
  "check_out_date": "2025-05-04"
}'
```

## Notes

1. Replace `localhost:9000` with your actual server address if different
2. The `region_id` should be a valid region ID from your Medusa instance (starting with "reg\_")
3. For the store GET request, you'll need to be logged in as a customer and include the session cookie
4. Replace `YOUR_SESSION_COOKIE` with an actual session cookie from a logged-in user
5. Replace `order_01HPVXS5JYQWQ5KZ3GCWH06W0E` with an actual order ID
6. Replace `cart_01JSNVC3RWG25MR8N1BSQ9N70V` with the cart ID returned from the POST request to `/store/hotel-management/cart`
7. Replace `ps_01JSNVC3RWG25MR8N1BSQ9N70V` with the payment session ID returned from the POST request to `/store/hotel-management/cart/payment`
8. The `number_of_rooms` parameter specifies how many rooms to book. The API will use this value to set the quantity of the line item in the cart and calculate the unit price by dividing the total amount by the number of rooms.
